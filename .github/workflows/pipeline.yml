name: Deploy Infra

on:
  push:
    branches:
      - main
  repository_dispatch:
    types: [deploy_infra]

jobs:
  deploy-aws:
    runs-on: self-hosted
    defaults:
      run:
        working-directory: ./cdk
    permissions:
      id-token: write # Required for requesting the JWT
      contents: read # Required for actions/checkout
    continue-on-error: true

    steps:
      - name: Checkout code
        uses: actions/checkout@v4

      - name: Setup Node.js
        uses: actions/setup-node@v4
        with:
          node-version: "20"

      - name: Setup PNPM
        uses: pnpm/action-setup@v3
        with:
          version: "10.7.0"
          run_install: false

      - name: Get pnpm store directory
        id: pnpm-cache
        shell: bash
        run: |
          echo "STORE_PATH=$(pnpm store path)" >> $GITHUB_OUTPUT

      - name: Setup pnpm cache
        uses: actions/cache@v4
        with:
          path: ${{ steps.pnpm-cache.outputs.STORE_PATH }}
          key: ${{ runner.os }}-pnpm-store-${{ hashFiles('**/pnpm-lock.yaml') }}
          restore-keys: |
            ${{ runner.os }}-pnpm-store-

      - name: Install dependencies
        run: pnpm install

      - name: Configure AWS credentials
        uses: aws-actions/configure-aws-credentials@v4
        with:
          role-to-assume: arn:aws:iam::851725421927:role/GitHubActionsRole
          aws-region: ap-southeast-1
          role-session-name: GitHubActionsSession

      - name: Deploy dev with CDK
        run: pnpm run cdk:deploy-dev

  deploy-services:
    runs-on: self-hosted
    needs: deploy-aws
    continue-on-error: true

    steps:
      - name: Checkout Infra code
        uses: actions/checkout@v4

      - name: Log in to GitHub Container Registry
        uses: docker/login-action@v3
        with:
          registry: ghcr.io
          username: thanh-nguyen03
          password: ${{ secrets.GHCR_PAT }}

      - name: Pull latest Infra code on server
        run: |
          cd /home/<USER>/eap/infra
          git pull origin main

      - name: Deploy Docker Compose stack
        run: |
          cd /home/<USER>/eap/infra

          # Pull latest images based on your docker-compose.yml
          # This will pull the images from GHCR
          docker compose pull

          # Bring up the services, ensure no local build, and remove old containers
          docker compose up -d --no-build --remove-orphans

          echo "Deployment complete for Infra and all services!"

  send-notifications:
    needs: [deploy-aws, deploy-services]
    runs-on: ubuntu-latest
    if: always()
    steps:
      - name: Send Discord Notification
        uses: sarisia/actions-status-discord@v1
        with:
          webhook: ${{ secrets.DISCORD_WEBHOOK_URL }}
          status: ${{ job.status }}
          title: "CI/CD Pipeline Status"
          description: |
            Repository: `${{ github.repository }}`
            Branch: `${{ github.ref_name }}`
            Commit: `${{ github.sha }}`
            Workflow: `${{ github.workflow }}`
            Run URL: ${{ github.server_url }}/${{ github.repository }}/actions/runs/${{ github.run_id }}

            **Status: ${{ job.status }}**
          color-success: 0x2ECC71 # Green
          color-failure: 0xE74C3C # Red
          color-cancelled: 0xF1C40F # Yellow
          include-details: true
