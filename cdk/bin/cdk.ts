#!/usr/bin/env node
import * as cdk from "aws-cdk-lib";
import { FrontendStack } from "../lib/FrontendStack";

const app = new cdk.App();

const stage = app.node.tryGetContext("stage");

if (!stage) {
  throw new Error(
    "Error: Stage must be specified using the context parameter: -c stage=<stage>"
  );
}

const envConfig = app.node.tryGetContext(stage);

if (!envConfig) {
  throw new Error(
    `Error: Environment configuration for stage ${stage} not found in cdk.json`
  );
}

const { accountId, region, envVariables } = envConfig;

const { applicationName } = envVariables;

const env = {
  accountId,
  region,
};

new FrontendStack(app, "FrontendStack", {
  env,
  applicationName,
});
