"use strict";Object.defineProperty(exports,"__esModule",{value:!0}),exports.DynamoMethod=void 0,exports.getDynamoResourceArn=getDynamoResourceArn,exports.transformAttributeValueMap=transformAttributeValueMap,exports.validateJsonPath=validateJsonPath,exports.validateJsonata=validateJsonata;var sfn=()=>{var tmp=require("../../../../aws-stepfunctions");return sfn=()=>tmp,tmp},core_1=()=>{var tmp=require("../../../../core");return core_1=()=>tmp,tmp},task_utils_1=()=>{var tmp=require("../../private/task-utils");return task_utils_1=()=>tmp,tmp},DynamoMethod;(function(DynamoMethod2){DynamoMethod2.GET="Get",DynamoMethod2.PUT="Put",DynamoMethod2.DELETE="Delete",DynamoMethod2.UPDATE="Update"})(DynamoMethod||(exports.DynamoMethod=DynamoMethod={}));function getDynamoResourceArn(method){return(0,task_utils_1().integrationResourceArn)("dynamodb",`${method.toLowerCase()}Item`,sfn().IntegrationPattern.REQUEST_RESPONSE)}function transformAttributeValueMap(attrMap){const transformedValue={};for(const key in attrMap)key&&(transformedValue[key]=attrMap[key].toObject());return attrMap?transformedValue:void 0}function validateJsonPath(value){if(!value.startsWith("$"))throw new(core_1()).UnscopedValidationError("Data JSON path values must either be exactly equal to '$' or start with '$.'")}function validateJsonata(value){if(!value.startsWith("{%")||!value.endsWith("%}"))throw new(core_1()).UnscopedValidationError("Data JSONata expression values must either be exactly start with '{%' and end with '%}'")}
