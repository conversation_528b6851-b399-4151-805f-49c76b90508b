"use strict";var _a;Object.defineProperty(exports,"__esModule",{value:!0}),exports.EncryptionOption=exports.AthenaStartQueryExecution=void 0;var jsiiDeprecationWarnings=()=>{var tmp=require("../../../.warnings.jsii.js");return jsiiDeprecationWarnings=()=>tmp,tmp};const JSII_RTTI_SYMBOL_1=Symbol.for("jsii.rtti");var iam=()=>{var tmp=require("../../../aws-iam");return iam=()=>tmp,tmp},sfn=()=>{var tmp=require("../../../aws-stepfunctions");return sfn=()=>tmp,tmp},cdk=()=>{var tmp=require("../../../core");return cdk=()=>tmp,tmp},core_1=()=>{var tmp=require("../../../core");return core_1=()=>tmp,tmp},task_utils_1=()=>{var tmp=require("../private/task-utils");return task_utils_1=()=>tmp,tmp};class AthenaStartQueryExecution extends sfn().TaskStateBase{static jsonPath(scope,id,props){try{jsiiDeprecationWarnings().aws_cdk_lib_aws_stepfunctions_tasks_AthenaStartQueryExecutionJsonPathProps(props)}catch(error){throw process.env.JSII_DEBUG!=="1"&&error.name==="DeprecationError"&&Error.captureStackTrace(error,this.jsonPath),error}return new AthenaStartQueryExecution(scope,id,props)}static jsonata(scope,id,props){try{jsiiDeprecationWarnings().aws_cdk_lib_aws_stepfunctions_tasks_AthenaStartQueryExecutionJsonataProps(props)}catch(error){throw process.env.JSII_DEBUG!=="1"&&error.name==="DeprecationError"&&Error.captureStackTrace(error,this.jsonata),error}return new AthenaStartQueryExecution(scope,id,{...props,queryLanguage:sfn().QueryLanguage.JSONATA})}constructor(scope,id,props){super(scope,id,props),this.props=props;try{jsiiDeprecationWarnings().aws_cdk_lib_aws_stepfunctions_tasks_AthenaStartQueryExecutionProps(props)}catch(error){throw process.env.JSII_DEBUG!=="1"&&error.name==="DeprecationError"&&Error.captureStackTrace(error,AthenaStartQueryExecution),error}this.integrationPattern=props.integrationPattern??sfn().IntegrationPattern.REQUEST_RESPONSE,(0,task_utils_1().validatePatternSupported)(this.integrationPattern,AthenaStartQueryExecution.SUPPORTED_INTEGRATION_PATTERNS),this.validateExecutionParameters(props.executionParameters),this.validateMaxAgeInMinutes(props.resultReuseConfigurationMaxAge),this.taskPolicies=this.createPolicyStatements()}validateExecutionParameters(executionParameters){if(executionParameters===void 0||cdk().Token.isUnresolved(executionParameters))return;if(executionParameters.length==0)throw new(core_1()).ValidationError("'executionParameters' must be a non-empty list",this);if(executionParameters.some(p=>p.length<1||p.length>1024))throw new(core_1()).ValidationError("'executionParameters' items's length must be between 1 and 1024 characters",this)}validateMaxAgeInMinutes(resultReuseConfigurationMaxAge){if(resultReuseConfigurationMaxAge===void 0||cdk().Token.isUnresolved(resultReuseConfigurationMaxAge))return;const maxAgeInMillis=resultReuseConfigurationMaxAge.toMilliseconds();if(maxAgeInMillis>0&&maxAgeInMillis<cdk().Duration.minutes(1).toMilliseconds())throw new(core_1()).ValidationError(`resultReuseConfigurationMaxAge must be greater than or equal to 1 minute or be equal to 0, got ${maxAgeInMillis} ms`,this);const maxAgeInMinutes=resultReuseConfigurationMaxAge.toMinutes();if(maxAgeInMinutes>10080)throw new(core_1()).ValidationError(`resultReuseConfigurationMaxAge must either be 0 or between 1 and 10080 minutes, got ${maxAgeInMinutes}`,this)}createPolicyStatements(){const policyStatements=[new(iam()).PolicyStatement({resources:[cdk().Stack.of(this).formatArn({service:"athena",resource:"datacatalog",resourceName:this.props.queryExecutionContext?.catalogName??"AwsDataCatalog"}),cdk().Stack.of(this).formatArn({service:"athena",resource:"workgroup",resourceName:this.props.workGroup??"primary"})],actions:["athena:getDataCatalog","athena:startQueryExecution","athena:getQueryExecution"]})];return policyStatements.push(new(iam()).PolicyStatement({actions:["s3:CreateBucket","s3:ListBucket","s3:GetBucketLocation","s3:GetObject"],resources:["*"]})),policyStatements.push(new(iam()).PolicyStatement({actions:["s3:AbortMultipartUpload","s3:ListBucketMultipartUploads","s3:ListMultipartUploadParts","s3:PutObject"],resources:[this.props.resultConfiguration?.outputLocation?.bucketName?cdk().Stack.of(this).formatArn({region:"",account:"",service:"s3",resource:this.props.resultConfiguration?.outputLocation?.bucketName,resourceName:`${this.props.resultConfiguration?.outputLocation?.objectKey}/*`}):"*"]})),policyStatements.push(new(iam()).PolicyStatement({actions:["lakeformation:GetDataAccess"],resources:["*"]})),policyStatements.push(new(iam()).PolicyStatement({actions:["glue:BatchCreatePartition","glue:BatchDeletePartition","glue:BatchDeleteTable","glue:BatchGetPartition","glue:CreateDatabase","glue:CreatePartition","glue:CreateTable","glue:DeleteDatabase","glue:DeletePartition","glue:DeleteTable","glue:GetDatabase","glue:GetDatabases","glue:GetPartition","glue:GetPartitions","glue:GetTable","glue:GetTables","glue:UpdateDatabase","glue:UpdatePartition","glue:UpdateTable"],resources:[cdk().Stack.of(this).formatArn({service:"glue",resource:"catalog"}),cdk().Stack.of(this).formatArn({service:"glue",resource:"database",resourceName:this.props.queryExecutionContext?.databaseName??"default"}),cdk().Stack.of(this).formatArn({service:"glue",resource:"table",resourceName:(this.props.queryExecutionContext?.databaseName??"default")+"/*"}),cdk().Stack.of(this).formatArn({service:"glue",resource:"userDefinedFunction",resourceName:(this.props.queryExecutionContext?.databaseName??"default")+"/*"})]})),policyStatements}renderEncryption(){return this.props.resultConfiguration?.encryptionConfiguration!==void 0?{EncryptionOption:this.props.resultConfiguration.encryptionConfiguration.encryptionOption,KmsKey:this.props.resultConfiguration.encryptionConfiguration.encryptionKey}:void 0}_renderTask(topLevelQueryLanguage){const queryLanguage=sfn()._getActualQueryLanguage(topLevelQueryLanguage,this.props.queryLanguage);return{Resource:(0,task_utils_1().integrationResourceArn)("athena","startQueryExecution",this.integrationPattern),...this._renderParametersOrArguments({QueryString:this.props.queryString,ClientRequestToken:this.props.clientRequestToken,QueryExecutionContext:this.props.queryExecutionContext?.catalogName||this.props.queryExecutionContext?.databaseName?{Catalog:this.props.queryExecutionContext?.catalogName,Database:this.props.queryExecutionContext?.databaseName}:void 0,ResultConfiguration:{EncryptionConfiguration:this.renderEncryption(),OutputLocation:this.props.resultConfiguration?.outputLocation?`s3://${this.props.resultConfiguration.outputLocation.bucketName}/${this.props.resultConfiguration.outputLocation.objectKey}/`:void 0},WorkGroup:this.props.workGroup,ExecutionParameters:this.props.executionParameters,ResultReuseConfiguration:this.props.resultReuseConfigurationMaxAge?{ResultReuseByAgeConfiguration:{Enabled:!0,MaxAgeInMinutes:this.props.resultReuseConfigurationMaxAge.toMinutes()}}:void 0},queryLanguage)}}}exports.AthenaStartQueryExecution=AthenaStartQueryExecution,_a=JSII_RTTI_SYMBOL_1,AthenaStartQueryExecution[_a]={fqn:"aws-cdk-lib.aws_stepfunctions_tasks.AthenaStartQueryExecution",version:"2.201.0"},AthenaStartQueryExecution.SUPPORTED_INTEGRATION_PATTERNS=[sfn().IntegrationPattern.REQUEST_RESPONSE,sfn().IntegrationPattern.RUN_JOB];var EncryptionOption;(function(EncryptionOption2){EncryptionOption2.S3_MANAGED="SSE_S3",EncryptionOption2.KMS="SSE_KMS",EncryptionOption2.CLIENT_SIDE_KMS="CSE_KMS"})(EncryptionOption||(exports.EncryptionOption=EncryptionOption={}));
