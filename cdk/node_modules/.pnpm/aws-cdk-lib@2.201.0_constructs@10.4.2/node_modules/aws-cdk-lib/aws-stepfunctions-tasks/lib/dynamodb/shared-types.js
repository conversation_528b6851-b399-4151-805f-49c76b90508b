"use strict";var _a,_b;Object.defineProperty(exports,"__esModule",{value:!0}),exports.DynamoAttributeValue=exports.DynamoProjectionExpression=exports.DynamoReturnValues=exports.DynamoItemCollectionMetrics=exports.DynamoConsumedCapacity=void 0;const JSII_RTTI_SYMBOL_1=Symbol.for("jsii.rtti");var utils_1=()=>{var tmp=require("./private/utils");return utils_1=()=>tmp,tmp},core_1=()=>{var tmp=require("../../../core");return core_1=()=>tmp,tmp},DynamoConsumedCapacity;(function(DynamoConsumedCapacity2){DynamoConsumedCapacity2.INDEXES="INDEXES",DynamoConsumedCapacity2.TOTAL="TOTAL",DynamoConsumedCapacity2.NONE="NONE"})(DynamoConsumedCapacity||(exports.DynamoConsumedCapacity=DynamoConsumedCapacity={}));var DynamoItemCollectionMetrics;(function(DynamoItemCollectionMetrics2){DynamoItemCollectionMetrics2.SIZE="SIZE",DynamoItemCollectionMetrics2.NONE="NONE"})(DynamoItemCollectionMetrics||(exports.DynamoItemCollectionMetrics=DynamoItemCollectionMetrics={}));var DynamoReturnValues;(function(DynamoReturnValues2){DynamoReturnValues2.NONE="NONE",DynamoReturnValues2.ALL_OLD="ALL_OLD",DynamoReturnValues2.UPDATED_OLD="UPDATED_OLD",DynamoReturnValues2.ALL_NEW="ALL_NEW",DynamoReturnValues2.UPDATED_NEW="UPDATED_NEW"})(DynamoReturnValues||(exports.DynamoReturnValues=DynamoReturnValues={}));class DynamoProjectionExpression{constructor(){this.expression=[]}withAttribute(attr){return this.expression.length?this.expression.push(`.${attr}`):this.expression.push(attr),this}atIndex(index){if(!this.expression.length)throw new(core_1()).UnscopedValidationError("Expression must start with an attribute");return this.expression.push(`[${index}]`),this}toString(){return this.expression.join("")}}exports.DynamoProjectionExpression=DynamoProjectionExpression,_a=JSII_RTTI_SYMBOL_1,DynamoProjectionExpression[_a]={fqn:"aws-cdk-lib.aws_stepfunctions_tasks.DynamoProjectionExpression",version:"2.201.0"};class DynamoAttributeValue{static fromString(value){return new DynamoAttributeValue({S:value})}static fromNumber(value){return new DynamoAttributeValue({N:value.toString()})}static numberFromString(value){return new DynamoAttributeValue({N:value.toString()})}static fromBinary(value){return new DynamoAttributeValue({B:value})}static fromStringSet(value){return new DynamoAttributeValue({SS:value})}static fromNumberSet(value){return new DynamoAttributeValue({NS:value.map(String)})}static numberSetFromStrings(value){return new DynamoAttributeValue({NS:value})}static fromBinarySet(value){return new DynamoAttributeValue({BS:value})}static fromMap(value){return new DynamoAttributeValue({M:(0,utils_1().transformAttributeValueMap)(value)})}static mapFromJsonata(value){return(0,utils_1().validateJsonata)(value),new DynamoAttributeValue({M:value})}static mapFromJsonPath(value){return(0,utils_1().validateJsonPath)(value),new DynamoAttributeValue({"M.$":value})}static fromList(value){return new DynamoAttributeValue({L:value.map(val=>val.toObject())})}static listFromJsonata(value){return(0,utils_1().validateJsonata)(value),new DynamoAttributeValue({L:value})}static listFromJsonPath(value){return(0,utils_1().validateJsonPath)(value),new DynamoAttributeValue({L:value})}static fromNull(value){return new DynamoAttributeValue({NULL:value})}static fromBoolean(value){return new DynamoAttributeValue({BOOL:value})}static booleanFromJsonata(value){return(0,utils_1().validateJsonata)(value),new DynamoAttributeValue({BOOL:value})}static booleanFromJsonPath(value){return(0,utils_1().validateJsonPath)(value),new DynamoAttributeValue({BOOL:value.toString()})}constructor(value){this.attributeValue=value}toObject(){return this.attributeValue}}exports.DynamoAttributeValue=DynamoAttributeValue,_b=JSII_RTTI_SYMBOL_1,DynamoAttributeValue[_b]={fqn:"aws-cdk-lib.aws_stepfunctions_tasks.DynamoAttributeValue",version:"2.201.0"};
