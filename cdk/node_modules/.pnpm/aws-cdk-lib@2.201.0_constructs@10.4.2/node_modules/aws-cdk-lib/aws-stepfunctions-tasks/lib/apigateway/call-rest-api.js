"use strict";var __decorate=exports&&exports.__decorate||function(decorators,target,key,desc){var c=arguments.length,r=c<3?target:desc===null?desc=Object.getOwnPropertyDescriptor(target,key):desc,d;if(typeof Reflect=="object"&&typeof Reflect.decorate=="function")r=Reflect.decorate(decorators,target,key,desc);else for(var i=decorators.length-1;i>=0;i--)(d=decorators[i])&&(r=(c<3?d(r):c>3?d(target,key,r):d(target,key))||r);return c>3&&r&&Object.defineProperty(target,key,r),r},_a,CallApiGatewayRestApiEndpoint_1;Object.defineProperty(exports,"__esModule",{value:!0}),exports.CallApiGatewayRestApiEndpoint=void 0;var jsiiDeprecationWarnings=()=>{var tmp=require("../../../.warnings.jsii.js");return jsiiDeprecationWarnings=()=>tmp,tmp};const JSII_RTTI_SYMBOL_1=Symbol.for("jsii.rtti");var base_1=()=>{var tmp=require("./base");return base_1=()=>tmp,tmp},sfn=()=>{var tmp=require("../../../aws-stepfunctions");return sfn=()=>tmp,tmp},cdk=()=>{var tmp=require("../../../core");return cdk=()=>tmp,tmp},prop_injectable_1=()=>{var tmp=require("../../../core/lib/prop-injectable");return prop_injectable_1=()=>tmp,tmp};let CallApiGatewayRestApiEndpoint=CallApiGatewayRestApiEndpoint_1=class CallApiGatewayRestApiEndpoint2 extends base_1().CallApiGatewayEndpointBase{static jsonPath(scope,id,props){try{jsiiDeprecationWarnings().aws_cdk_lib_aws_stepfunctions_tasks_CallApiGatewayRestApiEndpointJsonPathProps(props)}catch(error){throw process.env.JSII_DEBUG!=="1"&&error.name==="DeprecationError"&&Error.captureStackTrace(error,this.jsonPath),error}return new CallApiGatewayRestApiEndpoint_1(scope,id,props)}static jsonata(scope,id,props){try{jsiiDeprecationWarnings().aws_cdk_lib_aws_stepfunctions_tasks_CallApiGatewayRestApiEndpointJsonataProps(props)}catch(error){throw process.env.JSII_DEBUG!=="1"&&error.name==="DeprecationError"&&Error.captureStackTrace(error,this.jsonata),error}return new CallApiGatewayRestApiEndpoint_1(scope,id,{...props,queryLanguage:sfn().QueryLanguage.JSONATA})}constructor(scope,id,props){super(scope,id,props),this.props=props;try{jsiiDeprecationWarnings().aws_cdk_lib_aws_stepfunctions_tasks_CallApiGatewayRestApiEndpointProps(props)}catch(error){throw process.env.JSII_DEBUG!=="1"&&error.name==="DeprecationError"&&Error.captureStackTrace(error,CallApiGatewayRestApiEndpoint2),error}this.apiEndpoint=this.getApiEndpoint(props.region),this.arnForExecuteApi=props.api.arnForExecuteApi(props.method,props.apiPath,props.stageName),this.stageName=props.stageName,this.taskPolicies=this.createPolicyStatements()}getApiEndpoint(region){const apiStack=cdk().Stack.of(this.props.api);return`${this.props.api.restApiId}.execute-api.${region??apiStack.region}.${apiStack.urlSuffix}`}};exports.CallApiGatewayRestApiEndpoint=CallApiGatewayRestApiEndpoint,_a=JSII_RTTI_SYMBOL_1,CallApiGatewayRestApiEndpoint[_a]={fqn:"aws-cdk-lib.aws_stepfunctions_tasks.CallApiGatewayRestApiEndpoint",version:"2.201.0"},CallApiGatewayRestApiEndpoint.PROPERTY_INJECTION_ID="aws-cdk-lib.aws-stepfunctions-tasks.CallApiGatewayRestApiEndpoint",exports.CallApiGatewayRestApiEndpoint=CallApiGatewayRestApiEndpoint=CallApiGatewayRestApiEndpoint_1=__decorate([prop_injectable_1().propertyInjectable],CallApiGatewayRestApiEndpoint);
