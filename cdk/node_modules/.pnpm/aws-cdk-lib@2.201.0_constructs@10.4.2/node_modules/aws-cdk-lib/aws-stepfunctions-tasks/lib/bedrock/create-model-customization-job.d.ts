import { Construct } from 'constructs';
import * as bedrock from '../../../aws-bedrock';
import * as ec2 from '../../../aws-ec2';
import * as iam from '../../../aws-iam';
import * as kms from '../../../aws-kms';
import * as s3 from '../../../aws-s3';
import * as sfn from '../../../aws-stepfunctions';
/**
 * The customization type.
 *
 * @see https://docs.aws.amazon.com/bedrock/latest/userguide/custom-models.html
 */
export declare enum CustomizationType {
    /**
     * Fine-tuning
     *
     * Provide labeled data in order to train a model to improve performance on specific tasks.
     */
    FINE_TUNING = "FINE_TUNING",
    /**
     * Continued pre-training
     *
     * Provide unlabeled data to pre-train a foundation model by familiarizing it with certain types of inputs.
     */
    CONTINUED_PRE_TRAINING = "CONTINUED_PRE_TRAINING",
    /**
     * Distillation
     *
     * With Model Distillation, you can generate synthetic responses from a large foundation model (teacher)
     * and use that data to train a smaller model (student) for your specific use-case.
     */
    DISTILLATION = "DISTILLATION"
}
/**
 * The key/value pair for a tag.
 */
export interface CustomModelTag {
    /**
     * Key for the tag.
     */
    readonly key: string;
    /**
     * Value for the tag.
     */
    readonly value: string;
}
/**
 * S3 bucket configuration for data storage destination.
 */
export interface DataBucketConfiguration {
    /**
     * The S3 bucket.
     */
    readonly bucket: s3.IBucket;
    /**
     * Path to file or directory within the bucket.
     *
     * @default - root of the bucket
     */
    readonly path?: string;
}
/**
 * S3 bucket configuration for the output data.
 */
export interface OutputBucketConfiguration extends DataBucketConfiguration {
}
/**
 * S3 bucket configuration for the training data.
 */
export interface TrainingBucketConfiguration extends DataBucketConfiguration {
}
/**
 * S3 bucket configuration for the validation data.
 */
export interface ValidationBucketConfiguration extends DataBucketConfiguration {
}
/**
 * VPC configuration
 */
export interface IBedrockCreateModelCustomizationJobVpcConfig {
    /**
     * VPC configuration security groups
     *
     * The minimum number of security groups is 1.
     * The maximum number of security groups is 5.
     */
    readonly securityGroups: ec2.ISecurityGroup[];
    /**
     * VPC configuration subnets
     *
     * The minimum number of subnets is 1.
     * The maximum number of subnets is 16.
     */
    readonly subnets: ec2.ISubnet[];
}
/**
 * Properties for invoking a Bedrock Model
 */
export interface BedrockCreateModelCustomizationJobProps extends sfn.TaskStateBaseProps {
    /**
     * The base model.
     */
    readonly baseModel: bedrock.IModel;
    /**
     * A unique, case-sensitive identifier to ensure that the API request completes no more than one time.
     * If this token matches a previous request, Amazon Bedrock ignores the request, but does not return an error.
     *
     * The maximum length is 256 characters.
     *
     * @see https://docs.aws.amazon.com/AWSEC2/latest/APIReference/Run_Instance_Idempotency.html
     *
     * @default - no client request token
     */
    readonly clientRequestToken?: string;
    /**
     * The customization type.
     *
     * @default FINE_TUNING
     */
    readonly customizationType?: CustomizationType;
    /**
     * The custom model is encrypted at rest using this key.
     *
     * @see https://docs.aws.amazon.com/bedrock/latest/userguide/encryption-custom-job.html
     *
     * @default - encrypted with the AWS owned key
     */
    readonly customModelKmsKey?: kms.IKey;
    /**
     * A name for the resulting custom model.
     *
     * The maximum length is 63 characters.
     */
    readonly customModelName: string;
    /**
     * Tags to attach to the resulting custom model.
     *
     * The maximum number of tags is 200.
     *
     * @default - no tags
     */
    readonly customModelTags?: CustomModelTag[];
    /**
     * Parameters related to tuning the model.
     *
     * @see https://docs.aws.amazon.com/bedrock/latest/userguide/custom-models-hp.html
     *
     * @default - use default hyperparameters
     */
    readonly hyperParameters?: {
        [key: string]: string;
    };
    /**
     * A name for the fine-tuning job.
     *
     * The maximum length is 63 characters.
     */
    readonly jobName: string;
    /**
     * Tags to attach to the job.
     * The maximum number of tags is 200.
     *
     * @default - no tags
     */
    readonly jobTags?: CustomModelTag[];
    /**
     * The S3 bucket configuration where the output data is stored.
     *
     * @see https://docs.aws.amazon.com/bedrock/latest/APIReference/API_OutputDataConfig.html
     */
    readonly outputData: OutputBucketConfiguration;
    /**
     * The IAM role that Amazon Bedrock can assume to perform tasks on your behalf.
     *
     * For example, during model training, Amazon Bedrock needs your permission to read input data from an S3 bucket,
     * write model artifacts to an S3 bucket.
     * To pass this role to Amazon Bedrock, the caller of this API must have the iam:PassRole permission.
     *
     * @default - use auto generated role
     */
    readonly role?: iam.IRole;
    /**
     * The S3 bucket configuration where the training data is stored.
     *
     * @see https://docs.aws.amazon.com/bedrock/latest/APIReference/API_TrainingDataConfig.html
     */
    readonly trainingData: TrainingBucketConfiguration;
    /**
     * The S3 bucket configuration where the validation data is stored.
     * If you don't provide a validation dataset, specify the evaluation percentage by the `Evaluation percentage` hyperparameter.
     *
     * The maximum number is 10.
     *
     * @default undefined - validate using a subset of the training data
     *
     * @see https://docs.aws.amazon.com/bedrock/latest/APIReference/API_Validator.html
     */
    readonly validationData?: ValidationBucketConfiguration[];
    /**
     * The VPC configuration.
     *
     * @default - no VPC configuration
     */
    readonly vpcConfig?: IBedrockCreateModelCustomizationJobVpcConfig;
}
/**
 * A Step Functions Task to create model customization in Bedrock.
 */
export declare class BedrockCreateModelCustomizationJob extends sfn.TaskStateBase {
    private readonly props;
    private static readonly SUPPORTED_INTEGRATION_PATTERNS;
    protected readonly taskMetrics?: sfn.TaskMetricsConfig;
    protected readonly taskPolicies?: iam.PolicyStatement[];
    private readonly integrationPattern;
    private _role;
    constructor(scope: Construct, id: string, props: BedrockCreateModelCustomizationJobProps);
    /**
     * The IAM role for the bedrock create model customization job
     */
    get role(): iam.IRole;
    /**
     * Configure the IAM role for the bedrock create model customization job
     *
     * @see https://docs.aws.amazon.com/bedrock/latest/userguide/model-customization-iam-role.html
     */
    private renderBedrockCreateModelCustomizationJobRole;
    private createVpcConfigPolicyStatement;
    private renderPolicyStatements;
    private validateStringLength;
    private validatePattern;
    private validateArrayLength;
    /**
     * Provides the Bedrock CreateModelCustomizationJob service integration task configuration
     *
     * @internal
     */
    protected _renderTask(): any;
}
