import { Construct } from 'constructs';
import * as iam from '../../../aws-iam';
import * as sfn from '../../../aws-stepfunctions';
interface AthenaGetQueryExecutionOptions {
    /**
     * Query that will be retrieved
     *
     * Example value: `adfsaf-23trf23-f23rt23`
     */
    readonly queryExecutionId: string;
}
/**
 * Properties for getting a Query Execution using JSONPath
 */
export interface AthenaGetQueryExecutionJsonPathProps extends sfn.TaskStateJsonPathBaseProps, AthenaGetQueryExecutionOptions {
}
/**
 * Properties for getting a Query Execution using JSONata
 */
export interface AthenaGetQueryExecutionJsonataProps extends sfn.TaskStateJsonataBaseProps, AthenaGetQueryExecutionOptions {
}
/**
 * Properties for getting a Query Execution
 */
export interface AthenaGetQueryExecutionProps extends sfn.TaskStateBaseProps, AthenaGetQueryExecutionOptions {
}
/**
 * Get an Athena Query Execution as a Task
 *
 * @see https://docs.aws.amazon.com/step-functions/latest/dg/connect-athena.html
 */
export declare class AthenaGetQueryExecution extends sfn.TaskStateBase {
    private readonly props;
    /**
     * Get an Athena Query Execution as a Task that using JSONPath
     *
     * @see https://docs.aws.amazon.com/step-functions/latest/dg/connect-athena.html
     */
    static jsonPath(scope: Construct, id: string, props: AthenaGetQueryExecutionJsonPathProps): AthenaGetQueryExecution;
    /**
     * Get an Athena Query Execution as a Task that using JSONata
     *
     * @see https://docs.aws.amazon.com/step-functions/latest/dg/connect-athena.html
     */
    static jsonata(scope: Construct, id: string, props: AthenaGetQueryExecutionJsonataProps): AthenaGetQueryExecution;
    private static readonly SUPPORTED_INTEGRATION_PATTERNS;
    protected readonly taskMetrics?: sfn.TaskMetricsConfig;
    protected readonly taskPolicies?: iam.PolicyStatement[];
    private readonly integrationPattern;
    constructor(scope: Construct, id: string, props: AthenaGetQueryExecutionProps);
    /**
     * Provides the Athena get query execution service integration task configuration
     * @internal
     */
    protected _renderTask(topLevelQueryLanguage?: sfn.QueryLanguage): any;
}
export {};
