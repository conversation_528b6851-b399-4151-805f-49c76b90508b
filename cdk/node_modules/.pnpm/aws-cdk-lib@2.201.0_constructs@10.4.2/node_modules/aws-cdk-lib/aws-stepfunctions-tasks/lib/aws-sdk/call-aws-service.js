"use strict";var _a;Object.defineProperty(exports,"__esModule",{value:!0}),exports.CallAwsService=void 0;var jsiiDeprecationWarnings=()=>{var tmp=require("../../../.warnings.jsii.js");return jsiiDeprecationWarnings=()=>tmp,tmp};const JSII_RTTI_SYMBOL_1=Symbol.for("jsii.rtti");var iam=()=>{var tmp=require("../../../aws-iam");return iam=()=>tmp,tmp},sfn=()=>{var tmp=require("../../../aws-stepfunctions");return sfn=()=>tmp,tmp},core_1=()=>{var tmp=require("../../../core");return core_1=()=>tmp,tmp},task_utils_1=()=>{var tmp=require("../private/task-utils");return task_utils_1=()=>tmp,tmp};class CallAwsService extends sfn().TaskStateBase{static jsonPath(scope,id,props){try{jsiiDeprecationWarnings().aws_cdk_lib_aws_stepfunctions_tasks_CallAwsServiceJsonPathProps(props)}catch(error){throw process.env.JSII_DEBUG!=="1"&&error.name==="DeprecationError"&&Error.captureStackTrace(error,this.jsonPath),error}return new CallAwsService(scope,id,props)}static jsonata(scope,id,props){try{jsiiDeprecationWarnings().aws_cdk_lib_aws_stepfunctions_tasks_CallAwsServiceJsonataProps(props)}catch(error){throw process.env.JSII_DEBUG!=="1"&&error.name==="DeprecationError"&&Error.captureStackTrace(error,this.jsonata),error}return new CallAwsService(scope,id,{...props,queryLanguage:sfn().QueryLanguage.JSONATA})}constructor(scope,id,props){super(scope,id,props),this.props=props;try{jsiiDeprecationWarnings().aws_cdk_lib_aws_stepfunctions_tasks_CallAwsServiceProps(props)}catch(error){throw process.env.JSII_DEBUG!=="1"&&error.name==="DeprecationError"&&Error.captureStackTrace(error,CallAwsService),error}if(this.props.integrationPattern===sfn().IntegrationPattern.RUN_JOB)throw new(core_1()).ValidationError("The RUN_JOB integration pattern is not supported for CallAwsService",this);if(!core_1().Token.isUnresolved(this.props.action)&&!this.props.action.startsWith(this.props.action[0]?.toLowerCase()))throw new(core_1()).ValidationError(`action must be camelCase, got: ${this.props.action}`,this);if(this.props.parameters){const invalidKeys=Object.keys(this.props.parameters).filter(key=>!key.startsWith(key[0]?.toUpperCase()));if(invalidKeys.length)throw new(core_1()).ValidationError(`parameter names must be PascalCase, got: ${invalidKeys.join(", ")}`,this)}const iamService={cloudwatchlogs:"logs",efs:"elasticfilesystem",elasticloadbalancingv2:"elasticloadbalancing",mediapackagevod:"mediapackage-vod",mwaa:"airflow",sfn:"states"}[props.service]??props.service;this.taskPolicies=[new(iam()).PolicyStatement({resources:props.iamResources,actions:[props.iamAction??`${iamService}:${props.action}`]}),...props.additionalIamStatements??[]]}_renderTask(topLevelQueryLanguage){const queryLanguage=sfn()._getActualQueryLanguage(topLevelQueryLanguage,this.props.queryLanguage);let service=this.props.service;return core_1().Token.isUnresolved(service)||(service=service.toLowerCase()),{Resource:(0,task_utils_1().integrationResourceArn)("aws-sdk",`${service}:${this.props.action}`,this.props.integrationPattern),...this._renderParametersOrArguments(this.props.parameters??{},queryLanguage)}}}exports.CallAwsService=CallAwsService,_a=JSII_RTTI_SYMBOL_1,CallAwsService[_a]={fqn:"aws-cdk-lib.aws_stepfunctions_tasks.CallAwsService",version:"2.201.0"};
