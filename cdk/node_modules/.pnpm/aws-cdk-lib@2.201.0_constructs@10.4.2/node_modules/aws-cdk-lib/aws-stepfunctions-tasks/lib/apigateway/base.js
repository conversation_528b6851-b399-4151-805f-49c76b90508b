"use strict";Object.defineProperty(exports,"__esModule",{value:!0}),exports.CallApiGatewayEndpointBase=void 0;var base_types_1=()=>{var tmp=require("./base-types");return base_types_1=()=>tmp,tmp},iam=()=>{var tmp=require("../../../aws-iam");return iam=()=>tmp,tmp},sfn=()=>{var tmp=require("../../../aws-stepfunctions");return sfn=()=>tmp,tmp},core_1=()=>{var tmp=require("../../../core");return core_1=()=>tmp,tmp},task_utils_1=()=>{var tmp=require("../private/task-utils");return task_utils_1=()=>tmp,tmp};class CallApiGatewayEndpointBase extends sfn().TaskStateBase{constructor(scope,id,props){if(super(scope,id,props),this.baseProps=props,this.integrationPattern=props.integrationPattern??sfn().IntegrationPattern.REQUEST_RESPONSE,(0,task_utils_1().validatePatternSupported)(this.integrationPattern,CallApiGatewayEndpointBase.SUPPORTED_INTEGRATION_PATTERNS),this.integrationPattern===sfn().IntegrationPattern.WAIT_FOR_TASK_TOKEN&&!sfn().FieldUtils.containsTaskToken(this.baseProps.headers))throw new(core_1()).ValidationError("Task Token is required in `headers` for WAIT_FOR_TASK_TOKEN pattern. Use JsonPath.taskToken to set the token.",this)}_renderTask(topLevelQueryLanguage){const queryLanguage=sfn()._getActualQueryLanguage(topLevelQueryLanguage,this.baseProps.queryLanguage);return{Resource:(0,task_utils_1().integrationResourceArn)("apigateway","invoke",this.integrationPattern),...this._renderParametersOrArguments({ApiEndpoint:this.apiEndpoint,Method:this.baseProps.method,Headers:this.baseProps.headers?.value,Stage:this.stageName,Path:this.baseProps.apiPath,QueryParameters:this.baseProps.queryParameters?.value,RequestBody:this.baseProps.requestBody?.value,AuthType:this.baseProps.authType?this.baseProps.authType:"NO_AUTH"},queryLanguage)}}createPolicyStatements(){return this.baseProps.authType===base_types_1().AuthType.NO_AUTH?[]:[new(iam()).PolicyStatement({resources:[this.arnForExecuteApi],actions:["execute-api:Invoke"]})]}}exports.CallApiGatewayEndpointBase=CallApiGatewayEndpointBase,CallApiGatewayEndpointBase.SUPPORTED_INTEGRATION_PATTERNS=[sfn().IntegrationPattern.REQUEST_RESPONSE,sfn().IntegrationPattern.WAIT_FOR_TASK_TOKEN];
