import { Construct } from 'constructs';
import * as iam from '../../../aws-iam';
import * as sfn from '../../../aws-stepfunctions';
interface AthenaStopQueryExecutionOptions {
    /**
     * Query that will be stopped
     */
    readonly queryExecutionId: string;
}
/**
 * Properties for stopping a Query Execution using JSONPath
 */
export interface AthenaStopQueryExecutionJsonPathProps extends sfn.TaskStateJsonPathBaseProps, AthenaStopQueryExecutionOptions {
}
/**
 * Properties for stopping a Query Execution using JSONata
 */
export interface AthenaStopQueryExecutionJsonataProps extends sfn.TaskStateJsonataBaseProps, AthenaStopQueryExecutionOptions {
}
/**
 * Properties for stopping a Query Execution
 */
export interface AthenaStopQueryExecutionProps extends sfn.TaskStateBaseProps, AthenaStopQueryExecutionOptions {
}
/**
 * Stop an Athena Query Execution as a Task
 *
 * @see https://docs.aws.amazon.com/step-functions/latest/dg/connect-athena.html
 */
export declare class AthenaStopQueryExecution extends sfn.TaskStateBase {
    private readonly props;
    /**
     * Stop an Athena Query Execution as a Task using JSONPath
     */
    static jsonPath(scope: Construct, id: string, props: AthenaStopQueryExecutionJsonPathProps): AthenaStopQueryExecution;
    /**
     * Stop an Athena Query Execution as a Task using JSONata
     */
    static jsonata(scope: Construct, id: string, props: AthenaStopQueryExecutionJsonataProps): AthenaStopQueryExecution;
    private static readonly SUPPORTED_INTEGRATION_PATTERNS;
    protected readonly taskMetrics?: sfn.TaskMetricsConfig;
    protected readonly taskPolicies?: iam.PolicyStatement[];
    private readonly integrationPattern;
    constructor(scope: Construct, id: string, props: AthenaStopQueryExecutionProps);
    /**
     * Provides the Athena stop query execution service integration task configuration
     */
    /**
     * @internal
     */
    protected _renderTask(topLevelQueryLanguage?: sfn.QueryLanguage): any;
}
export {};
