import { Construct } from 'constructs';
import * as iam from '../../../aws-iam';
import * as sfn from '../../../aws-stepfunctions';
interface AthenaGetQueryResultsOptions {
    /**
     * Query that will be retrieved
     *
     * Example value: `adfsaf-23trf23-f23rt23`
     */
    readonly queryExecutionId: string;
    /**
     * Pagination token
     *
     * @default - No next token
     */
    readonly nextToken?: string;
    /**
     * Max number of results
     *
     * @default 1000
     */
    readonly maxResults?: number;
}
/**
 * Properties for getting a Query Results using JSONPath
 */
export interface AthenaGetQueryResultsJsonPathProps extends sfn.TaskStateJsonPathBaseProps, AthenaGetQueryResultsOptions {
}
/**
 * Properties for getting a Query Results using JSONata
 */
export interface AthenaGetQueryResultsJsonataProps extends sfn.TaskStateJsonataBaseProps, AthenaGetQueryResultsOptions {
}
/**
 * Properties for getting a Query Results
 */
export interface AthenaGetQueryResultsProps extends sfn.TaskStateBaseProps, AthenaGetQueryResultsOptions {
}
/**
 * Get an Athena Query Results as a Task
 *
 * @see https://docs.aws.amazon.com/step-functions/latest/dg/connect-athena.html
 */
export declare class AthenaGetQueryResults extends sfn.TaskStateBase {
    private readonly props;
    /**
     * Get an Athena Query Results as a Task that using JSONPath
     *
     * @see https://docs.aws.amazon.com/step-functions/latest/dg/connect-athena.html
     */
    static jsonPath(scope: Construct, id: string, props: AthenaGetQueryResultsJsonPathProps): AthenaGetQueryResults;
    /**
     * Get an Athena Query Results as a Task that using JSONata
     *
     * @see https://docs.aws.amazon.com/step-functions/latest/dg/connect-athena.html
     */
    static jsonata(scope: Construct, id: string, props: AthenaGetQueryResultsJsonataProps): AthenaGetQueryResults;
    private static readonly SUPPORTED_INTEGRATION_PATTERNS;
    protected readonly taskMetrics?: sfn.TaskMetricsConfig;
    protected readonly taskPolicies?: iam.PolicyStatement[];
    private readonly integrationPattern;
    constructor(scope: Construct, id: string, props: AthenaGetQueryResultsProps);
    /**
     * Provides the Athena get query results service integration task configuration
     * @internal
     */
    protected _renderTask(topLevelQueryLanguage?: sfn.QueryLanguage): any;
}
export {};
