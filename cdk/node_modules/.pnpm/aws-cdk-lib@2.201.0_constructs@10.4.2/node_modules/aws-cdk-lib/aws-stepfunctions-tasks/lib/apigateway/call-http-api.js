"use strict";var __decorate=exports&&exports.__decorate||function(decorators,target,key,desc){var c=arguments.length,r=c<3?target:desc===null?desc=Object.getOwnPropertyDescriptor(target,key):desc,d;if(typeof Reflect=="object"&&typeof Reflect.decorate=="function")r=Reflect.decorate(decorators,target,key,desc);else for(var i=decorators.length-1;i>=0;i--)(d=decorators[i])&&(r=(c<3?d(r):c>3?d(target,key,r):d(target,key))||r);return c>3&&r&&Object.defineProperty(target,key,r),r},_a,CallApiGatewayHttpApiEndpoint_1;Object.defineProperty(exports,"__esModule",{value:!0}),exports.CallApiGatewayHttpApiEndpoint=void 0;var jsiiDeprecationWarnings=()=>{var tmp=require("../../../.warnings.jsii.js");return jsiiDeprecationWarnings=()=>tmp,tmp};const JSII_RTTI_SYMBOL_1=Symbol.for("jsii.rtti");var base_1=()=>{var tmp=require("./base");return base_1=()=>tmp,tmp},sfn=()=>{var tmp=require("../../../aws-stepfunctions");return sfn=()=>tmp,tmp},cdk=()=>{var tmp=require("../../../core");return cdk=()=>tmp,tmp},prop_injectable_1=()=>{var tmp=require("../../../core/lib/prop-injectable");return prop_injectable_1=()=>tmp,tmp};let CallApiGatewayHttpApiEndpoint=CallApiGatewayHttpApiEndpoint_1=class CallApiGatewayHttpApiEndpoint2 extends base_1().CallApiGatewayEndpointBase{static jsonPath(scope,id,props){try{jsiiDeprecationWarnings().aws_cdk_lib_aws_stepfunctions_tasks_CallApiGatewayHttpApiEndpointJsonPathProps(props)}catch(error){throw process.env.JSII_DEBUG!=="1"&&error.name==="DeprecationError"&&Error.captureStackTrace(error,this.jsonPath),error}return new CallApiGatewayHttpApiEndpoint_1(scope,id,props)}static jsonata(scope,id,props){try{jsiiDeprecationWarnings().aws_cdk_lib_aws_stepfunctions_tasks_CallApiGatewayHttpApiEndpointJsonataProps(props)}catch(error){throw process.env.JSII_DEBUG!=="1"&&error.name==="DeprecationError"&&Error.captureStackTrace(error,this.jsonata),error}return new CallApiGatewayHttpApiEndpoint_1(scope,id,{...props,queryLanguage:sfn().QueryLanguage.JSONATA})}constructor(scope,id,props){super(scope,id,props),this.props=props;try{jsiiDeprecationWarnings().aws_cdk_lib_aws_stepfunctions_tasks_CallApiGatewayHttpApiEndpointProps(props)}catch(error){throw process.env.JSII_DEBUG!=="1"&&error.name==="DeprecationError"&&Error.captureStackTrace(error,CallApiGatewayHttpApiEndpoint2),error}this.apiEndpoint=this.getApiEndpoint(),this.arnForExecuteApi=this.getArnForExecuteApi(),this.stageName=props.stageName,this.taskPolicies=this.createPolicyStatements()}getApiEndpoint(){const apiStack=this.props.apiStack;return`${this.props.apiId}.execute-api.${apiStack.region}.${apiStack.urlSuffix}`}getArnForExecuteApi(){const{apiId,stageName,method,apiPath}=this.props;return this.props.apiStack.formatArn({service:"execute-api",resource:apiId,arnFormat:cdk().ArnFormat.SLASH_RESOURCE_NAME,resourceName:`${stageName}/${method}${apiPath}`})}};exports.CallApiGatewayHttpApiEndpoint=CallApiGatewayHttpApiEndpoint,_a=JSII_RTTI_SYMBOL_1,CallApiGatewayHttpApiEndpoint[_a]={fqn:"aws-cdk-lib.aws_stepfunctions_tasks.CallApiGatewayHttpApiEndpoint",version:"2.201.0"},CallApiGatewayHttpApiEndpoint.PROPERTY_INJECTION_ID="aws-cdk-lib.aws-stepfunctions-tasks.CallApiGatewayHttpApiEndpoint",exports.CallApiGatewayHttpApiEndpoint=CallApiGatewayHttpApiEndpoint=CallApiGatewayHttpApiEndpoint_1=__decorate([prop_injectable_1().propertyInjectable],CallApiGatewayHttpApiEndpoint);
