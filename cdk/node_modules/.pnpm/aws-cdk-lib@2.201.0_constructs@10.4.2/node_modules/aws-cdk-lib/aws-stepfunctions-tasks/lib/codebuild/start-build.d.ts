import { Construct } from 'constructs';
import * as codebuild from '../../../aws-codebuild';
import * as iam from '../../../aws-iam';
import * as sfn from '../../../aws-stepfunctions';
interface CodeBuildStartBuildOptions {
    /**
     * CodeBuild project to start
     */
    readonly project: codebuild.IProject;
    /**
     * A set of environment variables to be used for this build only.
     *
     * @default - the latest environment variables already defined in the build project.
     */
    readonly environmentVariablesOverride?: {
        [name: string]: codebuild.BuildEnvironmentVariable;
    };
}
/**
 * Properties for CodeBuildStartBuild using JSONPath
 */
export interface CodeBuildStartBuildJsonPathProps extends sfn.TaskStateJsonPathBaseProps, CodeBuildStartBuildOptions {
}
/**
 * Properties for CodeBuildStartBuild using JSONata
 */
export interface CodeBuildStartBuildJsonataProps extends sfn.TaskStateJsonataBaseProps, CodeBuildStartBuildOptions {
}
/**
 * Properties for CodeBuildStartBuild
 */
export interface CodeBuildStartBuildProps extends sfn.TaskStateBaseProps, CodeBuildStartBuildOptions {
}
/**
 * Start a CodeBuild Build as a task
 *
 * @see https://docs.aws.amazon.com/step-functions/latest/dg/connect-codebuild.html
 */
export declare class CodeBuildStartBuild extends sfn.TaskStateBase {
    private readonly props;
    /**
     * Start a CodeBuild Build as a task using JSONPath
     */
    static jsonPath(scope: Construct, id: string, props: CodeBuildStartBuildJsonPathProps): CodeBuildStartBuild;
    /**
     * Start a CodeBuild Build as a task using JSONata
     */
    static jsonata(scope: Construct, id: string, props: CodeBuildStartBuildJsonataProps): CodeBuildStartBuild;
    private static readonly SUPPORTED_INTEGRATION_PATTERNS;
    protected readonly taskMetrics?: sfn.TaskMetricsConfig;
    protected readonly taskPolicies?: iam.PolicyStatement[];
    private readonly integrationPattern;
    constructor(scope: Construct, id: string, props: CodeBuildStartBuildProps);
    private configurePolicyStatements;
    /**
     * Provides the CodeBuild StartBuild service integration task configuration
     */
    /**
     * @internal
     */
    protected _renderTask(topLevelQueryLanguage?: sfn.QueryLanguage): any;
    private serializeEnvVariables;
}
export {};
