"use strict";var _a;Object.defineProperty(exports,"__esModule",{value:!0}),exports.AthenaStopQueryExecution=void 0;var jsiiDeprecationWarnings=()=>{var tmp=require("../../../.warnings.jsii.js");return jsiiDeprecationWarnings=()=>tmp,tmp};const JSII_RTTI_SYMBOL_1=Symbol.for("jsii.rtti");var iam=()=>{var tmp=require("../../../aws-iam");return iam=()=>tmp,tmp},sfn=()=>{var tmp=require("../../../aws-stepfunctions");return sfn=()=>tmp,tmp},task_utils_1=()=>{var tmp=require("../private/task-utils");return task_utils_1=()=>tmp,tmp};class AthenaStopQueryExecution extends sfn().TaskStateBase{static jsonPath(scope,id,props){try{jsiiDeprecationWarnings().aws_cdk_lib_aws_stepfunctions_tasks_AthenaStopQueryExecutionJsonPathProps(props)}catch(error){throw process.env.JSII_DEBUG!=="1"&&error.name==="DeprecationError"&&Error.captureStackTrace(error,this.jsonPath),error}return new AthenaStopQueryExecution(scope,id,props)}static jsonata(scope,id,props){try{jsiiDeprecationWarnings().aws_cdk_lib_aws_stepfunctions_tasks_AthenaStopQueryExecutionJsonataProps(props)}catch(error){throw process.env.JSII_DEBUG!=="1"&&error.name==="DeprecationError"&&Error.captureStackTrace(error,this.jsonata),error}return new AthenaStopQueryExecution(scope,id,{...props,queryLanguage:sfn().QueryLanguage.JSONATA})}constructor(scope,id,props){super(scope,id,props),this.props=props;try{jsiiDeprecationWarnings().aws_cdk_lib_aws_stepfunctions_tasks_AthenaStopQueryExecutionProps(props)}catch(error){throw process.env.JSII_DEBUG!=="1"&&error.name==="DeprecationError"&&Error.captureStackTrace(error,AthenaStopQueryExecution),error}this.integrationPattern=props.integrationPattern??sfn().IntegrationPattern.REQUEST_RESPONSE,(0,task_utils_1().validatePatternSupported)(this.integrationPattern,AthenaStopQueryExecution.SUPPORTED_INTEGRATION_PATTERNS),this.taskPolicies=[new(iam()).PolicyStatement({resources:["*"],actions:["athena:stopQueryExecution"]})]}_renderTask(topLevelQueryLanguage){const queryLanguage=sfn()._getActualQueryLanguage(topLevelQueryLanguage,this.props.queryLanguage);return{Resource:(0,task_utils_1().integrationResourceArn)("athena","stopQueryExecution",this.integrationPattern),...this._renderParametersOrArguments({QueryExecutionId:this.props.queryExecutionId},queryLanguage)}}}exports.AthenaStopQueryExecution=AthenaStopQueryExecution,_a=JSII_RTTI_SYMBOL_1,AthenaStopQueryExecution[_a]={fqn:"aws-cdk-lib.aws_stepfunctions_tasks.AthenaStopQueryExecution",version:"2.201.0"},AthenaStopQueryExecution.SUPPORTED_INTEGRATION_PATTERNS=[sfn().IntegrationPattern.REQUEST_RESPONSE];
