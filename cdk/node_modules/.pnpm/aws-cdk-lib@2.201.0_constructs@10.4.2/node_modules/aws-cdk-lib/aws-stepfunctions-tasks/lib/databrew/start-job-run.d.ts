import { Construct } from 'constructs';
import * as iam from '../../../aws-iam';
import * as sfn from '../../../aws-stepfunctions';
interface GlueDataBrewStartJobRunOptions {
    /**
     * Glue DataBrew Job to run
     */
    readonly name: string;
}
/**
 * Properties for starting a job run with StartJobRun using JSONPath
 */
export interface GlueDataBrewStartJobRunJsonPathProps extends sfn.TaskStateJsonPathBaseProps, GlueDataBrewStartJobRunOptions {
}
/**
 * Properties for starting a job run with StartJobRun using JSONata
 */
export interface GlueDataBrewStartJobRunJsonataProps extends sfn.TaskStateJsonataBaseProps, GlueDataBrewStartJobRunOptions {
}
/**
 * Properties for starting a job run with StartJobRun
 */
export interface GlueDataBrewStartJobRunProps extends sfn.TaskStateBaseProps, GlueDataBrewStartJobRunOptions {
}
/**
 * Start a Job run as a Task
 *
 * @see https://docs.aws.amazon.com/step-functions/latest/dg/connect-databrew.html
 */
export declare class GlueDataBrewStartJobRun extends sfn.TaskStateBase {
    private readonly props;
    /**
     * Start a Job run as a Task using JSONPath
     *
     * @see https://docs.aws.amazon.com/step-functions/latest/dg/connect-databrew.html
     */
    static jsonPath(scope: Construct, id: string, props: GlueDataBrewStartJobRunJsonPathProps): GlueDataBrewStartJobRun;
    /**
     * Start a Job run as a Task using JSONata
     *
     * @see https://docs.aws.amazon.com/step-functions/latest/dg/connect-databrew.html
     */
    static jsonata(scope: Construct, id: string, props: GlueDataBrewStartJobRunJsonataProps): GlueDataBrewStartJobRun;
    private static readonly SUPPORTED_INTEGRATION_PATTERNS;
    protected readonly taskMetrics?: sfn.TaskMetricsConfig;
    protected readonly taskPolicies?: iam.PolicyStatement[];
    private readonly integrationPattern;
    /**
     */
    constructor(scope: Construct, id: string, props: GlueDataBrewStartJobRunProps);
    /**
     * Provides the Glue DataBrew Start Job Run task configuration
     * @internal
     */
    protected _renderTask(topLevelQueryLanguage?: sfn.QueryLanguage): any;
}
export {};
