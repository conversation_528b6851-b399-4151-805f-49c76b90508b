"use strict";var _a;Object.defineProperty(exports,"__esModule",{value:!0}),exports.AthenaGetQueryResults=void 0;var jsiiDeprecationWarnings=()=>{var tmp=require("../../../.warnings.jsii.js");return jsiiDeprecationWarnings=()=>tmp,tmp};const JSII_RTTI_SYMBOL_1=Symbol.for("jsii.rtti");var iam=()=>{var tmp=require("../../../aws-iam");return iam=()=>tmp,tmp},sfn=()=>{var tmp=require("../../../aws-stepfunctions");return sfn=()=>tmp,tmp},task_utils_1=()=>{var tmp=require("../private/task-utils");return task_utils_1=()=>tmp,tmp};class AthenaGetQueryResults extends sfn().TaskStateBase{static jsonPath(scope,id,props){try{jsiiDeprecationWarnings().aws_cdk_lib_aws_stepfunctions_tasks_AthenaGetQueryResultsJsonPathProps(props)}catch(error){throw process.env.JSII_DEBUG!=="1"&&error.name==="DeprecationError"&&Error.captureStackTrace(error,this.jsonPath),error}return new AthenaGetQueryResults(scope,id,props)}static jsonata(scope,id,props){try{jsiiDeprecationWarnings().aws_cdk_lib_aws_stepfunctions_tasks_AthenaGetQueryResultsJsonataProps(props)}catch(error){throw process.env.JSII_DEBUG!=="1"&&error.name==="DeprecationError"&&Error.captureStackTrace(error,this.jsonata),error}return new AthenaGetQueryResults(scope,id,{...props,queryLanguage:sfn().QueryLanguage.JSONATA})}constructor(scope,id,props){super(scope,id,props),this.props=props;try{jsiiDeprecationWarnings().aws_cdk_lib_aws_stepfunctions_tasks_AthenaGetQueryResultsProps(props)}catch(error){throw process.env.JSII_DEBUG!=="1"&&error.name==="DeprecationError"&&Error.captureStackTrace(error,AthenaGetQueryResults),error}this.integrationPattern=props.integrationPattern??sfn().IntegrationPattern.REQUEST_RESPONSE,(0,task_utils_1().validatePatternSupported)(this.integrationPattern,AthenaGetQueryResults.SUPPORTED_INTEGRATION_PATTERNS);const policyStatements=[new(iam()).PolicyStatement({resources:["*"],actions:["athena:getQueryResults"]})];policyStatements.push(new(iam()).PolicyStatement({actions:["s3:GetObject"],resources:["*"]})),this.taskPolicies=policyStatements}_renderTask(topLevelQueryLanguage){const queryLanguage=sfn()._getActualQueryLanguage(topLevelQueryLanguage,this.props.queryLanguage);return{Resource:(0,task_utils_1().integrationResourceArn)("athena","getQueryResults",this.integrationPattern),...this._renderParametersOrArguments({QueryExecutionId:this.props.queryExecutionId,NextToken:this.props.nextToken,MaxResults:this.props.maxResults},queryLanguage)}}}exports.AthenaGetQueryResults=AthenaGetQueryResults,_a=JSII_RTTI_SYMBOL_1,AthenaGetQueryResults[_a]={fqn:"aws-cdk-lib.aws_stepfunctions_tasks.AthenaGetQueryResults",version:"2.201.0"},AthenaGetQueryResults.SUPPORTED_INTEGRATION_PATTERNS=[sfn().IntegrationPattern.REQUEST_RESPONSE];
