"use strict";var _a;Object.defineProperty(exports,"__esModule",{value:!0}),exports.CodeBuildStartBuild=void 0;var jsiiDeprecationWarnings=()=>{var tmp=require("../../../.warnings.jsii.js");return jsiiDeprecationWarnings=()=>tmp,tmp};const JSII_RTTI_SYMBOL_1=Symbol.for("jsii.rtti");var codebuild=()=>{var tmp=require("../../../aws-codebuild");return codebuild=()=>tmp,tmp},iam=()=>{var tmp=require("../../../aws-iam");return iam=()=>tmp,tmp},sfn=()=>{var tmp=require("../../../aws-stepfunctions");return sfn=()=>tmp,tmp},cdk=()=>{var tmp=require("../../../core");return cdk=()=>tmp,tmp},task_utils_1=()=>{var tmp=require("../private/task-utils");return task_utils_1=()=>tmp,tmp};class CodeBuildStartBuild extends sfn().TaskStateBase{static jsonPath(scope,id,props){try{jsiiDeprecationWarnings().aws_cdk_lib_aws_stepfunctions_tasks_CodeBuildStartBuildJsonPathProps(props)}catch(error){throw process.env.JSII_DEBUG!=="1"&&error.name==="DeprecationError"&&Error.captureStackTrace(error,this.jsonPath),error}return new CodeBuildStartBuild(scope,id,props)}static jsonata(scope,id,props){try{jsiiDeprecationWarnings().aws_cdk_lib_aws_stepfunctions_tasks_CodeBuildStartBuildJsonataProps(props)}catch(error){throw process.env.JSII_DEBUG!=="1"&&error.name==="DeprecationError"&&Error.captureStackTrace(error,this.jsonata),error}return new CodeBuildStartBuild(scope,id,{...props,queryLanguage:sfn().QueryLanguage.JSONATA})}constructor(scope,id,props){super(scope,id,props),this.props=props;try{jsiiDeprecationWarnings().aws_cdk_lib_aws_stepfunctions_tasks_CodeBuildStartBuildProps(props)}catch(error){throw process.env.JSII_DEBUG!=="1"&&error.name==="DeprecationError"&&Error.captureStackTrace(error,CodeBuildStartBuild),error}this.integrationPattern=props.integrationPattern??sfn().IntegrationPattern.REQUEST_RESPONSE,(0,task_utils_1().validatePatternSupported)(this.integrationPattern,CodeBuildStartBuild.SUPPORTED_INTEGRATION_PATTERNS),this.taskMetrics={metricPrefixSingular:"CodeBuildProject",metricPrefixPlural:"CodeBuildProjects",metricDimensions:{ProjectArn:this.props.project.projectArn}},this.taskPolicies=this.configurePolicyStatements()}configurePolicyStatements(){let policyStatements=[new(iam()).PolicyStatement({resources:[this.props.project.projectArn],actions:["codebuild:StartBuild","codebuild:StopBuild","codebuild:BatchGetBuilds","codebuild:BatchGetReports"]})];return this.integrationPattern===sfn().IntegrationPattern.RUN_JOB&&policyStatements.push(new(iam()).PolicyStatement({actions:["events:PutTargets","events:PutRule","events:DescribeRule"],resources:[cdk().Stack.of(this).formatArn({service:"events",resource:"rule/StepFunctionsGetEventForCodeBuildStartBuildRule"})]})),policyStatements}_renderTask(topLevelQueryLanguage){const queryLanguage=sfn()._getActualQueryLanguage(topLevelQueryLanguage,this.props.queryLanguage);return{Resource:(0,task_utils_1().integrationResourceArn)("codebuild","startBuild",this.integrationPattern),...this._renderParametersOrArguments({ProjectName:this.props.project.projectName,EnvironmentVariablesOverride:this.props.environmentVariablesOverride?this.serializeEnvVariables(this.props.environmentVariablesOverride):void 0},queryLanguage)}}serializeEnvVariables(environmentVariables){return Object.keys(environmentVariables).map(name=>({Name:name,Type:environmentVariables[name].type||codebuild().BuildEnvironmentVariableType.PLAINTEXT,Value:environmentVariables[name].value}))}}exports.CodeBuildStartBuild=CodeBuildStartBuild,_a=JSII_RTTI_SYMBOL_1,CodeBuildStartBuild[_a]={fqn:"aws-cdk-lib.aws_stepfunctions_tasks.CodeBuildStartBuild",version:"2.201.0"},CodeBuildStartBuild.SUPPORTED_INTEGRATION_PATTERNS=[sfn().IntegrationPattern.REQUEST_RESPONSE,sfn().IntegrationPattern.RUN_JOB];
