"use strict";var _a;Object.defineProperty(exports,"__esModule",{value:!0}),exports.DynamoUpdateItem=void 0;var jsiiDeprecationWarnings=()=>{var tmp=require("../../../.warnings.jsii.js");return jsiiDeprecationWarnings=()=>tmp,tmp};const JSII_RTTI_SYMBOL_1=Symbol.for("jsii.rtti");var utils_1=()=>{var tmp=require("./private/utils");return utils_1=()=>tmp,tmp},iam=()=>{var tmp=require("../../../aws-iam");return iam=()=>tmp,tmp},sfn=()=>{var tmp=require("../../../aws-stepfunctions");return sfn=()=>tmp,tmp},core_1=()=>{var tmp=require("../../../core");return core_1=()=>tmp,tmp};class DynamoUpdateItem extends sfn().TaskStateBase{static jsonPath(scope,id,props){try{jsiiDeprecationWarnings().aws_cdk_lib_aws_stepfunctions_tasks_DynamoUpdateItemJsonPathProps(props)}catch(error){throw process.env.JSII_DEBUG!=="1"&&error.name==="DeprecationError"&&Error.captureStackTrace(error,this.jsonPath),error}return new DynamoUpdateItem(scope,id,props)}static jsonata(scope,id,props){try{jsiiDeprecationWarnings().aws_cdk_lib_aws_stepfunctions_tasks_DynamoUpdateItemJsonataProps(props)}catch(error){throw process.env.JSII_DEBUG!=="1"&&error.name==="DeprecationError"&&Error.captureStackTrace(error,this.jsonata),error}return new DynamoUpdateItem(scope,id,{...props,queryLanguage:sfn().QueryLanguage.JSONATA})}constructor(scope,id,props){super(scope,id,props),this.props=props;try{jsiiDeprecationWarnings().aws_cdk_lib_aws_stepfunctions_tasks_DynamoUpdateItemProps(props)}catch(error){throw process.env.JSII_DEBUG!=="1"&&error.name==="DeprecationError"&&Error.captureStackTrace(error,DynamoUpdateItem),error}this.taskPolicies=[new(iam()).PolicyStatement({resources:[core_1().Stack.of(this).formatArn({service:"dynamodb",resource:"table",resourceName:props.table.tableName})],actions:[`dynamodb:${utils_1().DynamoMethod.UPDATE}Item`]})]}_renderTask(topLevelQueryLanguage){const queryLanguage=sfn()._getActualQueryLanguage(topLevelQueryLanguage,this.props.queryLanguage);return{Resource:(0,utils_1().getDynamoResourceArn)(utils_1().DynamoMethod.UPDATE),...this._renderParametersOrArguments({Key:(0,utils_1().transformAttributeValueMap)(this.props.key),TableName:this.props.table.tableName,ConditionExpression:this.props.conditionExpression,ExpressionAttributeNames:this.props.expressionAttributeNames,ExpressionAttributeValues:(0,utils_1().transformAttributeValueMap)(this.props.expressionAttributeValues),ReturnConsumedCapacity:this.props.returnConsumedCapacity,ReturnItemCollectionMetrics:this.props.returnItemCollectionMetrics,ReturnValues:this.props.returnValues,UpdateExpression:this.props.updateExpression},queryLanguage)}}}exports.DynamoUpdateItem=DynamoUpdateItem,_a=JSII_RTTI_SYMBOL_1,DynamoUpdateItem[_a]={fqn:"aws-cdk-lib.aws_stepfunctions_tasks.DynamoUpdateItem",version:"2.201.0"};
