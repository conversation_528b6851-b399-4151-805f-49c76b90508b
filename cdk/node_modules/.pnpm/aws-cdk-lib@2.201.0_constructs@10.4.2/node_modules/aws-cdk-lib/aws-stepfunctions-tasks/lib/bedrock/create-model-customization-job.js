"use strict";var _a;Object.defineProperty(exports,"__esModule",{value:!0}),exports.BedrockCreateModelCustomizationJob=exports.CustomizationType=void 0;var jsiiDeprecationWarnings=()=>{var tmp=require("../../../.warnings.jsii.js");return jsiiDeprecationWarnings=()=>tmp,tmp};const JSII_RTTI_SYMBOL_1=Symbol.for("jsii.rtti");var iam=()=>{var tmp=require("../../../aws-iam");return iam=()=>tmp,tmp},sfn=()=>{var tmp=require("../../../aws-stepfunctions");return sfn=()=>tmp,tmp},core_1=()=>{var tmp=require("../../../core");return core_1=()=>tmp,tmp},task_utils_1=()=>{var tmp=require("../private/task-utils");return task_utils_1=()=>tmp,tmp},CustomizationType;(function(CustomizationType2){CustomizationType2.FINE_TUNING="FINE_TUNING",CustomizationType2.CONTINUED_PRE_TRAINING="CONTINUED_PRE_TRAINING",CustomizationType2.DISTILLATION="DISTILLATION"})(CustomizationType||(exports.CustomizationType=CustomizationType={}));class BedrockCreateModelCustomizationJob extends sfn().TaskStateBase{constructor(scope,id,props){super(scope,id,props),this.props=props;try{jsiiDeprecationWarnings().aws_cdk_lib_aws_stepfunctions_tasks_BedrockCreateModelCustomizationJobProps(props)}catch(error){throw process.env.JSII_DEBUG!=="1"&&error.name==="DeprecationError"&&Error.captureStackTrace(error,BedrockCreateModelCustomizationJob),error}if(this.integrationPattern=props.integrationPattern??sfn().IntegrationPattern.REQUEST_RESPONSE,this.validateStringLength("clientRequestToken",1,256,props.clientRequestToken),this.validatePattern("clientRequestToken",/^[a-zA-Z0-9](-*[a-zA-Z0-9])*$/,props.clientRequestToken),this.validateStringLength("customModelName",1,63,props.customModelName),this.validatePattern("customModelName",/^([0-9a-zA-Z][_-]?)+$/,props.customModelName),this.validateArrayLength("customModelTags",0,200,props.customModelTags),this.validateStringLength("jobName",1,63,props.jobName),this.validatePattern("jobName",/^[a-zA-Z0-9](-*[a-zA-Z0-9\+\-\.])*$/,props.jobName),this.validateArrayLength("jobTags",0,200,props.jobTags),this.validateArrayLength("validationData",1,10,props.validationData),this.validateArrayLength("securityGroups",1,5,props.vpcConfig?.securityGroups),this.validateArrayLength("subnets",1,16,props.vpcConfig?.subnets),(0,task_utils_1().validatePatternSupported)(this.integrationPattern,BedrockCreateModelCustomizationJob.SUPPORTED_INTEGRATION_PATTERNS),!this.props.validationData&&!this.props.hyperParameters?.["Evaluation percentage"])throw new(core_1()).ValidationError("validationData or Evaluation percentage hyperparameter must be provided.",this);if(this._role=this.renderBedrockCreateModelCustomizationJobRole(),this.taskPolicies=this.renderPolicyStatements(),this.props.customModelKmsKey){const poliyStatement=new(iam()).PolicyStatement({actions:["kms:Decrypt","kms:GenerateDataKey","kms:DescribeKey","kms:CreateGrant"],resources:["*"],principals:[new(iam()).ArnPrincipal(this._role.roleArn)],conditions:{StringEquals:{"kms:ViaService":[`bedrock.${core_1().Stack.of(this).region}.amazonaws.com`]}}});if(this.props.customModelKmsKey.addToResourcePolicy(poliyStatement,!0).statementAdded===!1)throw new(core_1()).ValidationError("Imported KMS key is not used as the `customModelKmsKey`.",this)}}get role(){return this._role}renderBedrockCreateModelCustomizationJobRole(){if(this.props.role)return this.props.role;const account=core_1().Stack.of(this).account;return new(iam()).Role(this,"BedrockRole",{assumedBy:new(iam()).ServicePrincipal("bedrock.amazonaws.com",{conditions:{StringEquals:{"aws:SourceAccount":account},ArnEquals:{"aws:SourceArn":core_1().Stack.of(this).formatArn({service:"bedrock",resource:"model-customization-job",resourceName:"*"})}}}),inlinePolicies:{BedrockCreateModelCustomizationJob:new(iam()).PolicyDocument({statements:[...this.createVpcConfigPolicyStatement(),new(iam()).PolicyStatement({actions:["s3:GetObject","s3:ListBucket"],resources:[this.props.trainingData.bucket.bucketArn,`${this.props.trainingData.bucket.bucketArn}/*`,...this.props.validationData?this.props.validationData.map(bucketConfig=>bucketConfig.bucket.bucketArn):[],...this.props.validationData?this.props.validationData.map(bucketConfig=>`${bucketConfig.bucket.bucketArn}/*`):[]]}),new(iam()).PolicyStatement({actions:["s3:GetObject","s3:PutObject","s3:ListBucket"],resources:[this.props.outputData.bucket.bucketArn,`${this.props.outputData.bucket.bucketArn}/*`]})]})}})}createVpcConfigPolicyStatement(){const vpcConfig=this.props.vpcConfig;return vpcConfig?[new(iam()).PolicyStatement({actions:["ec2:DescribeNetworkInterfaces","ec2:DescribeVpcs","ec2:DescribeDhcpOptions","ec2:DescribeSubnets","ec2:DescribeSecurityGroups"],resources:["*"]}),new(iam()).PolicyStatement({actions:["ec2:CreateNetworkInterface"],resources:[core_1().Stack.of(this).formatArn({service:"ec2",resource:"network-interface",resourceName:"*"})],conditions:{StringEquals:{"aws:RequestTag/BedrockManaged":["true"]},ArnEquals:{"aws:RequestTag/BedrockModelCustomizationJobArn":[core_1().Stack.of(this).formatArn({service:"bedrock",resource:"model-customization-job",resourceName:"*"})]}}}),new(iam()).PolicyStatement({actions:["ec2:CreateNetworkInterface"],resources:[...vpcConfig.securityGroups.map(securityGroup=>core_1().Stack.of(this).formatArn({service:"ec2",resource:"security-group",resourceName:securityGroup.securityGroupId})),...vpcConfig.subnets.map(subnet=>core_1().Stack.of(this).formatArn({service:"ec2",resource:"subnet",resourceName:subnet.subnetId}))]}),new(iam()).PolicyStatement({actions:["ec2:CreateNetworkInterfacePermission","ec2:DeleteNetworkInterface","ec2:DeleteNetworkInterfacePermission"],resources:["*"],conditions:{ArnEquals:{"ec2:Subnet":vpcConfig.subnets.map(subnet=>core_1().Stack.of(this).formatArn({service:"ec2",resource:"subnet",resourceName:subnet.subnetId})),"ec2:ResourceTag/BedrockModelCustomizationJobArn":[core_1().Stack.of(this).formatArn({service:"bedrock",resource:"model-customization-job",resourceName:"*"})]},StringEquals:{"ec2:ResourceTag/BedrockManaged":"true"}}}),new(iam()).PolicyStatement({actions:["ec2:CreateTags"],resources:[core_1().Stack.of(this).formatArn({service:"ec2",resource:"network-interface",resourceName:"*"})],conditions:{StringEquals:{"ec2:CreateAction":["CreateNetworkInterface"]},"ForAllValues:StringEquals":{"aws:TagKeys":["BedrockManaged","BedrockModelCustomizationJobArn"]}}})]:[]}renderPolicyStatements(){return[new(iam()).PolicyStatement({actions:["bedrock:CreateModelCustomizationJob","bedrock:TagResource"],resources:[this.props.baseModel.modelArn,core_1().Stack.of(this).formatArn({service:"bedrock",resource:"custom-model",resourceName:"*"}),core_1().Stack.of(this).formatArn({service:"bedrock",resource:"model-customization-job",resourceName:"*"})]}),new(iam()).PolicyStatement({actions:["iam:PassRole"],resources:[this._role.roleArn]})]}validateStringLength(name,min,max,value){if(value!==void 0&&!core_1().Token.isUnresolved(value)&&(value.length<min||value.length>max))throw new(core_1()).ValidationError(`${name} must be between ${min} and ${max} characters long, got: ${value.length}.`,this)}validatePattern(name,pattern,value){if(value!==void 0&&!core_1().Token.isUnresolved(value)&&!pattern.test(value))throw new(core_1()).ValidationError(`${name} must match the pattern ${pattern.toString()}, got: ${value}.`,this)}validateArrayLength(name,min,max,value){if(value!==void 0&&(value.length<min||value.length>max))throw new(core_1()).ValidationError(`${name} must be between ${min} and ${max} items long, got: ${value.length}.`,this)}_renderTask(){return{Resource:(0,task_utils_1().integrationResourceArn)("bedrock","createModelCustomizationJob"),Parameters:sfn().FieldUtils.renderObject({BaseModelIdentifier:this.props.baseModel.modelArn,ClientRequestToken:this.props.clientRequestToken,CustomizationType:this.props.customizationType,CustomModelKmsKeyId:this.props.customModelKmsKey?.keyArn,CustomModelName:this.props.customModelName,CustomModelTags:this.props.customModelTags?.map(tag=>({Key:tag.key,Value:tag.value})),HyperParameters:this.props.hyperParameters,JobName:this.props.jobName,JobTags:this.props.jobTags?.map(tag=>({Key:tag.key,Value:tag.value})),OutputDataConfig:{S3Uri:this.props.outputData.bucket.s3UrlForObject(this.props.outputData.path)},RoleArn:this._role.roleArn,TrainingDataConfig:{S3Uri:this.props.trainingData.bucket.s3UrlForObject(this.props.trainingData.path)},ValidationDataConfig:this.props.validationData&&{Validators:this.props.validationData.map(bucketConfig=>({S3Uri:bucketConfig.bucket.s3UrlForObject(bucketConfig.path)}))},VpcConfig:this.props.vpcConfig&&{SecurityGroupIds:this.props.vpcConfig.securityGroups.map(securityGroup=>securityGroup.securityGroupId),SubnetIds:this.props.vpcConfig.subnets.map(subnet=>subnet.subnetId)}})}}}exports.BedrockCreateModelCustomizationJob=BedrockCreateModelCustomizationJob,_a=JSII_RTTI_SYMBOL_1,BedrockCreateModelCustomizationJob[_a]={fqn:"aws-cdk-lib.aws_stepfunctions_tasks.BedrockCreateModelCustomizationJob",version:"2.201.0"},BedrockCreateModelCustomizationJob.SUPPORTED_INTEGRATION_PATTERNS=[sfn().IntegrationPattern.REQUEST_RESPONSE,sfn().IntegrationPattern.RUN_JOB];
