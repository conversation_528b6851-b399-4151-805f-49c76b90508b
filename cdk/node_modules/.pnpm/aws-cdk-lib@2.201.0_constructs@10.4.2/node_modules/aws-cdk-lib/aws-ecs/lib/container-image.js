"use strict";var _a;Object.defineProperty(exports,"__esModule",{value:!0}),exports.ContainerImage=void 0;var jsiiDeprecationWarnings=()=>{var tmp=require("../../.warnings.jsii.js");return jsiiDeprecationWarnings=()=>tmp,tmp};const JSII_RTTI_SYMBOL_1=Symbol.for("jsii.rtti");var aws_ecr_assets_1=()=>{var tmp=require("../../aws-ecr-assets");return aws_ecr_assets_1=()=>tmp,tmp};class ContainerImage{static fromRegistry(name,props={}){try{jsiiDeprecationWarnings().aws_cdk_lib_aws_ecs_RepositoryImageProps(props)}catch(error){throw process.env.JSII_DEBUG!=="1"&&error.name==="DeprecationError"&&Error.captureStackTrace(error,this.fromRegistry),error}return new(repository_1()).RepositoryImage(name,props)}static fromEcrRepository(repository,tag="latest"){try{jsiiDeprecationWarnings().aws_cdk_lib_aws_ecr_IRepository(repository)}catch(error){throw process.env.JSII_DEBUG!=="1"&&error.name==="DeprecationError"&&Error.captureStackTrace(error,this.fromEcrRepository),error}return new(ecr_1()).EcrImage(repository,tag)}static fromAsset(directory,props={}){try{jsiiDeprecationWarnings().aws_cdk_lib_aws_ecs_AssetImageProps(props)}catch(error){throw process.env.JSII_DEBUG!=="1"&&error.name==="DeprecationError"&&Error.captureStackTrace(error,this.fromAsset),error}return new(asset_image_1()).AssetImage(directory,props)}static fromDockerImageAsset(asset){try{jsiiDeprecationWarnings().aws_cdk_lib_aws_ecr_assets_DockerImageAsset(asset)}catch(error){throw process.env.JSII_DEBUG!=="1"&&error.name==="DeprecationError"&&Error.captureStackTrace(error,this.fromDockerImageAsset),error}return{bind(_scope,containerDefinition){return containerDefinition._defaultDisableVersionConsistency?.(),asset.repository.grantPull(containerDefinition.taskDefinition.obtainExecutionRole()),{imageName:asset.imageUri}}}}static fromTarball(tarballFile){return{bind(scope,containerDefinition){const asset=new(aws_ecr_assets_1()).TarballImageAsset(scope,"Tarball",{tarballFile});return asset.repository.grantPull(containerDefinition.taskDefinition.obtainExecutionRole()),{imageName:asset.imageUri}}}}}exports.ContainerImage=ContainerImage,_a=JSII_RTTI_SYMBOL_1,ContainerImage[_a]={fqn:"aws-cdk-lib.aws_ecs.ContainerImage",version:"2.201.0"};var asset_image_1=()=>{var tmp=require("./images/asset-image");return asset_image_1=()=>tmp,tmp},ecr_1=()=>{var tmp=require("./images/ecr");return ecr_1=()=>tmp,tmp},repository_1=()=>{var tmp=require("./images/repository");return repository_1=()=>tmp,tmp};
