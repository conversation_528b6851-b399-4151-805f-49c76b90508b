"use strict";Object.defineProperty(exports,"__esModule",{value:!0}),exports.ECSMetrics=void 0;class ECSMetrics{static cpuUtilizationAverage(dimensions){return{namespace:"AWS/ECS",metricName:"CPUUtilization",dimensionsMap:dimensions,statistic:"Average"}}static memoryUtilizationAverage(dimensions){return{namespace:"AWS/ECS",metricName:"MemoryUtilization",dimensionsMap:dimensions,statistic:"Average"}}static cpuReservationAverage(dimensions){return{namespace:"AWS/ECS",metricName:"CPUReservation",dimensionsMap:dimensions,statistic:"Average"}}static memoryReservationAverage(dimensions){return{namespace:"AWS/ECS",metricName:"MemoryReservation",dimensionsMap:dimensions,statistic:"Average"}}static gpuReservationAverage(dimensions){return{namespace:"AWS/ECS",metricName:"GPUReservation",dimensionsMap:dimensions,statistic:"Average"}}}exports.ECSMetrics=ECSMetrics;
