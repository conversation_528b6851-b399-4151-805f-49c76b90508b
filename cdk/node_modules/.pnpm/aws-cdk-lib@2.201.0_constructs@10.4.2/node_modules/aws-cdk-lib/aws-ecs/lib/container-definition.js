"use strict";var __decorate=exports&&exports.__decorate||function(decorators,target,key,desc){var c=arguments.length,r=c<3?target:desc===null?desc=Object.getOwnPropertyDescriptor(target,key):desc,d;if(typeof Reflect=="object"&&typeof Reflect.decorate=="function")r=Reflect.decorate(decorators,target,key,desc);else for(var i=decorators.length-1;i>=0;i--)(d=decorators[i])&&(r=(c<3?d(r):c>3?d(target,key,r):d(target,key))||r);return c>3&&r&&Object.defineProperty(target,key,r),r},_a,_b,_c,_d,_e;Object.defineProperty(exports,"__esModule",{value:!0}),exports.VersionConsistency=exports.AppProtocol=exports.Protocol=exports.ServiceConnect=exports.PortMap=exports.ContainerDependencyCondition=exports.UlimitName=exports.ContainerDefinition=exports.Secret=void 0;var jsiiDeprecationWarnings=()=>{var tmp=require("../../.warnings.jsii.js");return jsiiDeprecationWarnings=()=>tmp,tmp};const JSII_RTTI_SYMBOL_1=Symbol.for("jsii.rtti");var constructs_1=()=>{var tmp=require("constructs");return constructs_1=()=>tmp,tmp},task_definition_1=()=>{var tmp=require("./base/task-definition");return task_definition_1=()=>tmp,tmp},cdk=()=>{var tmp=require("../../core");return cdk=()=>tmp,tmp},core_1=()=>{var tmp=require("../../core");return core_1=()=>tmp,tmp},prop_injectable_1=()=>{var tmp=require("../../core/lib/prop-injectable");return prop_injectable_1=()=>tmp,tmp};class Secret{static fromSsmParameter(parameter){try{jsiiDeprecationWarnings().aws_cdk_lib_aws_ssm_IParameter(parameter)}catch(error){throw process.env.JSII_DEBUG!=="1"&&error.name==="DeprecationError"&&Error.captureStackTrace(error,this.fromSsmParameter),error}return{arn:parameter.parameterArn,grantRead:grantee=>parameter.grantRead(grantee)}}static fromSecretsManager(secret,field){try{jsiiDeprecationWarnings().aws_cdk_lib_aws_secretsmanager_ISecret(secret)}catch(error){throw process.env.JSII_DEBUG!=="1"&&error.name==="DeprecationError"&&Error.captureStackTrace(error,this.fromSecretsManager),error}return{arn:field?`${secret.secretArn}:${field}::`:secret.secretArn,hasField:!!field,grantRead:grantee=>secret.grantRead(grantee)}}static fromSecretsManagerVersion(secret,versionInfo,field){try{jsiiDeprecationWarnings().aws_cdk_lib_aws_secretsmanager_ISecret(secret),jsiiDeprecationWarnings().aws_cdk_lib_aws_ecs_SecretVersionInfo(versionInfo)}catch(error){throw process.env.JSII_DEBUG!=="1"&&error.name==="DeprecationError"&&Error.captureStackTrace(error,this.fromSecretsManagerVersion),error}return{arn:`${secret.secretArn}:${field??""}:${versionInfo.versionStage??""}:${versionInfo.versionId??""}`,hasField:!!field,grantRead:grantee=>secret.grantRead(grantee)}}}exports.Secret=Secret,_a=JSII_RTTI_SYMBOL_1,Secret[_a]={fqn:"aws-cdk-lib.aws_ecs.Secret",version:"2.201.0"};let ContainerDefinition=class ContainerDefinition2 extends constructs_1().Construct{constructor(scope,id,props){super(scope,id),this.props=props,this.mountPoints=new Array,this.portMappings=new Array,this.volumesFrom=new Array,this.ulimits=new Array,this.containerDependencies=new Array,this.inferenceAcceleratorResources=[],this.links=new Array,this.secrets=[];try{jsiiDeprecationWarnings().aws_cdk_lib_aws_ecs_ContainerDefinitionProps(props)}catch(error){throw process.env.JSII_DEBUG!=="1"&&error.name==="DeprecationError"&&Error.captureStackTrace(error,ContainerDefinition2),error}if(props.memoryLimitMiB!==void 0&&props.memoryReservationMiB!==void 0&&props.memoryLimitMiB<props.memoryReservationMiB)throw new(core_1()).ValidationError("MemoryLimitMiB should not be less than MemoryReservationMiB.",this);if(this.essential=props.essential??!0,this.taskDefinition=props.taskDefinition,this.memoryLimitSpecified=props.memoryLimitMiB!==void 0||props.memoryReservationMiB!==void 0,this.linuxParameters=props.linuxParameters,this.containerName=props.containerName??this.node.id,this.imageConfig=props.image.bind(this,this),this.imageName=this.imageConfig.imageName,this._namedPorts=new Map,this.versionConsistency=props.versionConsistency,props.logging&&(this.logDriverConfig=props.logging.bind(this,this)),props.secrets)for(const[name,secret]of Object.entries(props.secrets))this.addSecret(name,secret);if(this.dockerLabels={...props.dockerLabels},props.environment?this.environment={...props.environment}:this.environment={},props.environmentFiles){this.environmentFiles=[];for(const environmentFile of props.environmentFiles)this.environmentFiles.push(environmentFile.bind(this))}if(props.credentialSpecs){if(this.credentialSpecs=[],props.credentialSpecs.length>1)throw new(core_1()).ValidationError("Only one credential spec is allowed per container definition.",this);for(const credSpec of props.credentialSpecs)this.credentialSpecs.push(credSpec.bind())}props.cpu&&(this.cpu=props.cpu),props.taskDefinition._linkContainer(this),props.portMappings&&this.addPortMappings(...props.portMappings),props.inferenceAcceleratorResources&&this.addInferenceAcceleratorResource(...props.inferenceAcceleratorResources),this.pseudoTerminal=props.pseudoTerminal,props.ulimits&&this.addUlimits(...props.ulimits),this.validateRestartPolicy(props.enableRestartPolicy,props.restartIgnoredExitCodes,props.restartAttemptPeriod)}addLink(container,alias){try{jsiiDeprecationWarnings().aws_cdk_lib_aws_ecs_ContainerDefinition(container)}catch(error){throw process.env.JSII_DEBUG!=="1"&&error.name==="DeprecationError"&&Error.captureStackTrace(error,this.addLink),error}if(this.taskDefinition.networkMode!==task_definition_1().NetworkMode.BRIDGE)throw new(core_1()).ValidationError("You must use network mode Bridge to add container links.",this);alias!==void 0?this.links.push(`${container.containerName}:${alias}`):this.links.push(`${container.containerName}`)}addMountPoints(...mountPoints){try{jsiiDeprecationWarnings().aws_cdk_lib_aws_ecs_MountPoint(mountPoints)}catch(error){throw process.env.JSII_DEBUG!=="1"&&error.name==="DeprecationError"&&Error.captureStackTrace(error,this.addMountPoints),error}this.mountPoints.push(...mountPoints)}addScratch(scratch){try{jsiiDeprecationWarnings().aws_cdk_lib_aws_ecs_ScratchSpace(scratch)}catch(error){throw process.env.JSII_DEBUG!=="1"&&error.name==="DeprecationError"&&Error.captureStackTrace(error,this.addScratch),error}const mountPoint={containerPath:scratch.containerPath,readOnly:scratch.readOnly,sourceVolume:scratch.name},volume={host:{sourcePath:scratch.sourcePath},name:scratch.name};this.taskDefinition.addVolume(volume),this.addMountPoints(mountPoint)}addPortMappings(...portMappings){try{jsiiDeprecationWarnings().aws_cdk_lib_aws_ecs_PortMapping(portMappings)}catch(error){throw process.env.JSII_DEBUG!=="1"&&error.name==="DeprecationError"&&Error.captureStackTrace(error,this.addPortMappings),error}this.portMappings.push(...portMappings.map(pm=>{new PortMap(this.taskDefinition.networkMode,pm).validate();const serviceConnect=new ServiceConnect(this.taskDefinition.networkMode,pm);return serviceConnect.isServiceConnect()&&(serviceConnect.validate(),this.setNamedPort(pm)),this.addHostPortIfNeeded(pm)}))}addEnvironment(name,value){this.environment[name]=value}addDockerLabel(name,value){this.dockerLabels[name]=value}addSecret(name,secret){try{jsiiDeprecationWarnings().aws_cdk_lib_aws_ecs_Secret(secret)}catch(error){throw process.env.JSII_DEBUG!=="1"&&error.name==="DeprecationError"&&Error.captureStackTrace(error,this.addSecret),error}secret.grantRead(this.taskDefinition.obtainExecutionRole()),this.secrets.push({name,valueFrom:secret.arn})}addInferenceAcceleratorResource(...inferenceAcceleratorResources){this.inferenceAcceleratorResources.push(...inferenceAcceleratorResources.map(resource=>{for(const inferenceAccelerator of this.taskDefinition.inferenceAccelerators)if(resource===inferenceAccelerator.deviceName)return resource;throw new(core_1()).ValidationError(`Resource value ${resource} in container definition doesn't match any inference accelerator device name in the task definition.`,this)}))}addUlimits(...ulimits){try{jsiiDeprecationWarnings().aws_cdk_lib_aws_ecs_Ulimit(ulimits)}catch(error){throw process.env.JSII_DEBUG!=="1"&&error.name==="DeprecationError"&&Error.captureStackTrace(error,this.addUlimits),error}this.ulimits.push(...ulimits)}addContainerDependencies(...containerDependencies){try{jsiiDeprecationWarnings().aws_cdk_lib_aws_ecs_ContainerDependency(containerDependencies)}catch(error){throw process.env.JSII_DEBUG!=="1"&&error.name==="DeprecationError"&&Error.captureStackTrace(error,this.addContainerDependencies),error}this.containerDependencies.push(...containerDependencies)}addVolumesFrom(...volumesFrom){try{jsiiDeprecationWarnings().aws_cdk_lib_aws_ecs_VolumeFrom(volumesFrom)}catch(error){throw process.env.JSII_DEBUG!=="1"&&error.name==="DeprecationError"&&Error.captureStackTrace(error,this.addVolumesFrom),error}this.volumesFrom.push(...volumesFrom)}addToExecutionPolicy(statement){try{jsiiDeprecationWarnings().aws_cdk_lib_aws_iam_PolicyStatement(statement)}catch(error){throw process.env.JSII_DEBUG!=="1"&&error.name==="DeprecationError"&&Error.captureStackTrace(error,this.addToExecutionPolicy),error}this.taskDefinition.addToExecutionRolePolicy(statement)}findPortMapping(containerPort,protocol){try{jsiiDeprecationWarnings().aws_cdk_lib_aws_ecs_Protocol(protocol)}catch(error){throw process.env.JSII_DEBUG!=="1"&&error.name==="DeprecationError"&&Error.captureStackTrace(error,this.findPortMapping),error}for(const portMapping of this.portMappings){const p=portMapping.protocol||Protocol.TCP;if(portMapping.containerPort===containerPort&&p===protocol)return portMapping}}findPortMappingByName(name){return this._namedPorts.get(name)}setNamedPort(pm){if(pm.name){if(this._namedPorts.has(pm.name))throw new(core_1()).ValidationError(`Port mapping name '${pm.name}' already exists on this container`,this);this._namedPorts.set(pm.name,pm)}}addHostPortIfNeeded(pm){return this.taskDefinition.networkMode!==task_definition_1().NetworkMode.BRIDGE||pm.hostPort!==void 0||pm.containerPortRange!==void 0?pm:{...pm,hostPort:0}}validateRestartPolicy(enableRestartPolicy,restartIgnoredExitCodes,restartAttemptPeriod){if(enableRestartPolicy===!1&&(restartIgnoredExitCodes!==void 0||restartAttemptPeriod!==void 0))throw new(core_1()).ValidationError("The restartIgnoredExitCodes and restartAttemptPeriod cannot be specified if enableRestartPolicy is false",this);if(restartIgnoredExitCodes&&restartIgnoredExitCodes.length>50)throw new(core_1()).ValidationError(`Only up to 50 can be specified for restartIgnoredExitCodes, got: ${restartIgnoredExitCodes.length}`,this);if(restartAttemptPeriod&&(restartAttemptPeriod.toSeconds()<60||restartAttemptPeriod.toSeconds()>1800))throw new(core_1()).ValidationError(`The restartAttemptPeriod must be between 60 seconds and 1800 seconds, got ${restartAttemptPeriod.toSeconds()} seconds`,this)}get referencesSecretJsonField(){for(const secret of this.secrets)if(secret.valueFrom.endsWith("::"))return!0;return!1}get ingressPort(){if(this.portMappings.length===0)throw new(core_1()).ValidationError(`Container ${this.containerName} hasn't defined any ports. Call addPortMappings().`,this);const defaultPortMapping=this.portMappings[0];if(defaultPortMapping.hostPort!==void 0&&defaultPortMapping.hostPort!==0)return defaultPortMapping.hostPort;if(this.taskDefinition.networkMode===task_definition_1().NetworkMode.BRIDGE)return 0;if(defaultPortMapping.containerPortRange!==void 0)throw new(core_1()).ValidationError(`The first port mapping of the container ${this.containerName} must expose a single port.`,this);return defaultPortMapping.containerPort}get containerPort(){if(this.portMappings.length===0)throw new(core_1()).ValidationError(`Container ${this.containerName} hasn't defined any ports. Call addPortMappings().`,this);const defaultPortMapping=this.portMappings[0];if(defaultPortMapping.containerPortRange!==void 0)throw new(core_1()).ValidationError(`The first port mapping of the container ${this.containerName} must expose a single port.`,this);return defaultPortMapping.containerPort}_defaultDisableVersionConsistency(){this.versionConsistency||(this.versionConsistency=VersionConsistency.DISABLED)}renderContainerDefinition(_taskDefinition){try{jsiiDeprecationWarnings().aws_cdk_lib_aws_ecs_TaskDefinition(_taskDefinition)}catch(error){throw process.env.JSII_DEBUG!=="1"&&error.name==="DeprecationError"&&Error.captureStackTrace(error,this.renderContainerDefinition),error}return{command:this.props.command,credentialSpecs:this.credentialSpecs&&this.credentialSpecs.map(renderCredentialSpec),cpu:this.props.cpu,disableNetworking:this.props.disableNetworking,dependsOn:cdk().Lazy.any({produce:()=>this.containerDependencies.map(renderContainerDependency)},{omitEmptyArray:!0}),dnsSearchDomains:this.props.dnsSearchDomains,dnsServers:this.props.dnsServers,dockerLabels:Object.keys(this.dockerLabels).length?this.dockerLabels:void 0,dockerSecurityOptions:this.props.dockerSecurityOptions,entryPoint:this.props.entryPoint,essential:this.essential,hostname:this.props.hostname,image:this.imageConfig.imageName,interactive:this.props.interactive,memory:this.props.memoryLimitMiB,memoryReservation:this.props.memoryReservationMiB,mountPoints:cdk().Lazy.any({produce:()=>this.mountPoints.map(renderMountPoint)},{omitEmptyArray:!0}),name:this.containerName,portMappings:cdk().Lazy.any({produce:()=>this.portMappings.map(renderPortMapping)},{omitEmptyArray:!0}),privileged:this.props.privileged,pseudoTerminal:this.props.pseudoTerminal,readonlyRootFilesystem:this.props.readonlyRootFilesystem,repositoryCredentials:this.imageConfig.repositoryCredentials,startTimeout:this.props.startTimeout&&this.props.startTimeout.toSeconds(),stopTimeout:this.props.stopTimeout&&this.props.stopTimeout.toSeconds(),ulimits:cdk().Lazy.any({produce:()=>this.ulimits.map(renderUlimit)},{omitEmptyArray:!0}),user:this.props.user,versionConsistency:this.versionConsistency,volumesFrom:cdk().Lazy.any({produce:()=>this.volumesFrom.map(renderVolumeFrom)},{omitEmptyArray:!0}),workingDirectory:this.props.workingDirectory,logConfiguration:this.logDriverConfig,environment:this.environment&&Object.keys(this.environment).length?renderKV(this.environment,"name","value"):void 0,environmentFiles:this.environmentFiles&&renderEnvironmentFiles(cdk().Stack.of(this).partition,this.environmentFiles),secrets:this.secrets.length?this.secrets:void 0,extraHosts:this.props.extraHosts&&renderKV(this.props.extraHosts,"hostname","ipAddress"),healthCheck:this.props.healthCheck&&renderHealthCheck(this,this.props.healthCheck),links:cdk().Lazy.list({produce:()=>this.links},{omitEmpty:!0}),linuxParameters:this.linuxParameters&&this.linuxParameters.renderLinuxParameters(),resourceRequirements:!this.props.gpuCount&&this.inferenceAcceleratorResources.length==0?void 0:renderResourceRequirements(this.props.gpuCount,this.inferenceAcceleratorResources),systemControls:this.props.systemControls&&renderSystemControls(this.props.systemControls),restartPolicy:renderRestartPolicy(this.props.enableRestartPolicy,this.props.restartIgnoredExitCodes,this.props.restartAttemptPeriod)}}};exports.ContainerDefinition=ContainerDefinition,_b=JSII_RTTI_SYMBOL_1,ContainerDefinition[_b]={fqn:"aws-cdk-lib.aws_ecs.ContainerDefinition",version:"2.201.0"},ContainerDefinition.PROPERTY_INJECTION_ID="aws-cdk-lib.aws-ecs.ContainerDefinition",ContainerDefinition.CONTAINER_PORT_USE_RANGE=0,exports.ContainerDefinition=ContainerDefinition=__decorate([prop_injectable_1().propertyInjectable],ContainerDefinition);function renderKV(env,keyName,valueName){const ret=[];for(const[key,value]of Object.entries(env))ret.push({[keyName]:key,[valueName]:value});return ret}function renderEnvironmentFiles(partition,environmentFiles){const ret=[];for(const environmentFile of environmentFiles){const s3Location=environmentFile.s3Location;if(!s3Location)throw Error("Environment file must specify an S3 location");ret.push({type:environmentFile.fileType,value:`arn:${partition}:s3:::${s3Location.bucketName}/${s3Location.objectKey}`})}return ret}function renderCredentialSpec(credSpec){if(!credSpec.location)throw Error("CredentialSpec must specify a valid location or ARN");return`${credSpec.typePrefix}:${credSpec.location}`}function renderHealthCheck(scope,hc){if(hc.interval?.toSeconds()!==void 0&&(5>hc.interval?.toSeconds()||hc.interval?.toSeconds()>300))throw new(core_1()).ValidationError("Interval must be between 5 seconds and 300 seconds.",scope);if(hc.timeout?.toSeconds()!==void 0&&(2>hc.timeout?.toSeconds()||hc.timeout?.toSeconds()>120))throw new(core_1()).ValidationError("Timeout must be between 2 seconds and 120 seconds.",scope);if(hc.interval?.toSeconds()!==void 0&&hc.timeout?.toSeconds()!==void 0&&hc.interval?.toSeconds()<hc.timeout?.toSeconds())throw new(core_1()).ValidationError("Health check interval should be longer than timeout.",scope);return{command:getHealthCheckCommand(scope,hc),interval:hc.interval?.toSeconds()??30,retries:hc.retries??3,startPeriod:hc.startPeriod?.toSeconds(),timeout:hc.timeout?.toSeconds()??5}}function getHealthCheckCommand(scope,hc){const cmd=hc.command,hcCommand=new Array;if(cmd.length===0)throw new(core_1()).ValidationError("At least one argument must be supplied for health check command.",scope);return cmd.length===1?(hcCommand.push("CMD-SHELL",cmd[0]),hcCommand):(cmd[0]!=="CMD"&&cmd[0]!=="CMD-SHELL"&&hcCommand.push("CMD"),hcCommand.concat(cmd))}function renderResourceRequirements(gpuCount=0,inferenceAcceleratorResources=[]){const ret=[];for(const resource of inferenceAcceleratorResources)ret.push({type:"InferenceAccelerator",value:resource});return gpuCount>0&&ret.push({type:"GPU",value:gpuCount.toString()}),ret}var UlimitName;(function(UlimitName2){UlimitName2.CORE="core",UlimitName2.CPU="cpu",UlimitName2.DATA="data",UlimitName2.FSIZE="fsize",UlimitName2.LOCKS="locks",UlimitName2.MEMLOCK="memlock",UlimitName2.MSGQUEUE="msgqueue",UlimitName2.NICE="nice",UlimitName2.NOFILE="nofile",UlimitName2.NPROC="nproc",UlimitName2.RSS="rss",UlimitName2.RTPRIO="rtprio",UlimitName2.RTTIME="rttime",UlimitName2.SIGPENDING="sigpending",UlimitName2.STACK="stack"})(UlimitName||(exports.UlimitName=UlimitName={}));function renderUlimit(ulimit){return{name:ulimit.name,softLimit:ulimit.softLimit,hardLimit:ulimit.hardLimit}}var ContainerDependencyCondition;(function(ContainerDependencyCondition2){ContainerDependencyCondition2.START="START",ContainerDependencyCondition2.COMPLETE="COMPLETE",ContainerDependencyCondition2.SUCCESS="SUCCESS",ContainerDependencyCondition2.HEALTHY="HEALTHY"})(ContainerDependencyCondition||(exports.ContainerDependencyCondition=ContainerDependencyCondition={}));function renderContainerDependency(containerDependency){return{containerName:containerDependency.container.containerName,condition:containerDependency.condition||ContainerDependencyCondition.HEALTHY}}class PortMap{constructor(networkmode,pm){try{jsiiDeprecationWarnings().aws_cdk_lib_aws_ecs_NetworkMode(networkmode),jsiiDeprecationWarnings().aws_cdk_lib_aws_ecs_PortMapping(pm)}catch(error){throw process.env.JSII_DEBUG!=="1"&&error.name==="DeprecationError"&&Error.captureStackTrace(error,PortMap),error}this.networkmode=networkmode,this.portmapping=pm}validate(){if(!this.isvalidPortName())throw new(core_1()).UnscopedValidationError("Port mapping name cannot be an empty string.");if(this.portmapping.containerPort===ContainerDefinition.CONTAINER_PORT_USE_RANGE&&this.portmapping.containerPortRange===void 0)throw new(core_1()).UnscopedValidationError(`The containerPortRange must be set when containerPort is equal to ${ContainerDefinition.CONTAINER_PORT_USE_RANGE}`);if(this.portmapping.containerPort!==ContainerDefinition.CONTAINER_PORT_USE_RANGE&&this.portmapping.containerPortRange!==void 0)throw new(core_1()).UnscopedValidationError('Cannot set "containerPort" and "containerPortRange" at the same time.');if(this.portmapping.containerPort!==ContainerDefinition.CONTAINER_PORT_USE_RANGE&&(this.networkmode===task_definition_1().NetworkMode.AWS_VPC||this.networkmode===task_definition_1().NetworkMode.HOST)&&this.portmapping.hostPort!==void 0&&this.portmapping.hostPort!==this.portmapping.containerPort)throw new(core_1()).UnscopedValidationError("The host port must be left out or must be the same as the container port for AwsVpc or Host network mode.");if(this.portmapping.containerPortRange!==void 0){if(cdk().Token.isUnresolved(this.portmapping.containerPortRange))throw new(core_1()).UnscopedValidationError("The value of containerPortRange must be concrete (no Tokens)");if(this.portmapping.hostPort!==void 0)throw new(core_1()).UnscopedValidationError('Cannot set "hostPort" while using a port range for the container.');if(this.networkmode!==task_definition_1().NetworkMode.BRIDGE&&this.networkmode!==task_definition_1().NetworkMode.AWS_VPC)throw new(core_1()).UnscopedValidationError("Either AwsVpc or Bridge network mode is required to set a port range for the container.");if(!/^\d+-\d+$/.test(this.portmapping.containerPortRange))throw new(core_1()).UnscopedValidationError("The containerPortRange must be a string in the format [start port]-[end port].")}}isvalidPortName(){return this.portmapping.name!==""}}exports.PortMap=PortMap,_c=JSII_RTTI_SYMBOL_1,PortMap[_c]={fqn:"aws-cdk-lib.aws_ecs.PortMap",version:"2.201.0"};class ServiceConnect{constructor(networkmode,pm){try{jsiiDeprecationWarnings().aws_cdk_lib_aws_ecs_NetworkMode(networkmode),jsiiDeprecationWarnings().aws_cdk_lib_aws_ecs_PortMapping(pm)}catch(error){throw process.env.JSII_DEBUG!=="1"&&error.name==="DeprecationError"&&Error.captureStackTrace(error,ServiceConnect),error}this.portmapping=pm,this.networkmode=networkmode}isServiceConnect(){const hasPortname=this.portmapping.name,hasAppProtcol=this.portmapping.appProtocol;return!!(hasPortname||hasAppProtcol)}validate(){if(!this.isValidNetworkmode())throw new(core_1()).UnscopedValidationError(`Service connect related port mapping fields 'name' and 'appProtocol' are not supported for network mode ${this.networkmode}`);if(!this.isValidPortName())throw new(core_1()).UnscopedValidationError("Service connect-related port mapping field 'appProtocol' cannot be set without 'name'")}isValidNetworkmode(){const isAwsVpcMode=this.networkmode==task_definition_1().NetworkMode.AWS_VPC,isBridgeMode=this.networkmode==task_definition_1().NetworkMode.BRIDGE;return!!(isAwsVpcMode||isBridgeMode)}isValidPortName(){return!!this.portmapping.name}}exports.ServiceConnect=ServiceConnect,_d=JSII_RTTI_SYMBOL_1,ServiceConnect[_d]={fqn:"aws-cdk-lib.aws_ecs.ServiceConnect",version:"2.201.0"};var Protocol;(function(Protocol2){Protocol2.TCP="tcp",Protocol2.UDP="udp"})(Protocol||(exports.Protocol=Protocol={}));class AppProtocol{constructor(value){this.value=value}}exports.AppProtocol=AppProtocol,_e=JSII_RTTI_SYMBOL_1,AppProtocol[_e]={fqn:"aws-cdk-lib.aws_ecs.AppProtocol",version:"2.201.0"},AppProtocol.http=new AppProtocol("http"),AppProtocol.http2=new AppProtocol("http2"),AppProtocol.grpc=new AppProtocol("grpc");function renderPortMapping(pm){return{containerPort:pm.containerPort!==ContainerDefinition.CONTAINER_PORT_USE_RANGE?pm.containerPort:void 0,containerPortRange:pm.containerPortRange,hostPort:pm.hostPort,protocol:pm.protocol||Protocol.TCP,appProtocol:pm.appProtocol?.value,name:pm.name?pm.name:void 0}}function renderMountPoint(mp){return{containerPath:mp.containerPath,readOnly:mp.readOnly,sourceVolume:mp.sourceVolume}}var VersionConsistency;(function(VersionConsistency2){VersionConsistency2.ENABLED="enabled",VersionConsistency2.DISABLED="disabled"})(VersionConsistency||(exports.VersionConsistency=VersionConsistency={}));function renderVolumeFrom(vf){return{sourceContainer:vf.sourceContainer,readOnly:vf.readOnly}}function renderSystemControls(systemControls){return systemControls.map(sc=>({namespace:sc.namespace,value:sc.value}))}function renderRestartPolicy(enableRestartPolicy,restartIgnoredExitCodes,restartAttemptPeriod){if(!(enableRestartPolicy===void 0&&restartIgnoredExitCodes===void 0&&restartAttemptPeriod===void 0))return{enabled:enableRestartPolicy??!0,ignoredExitCodes:restartIgnoredExitCodes,restartAttemptPeriod:restartAttemptPeriod?.toSeconds()}}
