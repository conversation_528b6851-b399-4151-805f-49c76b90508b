"use strict";var _a,_b,_c;Object.defineProperty(exports,"__esModule",{value:!0}),exports.BottleRocketImage=exports.BottlerocketEcsVariant=exports.EcsOptimizedImage=exports.EcsOptimizedAmi=exports.WindowsOptimizedVersion=exports.AmiHardwareType=void 0;var jsiiDeprecationWarnings=()=>{var tmp=require("../../.warnings.jsii.js");return jsiiDeprecationWarnings=()=>tmp,tmp};const JSII_RTTI_SYMBOL_1=Symbol.for("jsii.rtti");var ec2=()=>{var tmp=require("../../aws-ec2");return ec2=()=>tmp,tmp},ssm=()=>{var tmp=require("../../aws-ssm");return ssm=()=>tmp,tmp},core_1=()=>{var tmp=require("../../core");return core_1=()=>tmp,tmp},AmiHardwareType;(function(AmiHardwareType2){AmiHardwareType2.STANDARD="Standard",AmiHardwareType2.GPU="GPU",AmiHardwareType2.ARM="ARM64",AmiHardwareType2.NEURON="Neuron"})(AmiHardwareType||(exports.AmiHardwareType=AmiHardwareType={}));var WindowsOptimizedVersion;(function(WindowsOptimizedVersion2){WindowsOptimizedVersion2.SERVER_2022="2022",WindowsOptimizedVersion2.SERVER_2019="2019",WindowsOptimizedVersion2.SERVER_2016="2016"})(WindowsOptimizedVersion||(exports.WindowsOptimizedVersion=WindowsOptimizedVersion={}));const BR_IMAGE_SYMBOL=Symbol.for("@aws-cdk/aws-ecs/lib/amis.BottleRocketImage");class EcsOptimizedAmi{constructor(props){if(this.hwType=props&&props.hardwareType||AmiHardwareType.STANDARD,props&&props.generation){if(props.generation===ec2().AmazonLinuxGeneration.AMAZON_LINUX&&this.hwType!==AmiHardwareType.STANDARD)throw new(core_1()).UnscopedValidationError("Amazon Linux does not support special hardware type. Use Amazon Linux 2 instead");if(props.windowsVersion)throw new(core_1()).UnscopedValidationError('"windowsVersion" and Linux image "generation" cannot be both set');this.generation=props.generation}else if(props&&props.windowsVersion){if(this.hwType!==AmiHardwareType.STANDARD)throw new(core_1()).UnscopedValidationError("Windows Server does not support special hardware type");this.windowsVersion=props.windowsVersion}else this.generation=ec2().AmazonLinuxGeneration.AMAZON_LINUX_2;if(this.amiParameterName="/aws/service/"+(this.windowsVersion?"ami-windows-latest/":"ecs/optimized-ami/")+(this.generation===ec2().AmazonLinuxGeneration.AMAZON_LINUX?"amazon-linux/":"")+(this.generation===ec2().AmazonLinuxGeneration.AMAZON_LINUX_2?"amazon-linux-2/":"")+(this.generation===ec2().AmazonLinuxGeneration.AMAZON_LINUX_2023?"amazon-linux-2023/":"")+(this.windowsVersion?`Windows_Server-${this.windowsVersion}-English-Full-ECS_Optimized/`:"")+(this.hwType===AmiHardwareType.GPU?"gpu/":"")+(this.hwType===AmiHardwareType.ARM?"arm64/":"")+(this.hwType===AmiHardwareType.NEURON?"inf/":"")+(this.windowsVersion?"image_id":"recommended/image_id"),this.cachedInContext=props?.cachedInContext??!1,this.additionalCacheKey=props?.additionalCacheKey,this.additionalCacheKey!==void 0&&!this.cachedInContext)throw new(core_1()).UnscopedValidationError('"additionalCacheKey" was set but "cachedInContext" is false, so it will have no effect')}getImage(scope){const ami=lookupImage(scope,this.cachedInContext,this.amiParameterName,this.additionalCacheKey),osType=this.windowsVersion?ec2().OperatingSystemType.WINDOWS:ec2().OperatingSystemType.LINUX;return{imageId:ami,osType,userData:ec2().UserData.forOperatingSystem(osType)}}}exports.EcsOptimizedAmi=EcsOptimizedAmi,_a=JSII_RTTI_SYMBOL_1,EcsOptimizedAmi[_a]={fqn:"aws-cdk-lib.aws_ecs.EcsOptimizedAmi",version:"2.201.0"};class EcsOptimizedImage{static amazonLinux2023(hardwareType=AmiHardwareType.STANDARD,options={}){try{jsiiDeprecationWarnings().aws_cdk_lib_aws_ecs_AmiHardwareType(hardwareType),jsiiDeprecationWarnings().aws_cdk_lib_aws_ecs_EcsOptimizedImageOptions(options)}catch(error){throw process.env.JSII_DEBUG!=="1"&&error.name==="DeprecationError"&&Error.captureStackTrace(error,this.amazonLinux2023),error}return new EcsOptimizedImage({generation:ec2().AmazonLinuxGeneration.AMAZON_LINUX_2023,hardwareType,cachedInContext:options.cachedInContext})}static amazonLinux2(hardwareType=AmiHardwareType.STANDARD,options={}){try{jsiiDeprecationWarnings().aws_cdk_lib_aws_ecs_AmiHardwareType(hardwareType),jsiiDeprecationWarnings().aws_cdk_lib_aws_ecs_EcsOptimizedImageOptions(options)}catch(error){throw process.env.JSII_DEBUG!=="1"&&error.name==="DeprecationError"&&Error.captureStackTrace(error,this.amazonLinux2),error}return new EcsOptimizedImage({generation:ec2().AmazonLinuxGeneration.AMAZON_LINUX_2,hardwareType,cachedInContext:options.cachedInContext})}static amazonLinux(options={}){try{jsiiDeprecationWarnings().aws_cdk_lib_aws_ecs_EcsOptimizedImageOptions(options)}catch(error){throw process.env.JSII_DEBUG!=="1"&&error.name==="DeprecationError"&&Error.captureStackTrace(error,this.amazonLinux),error}return new EcsOptimizedImage({generation:ec2().AmazonLinuxGeneration.AMAZON_LINUX,cachedInContext:options.cachedInContext})}static windows(windowsVersion,options={}){try{jsiiDeprecationWarnings().aws_cdk_lib_aws_ecs_WindowsOptimizedVersion(windowsVersion),jsiiDeprecationWarnings().aws_cdk_lib_aws_ecs_EcsOptimizedImageOptions(options)}catch(error){throw process.env.JSII_DEBUG!=="1"&&error.name==="DeprecationError"&&Error.captureStackTrace(error,this.windows),error}return new EcsOptimizedImage({windowsVersion,cachedInContext:options.cachedInContext})}constructor(props){if(this.hwType=props&&props.hardwareType,props.windowsVersion)this.windowsVersion=props.windowsVersion;else if(props.generation)this.generation=props.generation;else throw new(core_1()).UnscopedValidationError("This error should never be thrown");if(this.amiParameterName="/aws/service/"+(this.windowsVersion?"ami-windows-latest/":"ecs/optimized-ami/")+(this.generation===ec2().AmazonLinuxGeneration.AMAZON_LINUX?"amazon-linux/":"")+(this.generation===ec2().AmazonLinuxGeneration.AMAZON_LINUX_2?"amazon-linux-2/":"")+(this.generation===ec2().AmazonLinuxGeneration.AMAZON_LINUX_2023?"amazon-linux-2023/":"")+(this.windowsVersion?`Windows_Server-${this.windowsVersion}-English-Full-ECS_Optimized/`:"")+(this.hwType===AmiHardwareType.GPU?"gpu/":"")+(this.hwType===AmiHardwareType.ARM?"arm64/":"")+(this.hwType===AmiHardwareType.NEURON?"inf/":"")+(this.windowsVersion?"image_id":"recommended/image_id"),this.cachedInContext=props.cachedInContext??!1,this.additionalCacheKey=props.additionalCacheKey,this.additionalCacheKey!==void 0&&!this.cachedInContext)throw new(core_1()).UnscopedValidationError('"additionalCacheKey" was set but "cachedInContext" is false, so it will have no effect')}getImage(scope){const ami=lookupImage(scope,this.cachedInContext,this.amiParameterName,this.additionalCacheKey),osType=this.windowsVersion?ec2().OperatingSystemType.WINDOWS:ec2().OperatingSystemType.LINUX;return{imageId:ami,osType,userData:ec2().UserData.forOperatingSystem(osType)}}}exports.EcsOptimizedImage=EcsOptimizedImage,_b=JSII_RTTI_SYMBOL_1,EcsOptimizedImage[_b]={fqn:"aws-cdk-lib.aws_ecs.EcsOptimizedImage",version:"2.201.0"};var BottlerocketEcsVariant;(function(BottlerocketEcsVariant2){BottlerocketEcsVariant2.AWS_ECS_1="aws-ecs-1",BottlerocketEcsVariant2.AWS_ECS_1_NVIDIA="aws-ecs-1-nvidia",BottlerocketEcsVariant2.AWS_ECS_2="aws-ecs-2",BottlerocketEcsVariant2.AWS_ECS_2_NVIDIA="aws-ecs-2-nvidia"})(BottlerocketEcsVariant||(exports.BottlerocketEcsVariant=BottlerocketEcsVariant={}));class BottleRocketImage{static isBottleRocketImage(x){return x!==null&&typeof x=="object"&&BR_IMAGE_SYMBOL in x}constructor(props={}){try{jsiiDeprecationWarnings().aws_cdk_lib_aws_ecs_BottleRocketImageProps(props)}catch(error){throw process.env.JSII_DEBUG!=="1"&&error.name==="DeprecationError"&&Error.captureStackTrace(error,BottleRocketImage),error}if(this.variant=props.variant??BottlerocketEcsVariant.AWS_ECS_1,this.architecture=props.architecture??ec2().InstanceArchitecture.X86_64,this.amiParameterName=`/aws/service/bottlerocket/${this.variant}/${this.architecture}/latest/image_id`,this.cachedInContext=props.cachedInContext??!1,this.additionalCacheKey=props.additionalCacheKey,this.additionalCacheKey!==void 0&&!this.cachedInContext)throw new(core_1()).UnscopedValidationError('"additionalCacheKey" was set but "cachedInContext" is false, so it will have no effect')}getImage(scope){return{imageId:lookupImage(scope,this.cachedInContext,this.amiParameterName,this.additionalCacheKey),osType:ec2().OperatingSystemType.LINUX,userData:ec2().UserData.custom("")}}}exports.BottleRocketImage=BottleRocketImage,_c=JSII_RTTI_SYMBOL_1,BottleRocketImage[_c]={fqn:"aws-cdk-lib.aws_ecs.BottleRocketImage",version:"2.201.0"},Object.defineProperty(BottleRocketImage.prototype,BR_IMAGE_SYMBOL,{value:!0,enumerable:!1,writable:!1});function lookupImage(scope,cachedInContext,parameterName,additionalCacheKey){return cachedInContext?ssm().StringParameter.valueFromLookup(scope,parameterName,void 0,{additionalCacheKey}):ssm().StringParameter.valueForTypedStringParameterV2(scope,parameterName,ssm().ParameterValueType.AWS_EC2_IMAGE_ID)}
