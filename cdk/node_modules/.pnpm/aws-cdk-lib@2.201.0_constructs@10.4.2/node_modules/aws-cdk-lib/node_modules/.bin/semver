#!/bin/sh
basedir=$(dirname "$(echo "$0" | sed -e 's,\\,/,g')")

case `uname` in
    *CYGWIN*) basedir=`cygpath -w "$basedir"`;;
esac

if [ -z "$NODE_PATH" ]; then
  export NODE_PATH="/Users/<USER>/dev/Graduation_Project/erd-ai-platform/eap-infra/cdk/node_modules/.pnpm/aws-cdk-lib@2.201.0_constructs@10.4.2/node_modules/aws-cdk-lib/node_modules/semver/bin/node_modules:/Users/<USER>/dev/Graduation_Project/erd-ai-platform/eap-infra/cdk/node_modules/.pnpm/aws-cdk-lib@2.201.0_constructs@10.4.2/node_modules/aws-cdk-lib/node_modules/semver/node_modules:/Users/<USER>/dev/Graduation_Project/erd-ai-platform/eap-infra/cdk/node_modules/.pnpm/aws-cdk-lib@2.201.0_constructs@10.4.2/node_modules/aws-cdk-lib/node_modules:/Users/<USER>/dev/Graduation_Project/erd-ai-platform/eap-infra/cdk/node_modules/.pnpm/aws-cdk-lib@2.201.0_constructs@10.4.2/node_modules:/Users/<USER>/dev/Graduation_Project/erd-ai-platform/eap-infra/cdk/node_modules/.pnpm/node_modules"
else
  export NODE_PATH="/Users/<USER>/dev/Graduation_Project/erd-ai-platform/eap-infra/cdk/node_modules/.pnpm/aws-cdk-lib@2.201.0_constructs@10.4.2/node_modules/aws-cdk-lib/node_modules/semver/bin/node_modules:/Users/<USER>/dev/Graduation_Project/erd-ai-platform/eap-infra/cdk/node_modules/.pnpm/aws-cdk-lib@2.201.0_constructs@10.4.2/node_modules/aws-cdk-lib/node_modules/semver/node_modules:/Users/<USER>/dev/Graduation_Project/erd-ai-platform/eap-infra/cdk/node_modules/.pnpm/aws-cdk-lib@2.201.0_constructs@10.4.2/node_modules/aws-cdk-lib/node_modules:/Users/<USER>/dev/Graduation_Project/erd-ai-platform/eap-infra/cdk/node_modules/.pnpm/aws-cdk-lib@2.201.0_constructs@10.4.2/node_modules:/Users/<USER>/dev/Graduation_Project/erd-ai-platform/eap-infra/cdk/node_modules/.pnpm/node_modules:$NODE_PATH"
fi
if [ -x "$basedir/node" ]; then
  exec "$basedir/node"  "$basedir/../semver/bin/semver.js" "$@"
else
  exec node  "$basedir/../semver/bin/semver.js" "$@"
fi
