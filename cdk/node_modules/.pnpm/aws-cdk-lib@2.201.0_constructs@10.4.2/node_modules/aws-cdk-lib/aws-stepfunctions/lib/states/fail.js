"use strict";var _a;Object.defineProperty(exports,"__esModule",{value:!0}),exports.Fail=void 0;var jsiiDeprecationWarnings=()=>{var tmp=require("../../../.warnings.jsii.js");return jsiiDeprecationWarnings=()=>tmp,tmp};const JSII_RTTI_SYMBOL_1=Symbol.for("jsii.rtti");var state_type_1=()=>{var tmp=require("./private/state-type");return state_type_1=()=>tmp,tmp},state_1=()=>{var tmp=require("./state");return state_1=()=>tmp,tmp},core_1=()=>{var tmp=require("../../../core");return core_1=()=>tmp,tmp},types_1=()=>{var tmp=require("../types");return types_1=()=>tmp,tmp};class Fail extends state_1().State{static jsonPath(scope,id,props={}){try{jsiiDeprecationWarnings().aws_cdk_lib_aws_stepfunctions_FailJsonPathProps(props)}catch(error){throw process.env.JSII_DEBUG!=="1"&&error.name==="DeprecationError"&&Error.captureStackTrace(error,this.jsonPath),error}return new Fail(scope,id,props)}static jsonata(scope,id,props={}){try{jsiiDeprecationWarnings().aws_cdk_lib_aws_stepfunctions_FailJsonataProps(props)}catch(error){throw process.env.JSII_DEBUG!=="1"&&error.name==="DeprecationError"&&Error.captureStackTrace(error,this.jsonata),error}return new Fail(scope,id,{...props,queryLanguage:types_1().QueryLanguage.JSONATA})}constructor(scope,id,props={}){super(scope,id,props),this.endStates=[];try{jsiiDeprecationWarnings().aws_cdk_lib_aws_stepfunctions_FailProps(props)}catch(error){throw process.env.JSII_DEBUG!=="1"&&error.name==="DeprecationError"&&Error.captureStackTrace(error,Fail),error}this.error=props.error,this.errorPath=props.errorPath,this.cause=props.cause,this.causePath=props.causePath}toStateJson(queryLanguage){try{jsiiDeprecationWarnings().aws_cdk_lib_aws_stepfunctions_QueryLanguage(queryLanguage)}catch(error){throw process.env.JSII_DEBUG!=="1"&&error.name==="DeprecationError"&&Error.captureStackTrace(error,this.toStateJson),error}return{Type:state_type_1().StateType.FAIL,...this.renderQueryLanguage(queryLanguage),Comment:this.comment,Error:this.error,ErrorPath:this.isIntrinsicString(this.errorPath)?this.errorPath:(0,state_1().renderJsonPath)(this.errorPath),Cause:this.cause,CausePath:this.isIntrinsicString(this.causePath)?this.causePath:(0,state_1().renderJsonPath)(this.causePath)}}validateState(){const errors=super.validateState();return this.errorPath&&this.isIntrinsicString(this.errorPath)&&!this.isAllowedIntrinsic(this.errorPath)&&errors.push(`You must specify a valid intrinsic function in errorPath. Must be one of ${Fail.allowedIntrinsics.join(", ")}`),this.causePath&&this.isIntrinsicString(this.causePath)&&!this.isAllowedIntrinsic(this.causePath)&&errors.push(`You must specify a valid intrinsic function in causePath. Must be one of ${Fail.allowedIntrinsics.join(", ")}`),this.error&&this.errorPath&&errors.push("Fail state cannot have both error and errorPath"),this.cause&&this.causePath&&errors.push("Fail state cannot have both cause and causePath"),errors}isIntrinsicString(jsonPath){return!core_1().Token.isUnresolved(jsonPath)&&!jsonPath?.startsWith("$")}isAllowedIntrinsic(intrinsic){return Fail.allowedIntrinsics.some(allowed=>intrinsic.startsWith(allowed))}}exports.Fail=Fail,_a=JSII_RTTI_SYMBOL_1,Fail[_a]={fqn:"aws-cdk-lib.aws_stepfunctions.Fail",version:"2.201.0"},Fail.allowedIntrinsics=["States.Format","States.JsonToString","States.ArrayGetItem","States.Base64Encode","States.Base64Decode","States.Hash","States.UUID"];
