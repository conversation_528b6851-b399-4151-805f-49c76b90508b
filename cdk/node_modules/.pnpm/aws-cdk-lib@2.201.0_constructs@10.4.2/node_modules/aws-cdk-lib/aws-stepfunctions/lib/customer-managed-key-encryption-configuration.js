"use strict";var _a;Object.defineProperty(exports,"__esModule",{value:!0}),exports.CustomerManagedEncryptionConfiguration=void 0;var jsiiDeprecationWarnings=()=>{var tmp=require("../../.warnings.jsii.js");return jsiiDeprecationWarnings=()=>tmp,tmp};const JSII_RTTI_SYMBOL_1=Symbol.for("jsii.rtti");var encryption_configuration_1=()=>{var tmp=require("./encryption-configuration");return encryption_configuration_1=()=>tmp,tmp},cdk=()=>{var tmp=require("../../core");return cdk=()=>tmp,tmp};const CUSTOMER_MANAGED_KMS_KEY="CUSTOMER_MANAGED_KMS_KEY";class CustomerManagedEncryptionConfiguration extends encryption_configuration_1().EncryptionConfiguration{constructor(kmsKey,kmsDataKeyReusePeriodSeconds){super(CUSTOMER_MANAGED_KMS_KEY);try{jsiiDeprecationWarnings().aws_cdk_lib_aws_kms_IKey(kmsKey),jsiiDeprecationWarnings().aws_cdk_lib_Duration(kmsDataKeyReusePeriodSeconds)}catch(error){throw process.env.JSII_DEBUG!=="1"&&error.name==="DeprecationError"&&Error.captureStackTrace(error,CustomerManagedEncryptionConfiguration),error}this.kmsKey=kmsKey,this.validateKmsDataKeyReusePeriodSeconds(kmsDataKeyReusePeriodSeconds),this.kmsDataKeyReusePeriodSeconds=kmsDataKeyReusePeriodSeconds}isInvalidKmsDataKeyReusePeriodSeconds(kmsDataKeyReusePeriodSeconds){return kmsDataKeyReusePeriodSeconds.toSeconds()<60||kmsDataKeyReusePeriodSeconds.toSeconds()>900}validateKmsDataKeyReusePeriodSeconds(kmsDataKeyReusePeriodSeconds){if(kmsDataKeyReusePeriodSeconds&&this.isInvalidKmsDataKeyReusePeriodSeconds(kmsDataKeyReusePeriodSeconds))throw new(cdk()).UnscopedValidationError("kmsDataKeyReusePeriodSeconds must have a value between 60 and 900 seconds")}}exports.CustomerManagedEncryptionConfiguration=CustomerManagedEncryptionConfiguration,_a=JSII_RTTI_SYMBOL_1,CustomerManagedEncryptionConfiguration[_a]={fqn:"aws-cdk-lib.aws_stepfunctions.CustomerManagedEncryptionConfiguration",version:"2.201.0"};
