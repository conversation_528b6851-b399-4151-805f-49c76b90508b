"use strict";var _a,_b;Object.defineProperty(exports,"__esModule",{value:!0}),exports.Timeout=exports.IntegrationPattern=exports.TaskStateBase=void 0;var jsiiDeprecationWarnings=()=>{var tmp=require("../../../.warnings.jsii.js");return jsiiDeprecationWarnings=()=>tmp,tmp};const JSII_RTTI_SYMBOL_1=Symbol.for("jsii.rtti");var state_1=()=>{var tmp=require("./state");return state_1=()=>tmp,tmp},cloudwatch=()=>{var tmp=require("../../../aws-cloudwatch");return cloudwatch=()=>tmp,tmp},iam=()=>{var tmp=require("../../../aws-iam");return iam=()=>tmp,tmp},cdk=()=>{var tmp=require("../../../core");return cdk=()=>tmp,tmp},chain_1=()=>{var tmp=require("../chain");return chain_1=()=>tmp,tmp},fields_1=()=>{var tmp=require("../fields");return fields_1=()=>tmp,tmp},types_1=()=>{var tmp=require("../types");return types_1=()=>tmp,tmp};class TaskStateBase extends state_1().State{constructor(scope,id,props){super(scope,id,props);try{jsiiDeprecationWarnings().aws_cdk_lib_aws_stepfunctions_TaskStateBaseProps(props)}catch(error){throw process.env.JSII_DEBUG!=="1"&&error.name==="DeprecationError"&&Error.captureStackTrace(error,TaskStateBase),error}this.endStates=[this],this.timeout=props.timeout,this.taskTimeout=props.taskTimeout,this.heartbeat=props.heartbeat,this.heartbeatTimeout=props.heartbeatTimeout,this.credentials=props.credentials}addRetry(props={}){try{jsiiDeprecationWarnings().aws_cdk_lib_aws_stepfunctions_RetryProps(props)}catch(error){throw process.env.JSII_DEBUG!=="1"&&error.name==="DeprecationError"&&Error.captureStackTrace(error,this.addRetry),error}return super._addRetry(props),this}addCatch(handler,props={}){try{jsiiDeprecationWarnings().aws_cdk_lib_aws_stepfunctions_IChainable(handler),jsiiDeprecationWarnings().aws_cdk_lib_aws_stepfunctions_CatchProps(props)}catch(error){throw process.env.JSII_DEBUG!=="1"&&error.name==="DeprecationError"&&Error.captureStackTrace(error,this.addCatch),error}return super._addCatch(handler.startState,props),this}next(next){try{jsiiDeprecationWarnings().aws_cdk_lib_aws_stepfunctions_IChainable(next)}catch(error){throw process.env.JSII_DEBUG!=="1"&&error.name==="DeprecationError"&&Error.captureStackTrace(error,this.next),error}return super.makeNext(next.startState),chain_1().Chain.sequence(this,next)}toStateJson(topLevelQueryLanguage){try{jsiiDeprecationWarnings().aws_cdk_lib_aws_stepfunctions_QueryLanguage(topLevelQueryLanguage)}catch(error){throw process.env.JSII_DEBUG!=="1"&&error.name==="DeprecationError"&&Error.captureStackTrace(error,this.toStateJson),error}return{...this.renderQueryLanguage(topLevelQueryLanguage),...this.renderNextEnd(),...this.renderRetryCatch(),...this.renderTaskBase(),...this._renderTask(topLevelQueryLanguage)}}metric(metricName,props){try{jsiiDeprecationWarnings().aws_cdk_lib_aws_cloudwatch_MetricOptions(props)}catch(error){throw process.env.JSII_DEBUG!=="1"&&error.name==="DeprecationError"&&Error.captureStackTrace(error,this.metric),error}return new(cloudwatch()).Metric({namespace:"AWS/States",metricName,dimensionsMap:this.taskMetrics?.metricDimensions,statistic:"sum",...props}).attachTo(this)}metricRunTime(props){try{jsiiDeprecationWarnings().aws_cdk_lib_aws_cloudwatch_MetricOptions(props)}catch(error){throw process.env.JSII_DEBUG!=="1"&&error.name==="DeprecationError"&&Error.captureStackTrace(error,this.metricRunTime),error}return this.taskMetric(this.taskMetrics?.metricPrefixSingular,"RunTime",{statistic:"avg",...props})}metricScheduleTime(props){try{jsiiDeprecationWarnings().aws_cdk_lib_aws_cloudwatch_MetricOptions(props)}catch(error){throw process.env.JSII_DEBUG!=="1"&&error.name==="DeprecationError"&&Error.captureStackTrace(error,this.metricScheduleTime),error}return this.taskMetric(this.taskMetrics?.metricPrefixSingular,"ScheduleTime",{statistic:"avg",...props})}metricTime(props){try{jsiiDeprecationWarnings().aws_cdk_lib_aws_cloudwatch_MetricOptions(props)}catch(error){throw process.env.JSII_DEBUG!=="1"&&error.name==="DeprecationError"&&Error.captureStackTrace(error,this.metricTime),error}return this.taskMetric(this.taskMetrics?.metricPrefixSingular,"Time",{statistic:"avg",...props})}metricScheduled(props){try{jsiiDeprecationWarnings().aws_cdk_lib_aws_cloudwatch_MetricOptions(props)}catch(error){throw process.env.JSII_DEBUG!=="1"&&error.name==="DeprecationError"&&Error.captureStackTrace(error,this.metricScheduled),error}return this.taskMetric(this.taskMetrics?.metricPrefixPlural,"Scheduled",props)}metricTimedOut(props){try{jsiiDeprecationWarnings().aws_cdk_lib_aws_cloudwatch_MetricOptions(props)}catch(error){throw process.env.JSII_DEBUG!=="1"&&error.name==="DeprecationError"&&Error.captureStackTrace(error,this.metricTimedOut),error}return this.taskMetric(this.taskMetrics?.metricPrefixPlural,"TimedOut",props)}metricStarted(props){try{jsiiDeprecationWarnings().aws_cdk_lib_aws_cloudwatch_MetricOptions(props)}catch(error){throw process.env.JSII_DEBUG!=="1"&&error.name==="DeprecationError"&&Error.captureStackTrace(error,this.metricStarted),error}return this.taskMetric(this.taskMetrics?.metricPrefixPlural,"Started",props)}metricSucceeded(props){try{jsiiDeprecationWarnings().aws_cdk_lib_aws_cloudwatch_MetricOptions(props)}catch(error){throw process.env.JSII_DEBUG!=="1"&&error.name==="DeprecationError"&&Error.captureStackTrace(error,this.metricSucceeded),error}return this.taskMetric(this.taskMetrics?.metricPrefixPlural,"Succeeded",props)}metricFailed(props){try{jsiiDeprecationWarnings().aws_cdk_lib_aws_cloudwatch_MetricOptions(props)}catch(error){throw process.env.JSII_DEBUG!=="1"&&error.name==="DeprecationError"&&Error.captureStackTrace(error,this.metricFailed),error}return this.taskMetric(this.taskMetrics?.metricPrefixPlural,"Failed",props)}metricHeartbeatTimedOut(props){try{jsiiDeprecationWarnings().aws_cdk_lib_aws_cloudwatch_MetricOptions(props)}catch(error){throw process.env.JSII_DEBUG!=="1"&&error.name==="DeprecationError"&&Error.captureStackTrace(error,this.metricHeartbeatTimedOut),error}return this.taskMetric(this.taskMetrics?.metricPrefixPlural,"HeartbeatTimedOut",props)}whenBoundToGraph(graph){try{jsiiDeprecationWarnings().aws_cdk_lib_aws_stepfunctions_StateGraph(graph)}catch(error){throw process.env.JSII_DEBUG!=="1"&&error.name==="DeprecationError"&&Error.captureStackTrace(error,this.whenBoundToGraph),error}super.whenBoundToGraph(graph);for(const policyStatement of this.taskPolicies||[])graph.registerPolicyStatement(policyStatement);this.credentials&&graph.registerPolicyStatement(new(iam()).PolicyStatement({effect:iam().Effect.ALLOW,actions:["sts:AssumeRole"],resources:[this.credentials.role.resource]}))}_renderParametersOrArguments(paramOrArg,queryLanguage){return{Parameters:queryLanguage===types_1().QueryLanguage.JSON_PATH?fields_1().FieldUtils.renderObject(paramOrArg):void 0,Arguments:queryLanguage===types_1().QueryLanguage.JSONATA?paramOrArg:void 0}}taskMetric(prefix,suffix,props){if(prefix===void 0)throw new(cdk()).UnscopedValidationError("Task does not expose metrics. Use the 'metric()' function to add metrics.");return this.metric(prefix+suffix,props)}renderCredentials(){return this.credentials?fields_1().FieldUtils.renderObject({Credentials:{RoleArn:this.credentials.role.roleArn}}):void 0}renderTaskBase(topLevelQueryLanguage){return{Type:"Task",Comment:this.comment,TimeoutSeconds:this.timeout?.toSeconds()??this.taskTimeout?.seconds??this.taskTimeout?.jsonataExpression,TimeoutSecondsPath:this.taskTimeout?.path,HeartbeatSeconds:this.heartbeat?.toSeconds()??this.heartbeatTimeout?.seconds??this.heartbeatTimeout?.jsonataExpression,HeartbeatSecondsPath:this.heartbeatTimeout?.path,InputPath:(0,state_1().renderJsonPath)(this.inputPath),OutputPath:(0,state_1().renderJsonPath)(this.outputPath),ResultPath:(0,state_1().renderJsonPath)(this.resultPath),Arguments:this.arguments,Output:this.outputs,...this.renderResultSelector(),...this.renderCredentials(),...this.renderAssign(topLevelQueryLanguage)}}}exports.TaskStateBase=TaskStateBase,_a=JSII_RTTI_SYMBOL_1,TaskStateBase[_a]={fqn:"aws-cdk-lib.aws_stepfunctions.TaskStateBase",version:"2.201.0"};var IntegrationPattern;(function(IntegrationPattern2){IntegrationPattern2.REQUEST_RESPONSE="REQUEST_RESPONSE",IntegrationPattern2.RUN_JOB="RUN_JOB",IntegrationPattern2.WAIT_FOR_TASK_TOKEN="WAIT_FOR_TASK_TOKEN"})(IntegrationPattern||(exports.IntegrationPattern=IntegrationPattern={}));class Timeout{static duration(duration){try{jsiiDeprecationWarnings().aws_cdk_lib_Duration(duration)}catch(error){throw process.env.JSII_DEBUG!=="1"&&error.name==="DeprecationError"&&Error.captureStackTrace(error,this.duration),error}return{seconds:duration.toSeconds()}}static jsonata(jsonataExpression){return{jsonataExpression}}static at(path){return{path}}}exports.Timeout=Timeout,_b=JSII_RTTI_SYMBOL_1,Timeout[_b]={fqn:"aws-cdk-lib.aws_stepfunctions.Timeout",version:"2.201.0"};
