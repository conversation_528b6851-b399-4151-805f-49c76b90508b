"use strict";var _a;Object.defineProperty(exports,"__esModule",{value:!0}),exports.InputType=exports.TaskInput=void 0;const JSII_RTTI_SYMBOL_1=Symbol.for("jsii.rtti");var fields_1=()=>{var tmp=require("./fields");return fields_1=()=>tmp,tmp};class TaskInput{static fromText(text){return new TaskInput(InputType.TEXT,text)}static fromObject(obj){return new TaskInput(InputType.OBJECT,obj)}static fromJsonPathAt(path){return new TaskInput(InputType.TEXT,fields_1().JsonPath.stringAt(path))}static fromDataAt(path){return new TaskInput(InputType.TEXT,fields_1().JsonPath.stringAt(path))}static fromContextAt(path){return new TaskInput(InputType.TEXT,fields_1().JsonPath.stringAt(path))}constructor(type,value){this.type=type,this.value=value}}exports.TaskInput=TaskInput,_a=JSII_RTTI_SYMBOL_1,TaskInput[_a]={fqn:"aws-cdk-lib.aws_stepfunctions.TaskInput",version:"2.201.0"};var InputType;(function(InputType2){InputType2[InputType2.TEXT=0]="TEXT",InputType2[InputType2.OBJECT=1]="OBJECT"})(InputType||(exports.InputType=InputType={}));
