"use strict";Object.defineProperty(exports,"__esModule",{value:!0}),exports.IntrinsicParser=void 0;var core_1=()=>{var tmp=require("../../../core");return core_1=()=>tmp,tmp};class IntrinsicParser{constructor(expression){this.expression=expression,this.i=0}parseTopLevelIntrinsic(){this.ws();let ret;return this.char()==="$"?ret=this.parsePath():isAlphaNum(this.char())?ret=this.parseFnCall():this.raiseError("expected '$' or a function call"),this.ws(),this.eof||this.raiseError("unexpected trailing characters"),ret}parseIntrinsic(){if(this.ws(),this.char()==="$")return this.parsePath();if(isAlphaNum(this.char()))return this.parseFnCall();if(this.char()==="'")return this.parseStringLiteral();this.raiseError("expected $, function or single-quoted string")}parsePath(){const pathString=new Array;this.char()!=="$"&&this.raiseError("expected '$'"),pathString.push(this.consume());let done=!1;for(;!done&&!this.eof;)switch(this.char()){case".":case"$":pathString.push(this.consume());break;case"'":const{quoted}=this.consumeQuotedString();pathString.push(quoted);break;case"[":pathString.push(this.consumeBracketedExpression("]"));break;default:if(isAlphaNum(this.char())){pathString.push(this.consume());break}done=!0}return{type:"path",path:pathString.join("")}}parseFnCall(){const name=new Array;for(;this.char()!=="(";)name.push(this.consume());this.next(),this.ws();const args=[];for(;this.char()!==")";)if(args.push(this.parseIntrinsic()),this.ws(),this.char()===","){this.next();continue}else{if(this.char()===")")continue;this.raiseError("expected , or )")}return this.next(),{type:"fncall",arguments:args,functionName:name.join("")}}parseStringLiteral(){const{unquoted}=this.consumeQuotedString();return{type:"string-literal",literal:unquoted}}consumeBracketedExpression(closingBrace){const ret=new Array;for(ret.push(this.consume());this.char()!==closingBrace;)this.char()==="["?ret.push(this.consumeBracketedExpression("]")):this.char()==="{"?ret.push(this.consumeBracketedExpression("}")):ret.push(this.consume());return ret.push(this.consume()),ret.join("")}consumeQuotedString(){const quoted=new Array,unquoted=new Array;for(quoted.push(this.consume());this.char()!=="'";)this.char()==="\\"&&quoted.push(this.consume()),quoted.push(this.char()),unquoted.push(this.char()),this.next();return quoted.push(this.consume()),{quoted:quoted.join(""),unquoted:unquoted.join("")}}ws(){for(;!this.eof&&[" ","	",`
`].includes(this.char());)this.next()}get eof(){return this.i>=this.expression.length}char(){return this.eof&&this.raiseError("unexpected end of string"),this.expression[this.i]}next(){this.i++}consume(){const ret=this.char();return this.next(),ret}raiseError(message){throw new(core_1()).UnscopedValidationError(`Invalid JSONPath expression: ${message} at index ${this.i} in ${JSON.stringify(this.expression)}`)}}exports.IntrinsicParser=IntrinsicParser;function isAlphaNum(x){return x.match(/^[a-zA-Z0-9]$/)}
