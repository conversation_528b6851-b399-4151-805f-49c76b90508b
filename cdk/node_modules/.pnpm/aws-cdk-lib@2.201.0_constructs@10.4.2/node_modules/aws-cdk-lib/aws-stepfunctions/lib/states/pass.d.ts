import { Construct } from 'constructs';
import { AssignableStateOptions, JsonataCommonOptions, JsonPathCommonOptions, State, StateBaseProps } from './state';
import { Chain } from '../chain';
import { IChainable, INextable, QueryLanguage } from '../types';
/**
 * The result of a Pass operation
 */
export declare class Result {
    readonly value: any;
    /**
     * The result of the operation is a string
     */
    static fromString(value: string): Result;
    /**
     * The result of the operation is a number
     */
    static fromNumber(value: number): Result;
    /**
     * The result of the operation is a boolean
     */
    static fromBoolean(value: boolean): Result;
    /**
     * The result of the operation is an object
     */
    static fromObject(value: {
        [key: string]: any;
    }): Result;
    /**
     * The result of the operation is an array
     */
    static fromArray(value: any[]): Result;
    /**
     *
     * @param value result of the Pass operation
     */
    protected constructor(value: any);
}
interface PassJsonPathOptions extends JsonPathCommonOptions {
    /**
     * If given, treat as the result of this operation
     *
     * Can be used to inject or replace the current execution state.
     *
     * @default No injected result
     */
    readonly result?: Result;
    /**
     * JSONPath expression to indicate where to inject the state's output
     *
     * May also be the special value JsonPath.DISCARD, which will cause the state's
     * input to become its output.
     *
     * @default $
     */
    readonly resultPath?: string;
    /**
     * Parameters pass a collection of key-value pairs, either static values or JSONPath expressions that select from the input.
     *
     * @see
     * https://docs.aws.amazon.com/step-functions/latest/dg/input-output-inputpath-params.html#input-output-parameters
     *
     * @default No parameters
     */
    readonly parameters?: {
        [name: string]: any;
    };
}
/**
 * Properties for defining a Pass state that using JSONPath
 */
export interface PassJsonPathProps extends StateBaseProps, AssignableStateOptions, PassJsonPathOptions {
}
/**
 * Properties for defining a Pass state that using JSONata
 */
export interface PassJsonataProps extends StateBaseProps, AssignableStateOptions, JsonataCommonOptions {
}
/**
 * Properties for defining a Pass state
 */
export interface PassProps extends StateBaseProps, AssignableStateOptions, PassJsonPathOptions, JsonataCommonOptions {
}
/**
 * Define a Pass in the state machine
 *
 * A Pass state can be used to transform the current execution's state.
 */
export declare class Pass extends State implements INextable {
    /**
     * Define a Pass using JSONPath in the state machine
     *
     * A Pass state can be used to transform the current execution's state.
     */
    static jsonPath(scope: Construct, id: string, props?: PassJsonPathProps): Pass;
    /**
     * Define a Pass using JSONata in the state machine
     *
     * A Pass state can be used to transform the current execution's state.
     */
    static jsonata(scope: Construct, id: string, props?: PassJsonataProps): Pass;
    readonly endStates: INextable[];
    private readonly result?;
    constructor(scope: Construct, id: string, props?: PassProps);
    /**
     * Continue normal execution with the given state
     */
    next(next: IChainable): Chain;
    /**
     * Return the Amazon States Language object for this state
     */
    toStateJson(topLevelQueryLanguage?: QueryLanguage): object;
    /**
     * Render Parameters in ASL JSON format
     */
    private renderParameters;
}
export {};
