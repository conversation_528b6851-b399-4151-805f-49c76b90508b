"use strict";var _a;Object.defineProperty(exports,"__esModule",{value:!0}),exports.Condition=void 0;var jsiiDeprecationWarnings=()=>{var tmp=require("../../.warnings.jsii.js");return jsiiDeprecationWarnings=()=>tmp,tmp};const JSII_RTTI_SYMBOL_1=Symbol.for("jsii.rtti");var core_1=()=>{var tmp=require("../../core");return core_1=()=>tmp,tmp};class Condition{static isPresent(variable){return new VariableComparison(variable,ComparisonOperator.IsPresent,!0)}static isNotPresent(variable){return new VariableComparison(variable,ComparisonOperator.IsPresent,!1)}static isString(variable){return new VariableComparison(variable,ComparisonOperator.IsString,!0)}static isNotString(variable){return new VariableComparison(variable,ComparisonOperator.IsString,!1)}static isNumeric(variable){return new VariableComparison(variable,ComparisonOperator.IsNumeric,!0)}static isNotNumeric(variable){return new VariableComparison(variable,ComparisonOperator.IsNumeric,!1)}static isBoolean(variable){return new VariableComparison(variable,ComparisonOperator.IsBoolean,!0)}static isNotBoolean(variable){return new VariableComparison(variable,ComparisonOperator.IsBoolean,!1)}static isTimestamp(variable){return new VariableComparison(variable,ComparisonOperator.IsTimestamp,!0)}static isNotTimestamp(variable){return new VariableComparison(variable,ComparisonOperator.IsTimestamp,!1)}static isNotNull(variable){return new VariableComparison(variable,ComparisonOperator.IsNull,!1)}static isNull(variable){return new VariableComparison(variable,ComparisonOperator.IsNull,!0)}static booleanEquals(variable,value){return new VariableComparison(variable,ComparisonOperator.BooleanEquals,value)}static booleanEqualsJsonPath(variable,value){return new VariableComparison(variable,ComparisonOperator.BooleanEqualsPath,value)}static stringEqualsJsonPath(variable,value){return new VariableComparison(variable,ComparisonOperator.StringEqualsPath,value)}static stringEquals(variable,value){return new VariableComparison(variable,ComparisonOperator.StringEquals,value)}static stringLessThan(variable,value){return new VariableComparison(variable,ComparisonOperator.StringLessThan,value)}static stringLessThanJsonPath(variable,value){return new VariableComparison(variable,ComparisonOperator.StringLessThanPath,value)}static stringLessThanEquals(variable,value){return new VariableComparison(variable,ComparisonOperator.StringLessThanEquals,value)}static stringLessThanEqualsJsonPath(variable,value){return new VariableComparison(variable,ComparisonOperator.StringLessThanEqualsPath,value)}static stringGreaterThan(variable,value){return new VariableComparison(variable,ComparisonOperator.StringGreaterThan,value)}static stringGreaterThanJsonPath(variable,value){return new VariableComparison(variable,ComparisonOperator.StringGreaterThanPath,value)}static stringGreaterThanEqualsJsonPath(variable,value){return new VariableComparison(variable,ComparisonOperator.StringGreaterThanEqualsPath,value)}static stringGreaterThanEquals(variable,value){return new VariableComparison(variable,ComparisonOperator.StringGreaterThanEquals,value)}static numberEquals(variable,value){return new VariableComparison(variable,ComparisonOperator.NumericEquals,value)}static numberEqualsJsonPath(variable,value){return new VariableComparison(variable,ComparisonOperator.NumericEqualsPath,value)}static numberLessThan(variable,value){return new VariableComparison(variable,ComparisonOperator.NumericLessThan,value)}static numberLessThanJsonPath(variable,value){return new VariableComparison(variable,ComparisonOperator.NumericLessThanPath,value)}static numberLessThanEquals(variable,value){return new VariableComparison(variable,ComparisonOperator.NumericLessThanEquals,value)}static numberLessThanEqualsJsonPath(variable,value){return new VariableComparison(variable,ComparisonOperator.NumericLessThanEqualsPath,value)}static numberGreaterThan(variable,value){return new VariableComparison(variable,ComparisonOperator.NumericGreaterThan,value)}static numberGreaterThanJsonPath(variable,value){return new VariableComparison(variable,ComparisonOperator.NumericGreaterThanPath,value)}static numberGreaterThanEquals(variable,value){return new VariableComparison(variable,ComparisonOperator.NumericGreaterThanEquals,value)}static numberGreaterThanEqualsJsonPath(variable,value){return new VariableComparison(variable,ComparisonOperator.NumericGreaterThanEqualsPath,value)}static timestampEquals(variable,value){return new VariableComparison(variable,ComparisonOperator.TimestampEquals,value)}static timestampEqualsJsonPath(variable,value){return new VariableComparison(variable,ComparisonOperator.TimestampEqualsPath,value)}static timestampLessThan(variable,value){return new VariableComparison(variable,ComparisonOperator.TimestampLessThan,value)}static timestampLessThanJsonPath(variable,value){return new VariableComparison(variable,ComparisonOperator.TimestampLessThanPath,value)}static timestampLessThanEquals(variable,value){return new VariableComparison(variable,ComparisonOperator.TimestampLessThanEquals,value)}static timestampLessThanEqualsJsonPath(variable,value){return new VariableComparison(variable,ComparisonOperator.TimestampLessThanEqualsPath,value)}static timestampGreaterThan(variable,value){return new VariableComparison(variable,ComparisonOperator.TimestampGreaterThan,value)}static timestampGreaterThanJsonPath(variable,value){return new VariableComparison(variable,ComparisonOperator.TimestampGreaterThanPath,value)}static timestampGreaterThanEquals(variable,value){return new VariableComparison(variable,ComparisonOperator.TimestampGreaterThanEquals,value)}static timestampGreaterThanEqualsJsonPath(variable,value){return new VariableComparison(variable,ComparisonOperator.TimestampGreaterThanEqualsPath,value)}static stringMatches(variable,value){return new VariableComparison(variable,ComparisonOperator.StringMatches,value)}static and(...conditions){try{jsiiDeprecationWarnings().aws_cdk_lib_aws_stepfunctions_Condition(conditions)}catch(error){throw process.env.JSII_DEBUG!=="1"&&error.name==="DeprecationError"&&Error.captureStackTrace(error,this.and),error}return new CompoundCondition(CompoundOperator.And,...conditions)}static or(...conditions){try{jsiiDeprecationWarnings().aws_cdk_lib_aws_stepfunctions_Condition(conditions)}catch(error){throw process.env.JSII_DEBUG!=="1"&&error.name==="DeprecationError"&&Error.captureStackTrace(error,this.or),error}return new CompoundCondition(CompoundOperator.Or,...conditions)}static not(condition){try{jsiiDeprecationWarnings().aws_cdk_lib_aws_stepfunctions_Condition(condition)}catch(error){throw process.env.JSII_DEBUG!=="1"&&error.name==="DeprecationError"&&Error.captureStackTrace(error,this.not),error}return new NotCondition(condition)}static jsonata(condition){return new JsonataCondition(condition)}}exports.Condition=Condition,_a=JSII_RTTI_SYMBOL_1,Condition[_a]={fqn:"aws-cdk-lib.aws_stepfunctions.Condition",version:"2.201.0"};var ComparisonOperator;(function(ComparisonOperator2){ComparisonOperator2[ComparisonOperator2.StringEquals=0]="StringEquals",ComparisonOperator2[ComparisonOperator2.StringEqualsPath=1]="StringEqualsPath",ComparisonOperator2[ComparisonOperator2.StringLessThan=2]="StringLessThan",ComparisonOperator2[ComparisonOperator2.StringLessThanPath=3]="StringLessThanPath",ComparisonOperator2[ComparisonOperator2.StringGreaterThan=4]="StringGreaterThan",ComparisonOperator2[ComparisonOperator2.StringGreaterThanPath=5]="StringGreaterThanPath",ComparisonOperator2[ComparisonOperator2.StringLessThanEquals=6]="StringLessThanEquals",ComparisonOperator2[ComparisonOperator2.StringLessThanEqualsPath=7]="StringLessThanEqualsPath",ComparisonOperator2[ComparisonOperator2.StringGreaterThanEquals=8]="StringGreaterThanEquals",ComparisonOperator2[ComparisonOperator2.StringGreaterThanEqualsPath=9]="StringGreaterThanEqualsPath",ComparisonOperator2[ComparisonOperator2.NumericEquals=10]="NumericEquals",ComparisonOperator2[ComparisonOperator2.NumericEqualsPath=11]="NumericEqualsPath",ComparisonOperator2[ComparisonOperator2.NumericLessThan=12]="NumericLessThan",ComparisonOperator2[ComparisonOperator2.NumericLessThanPath=13]="NumericLessThanPath",ComparisonOperator2[ComparisonOperator2.NumericGreaterThan=14]="NumericGreaterThan",ComparisonOperator2[ComparisonOperator2.NumericGreaterThanPath=15]="NumericGreaterThanPath",ComparisonOperator2[ComparisonOperator2.NumericLessThanEquals=16]="NumericLessThanEquals",ComparisonOperator2[ComparisonOperator2.NumericLessThanEqualsPath=17]="NumericLessThanEqualsPath",ComparisonOperator2[ComparisonOperator2.NumericGreaterThanEquals=18]="NumericGreaterThanEquals",ComparisonOperator2[ComparisonOperator2.NumericGreaterThanEqualsPath=19]="NumericGreaterThanEqualsPath",ComparisonOperator2[ComparisonOperator2.BooleanEquals=20]="BooleanEquals",ComparisonOperator2[ComparisonOperator2.BooleanEqualsPath=21]="BooleanEqualsPath",ComparisonOperator2[ComparisonOperator2.TimestampEquals=22]="TimestampEquals",ComparisonOperator2[ComparisonOperator2.TimestampEqualsPath=23]="TimestampEqualsPath",ComparisonOperator2[ComparisonOperator2.TimestampLessThan=24]="TimestampLessThan",ComparisonOperator2[ComparisonOperator2.TimestampLessThanPath=25]="TimestampLessThanPath",ComparisonOperator2[ComparisonOperator2.TimestampGreaterThan=26]="TimestampGreaterThan",ComparisonOperator2[ComparisonOperator2.TimestampGreaterThanPath=27]="TimestampGreaterThanPath",ComparisonOperator2[ComparisonOperator2.TimestampLessThanEquals=28]="TimestampLessThanEquals",ComparisonOperator2[ComparisonOperator2.TimestampLessThanEqualsPath=29]="TimestampLessThanEqualsPath",ComparisonOperator2[ComparisonOperator2.TimestampGreaterThanEquals=30]="TimestampGreaterThanEquals",ComparisonOperator2[ComparisonOperator2.TimestampGreaterThanEqualsPath=31]="TimestampGreaterThanEqualsPath",ComparisonOperator2[ComparisonOperator2.IsNull=32]="IsNull",ComparisonOperator2[ComparisonOperator2.IsBoolean=33]="IsBoolean",ComparisonOperator2[ComparisonOperator2.IsNumeric=34]="IsNumeric",ComparisonOperator2[ComparisonOperator2.IsString=35]="IsString",ComparisonOperator2[ComparisonOperator2.IsTimestamp=36]="IsTimestamp",ComparisonOperator2[ComparisonOperator2.IsPresent=37]="IsPresent",ComparisonOperator2[ComparisonOperator2.StringMatches=38]="StringMatches"})(ComparisonOperator||(ComparisonOperator={}));var CompoundOperator;(function(CompoundOperator2){CompoundOperator2[CompoundOperator2.And=0]="And",CompoundOperator2[CompoundOperator2.Or=1]="Or"})(CompoundOperator||(CompoundOperator={}));class VariableComparison extends Condition{constructor(variable,comparisonOperator,value){if(super(),this.variable=variable,this.comparisonOperator=comparisonOperator,this.value=value,!/^\$|(\$[.[])/.test(variable))throw new(core_1()).UnscopedValidationError(`Variable reference must be '$', start with '$.', or start with '$[', got '${variable}'`)}renderCondition(){return{Variable:this.variable,[ComparisonOperator[this.comparisonOperator]]:this.value}}}class CompoundCondition extends Condition{constructor(operator,...conditions){if(super(),this.operator=operator,this.conditions=conditions,conditions.length===0)throw new(core_1()).UnscopedValidationError("Must supply at least one inner condition for a logical combination")}renderCondition(){return{[CompoundOperator[this.operator]]:this.conditions.map(c=>c.renderCondition())}}}class NotCondition extends Condition{constructor(comparisonOperation){super(),this.comparisonOperation=comparisonOperation}renderCondition(){return{Not:this.comparisonOperation.renderCondition()}}}class JsonataCondition extends Condition{constructor(condition){if(super(),this.condition=condition,!/^{%(.*)%}$/.test(condition))throw new(core_1()).UnscopedValidationError(`JSONata expression must be start with '{%' and end with '%}', got '${condition}'`)}renderCondition(){return{Condition:this.condition}}}
