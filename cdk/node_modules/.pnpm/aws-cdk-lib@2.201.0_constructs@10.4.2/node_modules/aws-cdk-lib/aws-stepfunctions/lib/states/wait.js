"use strict";var _a,_b;Object.defineProperty(exports,"__esModule",{value:!0}),exports.Wait=exports.WaitTime=void 0;var jsiiDeprecationWarnings=()=>{var tmp=require("../../../.warnings.jsii.js");return jsiiDeprecationWarnings=()=>tmp,tmp};const JSII_RTTI_SYMBOL_1=Symbol.for("jsii.rtti");var state_type_1=()=>{var tmp=require("./private/state-type");return state_type_1=()=>tmp,tmp},state_1=()=>{var tmp=require("./state");return state_1=()=>tmp,tmp},chain_1=()=>{var tmp=require("../chain");return chain_1=()=>tmp,tmp},types_1=()=>{var tmp=require("../types");return types_1=()=>tmp,tmp};class WaitTime{static duration(duration){try{jsiiDeprecationWarnings().aws_cdk_lib_Duration(duration)}catch(error){throw process.env.JSII_DEBUG!=="1"&&error.name==="DeprecationError"&&Error.captureStackTrace(error,this.duration),error}return new WaitTime({Seconds:duration.toSeconds()})}static seconds(seconds){return new WaitTime({Seconds:seconds})}static timestamp(timestamp){return new WaitTime({Timestamp:timestamp})}static secondsPath(path){return new WaitTime({SecondsPath:path})}static timestampPath(path){return new WaitTime({TimestampPath:path})}constructor(json){this.json=json}get _json(){return this.json}}exports.WaitTime=WaitTime,_a=JSII_RTTI_SYMBOL_1,WaitTime[_a]={fqn:"aws-cdk-lib.aws_stepfunctions.WaitTime",version:"2.201.0"};class Wait extends state_1().State{static jsonPath(scope,id,props){try{jsiiDeprecationWarnings().aws_cdk_lib_aws_stepfunctions_WaitJsonPathProps(props)}catch(error){throw process.env.JSII_DEBUG!=="1"&&error.name==="DeprecationError"&&Error.captureStackTrace(error,this.jsonPath),error}return new Wait(scope,id,props)}static jsonata(scope,id,props){try{jsiiDeprecationWarnings().aws_cdk_lib_aws_stepfunctions_WaitJsonataProps(props)}catch(error){throw process.env.JSII_DEBUG!=="1"&&error.name==="DeprecationError"&&Error.captureStackTrace(error,this.jsonata),error}return new Wait(scope,id,{...props,queryLanguage:types_1().QueryLanguage.JSONATA})}constructor(scope,id,props){super(scope,id,props);try{jsiiDeprecationWarnings().aws_cdk_lib_aws_stepfunctions_WaitProps(props)}catch(error){throw process.env.JSII_DEBUG!=="1"&&error.name==="DeprecationError"&&Error.captureStackTrace(error,Wait),error}this.time=props.time,this.endStates=[this]}next(next){try{jsiiDeprecationWarnings().aws_cdk_lib_aws_stepfunctions_IChainable(next)}catch(error){throw process.env.JSII_DEBUG!=="1"&&error.name==="DeprecationError"&&Error.captureStackTrace(error,this.next),error}return super.makeNext(next.startState),chain_1().Chain.sequence(this,next)}toStateJson(topLevelQueryLanguage){try{jsiiDeprecationWarnings().aws_cdk_lib_aws_stepfunctions_QueryLanguage(topLevelQueryLanguage)}catch(error){throw process.env.JSII_DEBUG!=="1"&&error.name==="DeprecationError"&&Error.captureStackTrace(error,this.toStateJson),error}return{Type:state_type_1().StateType.WAIT,...this.renderQueryLanguage(topLevelQueryLanguage),Comment:this.comment,...this.time._json,...this.renderInputOutput(),...this.renderNextEnd(),...this.renderAssign(topLevelQueryLanguage)}}}exports.Wait=Wait,_b=JSII_RTTI_SYMBOL_1,Wait[_b]={fqn:"aws-cdk-lib.aws_stepfunctions.Wait",version:"2.201.0"};
