import { EncryptionConfiguration } from './encryption-configuration';
import * as kms from '../../aws-kms';
import * as cdk from '../../core';
/**
 * Define a new CustomerManagedEncryptionConfiguration
 */
export declare class CustomerManagedEncryptionConfiguration extends EncryptionConfiguration {
    /**
     * The symmetric customer managed KMS key for server-side encryption of the state machine definition, and execution history or activity inputs.
     * Step Functions will reuse the key for a maximum of `kmsDataKeyReusePeriodSeconds`.
     *
     * @default - data is transparently encrypted using an AWS owned key
     */
    readonly kmsKey: kms.IKey;
    /**
     * Maximum duration that Step Functions will reuse customer managed data keys.
     * When the period expires, Step Functions will call GenerateDataKey.
     *
     * Must be between 60 and 900 seconds.
     *
     * @default Duration.seconds(300)
     */
    readonly kmsDataKeyReusePeriodSeconds?: cdk.Duration | undefined;
    constructor(kmsKey: kms.IKey, kmsDataKeyReusePeriodSeconds?: cdk.Duration);
    private isInvalidKmsDataKeyReusePeriodSeconds;
    private validateKmsDataKeyReusePeriodSeconds;
}
