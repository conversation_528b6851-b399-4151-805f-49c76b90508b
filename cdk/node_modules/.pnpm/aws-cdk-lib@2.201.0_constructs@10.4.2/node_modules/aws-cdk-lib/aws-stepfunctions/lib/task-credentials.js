"use strict";var _a;Object.defineProperty(exports,"__esModule",{value:!0}),exports.TaskRole=void 0;var jsiiDeprecationWarnings=()=>{var tmp=require("../../.warnings.jsii.js");return jsiiDeprecationWarnings=()=>tmp,tmp};const JSII_RTTI_SYMBOL_1=Symbol.for("jsii.rtti");var fields_1=()=>{var tmp=require("./fields");return fields_1=()=>tmp,tmp};class TaskRole{static fromRoleArnJsonPath(expression){return new JsonExpressionTaskRole(expression)}static fromRole(role){try{jsiiDeprecationWarnings().aws_cdk_lib_aws_iam_IRole(role)}catch(error){throw process.env.JSII_DEBUG!=="1"&&error.name==="DeprecationError"&&Error.captureStackTrace(error,this.fromRole),error}return new IamRoleTaskRole(role)}}exports.TaskRole=TaskRole,_a=JSII_RTTI_SYMBOL_1,TaskRole[_a]={fqn:"aws-cdk-lib.aws_stepfunctions.TaskRole",version:"2.201.0"};class JsonExpressionTaskRole extends TaskRole{constructor(expression){super(),this.roleArn=fields_1().JsonPath.stringAt(expression),this.resource="*"}}class IamRoleTaskRole extends TaskRole{constructor(role){super(),this.roleArn=role.roleArn,this.resource=role.roleArn}}
