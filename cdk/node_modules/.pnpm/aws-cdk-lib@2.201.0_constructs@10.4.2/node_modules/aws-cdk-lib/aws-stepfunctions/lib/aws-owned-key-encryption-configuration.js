"use strict";var _a;Object.defineProperty(exports,"__esModule",{value:!0}),exports.AwsOwnedEncryptionConfiguration=void 0;const JSII_RTTI_SYMBOL_1=Symbol.for("jsii.rtti");var encryption_configuration_1=()=>{var tmp=require("./encryption-configuration");return encryption_configuration_1=()=>tmp,tmp};const AWS_OWNED_KEY="AWS_OWNED_KEY";class AwsOwnedEncryptionConfiguration extends encryption_configuration_1().EncryptionConfiguration{constructor(){super(AWS_OWNED_KEY)}}exports.AwsOwnedEncryptionConfiguration=AwsOwnedEncryptionConfiguration,_a=JSII_RTTI_SYMBOL_1,AwsOwnedEncryptionConfiguration[_a]={fqn:"aws-cdk-lib.aws_stepfunctions.AwsOwnedEncryptionConfiguration",version:"2.201.0"};
