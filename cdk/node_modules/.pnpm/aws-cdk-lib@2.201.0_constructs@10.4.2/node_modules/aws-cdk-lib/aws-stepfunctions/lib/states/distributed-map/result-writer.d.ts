import * as iam from '../../../../aws-iam';
import { IBucket } from '../../../../aws-s3';
import { QueryLanguage } from '../../types';
/**
 * Interface for Result Writer configuration props
 * @deprecated use {@link ResultWriterV2Props} instead
 */
export interface ResultWriterProps {
    /**
     * S3 Bucket in which to save Map Run results
     */
    readonly bucket: IBucket;
    /**
     * S3 prefix in which to save Map Run results
     *
     * @default - No prefix
     */
    readonly prefix?: string;
}
/**
 * Interface for Result Writer configuration props
 */
export interface ResultWriterV2Props {
    /**
     * S3 Bucket in which to save Map Run results
     * @default - specify a bucket
     */
    readonly bucket?: IBucket;
    /**
     * S3 bucket name in which to save Map Run results, as JsonPath
     *
     * @default - no bucket path
     */
    readonly bucketNamePath?: string;
    /**
     * S3 prefix in which to save Map Run results
     *
     * @default - No prefix
     */
    readonly prefix?: string;
    /**
     * Configuration to format the output of the Child Workflow executions
     *
     * @default - Specify both Transformation and OutputType
     */
    readonly writerConfig?: WriterConfig;
}
/**
 * The transformation to be applied to the Output of the Child Workflow executions
 */
export declare enum Transformation {
    /**
     * Returns the output of the child workflow executions unchanged, in addition to the workflow metadata.
     * Default when exporting the child workflow execution results to Amazon S3 and WriterConfig is not specified.
     */
    NONE = "NONE",
    /**
     * Returns the output of the child workflow executions. Default when ResultWriter is not specified.
     */
    COMPACT = "COMPACT",
    /**
     * Returns the output of the child workflow executions.
     * If a child workflow execution returns an array,this option flattens the array,
     * prior to returning the result to a state output or writing the result to an Amazon S3 object.
     */
    FLATTEN = "FLATTEN"
}
/**
 * The format of the Output of the child workflow executions
 */
export declare enum OutputType {
    /**
     * Formats the results as a JSON array
     */
    JSON = "JSON",
    /**
     * Formats the results as JSON Lines
     */
    JSONL = "JSONL"
}
/**
 * Interface for Writer Config props
 */
export interface WriterConfigProps {
    /**
     * The transformation to be applied to the Output of the Child Workflow executions
     */
    readonly transformation: Transformation;
    /**
     * The format of the Output of the child workflow executions
     */
    readonly outputType: OutputType;
}
/**
 * Configuration to format the output
 */
export declare class WriterConfig {
    /**
     * The transformation to be applied to the Output of the Child Workflow executions
     */
    readonly transformation: Transformation;
    /**
     * The format of the Output of the child workflow executions
     */
    readonly outputType: OutputType;
    constructor(props: WriterConfigProps);
}
/**
 * Configuration for writing Distributed Map state results to S3
 * @deprecated use {@link ResultWriterV2} instead
 */
export declare class ResultWriter {
    /**
     * S3 Bucket in which to save Map Run results
     */
    readonly bucket: IBucket;
    /**
     * S3 prefix in which to save Map Run results
     *
     * @default - No prefix
     */
    readonly prefix?: string;
    constructor(props: ResultWriterProps);
    /**
     * Render ResultWriter in ASL JSON format
     */
    render(queryLanguage?: QueryLanguage): any;
    /**
     * Compile policy statements to provide relevent permissions to the state machine
     */
    providePolicyStatements(): iam.PolicyStatement[];
}
/**
 * Configuration for writing Distributed Map state results to S3
 * The ResultWriter field cannot be empty. You must specify one of these sets of sub-fields.
 *   writerConfig - to preview the formatted output, without saving the results to Amazon S3.
 *   bucket and prefix - to save the results to Amazon S3 without additional formatting.
 *   All three fields: writerConfig, bucket and prefix - to format the output and save it to Amazon S3.
 */
export declare class ResultWriterV2 {
    /**
     * S3 Bucket in which to save Map Run results
     */
    readonly bucket?: IBucket;
    /**
     * S3 bucket name in which to save Map Run results, as JsonPath
     */
    readonly bucketNamePath?: string;
    /**
     * S3 prefix in which to save Map Run results
     *
     * @default - No prefix
     */
    readonly prefix?: string;
    /**
     * Configuration to format the output of the Child Workflow executions
     */
    readonly writerConfig?: WriterConfig;
    constructor(props: ResultWriterV2Props);
    /**
     * Render ResultWriter in ASL JSON format
     */
    render(queryLanguage?: QueryLanguage): any;
    /**
     * Compile policy statements to provide relevent permissions to the state machine
     */
    providePolicyStatements(): iam.PolicyStatement[];
    /**
     * Validate that ResultWriter contains exactly either @see bucket or @see bucketNamePath
     */
    validateResultWriter(): string[];
}
