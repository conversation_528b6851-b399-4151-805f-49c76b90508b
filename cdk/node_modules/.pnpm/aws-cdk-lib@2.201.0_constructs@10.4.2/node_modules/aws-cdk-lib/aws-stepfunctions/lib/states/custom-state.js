"use strict";var _a;Object.defineProperty(exports,"__esModule",{value:!0}),exports.CustomState=void 0;var jsiiDeprecationWarnings=()=>{var tmp=require("../../../.warnings.jsii.js");return jsiiDeprecationWarnings=()=>tmp,tmp};const JSII_RTTI_SYMBOL_1=Symbol.for("jsii.rtti");var __1=()=>{var tmp=require("..");return __1=()=>tmp,tmp},state_1=()=>{var tmp=require("./state");return state_1=()=>tmp,tmp},core_1=()=>{var tmp=require("../../../core/");return core_1=()=>tmp,tmp};class CustomState extends state_1().State{constructor(scope,id,props){super(scope,id,{});try{jsiiDeprecationWarnings().aws_cdk_lib_aws_stepfunctions_CustomStateProps(props)}catch(error){throw process.env.JSII_DEBUG!=="1"&&error.name==="DeprecationError"&&Error.captureStackTrace(error,CustomState),error}this.endStates=[this],this.stateJson=props.stateJson}addRetry(props={}){try{jsiiDeprecationWarnings().aws_cdk_lib_aws_stepfunctions_RetryProps(props)}catch(error){throw process.env.JSII_DEBUG!=="1"&&error.name==="DeprecationError"&&Error.captureStackTrace(error,this.addRetry),error}return super._addRetry(props),this}addCatch(handler,props={}){try{jsiiDeprecationWarnings().aws_cdk_lib_aws_stepfunctions_IChainable(handler),jsiiDeprecationWarnings().aws_cdk_lib_aws_stepfunctions_CatchProps(props)}catch(error){throw process.env.JSII_DEBUG!=="1"&&error.name==="DeprecationError"&&Error.captureStackTrace(error,this.addCatch),error}return super._addCatch(handler.startState,props),this}next(next){try{jsiiDeprecationWarnings().aws_cdk_lib_aws_stepfunctions_IChainable(next)}catch(error){throw process.env.JSII_DEBUG!=="1"&&error.name==="DeprecationError"&&Error.captureStackTrace(error,this.next),error}return super.makeNext(next.startState),__1().Chain.sequence(this,next)}toStateJson(queryLanguage){try{jsiiDeprecationWarnings().aws_cdk_lib_aws_stepfunctions_QueryLanguage(queryLanguage)}catch(error){throw process.env.JSII_DEBUG!=="1"&&error.name==="DeprecationError"&&Error.captureStackTrace(error,this.toStateJson),error}const state={...this.renderQueryLanguage(queryLanguage),...this.renderNextEnd(),...this.stateJson,...this.renderRetryCatch()};return this.hasMultipleRetrySources(state)&&this.addMultipleRetrySourcesWarning(),this.hasMultipleCatchSources(state)&&this.addMultipleCatchSourcesWarning(),Array.isArray(this.stateJson.Retry)&&(state.Retry=Array.isArray(state.Retry)?[...state.Retry,...this.stateJson.Retry]:[...this.stateJson.Retry]),Array.isArray(this.stateJson.Catch)&&(state.Catch=Array.isArray(state.Catch)?[...state.Catch,...this.stateJson.Catch]:[...this.stateJson.Catch]),state}hasMultipleRetrySources(state){return!Array.isArray(state.Retry)||!Array.isArray(this.stateJson.Retry)?!1:state.Retry.length>0&&this.stateJson.Retry.length>0}hasMultipleCatchSources(state){return!Array.isArray(state.Catch)||!Array.isArray(this.stateJson.Catch)?!1:state.Catch.length>0&&this.stateJson.Catch.length>0}addMultipleRetrySourcesWarning(){core_1().Annotations.of(this).addWarningV2("@aws-cdk/aws-stepfunctions:multipleRetrySources",["CustomState constructs can configure state retries using the stateJson property or by using the addRetry() function.","When retries are configured using both of these, the state definition's Retry field is generated ","by first rendering retries from addRetry(), then rendering retries from the stateJson."].join(`
`))}addMultipleCatchSourcesWarning(){core_1().Annotations.of(this).addWarningV2("@aws-cdk/aws-stepfunctions:multipleCatchSources",["CustomState constructs can configure state catchers using the stateJson property or by using the addCatch() function.","When catchers are configured using both of these, the state definition's Catch field is generated ","by first rendering catchers from addCatch(), then rendering catchers from the stateJson."].join(`
`))}}exports.CustomState=CustomState,_a=JSII_RTTI_SYMBOL_1,CustomState[_a]={fqn:"aws-cdk-lib.aws_stepfunctions.CustomState",version:"2.201.0"};
