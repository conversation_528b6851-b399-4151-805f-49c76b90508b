import { Construct } from 'constructs';
import { ItemBatcher } from './distributed-map/item-batcher';
import { IItemReader } from './distributed-map/item-reader';
import { ResultWriter, ResultWriterV2 } from './distributed-map/result-writer';
import { MapBase, MapBaseJsonataOptions, MapBaseJsonPathOptions, MapBaseOptions, MapBaseProps } from './map-base';
import { StateGraph } from '../state-graph';
import { StateMachineType } from '../state-machine';
import { CatchProps, IChainable, INextable, ProcessorConfig, QueryLanguage, RetryProps } from '../types';
import { StateBaseProps } from './state';
interface DistributedMapBaseOptions extends MapBaseOptions {
    /**
     * MapExecutionType
     *
     * The execution type of the distributed map state
     *
     * This property overwrites ProcessorConfig.executionType
     *
     * @default StateMachineType.STANDARD
     */
    readonly mapExecutionType?: StateMachineType;
    /**
     * ItemReader
     *
     * Configuration for where to read items dataset in S3 to iterate
     *
     * @default - No itemReader
     */
    readonly itemReader?: IItemReader;
    /**
     * ToleratedFailurePercentage
     *
     * Percentage of failed items to tolerate in a Map Run, as static number
     *
     * @default - No toleratedFailurePercentage
     */
    readonly toleratedFailurePercentage?: number;
    /**
     * ToleratedFailureCount
     *
     * Number of failed items to tolerate in a Map Run, as static number
     *
     * @default - No toleratedFailureCount
     */
    readonly toleratedFailureCount?: number;
    /**
     * Label
     *
     * Unique name for the Distributed Map state added to each Map Run
     *
     * @default - No label
     */
    readonly label?: string;
    /**
     * Configuration for S3 location in which to save Map Run results
     *
     * @deprecated Use {@link resultWriterV2}
     * @default - No resultWriter
     */
    readonly resultWriter?: ResultWriter;
    /**
     * Configuration for S3 location in which to save Map Run results
     * Enable "@aws-cdk/aws-stepfunctions:useDistributedMapResultWriterV2" feature in the context to use resultWriterV2
     * Example: stack.node.setContext("@aws-cdk/aws-stepfunctions:useDistributedMapResultWriterV2", true);
     *
     * @default - No resultWriterV2
     */
    readonly resultWriterV2?: ResultWriterV2;
    /**
     * Specifies to process a group of items in a single child workflow execution
     *
     * @default - No itemBatcher
     */
    readonly itemBatcher?: ItemBatcher;
}
interface DistributedMapJsonPathOptions extends MapBaseJsonPathOptions {
    /**
     * ToleratedFailurePercentagePath
     *
     * Percentage of failed items to tolerate in a Map Run, as JsonPath
     *
     * @default - No toleratedFailurePercentagePath
     */
    readonly toleratedFailurePercentagePath?: string;
    /**
     * ToleratedFailureCountPath
     *
     * Number of failed items to tolerate in a Map Run, as JsonPath
     *
     * @default - No toleratedFailureCountPath
     */
    readonly toleratedFailureCountPath?: string;
}
/**
 * Properties for configuring a Distribute Map state that using JSONPath
 */
export interface DistributedMapJsonPathProps extends StateBaseProps, DistributedMapBaseOptions, DistributedMapJsonPathOptions {
}
/**
 * Properties for configuring a Distribute Map state that using JSONata
 */
export interface DistributedMapJsonataProps extends StateBaseProps, DistributedMapBaseOptions, MapBaseJsonataOptions {
}
/**
 * Properties for configuring a Distribute Map state
 */
export interface DistributedMapProps extends MapBaseProps, DistributedMapBaseOptions, DistributedMapJsonPathOptions, MapBaseJsonataOptions {
}
/**
 * Define a Distributed Mode Map state in the state machine
 *
 * A `Map` state can be used to run a set of steps for each element of an input array.
 * A Map state will execute the same steps for multiple entries of an array in the state input.
 *
 * While the Parallel state executes multiple branches of steps using the same input, a Map state
 * will execute the same steps for multiple entries of an array in the state input.
 *
 * A `Map` state in `Distributed` mode will execute a child workflow for each iteration of the Map state.
 * This serves to increase concurrency and allows for larger workloads to be run in a single state machine.
 * @see https://docs.aws.amazon.com/step-functions/latest/dg/concepts-asl-use-map-state-distributed.html
 */
export declare class DistributedMap extends MapBase implements INextable {
    /**
     * Define a Distributed Mode Map state using JSONPath in the state machine
     *
     * A `Map` state can be used to run a set of steps for each element of an input array.
     * A Map state will execute the same steps for multiple entries of an array in the state input.
     *
     * While the Parallel state executes multiple branches of steps using the same input, a Map state
     * will execute the same steps for multiple entries of an array in the state input.
     *
     * A `Map` state in `Distributed` mode will execute a child workflow for each iteration of the Map state.
     * This serves to increase concurrency and allows for larger workloads to be run in a single state machine.
     * @see https://docs.aws.amazon.com/step-functions/latest/dg/concepts-asl-use-map-state-distributed.html
     */
    static jsonPath(scope: Construct, id: string, props?: DistributedMapJsonPathProps): DistributedMap;
    /**
     * Define a Distributed Mode Map state using JSONata in the state machine
     *
     * A `Map` state can be used to run a set of steps for each element of an input array.
     * A Map state will execute the same steps for multiple entries of an array in the state input.
     *
     * While the Parallel state executes multiple branches of steps using the same input, a Map state
     * will execute the same steps for multiple entries of an array in the state input.
     *
     * A `Map` state in `Distributed` mode will execute a child workflow for each iteration of the Map state.
     * This serves to increase concurrency and allows for larger workloads to be run in a single state machine.
     * @see https://docs.aws.amazon.com/step-functions/latest/dg/concepts-asl-use-map-state-distributed.html
     */
    static jsonata(scope: Construct, id: string, props?: DistributedMapJsonataProps): DistributedMap;
    /**
     * Return whether the given object is a DistributedMap.
     */
    static isDistributedMap(x: any): x is DistributedMap;
    private readonly mapExecutionType?;
    private readonly itemReader?;
    private readonly toleratedFailurePercentage?;
    private readonly toleratedFailurePercentagePath?;
    private readonly toleratedFailureCount?;
    private readonly toleratedFailureCountPath?;
    private readonly label?;
    private readonly resultWriter?;
    private readonly resultWriterV2?;
    private readonly itemBatcher?;
    constructor(scope: Construct, id: string, props?: DistributedMapProps);
    private getResultWriter;
    /**
     * Validate this state
     */
    protected validateState(): string[];
    protected whenBoundToGraph(graph: StateGraph): void;
    /**
     * Add retry configuration for this state
     *
     * This controls if and how the execution will be retried if a particular
     * error occurs.
     */
    addRetry(props?: RetryProps): DistributedMap;
    /**
     * Add a recovery handler for this state
     *
     * When a particular error occurs, execution will continue at the error
     * handler instead of failing the state machine execution.
     */
    addCatch(handler: IChainable, props?: CatchProps): DistributedMap;
    /**
     * Define item processor in a Distributed Map.
     *
     * A Distributed Map must have a non-empty item processor
     */
    itemProcessor(processor: IChainable, config?: ProcessorConfig): DistributedMap;
    /**
     * Return the Amazon States Language object for this state
     */
    toStateJson(stateMachineQueryLanguage?: QueryLanguage): object;
    private addWarningIfResultWriterIsEmpty;
    /**
     * Render the ItemReader as JSON object
     */
    private renderItemReader;
    /**
     * Render ResultWriter in ASL JSON format
     */
    private renderResultWriter;
    /**
     * Render ItemBatcher in ASL JSON format
     */
    private renderItemBatcher;
}
export {};
