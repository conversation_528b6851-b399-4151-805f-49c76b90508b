"use strict";var _a;Object.defineProperty(exports,"__esModule",{value:!0}),exports.DistributedMap=void 0;var jsiiDeprecationWarnings=()=>{var tmp=require("../../../.warnings.jsii.js");return jsiiDeprecationWarnings=()=>tmp,tmp};const JSII_RTTI_SYMBOL_1=Symbol.for("jsii.rtti");var map_base_1=()=>{var tmp=require("./map-base");return map_base_1=()=>tmp,tmp},core_1=()=>{var tmp=require("../../../core");return core_1=()=>tmp,tmp},fields_1=()=>{var tmp=require("../fields");return fields_1=()=>tmp,tmp},state_graph_1=()=>{var tmp=require("../state-graph");return state_graph_1=()=>tmp,tmp},state_machine_1=()=>{var tmp=require("../state-machine");return state_machine_1=()=>tmp,tmp},types_1=()=>{var tmp=require("../types");return types_1=()=>tmp,tmp},cx_api_1=()=>{var tmp=require("../../../cx-api");return cx_api_1=()=>tmp,tmp};const DISTRIBUTED_MAP_SYMBOL=Symbol.for("@aws-cdk/aws-stepfunctions.DistributedMap");class DistributedMap extends map_base_1().MapBase{static jsonPath(scope,id,props={}){try{jsiiDeprecationWarnings().aws_cdk_lib_aws_stepfunctions_DistributedMapJsonPathProps(props)}catch(error){throw process.env.JSII_DEBUG!=="1"&&error.name==="DeprecationError"&&Error.captureStackTrace(error,this.jsonPath),error}return new DistributedMap(scope,id,props)}static jsonata(scope,id,props={}){try{jsiiDeprecationWarnings().aws_cdk_lib_aws_stepfunctions_DistributedMapJsonataProps(props)}catch(error){throw process.env.JSII_DEBUG!=="1"&&error.name==="DeprecationError"&&Error.captureStackTrace(error,this.jsonata),error}return new DistributedMap(scope,id,{...props,queryLanguage:types_1().QueryLanguage.JSONATA})}static isDistributedMap(x){return x!==null&&typeof x=="object"&&DISTRIBUTED_MAP_SYMBOL in x}constructor(scope,id,props={}){super(scope,id,props);try{jsiiDeprecationWarnings().aws_cdk_lib_aws_stepfunctions_DistributedMapProps(props)}catch(error){throw process.env.JSII_DEBUG!=="1"&&error.name==="DeprecationError"&&Error.captureStackTrace(error,DistributedMap),error}this.mapExecutionType=props.mapExecutionType??state_machine_1().StateMachineType.STANDARD,this.itemReader=props.itemReader,this.toleratedFailurePercentage=props.toleratedFailurePercentage,this.toleratedFailurePercentagePath=props.toleratedFailurePercentagePath,this.toleratedFailureCount=props.toleratedFailureCount,this.toleratedFailureCountPath=props.toleratedFailureCountPath,this.label=props.label,this.itemBatcher=props.itemBatcher,this.resultWriter=props.resultWriter,this.resultWriterV2=props.resultWriterV2,this.processorMode=types_1().ProcessorMode.DISTRIBUTED}getResultWriter(){return core_1().FeatureFlags.of(this).isEnabled(cx_api_1().STEPFUNCTIONS_USE_DISTRIBUTED_MAP_RESULT_WRITER_V2)?this.resultWriterV2:this.resultWriter}validateState(){const errors=super.validateState();return this.processorConfig?.mode===types_1().ProcessorMode.INLINE&&errors.push("Processing mode cannot be `INLINE` for a Distributed Map"),this.itemsPath&&this.itemReader&&errors.push("Provide either `itemsPath` or `itemReader`, but not both"),this.toleratedFailurePercentage&&this.toleratedFailurePercentagePath&&errors.push("Provide either `toleratedFailurePercentage` or `toleratedFailurePercentagePath`, but not both"),this.toleratedFailurePercentage&&!(this.toleratedFailurePercentage>=0&&this.toleratedFailurePercentage<=100)&&errors.push("toleratedFailurePercentage must be between 0 and 100"),this.toleratedFailureCount&&this.toleratedFailureCountPath&&errors.push("Provide either `toleratedFailureCount` or `toleratedFailureCountPath`, but not both"),this.itemBatcher&&errors.push(...this.itemBatcher.validateItemBatcher()),this.itemReader&&errors.push(...this.itemReader.validateItemReader()),this.resultWriterV2&&errors.push(...this.resultWriterV2.validateResultWriter()),this.label&&(this.label.length>40&&errors.push("label must be 40 characters or less"),/[\s\?\*\<\>\{\}\\[\]\:\;\,\|\^\~\$\#\%\&\`\"]|[\u0000-\u001f]|[\u007f-\u009f]/gi.test(this.label)&&errors.push("label cannot contain any whitespace or special characters")),errors}whenBoundToGraph(graph){try{jsiiDeprecationWarnings().aws_cdk_lib_aws_stepfunctions_StateGraph(graph)}catch(error){throw process.env.JSII_DEBUG!=="1"&&error.name==="DeprecationError"&&Error.captureStackTrace(error,this.whenBoundToGraph),error}super.whenBoundToGraph(graph);const resultWriter=this.getResultWriter();resultWriter&&resultWriter.providePolicyStatements().forEach(policyStatement=>{graph.registerPolicyStatement(policyStatement)}),this.itemReader&&this.itemReader.providePolicyStatements().forEach(policyStatement=>{graph.registerPolicyStatement(policyStatement)})}addRetry(props={}){try{jsiiDeprecationWarnings().aws_cdk_lib_aws_stepfunctions_RetryProps(props)}catch(error){throw process.env.JSII_DEBUG!=="1"&&error.name==="DeprecationError"&&Error.captureStackTrace(error,this.addRetry),error}return super._addRetry(props),this}addCatch(handler,props={}){try{jsiiDeprecationWarnings().aws_cdk_lib_aws_stepfunctions_IChainable(handler),jsiiDeprecationWarnings().aws_cdk_lib_aws_stepfunctions_CatchProps(props)}catch(error){throw process.env.JSII_DEBUG!=="1"&&error.name==="DeprecationError"&&Error.captureStackTrace(error,this.addCatch),error}return super._addCatch(handler.startState,props),this}itemProcessor(processor,config={}){try{jsiiDeprecationWarnings().aws_cdk_lib_aws_stepfunctions_IChainable(processor),jsiiDeprecationWarnings().aws_cdk_lib_aws_stepfunctions_ProcessorConfig(config)}catch(error){throw process.env.JSII_DEBUG!=="1"&&error.name==="DeprecationError"&&Error.captureStackTrace(error,this.itemProcessor),error}const name=`Map ${this.stateId} Item Processor`,stateGraph=new(state_graph_1()).StateGraph(processor.startState,name);return super.addItemProcessor(stateGraph,config),this}toStateJson(stateMachineQueryLanguage){try{jsiiDeprecationWarnings().aws_cdk_lib_aws_stepfunctions_QueryLanguage(stateMachineQueryLanguage)}catch(error){throw process.env.JSII_DEBUG!=="1"&&error.name==="DeprecationError"&&Error.captureStackTrace(error,this.toStateJson),error}let rendered=super.toStateJson(stateMachineQueryLanguage);rendered.ItemProcessor.ProcessorConfig.ExecutionType&&core_1().Annotations.of(this).addWarningV2("@aws-cdk/aws-stepfunctions:propertyIgnored","Property 'ProcessorConfig.executionType' is ignored, use the 'mapExecutionType' in the 'DistributedMap' class instead."),rendered.ItemProcessor.ProcessorConfig.ExecutionType=this.mapExecutionType;const renderedResultWriter=this.renderResultWriter(stateMachineQueryLanguage);this.addWarningIfResultWriterIsEmpty(renderedResultWriter);const stateQueryLanguage=this.queryLanguage??stateMachineQueryLanguage;return{...rendered,...this.renderItemReader(stateQueryLanguage),...this.renderItemBatcher(),...this.toleratedFailurePercentage&&{ToleratedFailurePercentage:this.toleratedFailurePercentage},...this.toleratedFailurePercentagePath&&{ToleratedFailurePercentagePath:this.toleratedFailurePercentagePath},...this.toleratedFailureCount&&{ToleratedFailureCount:this.toleratedFailureCount},...this.toleratedFailureCountPath&&{ToleratedFailureCountPath:this.toleratedFailureCountPath},...this.label&&{Label:this.label},...renderedResultWriter}}addWarningIfResultWriterIsEmpty(renderedResultWriter){renderedResultWriter&&renderedResultWriter.ResultWriter&&renderedResultWriter.ResultWriter.Resource===void 0&&renderedResultWriter.ResultWriter.WriterConfig===void 0&&core_1().Annotations.of(this).addWarningV2("@aws-cdk/aws-stepfunctions:emptyResultWriter","ResultWriter should specify at least the WriterConfig or the Bucket and Prefix")}renderItemReader(queryLanguage){if(this.itemReader)return fields_1().FieldUtils.renderObject({ItemReader:this.itemReader.render(queryLanguage)})}renderResultWriter(queryLanguage){const resultWriter=this.getResultWriter();if(resultWriter)return fields_1().FieldUtils.renderObject({ResultWriter:resultWriter.render(queryLanguage)})}renderItemBatcher(){if(this.itemBatcher)return{ItemBatcher:this.itemBatcher.render()}}}exports.DistributedMap=DistributedMap,_a=JSII_RTTI_SYMBOL_1,DistributedMap[_a]={fqn:"aws-cdk-lib.aws_stepfunctions.DistributedMap",version:"2.201.0"},Object.defineProperty(DistributedMap.prototype,DISTRIBUTED_MAP_SYMBOL,{value:!0,enumerable:!1,writable:!1});
