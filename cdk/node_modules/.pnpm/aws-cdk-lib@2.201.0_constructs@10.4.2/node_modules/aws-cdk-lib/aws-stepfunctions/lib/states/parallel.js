"use strict";var _a;Object.defineProperty(exports,"__esModule",{value:!0}),exports.Parallel=void 0;var jsiiDeprecationWarnings=()=>{var tmp=require("../../../.warnings.jsii.js");return jsiiDeprecationWarnings=()=>tmp,tmp};const JSII_RTTI_SYMBOL_1=Symbol.for("jsii.rtti");var state_type_1=()=>{var tmp=require("./private/state-type");return state_type_1=()=>tmp,tmp},state_1=()=>{var tmp=require("./state");return state_1=()=>tmp,tmp},chain_1=()=>{var tmp=require("../chain");return chain_1=()=>tmp,tmp},fields_1=()=>{var tmp=require("../fields");return fields_1=()=>tmp,tmp},state_graph_1=()=>{var tmp=require("../state-graph");return state_graph_1=()=>tmp,tmp},types_1=()=>{var tmp=require("../types");return types_1=()=>tmp,tmp};class Parallel extends state_1().State{static jsonPath(scope,id,props={}){try{jsiiDeprecationWarnings().aws_cdk_lib_aws_stepfunctions_ParallelJsonPathProps(props)}catch(error){throw process.env.JSII_DEBUG!=="1"&&error.name==="DeprecationError"&&Error.captureStackTrace(error,this.jsonPath),error}return new Parallel(scope,id,props)}static jsonata(scope,id,props={}){try{jsiiDeprecationWarnings().aws_cdk_lib_aws_stepfunctions_ParallelJsonataProps(props)}catch(error){throw process.env.JSII_DEBUG!=="1"&&error.name==="DeprecationError"&&Error.captureStackTrace(error,this.jsonata),error}return new Parallel(scope,id,{...props,queryLanguage:types_1().QueryLanguage.JSONATA})}constructor(scope,id,props={}){super(scope,id,props),this._branches=[];try{jsiiDeprecationWarnings().aws_cdk_lib_aws_stepfunctions_ParallelProps(props)}catch(error){throw process.env.JSII_DEBUG!=="1"&&error.name==="DeprecationError"&&Error.captureStackTrace(error,Parallel),error}this.endStates=[this]}addRetry(props={}){try{jsiiDeprecationWarnings().aws_cdk_lib_aws_stepfunctions_RetryProps(props)}catch(error){throw process.env.JSII_DEBUG!=="1"&&error.name==="DeprecationError"&&Error.captureStackTrace(error,this.addRetry),error}return super._addRetry(props),this}addCatch(handler,props={}){try{jsiiDeprecationWarnings().aws_cdk_lib_aws_stepfunctions_IChainable(handler),jsiiDeprecationWarnings().aws_cdk_lib_aws_stepfunctions_CatchProps(props)}catch(error){throw process.env.JSII_DEBUG!=="1"&&error.name==="DeprecationError"&&Error.captureStackTrace(error,this.addCatch),error}return super._addCatch(handler.startState,props),this}next(next){try{jsiiDeprecationWarnings().aws_cdk_lib_aws_stepfunctions_IChainable(next)}catch(error){throw process.env.JSII_DEBUG!=="1"&&error.name==="DeprecationError"&&Error.captureStackTrace(error,this.next),error}return super.makeNext(next.startState),chain_1().Chain.sequence(this,next)}branch(...branches){try{jsiiDeprecationWarnings().aws_cdk_lib_aws_stepfunctions_IChainable(branches)}catch(error){throw process.env.JSII_DEBUG!=="1"&&error.name==="DeprecationError"&&Error.captureStackTrace(error,this.branch),error}return this._branches.push(...branches),this}bindToGraph(graph){try{jsiiDeprecationWarnings().aws_cdk_lib_aws_stepfunctions_StateGraph(graph)}catch(error){throw process.env.JSII_DEBUG!=="1"&&error.name==="DeprecationError"&&Error.captureStackTrace(error,this.bindToGraph),error}for(const branch of this._branches){const name=`Parallel '${this.stateId}' branch ${this.branches.length+1}`;super.addBranch(new(state_graph_1()).StateGraph(branch.startState,name))}return this._branches.splice(0,this._branches.length),super.bindToGraph(graph)}toStateJson(topLevelQueryLanguage){try{jsiiDeprecationWarnings().aws_cdk_lib_aws_stepfunctions_QueryLanguage(topLevelQueryLanguage)}catch(error){throw process.env.JSII_DEBUG!=="1"&&error.name==="DeprecationError"&&Error.captureStackTrace(error,this.toStateJson),error}return{Type:state_type_1().StateType.PARALLEL,...this.renderQueryLanguage(topLevelQueryLanguage),Comment:this.comment,ResultPath:(0,state_1().renderJsonPath)(this.resultPath),...this.renderNextEnd(),...this.renderInputOutput(),...this.renderRetryCatch(),...this.renderBranches(),...this.renderResultSelector(),...this.renderParameters(),...this.renderAssign(topLevelQueryLanguage)}}renderParameters(){if(this.parameters)return fields_1().FieldUtils.renderObject({Parameters:this.parameters})}validateState(){return this.branches.length===0?["Parallel must have at least one branch"]:[]}}exports.Parallel=Parallel,_a=JSII_RTTI_SYMBOL_1,Parallel[_a]={fqn:"aws-cdk-lib.aws_stepfunctions.Parallel",version:"2.201.0"};
