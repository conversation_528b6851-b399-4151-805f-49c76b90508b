"use strict";var _a;Object.defineProperty(exports,"__esModule",{value:!0}),exports.Chain=void 0;var jsiiDeprecationWarnings=()=>{var tmp=require("../../.warnings.jsii.js");return jsiiDeprecationWarnings=()=>tmp,tmp};const JSII_RTTI_SYMBOL_1=Symbol.for("jsii.rtti");var core_1=()=>{var tmp=require("../../core");return core_1=()=>tmp,tmp},parallel_1=()=>{var tmp=require("./states/parallel");return parallel_1=()=>tmp,tmp};class Chain{static start(state){try{jsiiDeprecationWarnings().aws_cdk_lib_aws_stepfunctions_IChainable(state)}catch(error){throw process.env.JSII_DEBUG!=="1"&&error.name==="DeprecationError"&&Error.captureStackTrace(error,this.start),error}return new Chain(state.startState,state.endStates,state)}static sequence(start,next){try{jsiiDeprecationWarnings().aws_cdk_lib_aws_stepfunctions_IChainable(start),jsiiDeprecationWarnings().aws_cdk_lib_aws_stepfunctions_IChainable(next)}catch(error){throw process.env.JSII_DEBUG!=="1"&&error.name==="DeprecationError"&&Error.captureStackTrace(error,this.sequence),error}return new Chain(start.startState,next.endStates,next)}static custom(startState,endStates,lastAdded){try{jsiiDeprecationWarnings().aws_cdk_lib_aws_stepfunctions_State(startState),jsiiDeprecationWarnings().aws_cdk_lib_aws_stepfunctions_IChainable(lastAdded)}catch(error){throw process.env.JSII_DEBUG!=="1"&&error.name==="DeprecationError"&&Error.captureStackTrace(error,this.custom),error}return new Chain(startState,endStates,lastAdded)}constructor(startState,endStates,lastAdded){this.lastAdded=lastAdded,this.id=lastAdded.id,this.startState=startState,this.endStates=endStates}next(next){try{jsiiDeprecationWarnings().aws_cdk_lib_aws_stepfunctions_IChainable(next)}catch(error){throw process.env.JSII_DEBUG!=="1"&&error.name==="DeprecationError"&&Error.captureStackTrace(error,this.next),error}if(this.endStates.length===0)throw new(core_1()).UnscopedValidationError(`Cannot add to chain: last state in chain (${this.lastAdded.id}) does not allow it`);for(const endState of this.endStates)endState.next(next);return new Chain(this.startState,next.endStates,next)}toSingleState(id,props={}){try{jsiiDeprecationWarnings().aws_cdk_lib_aws_stepfunctions_ParallelProps(props)}catch(error){throw process.env.JSII_DEBUG!=="1"&&error.name==="DeprecationError"&&Error.captureStackTrace(error,this.toSingleState),error}return new(parallel_1()).Parallel(this.startState,id,props).branch(this)}}exports.Chain=Chain,_a=JSII_RTTI_SYMBOL_1,Chain[_a]={fqn:"aws-cdk-lib.aws_stepfunctions.Chain",version:"2.201.0"};
