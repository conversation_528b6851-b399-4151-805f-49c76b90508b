"use strict";Object.defineProperty(exports,"__esModule",{value:!0}),exports.StatesMetrics=void 0;class StatesMetrics{static executionTimeAverage(dimensions){return{namespace:"AWS/States",metricName:"ExecutionTime",dimensionsMap:dimensions,statistic:"Average"}}static executionsFailedSum(dimensions){return{namespace:"AWS/States",metricName:"ExecutionsFailed",dimensionsMap:dimensions,statistic:"Sum"}}static executionsSucceededSum(dimensions){return{namespace:"AWS/States",metricName:"ExecutionsSucceeded",dimensionsMap:dimensions,statistic:"Sum"}}static executionsThrottledSum(dimensions){return{namespace:"AWS/States",metricName:"ExecutionsThrottled",dimensionsMap:dimensions,statistic:"Sum"}}static executionsAbortedSum(dimensions){return{namespace:"AWS/States",metricName:"ExecutionsAborted",dimensionsMap:dimensions,statistic:"Sum"}}static executionsTimedOutSum(dimensions){return{namespace:"AWS/States",metricName:"ExecutionsTimedOut",dimensionsMap:dimensions,statistic:"Sum"}}static activityTimeAverage(dimensions){return{namespace:"AWS/States",metricName:"ActivityTime",dimensionsMap:dimensions,statistic:"Average"}}static activitiesSucceededSum(dimensions){return{namespace:"AWS/States",metricName:"ActivitiesSucceeded",dimensionsMap:dimensions,statistic:"Sum"}}static activitiesFailedSum(dimensions){return{namespace:"AWS/States",metricName:"ActivitiesFailed",dimensionsMap:dimensions,statistic:"Sum"}}static activitiesHeartbeatTimedOutSum(dimensions){return{namespace:"AWS/States",metricName:"ActivitiesHeartbeatTimedOut",dimensionsMap:dimensions,statistic:"Sum"}}static activitiesScheduledSum(dimensions){return{namespace:"AWS/States",metricName:"ActivitiesScheduled",dimensionsMap:dimensions,statistic:"Sum"}}static activitiesStartedSum(dimensions){return{namespace:"AWS/States",metricName:"ActivitiesStarted",dimensionsMap:dimensions,statistic:"Sum"}}static activitiesTimedOutSum(dimensions){return{namespace:"AWS/States",metricName:"ActivitiesTimedOut",dimensionsMap:dimensions,statistic:"Sum"}}static activityRunTimeAverage(dimensions){return{namespace:"AWS/States",metricName:"ActivityRunTime",dimensionsMap:dimensions,statistic:"Average"}}static activityScheduleTimeAverage(dimensions){return{namespace:"AWS/States",metricName:"ActivityScheduleTime",dimensionsMap:dimensions,statistic:"Average"}}}exports.StatesMetrics=StatesMetrics;
