import { Construct } from 'constructs';
import { JsonataCommonOptions, JsonPathCommonOptions, State, StateBaseProps } from './state';
import { INextable, QueryLanguage } from '../types';
/**
 * Properties for defining a Succeed state that using JSONPath
 */
export interface SucceedJsonPathProps extends StateBaseProps, JsonPathCommonOptions {
}
/**
 * Properties for defining a Succeed state that using JSONata
 */
export interface SucceedJsonataProps extends StateBaseProps, JsonataCommonOptions {
}
/**
 * Properties for defining a Succeed state
 */
export interface SucceedProps extends StateBaseProps, JsonPathCommonOptions, JsonataCommonOptions {
}
/**
 * Define a Succeed state in the state machine
 *
 * Reaching a Succeed state terminates the state execution in success.
 */
export declare class Succeed extends State {
    /**
     * Define a Succeed state in the state machine
     *
     * Reaching a Succeed state terminates the state execution in success.
     */
    static jsonPath(scope: Construct, id: string, props?: SucceedJsonPathProps): Succeed;
    /**
     * Define a Succeed state in the state machine
     *
     * Reaching a Succeed state terminates the state execution in success.
     */
    static jsonata(scope: Construct, id: string, props?: SucceedJsonataProps): Succeed;
    readonly endStates: INextable[];
    constructor(scope: Construct, id: string, props?: SucceedProps);
    /**
     * Return the Amazon States Language object for this state
     */
    toStateJson(queryLanguage?: QueryLanguage): object;
}
