"use strict";var _a;Object.defineProperty(exports,"__esModule",{value:!0}),exports.EncryptionConfiguration=void 0;const JSII_RTTI_SYMBOL_1=Symbol.for("jsii.rtti");class EncryptionConfiguration{constructor(type){this.type=type}}exports.EncryptionConfiguration=EncryptionConfiguration,_a=JSII_RTTI_SYMBOL_1,EncryptionConfiguration[_a]={fqn:"aws-cdk-lib.aws_stepfunctions.EncryptionConfiguration",version:"2.201.0"};
