"use strict";var _a;Object.defineProperty(exports,"__esModule",{value:!0}),exports.Task=void 0;const JSII_RTTI_SYMBOL_1=Symbol.for("jsii.rtti");var state_type_1=()=>{var tmp=require("./private/state-type");return state_type_1=()=>tmp,tmp},state_1=()=>{var tmp=require("./state");return state_1=()=>tmp,tmp},cloudwatch=()=>{var tmp=require("../../../aws-cloudwatch");return cloudwatch=()=>tmp,tmp},cdk=()=>{var tmp=require("../../../core");return cdk=()=>tmp,tmp},chain_1=()=>{var tmp=require("../chain");return chain_1=()=>tmp,tmp},fields_1=()=>{var tmp=require("../fields");return fields_1=()=>tmp,tmp},util_1=()=>{var tmp=require("../private/util");return util_1=()=>tmp,tmp};class Task extends state_1().State{constructor(scope,id,props){super(scope,id,props),this.timeout=props.timeout;const taskProps=props.task.bind(this);this.taskProps={...taskProps,parameters:(0,util_1().noEmptyObject)({...taskProps.parameters||{},...props.parameters||{}})},this.endStates=[this]}addRetry(props={}){return super._addRetry(props),this}addCatch(handler,props={}){return super._addCatch(handler.startState,props),this}next(next){return super.makeNext(next.startState),chain_1().Chain.sequence(this,next)}toStateJson(){return{...this.renderNextEnd(),...this.renderRetryCatch(),...this.renderInputOutput(),Type:state_type_1().StateType.TASK,Comment:this.comment,Resource:this.taskProps.resourceArn,Parameters:this.taskProps.parameters&&fields_1().FieldUtils.renderObject(this.taskProps.parameters),ResultPath:(0,state_1().renderJsonPath)(this.resultPath),TimeoutSeconds:this.timeout&&this.timeout.toSeconds(),HeartbeatSeconds:this.taskProps.heartbeat&&this.taskProps.heartbeat.toSeconds()}}metric(metricName,props){return new(cloudwatch()).Metric({namespace:"AWS/States",metricName,dimensions:this.taskProps.metricDimensions,statistic:"sum",...props}).attachTo(this)}metricRunTime(props){return this.taskMetric(this.taskProps.metricPrefixSingular,"RunTime",{statistic:"avg",...props})}metricScheduleTime(props){return this.taskMetric(this.taskProps.metricPrefixSingular,"ScheduleTime",{statistic:"avg",...props})}metricTime(props){return this.taskMetric(this.taskProps.metricPrefixSingular,"Time",{statistic:"avg",...props})}metricScheduled(props){return this.taskMetric(this.taskProps.metricPrefixPlural,"Scheduled",props)}metricTimedOut(props){return this.taskMetric(this.taskProps.metricPrefixPlural,"TimedOut",props)}metricStarted(props){return this.taskMetric(this.taskProps.metricPrefixPlural,"Started",props)}metricSucceeded(props){return this.taskMetric(this.taskProps.metricPrefixPlural,"Succeeded",props)}metricFailed(props){return this.taskMetric(this.taskProps.metricPrefixPlural,"Failed",props)}metricHeartbeatTimedOut(props){return this.taskMetric(this.taskProps.metricPrefixPlural,"HeartbeatTimedOut",props)}whenBoundToGraph(graph){super.whenBoundToGraph(graph);for(const policyStatement of this.taskProps.policyStatements||[])graph.registerPolicyStatement(policyStatement)}taskMetric(prefix,suffix,props){if(prefix===void 0)throw new(cdk()).UnscopedValidationError("This Task Resource does not expose metrics");return this.metric(prefix+suffix,props)}}exports.Task=Task,_a=JSII_RTTI_SYMBOL_1,Task[_a]={fqn:"aws-cdk-lib.aws_stepfunctions.Task",version:"2.201.0"};
