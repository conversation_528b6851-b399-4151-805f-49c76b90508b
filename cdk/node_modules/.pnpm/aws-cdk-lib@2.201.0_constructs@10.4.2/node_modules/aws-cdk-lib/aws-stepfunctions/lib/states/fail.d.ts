import { Construct } from 'constructs';
import { State, StateBaseProps } from './state';
import { INextable, QueryLanguage } from '../types';
interface FailBaseOptions {
    /**
     * Error code used to represent this failure
     *
     * @default - No error code
     */
    readonly error?: string;
    /**
     * A description for the cause of the failure
     *
     * @default - No description
     */
    readonly cause?: string;
}
interface FailJsonPathOptions {
    /**
     * JsonPath expression to select part of the state to be the error to this state.
     *
     * You can also use an intrinsic function that returns a string to specify this property.
     * The allowed functions include States.Format, States.JsonToString, States.ArrayGetItem, States.Base64Encode, States.Base64Decode, States.Hash, and States.UUID.
     *
     * @default - No error path
     */
    readonly errorPath?: string;
    /**
     * JsonPath expression to select part of the state to be the cause to this state.
     *
     * You can also use an intrinsic function that returns a string to specify this property.
     * The allowed functions include States.Format, States.JsonToString, States.ArrayGetItem, States.Base64Encode, States.Base64Decode, States.Hash, and States.UUID.
     *
     * @default - No cause path
     */
    readonly causePath?: string;
}
/**
 * Properties for defining a Fail state that using JSONPath
 */
export interface FailJsonPathProps extends StateBaseProps, FailBaseOptions, FailJsonPathOptions {
}
/**
 * Properties for defining a Fail state that using JSONata
 */
export interface FailJsonataProps extends StateBaseProps, FailBaseOptions {
}
/**
 * Properties for defining a Fail state
 */
export interface FailProps extends StateBaseProps, FailBaseOptions, FailJsonPathOptions {
}
/**
 * Define a Fail state in the state machine
 *
 * Reaching a Fail state terminates the state execution in failure.
 */
export declare class Fail extends State {
    /**
     * Define a Fail state using JSONPath in the state machine
     *
     * Reaching a Fail state terminates the state execution in failure.
     */
    static jsonPath(scope: Construct, id: string, props?: FailJsonPathProps): Fail;
    /**
     * Define a Fail state using JSONata in the state machine
     *
     * Reaching a Fail state terminates the state execution in failure.
     */
    static jsonata(scope: Construct, id: string, props?: FailJsonataProps): Fail;
    private static allowedIntrinsics;
    readonly endStates: INextable[];
    private readonly error?;
    private readonly errorPath?;
    private readonly cause?;
    private readonly causePath?;
    constructor(scope: Construct, id: string, props?: FailProps);
    /**
     * Return the Amazon States Language object for this state
     */
    toStateJson(queryLanguage?: QueryLanguage): object;
    /**
     * Validate this state
     */
    protected validateState(): string[];
    private isIntrinsicString;
    private isAllowedIntrinsic;
}
export {};
