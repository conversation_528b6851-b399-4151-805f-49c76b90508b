"use strict";var _a,_b;Object.defineProperty(exports,"__esModule",{value:!0}),exports.MapBase=exports.isPositiveInteger=exports.ProvideItems=void 0;var jsiiDeprecationWarnings=()=>{var tmp=require("../../../.warnings.jsii.js");return jsiiDeprecationWarnings=()=>tmp,tmp};const JSII_RTTI_SYMBOL_1=Symbol.for("jsii.rtti");var state_type_1=()=>{var tmp=require("./private/state-type");return state_type_1=()=>tmp,tmp},state_1=()=>{var tmp=require("./state");return state_1=()=>tmp,tmp},core_1=()=>{var tmp=require("../../../core");return core_1=()=>tmp,tmp},chain_1=()=>{var tmp=require("../chain");return chain_1=()=>tmp,tmp},fields_1=()=>{var tmp=require("../fields");return fields_1=()=>tmp,tmp},jsonata_1=()=>{var tmp=require("../private/jsonata");return jsonata_1=()=>tmp,tmp},types_1=()=>{var tmp=require("../types");return types_1=()=>tmp,tmp};class ProvideItems{static jsonArray(array){return{items:array}}static jsonata(jsonataExpression){return{items:jsonataExpression}}}exports.ProvideItems=ProvideItems,_a=JSII_RTTI_SYMBOL_1,ProvideItems[_a]={fqn:"aws-cdk-lib.aws_stepfunctions.ProvideItems",version:"2.201.0"};const isPositiveInteger=value=>{const isFloat=Math.floor(value)!==value,isNotPositiveInteger=value<0||value>Number.MAX_SAFE_INTEGER;return!isFloat&&!isNotPositiveInteger};exports.isPositiveInteger=isPositiveInteger;class MapBase extends state_1().State{constructor(scope,id,props={}){super(scope,id,props);try{jsiiDeprecationWarnings().aws_cdk_lib_aws_stepfunctions_MapBaseProps(props)}catch(error){throw process.env.JSII_DEBUG!=="1"&&error.name==="DeprecationError"&&Error.captureStackTrace(error,MapBase),error}this.endStates=[this],this.maxConcurrency=props.maxConcurrency,this.maxConcurrencyPath=props.maxConcurrencyPath,this.items=props.items,this.itemsPath=props.itemsPath,this.itemSelector=props.itemSelector,this.jsonataItemSelector=props.jsonataItemSelector}next(next){try{jsiiDeprecationWarnings().aws_cdk_lib_aws_stepfunctions_IChainable(next)}catch(error){throw process.env.JSII_DEBUG!=="1"&&error.name==="DeprecationError"&&Error.captureStackTrace(error,this.next),error}return super.makeNext(next.startState),chain_1().Chain.sequence(this,next)}toStateJson(topLevelQueryLanguage){try{jsiiDeprecationWarnings().aws_cdk_lib_aws_stepfunctions_QueryLanguage(topLevelQueryLanguage)}catch(error){throw process.env.JSII_DEBUG!=="1"&&error.name==="DeprecationError"&&Error.captureStackTrace(error,this.toStateJson),error}return{Type:state_type_1().StateType.MAP,...this.renderQueryLanguage(topLevelQueryLanguage),Comment:this.comment,ResultPath:(0,state_1().renderJsonPath)(this.resultPath),...this.renderNextEnd(),...this.renderInputOutput(),...this.renderResultSelector(),...this.renderRetryCatch(),Items:this.items?.items,...this.renderItemsPath(),...this.renderItemSelector(),...this.renderItemProcessor(),...this.maxConcurrency&&{MaxConcurrency:this.maxConcurrency},...this.maxConcurrencyPath&&{MaxConcurrencyPath:(0,state_1().renderJsonPath)(this.maxConcurrencyPath)},...this.renderAssign(topLevelQueryLanguage)}}validateState(){const errors=[];return this.processorConfig?.mode===types_1().ProcessorMode.DISTRIBUTED&&!this.processorConfig?.executionType&&errors.push("You must specify an execution type for the distributed Map workflow"),this.maxConcurrency&&!core_1().Token.isUnresolved(this.maxConcurrency)&&!(0,exports.isPositiveInteger)(this.maxConcurrency)&&errors.push("maxConcurrency has to be a positive integer"),this.maxConcurrency&&this.maxConcurrencyPath&&errors.push("Provide either `maxConcurrency` or `maxConcurrencyPath`, but not both"),this.itemSelector&&this.jsonataItemSelector&&errors.push("Provide either `itemSelector` or `jsonataItemSelector`, but not both"),this.jsonataItemSelector&&!(0,jsonata_1().isValidJsonataExpression)(this.jsonataItemSelector)&&errors.push("The `jsonataItemSelector` property must be a valid JSONata expression"),errors}renderItemsPath(){return{ItemsPath:(0,state_1().renderJsonPath)(this.itemsPath)}}renderItemSelector(){if(!(!this.itemSelector&&!this.jsonataItemSelector))return fields_1().FieldUtils.renderObject({ItemSelector:this.itemSelector??this.jsonataItemSelector})}}exports.MapBase=MapBase,_b=JSII_RTTI_SYMBOL_1,MapBase[_b]={fqn:"aws-cdk-lib.aws_stepfunctions.MapBase",version:"2.201.0"};
