"use strict";var _a;Object.defineProperty(exports,"__esModule",{value:!0}),exports.Succeed=void 0;var jsiiDeprecationWarnings=()=>{var tmp=require("../../../.warnings.jsii.js");return jsiiDeprecationWarnings=()=>tmp,tmp};const JSII_RTTI_SYMBOL_1=Symbol.for("jsii.rtti");var state_type_1=()=>{var tmp=require("./private/state-type");return state_type_1=()=>tmp,tmp},state_1=()=>{var tmp=require("./state");return state_1=()=>tmp,tmp},types_1=()=>{var tmp=require("../types");return types_1=()=>tmp,tmp};class Succeed extends state_1().State{static jsonPath(scope,id,props={}){try{jsiiDeprecationWarnings().aws_cdk_lib_aws_stepfunctions_SucceedJsonPathProps(props)}catch(error){throw process.env.JSII_DEBUG!=="1"&&error.name==="DeprecationError"&&Error.captureStackTrace(error,this.jsonPath),error}return new Succeed(scope,id,props)}static jsonata(scope,id,props={}){try{jsiiDeprecationWarnings().aws_cdk_lib_aws_stepfunctions_SucceedJsonataProps(props)}catch(error){throw process.env.JSII_DEBUG!=="1"&&error.name==="DeprecationError"&&Error.captureStackTrace(error,this.jsonata),error}return new Succeed(scope,id,{...props,queryLanguage:types_1().QueryLanguage.JSONATA})}constructor(scope,id,props={}){super(scope,id,props),this.endStates=[];try{jsiiDeprecationWarnings().aws_cdk_lib_aws_stepfunctions_SucceedProps(props)}catch(error){throw process.env.JSII_DEBUG!=="1"&&error.name==="DeprecationError"&&Error.captureStackTrace(error,Succeed),error}}toStateJson(queryLanguage){try{jsiiDeprecationWarnings().aws_cdk_lib_aws_stepfunctions_QueryLanguage(queryLanguage)}catch(error){throw process.env.JSII_DEBUG!=="1"&&error.name==="DeprecationError"&&Error.captureStackTrace(error,this.toStateJson),error}return{Type:state_type_1().StateType.SUCCEED,...this.renderQueryLanguage(queryLanguage),Comment:this.comment,...this.renderInputOutput()}}}exports.Succeed=Succeed,_a=JSII_RTTI_SYMBOL_1,Succeed[_a]={fqn:"aws-cdk-lib.aws_stepfunctions.Succeed",version:"2.201.0"};
