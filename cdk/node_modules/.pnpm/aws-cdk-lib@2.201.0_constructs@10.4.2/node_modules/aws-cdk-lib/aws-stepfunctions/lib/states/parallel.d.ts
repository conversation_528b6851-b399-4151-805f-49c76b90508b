import { Construct } from 'constructs';
import { AssignableStateOptions, JsonataCommonOptions, JsonPathCommonOptions, State, StateBaseProps } from './state';
import { Chain } from '../chain';
import { StateGraph } from '../state-graph';
import { CatchProps, IChainable, INextable, QueryLanguage, RetryProps } from '../types';
interface ParallelJsonPathOptions extends JsonPathCommonOptions {
    /**
     * JSONPath expression to indicate where to inject the state's output
     *
     * May also be the special value JsonPath.DISCARD, which will cause the state's
     * input to become its output.
     *
     * @default $
     */
    readonly resultPath?: string;
    /**
     * The JSON that will replace the state's raw result and become the effective
     * result before ResultPath is applied.
     *
     * You can use ResultSelector to create a payload with values that are static
     * or selected from the state's raw result.
     *
     * @see
     * https://docs.aws.amazon.com/step-functions/latest/dg/input-output-inputpath-params.html#input-output-resultselector
     *
     * @default - None
     */
    readonly resultSelector?: {
        [key: string]: any;
    };
    /**
     * Parameters pass a collection of key-value pairs, either static values or JSONPath expressions that select from the input.
     *
     * @see
     * https://docs.aws.amazon.com/step-functions/latest/dg/input-output-inputpath-params.html#input-output-parameters
     *
     * @default No parameters
     */
    readonly parameters?: {
        [name: string]: any;
    };
}
interface ParallelJsonataOptions extends JsonataCommonOptions {
    /**
     * Parameters pass a collection of key-value pairs, either static values or JSONata expressions that select from the input.
     *
     * @see
     * https://docs.aws.amazon.com/step-functions/latest/dg/transforming-data.html
     *
     * @default No arguments
     */
    readonly arguments?: {
        [name: string]: any;
    };
}
/**
 * Properties for defining a Parallel state that using JSONPath
 */
export interface ParallelJsonPathProps extends StateBaseProps, AssignableStateOptions, ParallelJsonPathOptions {
}
/**
 * Properties for defining a Parallel state that using JSONata
 */
export interface ParallelJsonataProps extends StateBaseProps, AssignableStateOptions, ParallelJsonataOptions {
}
/**
 * Properties for defining a Parallel state
 */
export interface ParallelProps extends StateBaseProps, AssignableStateOptions, ParallelJsonPathOptions, ParallelJsonataOptions {
}
/**
 * Define a Parallel state in the state machine
 *
 * A Parallel state can be used to run one or more state machines at the same
 * time.
 *
 * The Result of a Parallel state is an array of the results of its substatemachines.
 */
export declare class Parallel extends State implements INextable {
    /**
     * Define a Parallel state using JSONPath in the state machine
     *
     * A Parallel state can be used to run one or more state machines at the same
     * time.
     *
     * The Result of a Parallel state is an array of the results of its substatemachines.
     */
    static jsonPath(scope: Construct, id: string, props?: ParallelJsonPathProps): Parallel;
    /**
     * Define a Parallel state using JSONata in the state machine
     *
     * A Parallel state can be used to run one or more state machines at the same
     * time.
     *
     * The Result of a Parallel state is an array of the results of its substatemachines.
     */
    static jsonata(scope: Construct, id: string, props?: ParallelJsonataProps): Parallel;
    readonly endStates: INextable[];
    private readonly _branches;
    constructor(scope: Construct, id: string, props?: ParallelProps);
    /**
     * Add retry configuration for this state
     *
     * This controls if and how the execution will be retried if a particular
     * error occurs.
     */
    addRetry(props?: RetryProps): Parallel;
    /**
     * Add a recovery handler for this state
     *
     * When a particular error occurs, execution will continue at the error
     * handler instead of failing the state machine execution.
     */
    addCatch(handler: IChainable, props?: CatchProps): Parallel;
    /**
     * Continue normal execution with the given state
     */
    next(next: IChainable): Chain;
    /**
     * Define one or more branches to run in parallel
     */
    branch(...branches: IChainable[]): Parallel;
    /**
     * Overwrites State.bindToGraph. Adds branches to
     * the Parallel state here so that any necessary
     * prefixes are appended first.
     */
    bindToGraph(graph: StateGraph): void;
    /**
     * Return the Amazon States Language object for this state
     */
    toStateJson(topLevelQueryLanguage?: QueryLanguage): object;
    /**
     * Render Parameters in ASL JSON format
     */
    private renderParameters;
    /**
     * Validate this state
     */
    protected validateState(): string[];
}
export {};
