"use strict";Object.defineProperty(exports,"__esModule",{value:!0}),exports.findJsonataExpressions=exports.isValidJsonataExpression=void 0;const isValidJsonataExpression=expression=>/^{%(.*)%}$/.test(expression);exports.isValidJsonataExpression=isValidJsonataExpression;const findJsonataExpressions=value=>{const recursive=v=>typeof v=="string"&&(0,exports.isValidJsonataExpression)(v)?[v]:v===null?[]:Array.isArray(v)?v.flatMap(recursive):typeof v=="object"?Object.values(v).flatMap(recursive):[];return new Set(recursive(value))};exports.findJsonataExpressions=findJsonataExpressions;
