"use strict";var _a;Object.defineProperty(exports,"__esModule",{value:!0}),exports.StateMachineFragment=void 0;var jsiiDeprecationWarnings=()=>{var tmp=require("../../.warnings.jsii.js");return jsiiDeprecationWarnings=()=>tmp,tmp};const JSII_RTTI_SYMBOL_1=Symbol.for("jsii.rtti");var constructs_1=()=>{var tmp=require("constructs");return constructs_1=()=>tmp,tmp},chain_1=()=>{var tmp=require("./chain");return chain_1=()=>tmp,tmp},parallel_1=()=>{var tmp=require("./states/parallel");return parallel_1=()=>tmp,tmp},state_1=()=>{var tmp=require("./states/state");return state_1=()=>tmp,tmp};class StateMachineFragment extends constructs_1().Construct{get id(){return this.node.id}prefixStates(prefix){return state_1().State.prefixStates(this,prefix||`${this.id}: `),this}toSingleState(options={}){try{jsiiDeprecationWarnings().aws_cdk_lib_aws_stepfunctions_SingleStateOptions(options)}catch(error){throw process.env.JSII_DEBUG!=="1"&&error.name==="DeprecationError"&&Error.captureStackTrace(error,this.toSingleState),error}const stateId=options.stateId||this.id;return this.prefixStates(options.prefixStates||`${stateId}: `),new(parallel_1()).Parallel(this,stateId,options).branch(this)}next(next){try{jsiiDeprecationWarnings().aws_cdk_lib_aws_stepfunctions_IChainable(next)}catch(error){throw process.env.JSII_DEBUG!=="1"&&error.name==="DeprecationError"&&Error.captureStackTrace(error,this.next),error}return chain_1().Chain.start(this).next(next)}}exports.StateMachineFragment=StateMachineFragment,_a=JSII_RTTI_SYMBOL_1,StateMachineFragment[_a]={fqn:"aws-cdk-lib.aws_stepfunctions.StateMachineFragment",version:"2.201.0"};
