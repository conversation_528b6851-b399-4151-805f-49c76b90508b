"use strict";var _a;Object.defineProperty(exports,"__esModule",{value:!0}),exports.Choice=void 0;var jsiiDeprecationWarnings=()=>{var tmp=require("../../../.warnings.jsii.js");return jsiiDeprecationWarnings=()=>tmp,tmp};const JSII_RTTI_SYMBOL_1=Symbol.for("jsii.rtti");var state_type_1=()=>{var tmp=require("./private/state-type");return state_type_1=()=>tmp,tmp},state_1=()=>{var tmp=require("./state");return state_1=()=>tmp,tmp},core_1=()=>{var tmp=require("../../../core");return core_1=()=>tmp,tmp},chain_1=()=>{var tmp=require("../chain");return chain_1=()=>tmp,tmp},types_1=()=>{var tmp=require("../types");return types_1=()=>tmp,tmp};class Choice extends state_1().State{static jsonPath(scope,id,props={}){try{jsiiDeprecationWarnings().aws_cdk_lib_aws_stepfunctions_ChoiceJsonPathProps(props)}catch(error){throw process.env.JSII_DEBUG!=="1"&&error.name==="DeprecationError"&&Error.captureStackTrace(error,this.jsonPath),error}return new Choice(scope,id,props)}static jsonata(scope,id,props={}){try{jsiiDeprecationWarnings().aws_cdk_lib_aws_stepfunctions_ChoiceJsonataProps(props)}catch(error){throw process.env.JSII_DEBUG!=="1"&&error.name==="DeprecationError"&&Error.captureStackTrace(error,this.jsonata),error}return new Choice(scope,id,{...props,queryLanguage:types_1().QueryLanguage.JSONATA})}constructor(scope,id,props={}){super(scope,id,props),this.endStates=[];try{jsiiDeprecationWarnings().aws_cdk_lib_aws_stepfunctions_ChoiceProps(props)}catch(error){throw process.env.JSII_DEBUG!=="1"&&error.name==="DeprecationError"&&Error.captureStackTrace(error,Choice),error}}when(condition,next,options){try{jsiiDeprecationWarnings().aws_cdk_lib_aws_stepfunctions_Condition(condition),jsiiDeprecationWarnings().aws_cdk_lib_aws_stepfunctions_IChainable(next),jsiiDeprecationWarnings().aws_cdk_lib_aws_stepfunctions_ChoiceTransitionOptions(options)}catch(error){throw process.env.JSII_DEBUG!=="1"&&error.name==="DeprecationError"&&Error.captureStackTrace(error,this.when),error}return super.addChoice(condition,next.startState,options),this}otherwise(def){try{jsiiDeprecationWarnings().aws_cdk_lib_aws_stepfunctions_IChainable(def)}catch(error){throw process.env.JSII_DEBUG!=="1"&&error.name==="DeprecationError"&&Error.captureStackTrace(error,this.otherwise),error}return super.makeDefault(def.startState),this}afterwards(options={}){try{jsiiDeprecationWarnings().aws_cdk_lib_aws_stepfunctions_AfterwardsOptions(options)}catch(error){throw process.env.JSII_DEBUG!=="1"&&error.name==="DeprecationError"&&Error.captureStackTrace(error,this.afterwards),error}const endStates=state_1().State.filterNextables(state_1().State.findReachableEndStates(this,{includeErrorHandlers:options.includeErrorHandlers}));if(options.includeOtherwise&&this.defaultChoice)throw new(core_1()).UnscopedValidationError(`'includeOtherwise' set but Choice state ${this.stateId} already has an 'otherwise' transition`);return options.includeOtherwise&&endStates.push(new DefaultAsNext(this)),chain_1().Chain.custom(this,endStates,this)}toStateJson(topLevelQueryLanguage){try{jsiiDeprecationWarnings().aws_cdk_lib_aws_stepfunctions_QueryLanguage(topLevelQueryLanguage)}catch(error){throw process.env.JSII_DEBUG!=="1"&&error.name==="DeprecationError"&&Error.captureStackTrace(error,this.toStateJson),error}return{Type:state_type_1().StateType.CHOICE,...this.renderQueryLanguage(topLevelQueryLanguage),Comment:this.comment,...this.renderInputOutput(),...this.renderChoices(),...this.renderAssign(topLevelQueryLanguage)}}}exports.Choice=Choice,_a=JSII_RTTI_SYMBOL_1,Choice[_a]={fqn:"aws-cdk-lib.aws_stepfunctions.Choice",version:"2.201.0"};class DefaultAsNext{constructor(choice){this.choice=choice}next(state){return this.choice.otherwise(state),chain_1().Chain.sequence(this.choice,state)}}
