import { Construct } from 'constructs';
import { AssignableStateOptions, JsonataCommonOptions, JsonPathCommonOptions, State, StateBaseProps } from './state';
import * as cdk from '../../../core';
import { Chain } from '../chain';
import { IChainable, INextable, QueryLanguage } from '../types';
/**
 * Represents the Wait state which delays a state machine from continuing for a specified time
 * @see https://docs.aws.amazon.com/step-functions/latest/dg/amazon-states-language-wait-state.html
 */
export declare class WaitTime {
    private readonly json;
    /**
     * Wait a fixed amount of time.
     */
    static duration(duration: cdk.Duration): WaitTime;
    /**
     * Wait for a number of seconds stored in the state object from string.
     * This method can use JSONata expression.
     *
     * If you want to use fixed value, we recommend using `WaitTime.duration()`
     *
     * Example value: `{% $waitSeconds %}`
     */
    static seconds(seconds: string): WaitTime;
    /**
     * Wait until the given ISO8601 timestamp.
     * This method can use JSONata expression.
     *
     * Example value: `2016-03-14T01:59:00Z`
     */
    static timestamp(timestamp: string): WaitTime;
    /**
     * Wait for a number of seconds stored in the state object.
     *
     * Example value: `$.waitSeconds`
     */
    static secondsPath(path: string): WaitTime;
    /**
     * Wait until a timestamp found in the state object.
     *
     * Example value: `$.waitTimestamp`
     */
    static timestampPath(path: string): WaitTime;
    private constructor();
    /**
     * @internal
     */
    get _json(): any;
}
interface WaitOptions {
    /**
     * Wait duration.
     */
    readonly time: WaitTime;
}
/**
 * Properties for defining a Wait state that using JSONPath
 */
export interface WaitJsonPathProps extends StateBaseProps, AssignableStateOptions, WaitOptions, JsonPathCommonOptions {
}
/**
 * Properties for defining a Wait state that using JSONata
 */
export interface WaitJsonataProps extends StateBaseProps, AssignableStateOptions, WaitOptions, JsonataCommonOptions {
}
/**
 * Properties for defining a Wait state
 */
export interface WaitProps extends StateBaseProps, AssignableStateOptions, WaitOptions {
}
/**
 * Define a Wait state in the state machine
 *
 * A Wait state can be used to delay execution of the state machine for a while.
 */
export declare class Wait extends State implements INextable {
    /**
     * Define a Wait state using JSONPath in the state machine
     *
     * A Wait state can be used to delay execution of the state machine for a while.
     */
    static jsonPath(scope: Construct, id: string, props: WaitJsonPathProps): Wait;
    /**
     * Define a Wait state using JSONata in the state machine
     *
     * A Wait state can be used to delay execution of the state machine for a while.
     */
    static jsonata(scope: Construct, id: string, props: WaitJsonataProps): Wait;
    readonly endStates: INextable[];
    private readonly time;
    constructor(scope: Construct, id: string, props: WaitProps);
    /**
     * Continue normal execution with the given state
     */
    next(next: IChainable): Chain;
    /**
     * Return the Amazon States Language object for this state
     */
    toStateJson(topLevelQueryLanguage?: QueryLanguage): object;
}
export {};
