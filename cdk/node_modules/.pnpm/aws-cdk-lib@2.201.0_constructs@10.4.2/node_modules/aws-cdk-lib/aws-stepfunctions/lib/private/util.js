"use strict";Object.defineProperty(exports,"__esModule",{value:!0}),exports.noEmptyObject=noEmptyObject,exports.buildEncryptionConfiguration=buildEncryptionConfiguration;var aws_owned_key_encryption_configuration_1=()=>{var tmp=require("../aws-owned-key-encryption-configuration");return aws_owned_key_encryption_configuration_1=()=>tmp,tmp},customer_managed_key_encryption_configuration_1=()=>{var tmp=require("../customer-managed-key-encryption-configuration");return customer_managed_key_encryption_configuration_1=()=>tmp,tmp};function noEmptyObject(o){if(Object.keys(o).length!==0)return o}function buildEncryptionConfiguration(encryptionConfiguration){if(encryptionConfiguration instanceof aws_owned_key_encryption_configuration_1().AwsOwnedEncryptionConfiguration)return{type:encryptionConfiguration.type};if(encryptionConfiguration instanceof customer_managed_key_encryption_configuration_1().CustomerManagedEncryptionConfiguration)return{kmsKeyId:encryptionConfiguration.kmsKey.keyArn,kmsDataKeyReusePeriodSeconds:encryptionConfiguration.kmsDataKeyReusePeriodSeconds?encryptionConfiguration.kmsDataKeyReusePeriodSeconds.toSeconds():300,type:encryptionConfiguration.type}}
