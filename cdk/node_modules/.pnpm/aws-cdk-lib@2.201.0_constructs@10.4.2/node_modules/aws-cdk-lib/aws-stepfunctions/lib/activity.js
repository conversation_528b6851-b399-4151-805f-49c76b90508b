"use strict";var __decorate=exports&&exports.__decorate||function(decorators,target,key,desc){var c=arguments.length,r=c<3?target:desc===null?desc=Object.getOwnPropertyDescriptor(target,key):desc,d;if(typeof Reflect=="object"&&typeof Reflect.decorate=="function")r=Reflect.decorate(decorators,target,key,desc);else for(var i=decorators.length-1;i>=0;i--)(d=decorators[i])&&(r=(c<3?d(r):c>3?d(target,key,r):d(target,key))||r);return c>3&&r&&Object.defineProperty(target,key,r),r},_a,Activity_1;Object.defineProperty(exports,"__esModule",{value:!0}),exports.Activity=void 0;var jsiiDeprecationWarnings=()=>{var tmp=require("../../.warnings.jsii.js");return jsiiDeprecationWarnings=()=>tmp,tmp};const JSII_RTTI_SYMBOL_1=Symbol.for("jsii.rtti");var customer_managed_key_encryption_configuration_1=()=>{var tmp=require("./customer-managed-key-encryption-configuration");return customer_managed_key_encryption_configuration_1=()=>tmp,tmp},util_1=()=>{var tmp=require("./private/util");return util_1=()=>tmp,tmp},stepfunctions_canned_metrics_generated_1=()=>{var tmp=require("./stepfunctions-canned-metrics.generated");return stepfunctions_canned_metrics_generated_1=()=>tmp,tmp},stepfunctions_generated_1=()=>{var tmp=require("./stepfunctions.generated");return stepfunctions_generated_1=()=>tmp,tmp},cloudwatch=()=>{var tmp=require("../../aws-cloudwatch");return cloudwatch=()=>tmp,tmp},iam=()=>{var tmp=require("../../aws-iam");return iam=()=>tmp,tmp},core_1=()=>{var tmp=require("../../core");return core_1=()=>tmp,tmp},metadata_resource_1=()=>{var tmp=require("../../core/lib/metadata-resource");return metadata_resource_1=()=>tmp,tmp},prop_injectable_1=()=>{var tmp=require("../../core/lib/prop-injectable");return prop_injectable_1=()=>tmp,tmp};let Activity=Activity_1=class Activity2 extends core_1().Resource{static fromActivityArn(scope,id,activityArn){class Imported extends core_1().Resource{get activityArn(){return activityArn}get activityName(){return core_1().Stack.of(this).splitArn(activityArn,core_1().ArnFormat.COLON_RESOURCE_NAME).resourceName||""}}return new Imported(scope,id)}static fromActivityName(scope,id,activityName){return Activity_1.fromActivityArn(scope,id,core_1().Stack.of(scope).formatArn({service:"states",resource:"activity",resourceName:activityName,arnFormat:core_1().ArnFormat.COLON_RESOURCE_NAME}))}constructor(scope,id,props={}){super(scope,id,{physicalName:props.activityName||core_1().Lazy.string({produce:()=>this.generateName()})});try{jsiiDeprecationWarnings().aws_cdk_lib_aws_stepfunctions_ActivityProps(props)}catch(error){throw process.env.JSII_DEBUG!=="1"&&error.name==="DeprecationError"&&Error.captureStackTrace(error,Activity2),error}(0,metadata_resource_1().addConstructMetadata)(this,props),this.encryptionConfiguration=props.encryptionConfiguration,props.encryptionConfiguration instanceof customer_managed_key_encryption_configuration_1().CustomerManagedEncryptionConfiguration&&props.encryptionConfiguration.kmsKey.addToResourcePolicy(new(iam()).PolicyStatement({resources:["*"],actions:["kms:Decrypt","kms:GenerateDataKey"],principals:[new(iam()).ServicePrincipal("states.amazonaws.com")],conditions:{StringEquals:{"kms:EncryptionContext:aws:states:activityArn":core_1().Stack.of(this).formatArn({service:"states",resource:"activity",sep:":",resourceName:this.physicalName})}}}));const resource=new(stepfunctions_generated_1()).CfnActivity(this,"Resource",{name:this.physicalName,encryptionConfiguration:(0,util_1().buildEncryptionConfiguration)(props.encryptionConfiguration)});this.activityArn=this.getResourceArnAttribute(resource.ref,{service:"states",resource:"activity",resourceName:this.physicalName,arnFormat:core_1().ArnFormat.COLON_RESOURCE_NAME}),this.activityName=this.getResourceNameAttribute(resource.attrName)}grant(identity,...actions){try{jsiiDeprecationWarnings().aws_cdk_lib_aws_iam_IGrantable(identity)}catch(error){throw process.env.JSII_DEBUG!=="1"&&error.name==="DeprecationError"&&Error.captureStackTrace(error,this.grant),error}return iam().Grant.addToPrincipal({grantee:identity,actions,resourceArns:[this.activityArn]})}metric(metricName,props){try{jsiiDeprecationWarnings().aws_cdk_lib_aws_cloudwatch_MetricOptions(props)}catch(error){throw process.env.JSII_DEBUG!=="1"&&error.name==="DeprecationError"&&Error.captureStackTrace(error,this.metric),error}return new(cloudwatch()).Metric({namespace:"AWS/States",metricName,dimensions:{ActivityArn:this.activityArn},statistic:"sum",...props}).attachTo(this)}metricRunTime(props){try{jsiiDeprecationWarnings().aws_cdk_lib_aws_cloudwatch_MetricOptions(props)}catch(error){throw process.env.JSII_DEBUG!=="1"&&error.name==="DeprecationError"&&Error.captureStackTrace(error,this.metricRunTime),error}return this.cannedMetric(stepfunctions_canned_metrics_generated_1().StatesMetrics.activityRunTimeAverage,props)}metricScheduleTime(props){try{jsiiDeprecationWarnings().aws_cdk_lib_aws_cloudwatch_MetricOptions(props)}catch(error){throw process.env.JSII_DEBUG!=="1"&&error.name==="DeprecationError"&&Error.captureStackTrace(error,this.metricScheduleTime),error}return this.cannedMetric(stepfunctions_canned_metrics_generated_1().StatesMetrics.activityScheduleTimeAverage,props)}metricTime(props){try{jsiiDeprecationWarnings().aws_cdk_lib_aws_cloudwatch_MetricOptions(props)}catch(error){throw process.env.JSII_DEBUG!=="1"&&error.name==="DeprecationError"&&Error.captureStackTrace(error,this.metricTime),error}return this.cannedMetric(stepfunctions_canned_metrics_generated_1().StatesMetrics.activityTimeAverage,props)}metricScheduled(props){try{jsiiDeprecationWarnings().aws_cdk_lib_aws_cloudwatch_MetricOptions(props)}catch(error){throw process.env.JSII_DEBUG!=="1"&&error.name==="DeprecationError"&&Error.captureStackTrace(error,this.metricScheduled),error}return this.cannedMetric(stepfunctions_canned_metrics_generated_1().StatesMetrics.activitiesScheduledSum,props)}metricTimedOut(props){try{jsiiDeprecationWarnings().aws_cdk_lib_aws_cloudwatch_MetricOptions(props)}catch(error){throw process.env.JSII_DEBUG!=="1"&&error.name==="DeprecationError"&&Error.captureStackTrace(error,this.metricTimedOut),error}return this.cannedMetric(stepfunctions_canned_metrics_generated_1().StatesMetrics.activitiesTimedOutSum,props)}metricStarted(props){try{jsiiDeprecationWarnings().aws_cdk_lib_aws_cloudwatch_MetricOptions(props)}catch(error){throw process.env.JSII_DEBUG!=="1"&&error.name==="DeprecationError"&&Error.captureStackTrace(error,this.metricStarted),error}return this.cannedMetric(stepfunctions_canned_metrics_generated_1().StatesMetrics.activitiesStartedSum,props)}metricSucceeded(props){try{jsiiDeprecationWarnings().aws_cdk_lib_aws_cloudwatch_MetricOptions(props)}catch(error){throw process.env.JSII_DEBUG!=="1"&&error.name==="DeprecationError"&&Error.captureStackTrace(error,this.metricSucceeded),error}return this.cannedMetric(stepfunctions_canned_metrics_generated_1().StatesMetrics.activitiesSucceededSum,props)}metricFailed(props){try{jsiiDeprecationWarnings().aws_cdk_lib_aws_cloudwatch_MetricOptions(props)}catch(error){throw process.env.JSII_DEBUG!=="1"&&error.name==="DeprecationError"&&Error.captureStackTrace(error,this.metricFailed),error}return this.cannedMetric(stepfunctions_canned_metrics_generated_1().StatesMetrics.activitiesFailedSum,props)}metricHeartbeatTimedOut(props){try{jsiiDeprecationWarnings().aws_cdk_lib_aws_cloudwatch_MetricOptions(props)}catch(error){throw process.env.JSII_DEBUG!=="1"&&error.name==="DeprecationError"&&Error.captureStackTrace(error,this.metricHeartbeatTimedOut),error}return this.cannedMetric(stepfunctions_canned_metrics_generated_1().StatesMetrics.activitiesHeartbeatTimedOutSum,props)}generateName(){const name=core_1().Names.uniqueId(this);return name.length>80?name.substring(0,40)+name.substring(name.length-40):name}cannedMetric(fn,props){return new(cloudwatch()).Metric({...fn({ActivityArn:this.activityArn}),...props}).attachTo(this)}};exports.Activity=Activity,_a=JSII_RTTI_SYMBOL_1,Activity[_a]={fqn:"aws-cdk-lib.aws_stepfunctions.Activity",version:"2.201.0"},Activity.PROPERTY_INJECTION_ID="aws-cdk-lib.aws-stepfunctions.Activity",__decorate([(0,metadata_resource_1().MethodMetadata)()],Activity.prototype,"grant",null),__decorate([(0,metadata_resource_1().MethodMetadata)()],Activity.prototype,"metric",null),__decorate([(0,metadata_resource_1().MethodMetadata)()],Activity.prototype,"metricRunTime",null),__decorate([(0,metadata_resource_1().MethodMetadata)()],Activity.prototype,"metricScheduleTime",null),__decorate([(0,metadata_resource_1().MethodMetadata)()],Activity.prototype,"metricTime",null),__decorate([(0,metadata_resource_1().MethodMetadata)()],Activity.prototype,"metricScheduled",null),__decorate([(0,metadata_resource_1().MethodMetadata)()],Activity.prototype,"metricTimedOut",null),__decorate([(0,metadata_resource_1().MethodMetadata)()],Activity.prototype,"metricStarted",null),__decorate([(0,metadata_resource_1().MethodMetadata)()],Activity.prototype,"metricSucceeded",null),__decorate([(0,metadata_resource_1().MethodMetadata)()],Activity.prototype,"metricFailed",null),__decorate([(0,metadata_resource_1().MethodMetadata)()],Activity.prototype,"metricHeartbeatTimedOut",null),exports.Activity=Activity=Activity_1=__decorate([prop_injectable_1().propertyInjectable],Activity);
