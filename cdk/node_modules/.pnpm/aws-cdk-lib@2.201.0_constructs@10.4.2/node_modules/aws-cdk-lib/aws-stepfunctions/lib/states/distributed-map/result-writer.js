"use strict";var _a,_b,_c;Object.defineProperty(exports,"__esModule",{value:!0}),exports.ResultWriterV2=exports.ResultWriter=exports.WriterConfig=exports.OutputType=exports.Transformation=void 0;var jsiiDeprecationWarnings=()=>{var tmp=require("../../../../.warnings.jsii.js");return jsiiDeprecationWarnings=()=>tmp,tmp};const JSII_RTTI_SYMBOL_1=Symbol.for("jsii.rtti");var iam=()=>{var tmp=require("../../../../aws-iam");return iam=()=>tmp,tmp},core_1=()=>{var tmp=require("../../../../core");return core_1=()=>tmp,tmp},fields_1=()=>{var tmp=require("../../fields");return fields_1=()=>tmp,tmp},types_1=()=>{var tmp=require("../../types");return types_1=()=>tmp,tmp},Transformation;(function(Transformation2){Transformation2.NONE="NONE",Transformation2.COMPACT="COMPACT",Transformation2.FLATTEN="FLATTEN"})(Transformation||(exports.Transformation=Transformation={}));var OutputType;(function(OutputType2){OutputType2.JSON="JSON",OutputType2.JSONL="JSONL"})(OutputType||(exports.OutputType=OutputType={}));class WriterConfig{constructor(props){try{jsiiDeprecationWarnings().aws_cdk_lib_aws_stepfunctions_WriterConfigProps(props)}catch(error){throw process.env.JSII_DEBUG!=="1"&&error.name==="DeprecationError"&&Error.captureStackTrace(error,WriterConfig),error}this.transformation=props.transformation,this.outputType=props.outputType}}exports.WriterConfig=WriterConfig,_a=JSII_RTTI_SYMBOL_1,WriterConfig[_a]={fqn:"aws-cdk-lib.aws_stepfunctions.WriterConfig",version:"2.201.0"};function buildS3PutObjectPolicyStatements(bucketName){const resource=bucketName?core_1().Arn.format({region:"",account:"",partition:core_1().Aws.PARTITION,service:"s3",resource:bucketName,resourceName:"*"}):"*";return[new(iam()).PolicyStatement({actions:["s3:PutObject","s3:GetObject","s3:ListMultipartUploadParts","s3:AbortMultipartUpload"],resources:[resource]})]}const statesS3PutObjectResource=core_1().Arn.format({region:"",account:"",partition:core_1().Aws.PARTITION,service:"states",resource:"s3",resourceName:"putObject",arnFormat:core_1().ArnFormat.COLON_RESOURCE_NAME});class ResultWriter{constructor(props){try{jsiiDeprecationWarnings().print("aws-cdk-lib.aws_stepfunctions.ResultWriter","use {@link ResultWriterV2 } instead"),jsiiDeprecationWarnings().aws_cdk_lib_aws_stepfunctions_ResultWriterProps(props)}catch(error){throw process.env.JSII_DEBUG!=="1"&&error.name==="DeprecationError"&&Error.captureStackTrace(error,ResultWriter),error}this.bucket=props.bucket,this.prefix=props.prefix}render(queryLanguage){try{jsiiDeprecationWarnings().print("aws-cdk-lib.aws_stepfunctions.ResultWriter#render","use {@link ResultWriterV2 } instead"),jsiiDeprecationWarnings().aws_cdk_lib_aws_stepfunctions_QueryLanguage(queryLanguage)}catch(error){throw process.env.JSII_DEBUG!=="1"&&error.name==="DeprecationError"&&Error.captureStackTrace(error,this.render),error}const argumentOrParameter={Bucket:this.bucket.bucketName,...this.prefix&&{Prefix:this.prefix}};return fields_1().FieldUtils.renderObject({Resource:statesS3PutObjectResource,...queryLanguage===types_1().QueryLanguage.JSONATA?{Arguments:argumentOrParameter}:{Parameters:argumentOrParameter}})}providePolicyStatements(){try{jsiiDeprecationWarnings().print("aws-cdk-lib.aws_stepfunctions.ResultWriter#providePolicyStatements","use {@link ResultWriterV2 } instead")}catch(error){throw process.env.JSII_DEBUG!=="1"&&error.name==="DeprecationError"&&Error.captureStackTrace(error,this.providePolicyStatements),error}return this.bucket?.bucketName?buildS3PutObjectPolicyStatements(this.bucket.bucketName):[]}}exports.ResultWriter=ResultWriter,_b=JSII_RTTI_SYMBOL_1,ResultWriter[_b]={fqn:"aws-cdk-lib.aws_stepfunctions.ResultWriter",version:"2.201.0"};class ResultWriterV2{constructor(props){try{jsiiDeprecationWarnings().aws_cdk_lib_aws_stepfunctions_ResultWriterV2Props(props)}catch(error){throw process.env.JSII_DEBUG!=="1"&&error.name==="DeprecationError"&&Error.captureStackTrace(error,ResultWriterV2),error}this.bucket=props.bucket,this.bucketNamePath=props.bucketNamePath,this.prefix=props.prefix,this.writerConfig=props.writerConfig}render(queryLanguage){try{jsiiDeprecationWarnings().aws_cdk_lib_aws_stepfunctions_QueryLanguage(queryLanguage)}catch(error){throw process.env.JSII_DEBUG!=="1"&&error.name==="DeprecationError"&&Error.captureStackTrace(error,this.render),error}const shouldRenderResourceAndParameters=!!(this.bucket||this.bucketNamePath),argumentOrParameter=shouldRenderResourceAndParameters?{...this.bucket&&{Bucket:this.bucket.bucketName},...this.bucketNamePath&&{Bucket:this.bucketNamePath},...this.prefix&&{Prefix:this.prefix}}:void 0;return fields_1().FieldUtils.renderObject({...shouldRenderResourceAndParameters&&{Resource:statesS3PutObjectResource},...argumentOrParameter&&(queryLanguage===types_1().QueryLanguage.JSONATA?{Arguments:argumentOrParameter}:{Parameters:argumentOrParameter}),...this.writerConfig&&{WriterConfig:{OutputType:this.writerConfig?.outputType,Transformation:this.writerConfig?.transformation}}})}providePolicyStatements(){return!this.bucket?.bucketName&&!this.bucketNamePath?[]:this.bucketNamePath?buildS3PutObjectPolicyStatements():buildS3PutObjectPolicyStatements(this.bucket?.bucketName)}validateResultWriter(){const errors=[];return this.bucket&&this.bucketNamePath&&errors.push("Provide either `bucket` or `bucketNamePath`, but not both"),errors}}exports.ResultWriterV2=ResultWriterV2,_c=JSII_RTTI_SYMBOL_1,ResultWriterV2[_c]={fqn:"aws-cdk-lib.aws_stepfunctions.ResultWriterV2",version:"2.201.0"};
