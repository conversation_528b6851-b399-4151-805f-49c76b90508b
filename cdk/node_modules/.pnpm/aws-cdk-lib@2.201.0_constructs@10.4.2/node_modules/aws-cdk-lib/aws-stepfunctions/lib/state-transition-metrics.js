"use strict";var _a;Object.defineProperty(exports,"__esModule",{value:!0}),exports.StateTransitionMetric=void 0;var jsiiDeprecationWarnings=()=>{var tmp=require("../../.warnings.jsii.js");return jsiiDeprecationWarnings=()=>tmp,tmp};const JSII_RTTI_SYMBOL_1=Symbol.for("jsii.rtti");var cloudwatch=()=>{var tmp=require("../../aws-cloudwatch");return cloudwatch=()=>tmp,tmp};class StateTransitionMetric{static metric(metricName,props){try{jsiiDeprecationWarnings().aws_cdk_lib_aws_cloudwatch_MetricOptions(props)}catch(error){throw process.env.JSII_DEBUG!=="1"&&error.name==="DeprecationError"&&Error.captureStackTrace(error,this.metric),error}return new(cloudwatch()).Metric({namespace:"AWS/States",metricName,dimensionsMap:{ServiceMetric:"StateTransition"},...props})}static metricProvisionedBucketSize(props){try{jsiiDeprecationWarnings().aws_cdk_lib_aws_cloudwatch_MetricOptions(props)}catch(error){throw process.env.JSII_DEBUG!=="1"&&error.name==="DeprecationError"&&Error.captureStackTrace(error,this.metricProvisionedBucketSize),error}return StateTransitionMetric.metric("ProvisionedBucketSize",props)}static metricProvisionedRefillRate(props){try{jsiiDeprecationWarnings().aws_cdk_lib_aws_cloudwatch_MetricOptions(props)}catch(error){throw process.env.JSII_DEBUG!=="1"&&error.name==="DeprecationError"&&Error.captureStackTrace(error,this.metricProvisionedRefillRate),error}return StateTransitionMetric.metric("ProvisionedRefillRate",props)}static metricConsumedCapacity(props){try{jsiiDeprecationWarnings().aws_cdk_lib_aws_cloudwatch_MetricOptions(props)}catch(error){throw process.env.JSII_DEBUG!=="1"&&error.name==="DeprecationError"&&Error.captureStackTrace(error,this.metricConsumedCapacity),error}return StateTransitionMetric.metric("ConsumedCapacity",props)}static metricThrottledEvents(props){try{jsiiDeprecationWarnings().aws_cdk_lib_aws_cloudwatch_MetricOptions(props)}catch(error){throw process.env.JSII_DEBUG!=="1"&&error.name==="DeprecationError"&&Error.captureStackTrace(error,this.metricThrottledEvents),error}return StateTransitionMetric.metric("ThrottledEvents",{statistic:"sum",...props})}}exports.StateTransitionMetric=StateTransitionMetric,_a=JSII_RTTI_SYMBOL_1,StateTransitionMetric[_a]={fqn:"aws-cdk-lib.aws_stepfunctions.StateTransitionMetric",version:"2.201.0"};
