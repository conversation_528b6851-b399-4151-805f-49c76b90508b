"use strict";var _a,_b,_c,_d;Object.defineProperty(exports,"__esModule",{value:!0}),exports.CfnStateMachineVersion=exports.CfnStateMachineAlias=exports.CfnStateMachine=exports.CfnActivity=void 0;var jsiiDeprecationWarnings=()=>{var tmp=require("../../.warnings.jsii.js");return jsiiDeprecationWarnings=()=>tmp,tmp};const JSII_RTTI_SYMBOL_1=Symbol.for("jsii.rtti");var cdk=()=>{var tmp=require("../../core");return cdk=()=>tmp,tmp},cfn_parse=()=>{var tmp=require("../../core/lib/helpers-internal");return cfn_parse=()=>tmp,tmp},cdk_errors=()=>{var tmp=require("../../core/lib/errors");return cdk_errors=()=>tmp,tmp};class CfnActivity extends cdk().CfnResource{static _fromCloudFormation(scope,id,resourceAttributes,options){resourceAttributes=resourceAttributes||{};const resourceProperties=options.parser.parseValue(resourceAttributes.Properties),propsResult=CfnActivityPropsFromCloudFormation(resourceProperties);if(cdk().isResolvableObject(propsResult.value))throw new(cdk_errors()).ValidationError("Unexpected IResolvable",scope);const ret=new CfnActivity(scope,id,propsResult.value);for(const[propKey,propVal]of Object.entries(propsResult.extraProperties))ret.addPropertyOverride(propKey,propVal);return options.parser.handleAttributes(ret,resourceAttributes,id),ret}constructor(scope,id,props){super(scope,id,{type:CfnActivity.CFN_RESOURCE_TYPE_NAME,properties:props});try{jsiiDeprecationWarnings().aws_cdk_lib_aws_stepfunctions_CfnActivityProps(props)}catch(error){throw process.env.JSII_DEBUG!=="1"&&error.name==="DeprecationError"&&Error.captureStackTrace(error,CfnActivity),error}cdk().requireProperty(props,"name",this),this.attrArn=cdk().Token.asString(this.getAtt("Arn",cdk().ResolutionTypeHint.STRING)),this.attrName=cdk().Token.asString(this.getAtt("Name",cdk().ResolutionTypeHint.STRING)),this.encryptionConfiguration=props.encryptionConfiguration,this.name=props.name,this.tags=new(cdk()).TagManager(cdk().TagType.STANDARD,"AWS::StepFunctions::Activity",props.tags,{tagPropertyName:"tags"}),this.tagsRaw=props.tags}get cfnProperties(){return{encryptionConfiguration:this.encryptionConfiguration,name:this.name,tags:this.tags.renderTags()}}inspect(inspector){try{jsiiDeprecationWarnings().aws_cdk_lib_TreeInspector(inspector)}catch(error){throw process.env.JSII_DEBUG!=="1"&&error.name==="DeprecationError"&&Error.captureStackTrace(error,this.inspect),error}inspector.addAttribute("aws:cdk:cloudformation:type",CfnActivity.CFN_RESOURCE_TYPE_NAME),inspector.addAttribute("aws:cdk:cloudformation:props",this.cfnProperties)}renderProperties(props){return convertCfnActivityPropsToCloudFormation(props)}}exports.CfnActivity=CfnActivity,_a=JSII_RTTI_SYMBOL_1,CfnActivity[_a]={fqn:"aws-cdk-lib.aws_stepfunctions.CfnActivity",version:"2.201.0"},CfnActivity.CFN_RESOURCE_TYPE_NAME="AWS::StepFunctions::Activity";function CfnActivityTagsEntryPropertyValidator(properties){if(!cdk().canInspect(properties))return cdk().VALIDATION_SUCCESS;const errors=new(cdk()).ValidationResults;return properties&&typeof properties=="object"&&!Array.isArray(properties)||errors.collect(new(cdk()).ValidationResult("Expected an object, but received: "+JSON.stringify(properties))),errors.collect(cdk().propertyValidator("key",cdk().requiredValidator)(properties.key)),errors.collect(cdk().propertyValidator("key",cdk().validateString)(properties.key)),errors.collect(cdk().propertyValidator("value",cdk().requiredValidator)(properties.value)),errors.collect(cdk().propertyValidator("value",cdk().validateString)(properties.value)),errors.wrap('supplied properties not correct for "TagsEntryProperty"')}function convertCfnActivityTagsEntryPropertyToCloudFormation(properties){return cdk().canInspect(properties)?(CfnActivityTagsEntryPropertyValidator(properties).assertSuccess(),{Key:cdk().stringToCloudFormation(properties.key),Value:cdk().stringToCloudFormation(properties.value)}):properties}function CfnActivityTagsEntryPropertyFromCloudFormation(properties){if(cdk().isResolvableObject(properties))return new(cfn_parse()).FromCloudFormationResult(properties);if(properties=properties??{},!(properties&&typeof properties=="object"&&!Array.isArray(properties)))return new(cfn_parse()).FromCloudFormationResult(properties);const ret=new(cfn_parse()).FromCloudFormationPropertyObject;return ret.addPropertyResult("key","Key",properties.Key!=null?cfn_parse().FromCloudFormation.getString(properties.Key):void 0),ret.addPropertyResult("value","Value",properties.Value!=null?cfn_parse().FromCloudFormation.getString(properties.Value):void 0),ret.addUnrecognizedPropertiesAsExtra(properties),ret}function CfnActivityEncryptionConfigurationPropertyValidator(properties){if(!cdk().canInspect(properties))return cdk().VALIDATION_SUCCESS;const errors=new(cdk()).ValidationResults;return properties&&typeof properties=="object"&&!Array.isArray(properties)||errors.collect(new(cdk()).ValidationResult("Expected an object, but received: "+JSON.stringify(properties))),errors.collect(cdk().propertyValidator("kmsDataKeyReusePeriodSeconds",cdk().validateNumber)(properties.kmsDataKeyReusePeriodSeconds)),errors.collect(cdk().propertyValidator("kmsKeyId",cdk().validateString)(properties.kmsKeyId)),errors.collect(cdk().propertyValidator("type",cdk().requiredValidator)(properties.type)),errors.collect(cdk().propertyValidator("type",cdk().validateString)(properties.type)),errors.wrap('supplied properties not correct for "EncryptionConfigurationProperty"')}function convertCfnActivityEncryptionConfigurationPropertyToCloudFormation(properties){return cdk().canInspect(properties)?(CfnActivityEncryptionConfigurationPropertyValidator(properties).assertSuccess(),{KmsDataKeyReusePeriodSeconds:cdk().numberToCloudFormation(properties.kmsDataKeyReusePeriodSeconds),KmsKeyId:cdk().stringToCloudFormation(properties.kmsKeyId),Type:cdk().stringToCloudFormation(properties.type)}):properties}function CfnActivityEncryptionConfigurationPropertyFromCloudFormation(properties){if(cdk().isResolvableObject(properties))return new(cfn_parse()).FromCloudFormationResult(properties);if(properties=properties??{},!(properties&&typeof properties=="object"&&!Array.isArray(properties)))return new(cfn_parse()).FromCloudFormationResult(properties);const ret=new(cfn_parse()).FromCloudFormationPropertyObject;return ret.addPropertyResult("kmsDataKeyReusePeriodSeconds","KmsDataKeyReusePeriodSeconds",properties.KmsDataKeyReusePeriodSeconds!=null?cfn_parse().FromCloudFormation.getNumber(properties.KmsDataKeyReusePeriodSeconds):void 0),ret.addPropertyResult("kmsKeyId","KmsKeyId",properties.KmsKeyId!=null?cfn_parse().FromCloudFormation.getString(properties.KmsKeyId):void 0),ret.addPropertyResult("type","Type",properties.Type!=null?cfn_parse().FromCloudFormation.getString(properties.Type):void 0),ret.addUnrecognizedPropertiesAsExtra(properties),ret}function CfnActivityPropsValidator(properties){if(!cdk().canInspect(properties))return cdk().VALIDATION_SUCCESS;const errors=new(cdk()).ValidationResults;return properties&&typeof properties=="object"&&!Array.isArray(properties)||errors.collect(new(cdk()).ValidationResult("Expected an object, but received: "+JSON.stringify(properties))),errors.collect(cdk().propertyValidator("encryptionConfiguration",CfnActivityEncryptionConfigurationPropertyValidator)(properties.encryptionConfiguration)),errors.collect(cdk().propertyValidator("name",cdk().requiredValidator)(properties.name)),errors.collect(cdk().propertyValidator("name",cdk().validateString)(properties.name)),errors.collect(cdk().propertyValidator("tags",cdk().listValidator(CfnActivityTagsEntryPropertyValidator))(properties.tags)),errors.wrap('supplied properties not correct for "CfnActivityProps"')}function convertCfnActivityPropsToCloudFormation(properties){return cdk().canInspect(properties)?(CfnActivityPropsValidator(properties).assertSuccess(),{EncryptionConfiguration:convertCfnActivityEncryptionConfigurationPropertyToCloudFormation(properties.encryptionConfiguration),Name:cdk().stringToCloudFormation(properties.name),Tags:cdk().listMapper(convertCfnActivityTagsEntryPropertyToCloudFormation)(properties.tags)}):properties}function CfnActivityPropsFromCloudFormation(properties){if(cdk().isResolvableObject(properties))return new(cfn_parse()).FromCloudFormationResult(properties);if(properties=properties??{},!(properties&&typeof properties=="object"&&!Array.isArray(properties)))return new(cfn_parse()).FromCloudFormationResult(properties);const ret=new(cfn_parse()).FromCloudFormationPropertyObject;return ret.addPropertyResult("encryptionConfiguration","EncryptionConfiguration",properties.EncryptionConfiguration!=null?CfnActivityEncryptionConfigurationPropertyFromCloudFormation(properties.EncryptionConfiguration):void 0),ret.addPropertyResult("name","Name",properties.Name!=null?cfn_parse().FromCloudFormation.getString(properties.Name):void 0),ret.addPropertyResult("tags","Tags",properties.Tags!=null?cfn_parse().FromCloudFormation.getArray(CfnActivityTagsEntryPropertyFromCloudFormation)(properties.Tags):void 0),ret.addUnrecognizedPropertiesAsExtra(properties),ret}class CfnStateMachine extends cdk().CfnResource{static _fromCloudFormation(scope,id,resourceAttributes,options){resourceAttributes=resourceAttributes||{};const resourceProperties=options.parser.parseValue(resourceAttributes.Properties),propsResult=CfnStateMachinePropsFromCloudFormation(resourceProperties);if(cdk().isResolvableObject(propsResult.value))throw new(cdk_errors()).ValidationError("Unexpected IResolvable",scope);const ret=new CfnStateMachine(scope,id,propsResult.value);for(const[propKey,propVal]of Object.entries(propsResult.extraProperties))ret.addPropertyOverride(propKey,propVal);return options.parser.handleAttributes(ret,resourceAttributes,id),ret}constructor(scope,id,props){super(scope,id,{type:CfnStateMachine.CFN_RESOURCE_TYPE_NAME,properties:props});try{jsiiDeprecationWarnings().aws_cdk_lib_aws_stepfunctions_CfnStateMachineProps(props)}catch(error){throw process.env.JSII_DEBUG!=="1"&&error.name==="DeprecationError"&&Error.captureStackTrace(error,CfnStateMachine),error}cdk().requireProperty(props,"roleArn",this),this.attrArn=cdk().Token.asString(this.getAtt("Arn",cdk().ResolutionTypeHint.STRING)),this.attrName=cdk().Token.asString(this.getAtt("Name",cdk().ResolutionTypeHint.STRING)),this.attrStateMachineRevisionId=cdk().Token.asString(this.getAtt("StateMachineRevisionId",cdk().ResolutionTypeHint.STRING)),this.definition=props.definition,this.definitionS3Location=props.definitionS3Location,this.definitionString=props.definitionString,this.definitionSubstitutions=props.definitionSubstitutions,this.encryptionConfiguration=props.encryptionConfiguration,this.loggingConfiguration=props.loggingConfiguration,this.roleArn=props.roleArn,this.stateMachineName=props.stateMachineName,this.stateMachineType=props.stateMachineType,this.tags=new(cdk()).TagManager(cdk().TagType.STANDARD,"AWS::StepFunctions::StateMachine",props.tags,{tagPropertyName:"tags"}),this.tagsRaw=props.tags,this.tracingConfiguration=props.tracingConfiguration}get cfnProperties(){return{definition:this.definition,definitionS3Location:this.definitionS3Location,definitionString:this.definitionString,definitionSubstitutions:this.definitionSubstitutions,encryptionConfiguration:this.encryptionConfiguration,loggingConfiguration:this.loggingConfiguration,roleArn:this.roleArn,stateMachineName:this.stateMachineName,stateMachineType:this.stateMachineType,tags:this.tags.renderTags(),tracingConfiguration:this.tracingConfiguration}}inspect(inspector){try{jsiiDeprecationWarnings().aws_cdk_lib_TreeInspector(inspector)}catch(error){throw process.env.JSII_DEBUG!=="1"&&error.name==="DeprecationError"&&Error.captureStackTrace(error,this.inspect),error}inspector.addAttribute("aws:cdk:cloudformation:type",CfnStateMachine.CFN_RESOURCE_TYPE_NAME),inspector.addAttribute("aws:cdk:cloudformation:props",this.cfnProperties)}renderProperties(props){return convertCfnStateMachinePropsToCloudFormation(props)}}exports.CfnStateMachine=CfnStateMachine,_b=JSII_RTTI_SYMBOL_1,CfnStateMachine[_b]={fqn:"aws-cdk-lib.aws_stepfunctions.CfnStateMachine",version:"2.201.0"},CfnStateMachine.CFN_RESOURCE_TYPE_NAME="AWS::StepFunctions::StateMachine";function CfnStateMachineCloudWatchLogsLogGroupPropertyValidator(properties){if(!cdk().canInspect(properties))return cdk().VALIDATION_SUCCESS;const errors=new(cdk()).ValidationResults;return properties&&typeof properties=="object"&&!Array.isArray(properties)||errors.collect(new(cdk()).ValidationResult("Expected an object, but received: "+JSON.stringify(properties))),errors.collect(cdk().propertyValidator("logGroupArn",cdk().validateString)(properties.logGroupArn)),errors.wrap('supplied properties not correct for "CloudWatchLogsLogGroupProperty"')}function convertCfnStateMachineCloudWatchLogsLogGroupPropertyToCloudFormation(properties){return cdk().canInspect(properties)?(CfnStateMachineCloudWatchLogsLogGroupPropertyValidator(properties).assertSuccess(),{LogGroupArn:cdk().stringToCloudFormation(properties.logGroupArn)}):properties}function CfnStateMachineCloudWatchLogsLogGroupPropertyFromCloudFormation(properties){if(cdk().isResolvableObject(properties))return new(cfn_parse()).FromCloudFormationResult(properties);if(properties=properties??{},!(properties&&typeof properties=="object"&&!Array.isArray(properties)))return new(cfn_parse()).FromCloudFormationResult(properties);const ret=new(cfn_parse()).FromCloudFormationPropertyObject;return ret.addPropertyResult("logGroupArn","LogGroupArn",properties.LogGroupArn!=null?cfn_parse().FromCloudFormation.getString(properties.LogGroupArn):void 0),ret.addUnrecognizedPropertiesAsExtra(properties),ret}function CfnStateMachineLogDestinationPropertyValidator(properties){if(!cdk().canInspect(properties))return cdk().VALIDATION_SUCCESS;const errors=new(cdk()).ValidationResults;return properties&&typeof properties=="object"&&!Array.isArray(properties)||errors.collect(new(cdk()).ValidationResult("Expected an object, but received: "+JSON.stringify(properties))),errors.collect(cdk().propertyValidator("cloudWatchLogsLogGroup",CfnStateMachineCloudWatchLogsLogGroupPropertyValidator)(properties.cloudWatchLogsLogGroup)),errors.wrap('supplied properties not correct for "LogDestinationProperty"')}function convertCfnStateMachineLogDestinationPropertyToCloudFormation(properties){return cdk().canInspect(properties)?(CfnStateMachineLogDestinationPropertyValidator(properties).assertSuccess(),{CloudWatchLogsLogGroup:convertCfnStateMachineCloudWatchLogsLogGroupPropertyToCloudFormation(properties.cloudWatchLogsLogGroup)}):properties}function CfnStateMachineLogDestinationPropertyFromCloudFormation(properties){if(cdk().isResolvableObject(properties))return new(cfn_parse()).FromCloudFormationResult(properties);if(properties=properties??{},!(properties&&typeof properties=="object"&&!Array.isArray(properties)))return new(cfn_parse()).FromCloudFormationResult(properties);const ret=new(cfn_parse()).FromCloudFormationPropertyObject;return ret.addPropertyResult("cloudWatchLogsLogGroup","CloudWatchLogsLogGroup",properties.CloudWatchLogsLogGroup!=null?CfnStateMachineCloudWatchLogsLogGroupPropertyFromCloudFormation(properties.CloudWatchLogsLogGroup):void 0),ret.addUnrecognizedPropertiesAsExtra(properties),ret}function CfnStateMachineLoggingConfigurationPropertyValidator(properties){if(!cdk().canInspect(properties))return cdk().VALIDATION_SUCCESS;const errors=new(cdk()).ValidationResults;return properties&&typeof properties=="object"&&!Array.isArray(properties)||errors.collect(new(cdk()).ValidationResult("Expected an object, but received: "+JSON.stringify(properties))),errors.collect(cdk().propertyValidator("destinations",cdk().listValidator(CfnStateMachineLogDestinationPropertyValidator))(properties.destinations)),errors.collect(cdk().propertyValidator("includeExecutionData",cdk().validateBoolean)(properties.includeExecutionData)),errors.collect(cdk().propertyValidator("level",cdk().validateString)(properties.level)),errors.wrap('supplied properties not correct for "LoggingConfigurationProperty"')}function convertCfnStateMachineLoggingConfigurationPropertyToCloudFormation(properties){return cdk().canInspect(properties)?(CfnStateMachineLoggingConfigurationPropertyValidator(properties).assertSuccess(),{Destinations:cdk().listMapper(convertCfnStateMachineLogDestinationPropertyToCloudFormation)(properties.destinations),IncludeExecutionData:cdk().booleanToCloudFormation(properties.includeExecutionData),Level:cdk().stringToCloudFormation(properties.level)}):properties}function CfnStateMachineLoggingConfigurationPropertyFromCloudFormation(properties){if(cdk().isResolvableObject(properties))return new(cfn_parse()).FromCloudFormationResult(properties);if(properties=properties??{},!(properties&&typeof properties=="object"&&!Array.isArray(properties)))return new(cfn_parse()).FromCloudFormationResult(properties);const ret=new(cfn_parse()).FromCloudFormationPropertyObject;return ret.addPropertyResult("destinations","Destinations",properties.Destinations!=null?cfn_parse().FromCloudFormation.getArray(CfnStateMachineLogDestinationPropertyFromCloudFormation)(properties.Destinations):void 0),ret.addPropertyResult("includeExecutionData","IncludeExecutionData",properties.IncludeExecutionData!=null?cfn_parse().FromCloudFormation.getBoolean(properties.IncludeExecutionData):void 0),ret.addPropertyResult("level","Level",properties.Level!=null?cfn_parse().FromCloudFormation.getString(properties.Level):void 0),ret.addUnrecognizedPropertiesAsExtra(properties),ret}function CfnStateMachineS3LocationPropertyValidator(properties){if(!cdk().canInspect(properties))return cdk().VALIDATION_SUCCESS;const errors=new(cdk()).ValidationResults;return properties&&typeof properties=="object"&&!Array.isArray(properties)||errors.collect(new(cdk()).ValidationResult("Expected an object, but received: "+JSON.stringify(properties))),errors.collect(cdk().propertyValidator("bucket",cdk().requiredValidator)(properties.bucket)),errors.collect(cdk().propertyValidator("bucket",cdk().validateString)(properties.bucket)),errors.collect(cdk().propertyValidator("key",cdk().requiredValidator)(properties.key)),errors.collect(cdk().propertyValidator("key",cdk().validateString)(properties.key)),errors.collect(cdk().propertyValidator("version",cdk().validateString)(properties.version)),errors.wrap('supplied properties not correct for "S3LocationProperty"')}function convertCfnStateMachineS3LocationPropertyToCloudFormation(properties){return cdk().canInspect(properties)?(CfnStateMachineS3LocationPropertyValidator(properties).assertSuccess(),{Bucket:cdk().stringToCloudFormation(properties.bucket),Key:cdk().stringToCloudFormation(properties.key),Version:cdk().stringToCloudFormation(properties.version)}):properties}function CfnStateMachineS3LocationPropertyFromCloudFormation(properties){if(cdk().isResolvableObject(properties))return new(cfn_parse()).FromCloudFormationResult(properties);if(properties=properties??{},!(properties&&typeof properties=="object"&&!Array.isArray(properties)))return new(cfn_parse()).FromCloudFormationResult(properties);const ret=new(cfn_parse()).FromCloudFormationPropertyObject;return ret.addPropertyResult("bucket","Bucket",properties.Bucket!=null?cfn_parse().FromCloudFormation.getString(properties.Bucket):void 0),ret.addPropertyResult("key","Key",properties.Key!=null?cfn_parse().FromCloudFormation.getString(properties.Key):void 0),ret.addPropertyResult("version","Version",properties.Version!=null?cfn_parse().FromCloudFormation.getString(properties.Version):void 0),ret.addUnrecognizedPropertiesAsExtra(properties),ret}function CfnStateMachineTagsEntryPropertyValidator(properties){if(!cdk().canInspect(properties))return cdk().VALIDATION_SUCCESS;const errors=new(cdk()).ValidationResults;return properties&&typeof properties=="object"&&!Array.isArray(properties)||errors.collect(new(cdk()).ValidationResult("Expected an object, but received: "+JSON.stringify(properties))),errors.collect(cdk().propertyValidator("key",cdk().requiredValidator)(properties.key)),errors.collect(cdk().propertyValidator("key",cdk().validateString)(properties.key)),errors.collect(cdk().propertyValidator("value",cdk().requiredValidator)(properties.value)),errors.collect(cdk().propertyValidator("value",cdk().validateString)(properties.value)),errors.wrap('supplied properties not correct for "TagsEntryProperty"')}function convertCfnStateMachineTagsEntryPropertyToCloudFormation(properties){return cdk().canInspect(properties)?(CfnStateMachineTagsEntryPropertyValidator(properties).assertSuccess(),{Key:cdk().stringToCloudFormation(properties.key),Value:cdk().stringToCloudFormation(properties.value)}):properties}function CfnStateMachineTagsEntryPropertyFromCloudFormation(properties){if(cdk().isResolvableObject(properties))return new(cfn_parse()).FromCloudFormationResult(properties);if(properties=properties??{},!(properties&&typeof properties=="object"&&!Array.isArray(properties)))return new(cfn_parse()).FromCloudFormationResult(properties);const ret=new(cfn_parse()).FromCloudFormationPropertyObject;return ret.addPropertyResult("key","Key",properties.Key!=null?cfn_parse().FromCloudFormation.getString(properties.Key):void 0),ret.addPropertyResult("value","Value",properties.Value!=null?cfn_parse().FromCloudFormation.getString(properties.Value):void 0),ret.addUnrecognizedPropertiesAsExtra(properties),ret}function CfnStateMachineTracingConfigurationPropertyValidator(properties){if(!cdk().canInspect(properties))return cdk().VALIDATION_SUCCESS;const errors=new(cdk()).ValidationResults;return properties&&typeof properties=="object"&&!Array.isArray(properties)||errors.collect(new(cdk()).ValidationResult("Expected an object, but received: "+JSON.stringify(properties))),errors.collect(cdk().propertyValidator("enabled",cdk().validateBoolean)(properties.enabled)),errors.wrap('supplied properties not correct for "TracingConfigurationProperty"')}function convertCfnStateMachineTracingConfigurationPropertyToCloudFormation(properties){return cdk().canInspect(properties)?(CfnStateMachineTracingConfigurationPropertyValidator(properties).assertSuccess(),{Enabled:cdk().booleanToCloudFormation(properties.enabled)}):properties}function CfnStateMachineTracingConfigurationPropertyFromCloudFormation(properties){if(cdk().isResolvableObject(properties))return new(cfn_parse()).FromCloudFormationResult(properties);if(properties=properties??{},!(properties&&typeof properties=="object"&&!Array.isArray(properties)))return new(cfn_parse()).FromCloudFormationResult(properties);const ret=new(cfn_parse()).FromCloudFormationPropertyObject;return ret.addPropertyResult("enabled","Enabled",properties.Enabled!=null?cfn_parse().FromCloudFormation.getBoolean(properties.Enabled):void 0),ret.addUnrecognizedPropertiesAsExtra(properties),ret}function CfnStateMachineEncryptionConfigurationPropertyValidator(properties){if(!cdk().canInspect(properties))return cdk().VALIDATION_SUCCESS;const errors=new(cdk()).ValidationResults;return properties&&typeof properties=="object"&&!Array.isArray(properties)||errors.collect(new(cdk()).ValidationResult("Expected an object, but received: "+JSON.stringify(properties))),errors.collect(cdk().propertyValidator("kmsDataKeyReusePeriodSeconds",cdk().validateNumber)(properties.kmsDataKeyReusePeriodSeconds)),errors.collect(cdk().propertyValidator("kmsKeyId",cdk().validateString)(properties.kmsKeyId)),errors.collect(cdk().propertyValidator("type",cdk().requiredValidator)(properties.type)),errors.collect(cdk().propertyValidator("type",cdk().validateString)(properties.type)),errors.wrap('supplied properties not correct for "EncryptionConfigurationProperty"')}function convertCfnStateMachineEncryptionConfigurationPropertyToCloudFormation(properties){return cdk().canInspect(properties)?(CfnStateMachineEncryptionConfigurationPropertyValidator(properties).assertSuccess(),{KmsDataKeyReusePeriodSeconds:cdk().numberToCloudFormation(properties.kmsDataKeyReusePeriodSeconds),KmsKeyId:cdk().stringToCloudFormation(properties.kmsKeyId),Type:cdk().stringToCloudFormation(properties.type)}):properties}function CfnStateMachineEncryptionConfigurationPropertyFromCloudFormation(properties){if(cdk().isResolvableObject(properties))return new(cfn_parse()).FromCloudFormationResult(properties);if(properties=properties??{},!(properties&&typeof properties=="object"&&!Array.isArray(properties)))return new(cfn_parse()).FromCloudFormationResult(properties);const ret=new(cfn_parse()).FromCloudFormationPropertyObject;return ret.addPropertyResult("kmsDataKeyReusePeriodSeconds","KmsDataKeyReusePeriodSeconds",properties.KmsDataKeyReusePeriodSeconds!=null?cfn_parse().FromCloudFormation.getNumber(properties.KmsDataKeyReusePeriodSeconds):void 0),ret.addPropertyResult("kmsKeyId","KmsKeyId",properties.KmsKeyId!=null?cfn_parse().FromCloudFormation.getString(properties.KmsKeyId):void 0),ret.addPropertyResult("type","Type",properties.Type!=null?cfn_parse().FromCloudFormation.getString(properties.Type):void 0),ret.addUnrecognizedPropertiesAsExtra(properties),ret}function CfnStateMachinePropsValidator(properties){if(!cdk().canInspect(properties))return cdk().VALIDATION_SUCCESS;const errors=new(cdk()).ValidationResults;return properties&&typeof properties=="object"&&!Array.isArray(properties)||errors.collect(new(cdk()).ValidationResult("Expected an object, but received: "+JSON.stringify(properties))),errors.collect(cdk().propertyValidator("definition",cdk().validateObject)(properties.definition)),errors.collect(cdk().propertyValidator("definitionS3Location",CfnStateMachineS3LocationPropertyValidator)(properties.definitionS3Location)),errors.collect(cdk().propertyValidator("definitionString",cdk().validateString)(properties.definitionString)),errors.collect(cdk().propertyValidator("definitionSubstitutions",cdk().hashValidator(cdk().validateString))(properties.definitionSubstitutions)),errors.collect(cdk().propertyValidator("encryptionConfiguration",CfnStateMachineEncryptionConfigurationPropertyValidator)(properties.encryptionConfiguration)),errors.collect(cdk().propertyValidator("loggingConfiguration",CfnStateMachineLoggingConfigurationPropertyValidator)(properties.loggingConfiguration)),errors.collect(cdk().propertyValidator("roleArn",cdk().requiredValidator)(properties.roleArn)),errors.collect(cdk().propertyValidator("roleArn",cdk().validateString)(properties.roleArn)),errors.collect(cdk().propertyValidator("stateMachineName",cdk().validateString)(properties.stateMachineName)),errors.collect(cdk().propertyValidator("stateMachineType",cdk().validateString)(properties.stateMachineType)),errors.collect(cdk().propertyValidator("tags",cdk().listValidator(CfnStateMachineTagsEntryPropertyValidator))(properties.tags)),errors.collect(cdk().propertyValidator("tracingConfiguration",CfnStateMachineTracingConfigurationPropertyValidator)(properties.tracingConfiguration)),errors.wrap('supplied properties not correct for "CfnStateMachineProps"')}function convertCfnStateMachinePropsToCloudFormation(properties){return cdk().canInspect(properties)?(CfnStateMachinePropsValidator(properties).assertSuccess(),{Definition:cdk().objectToCloudFormation(properties.definition),DefinitionS3Location:convertCfnStateMachineS3LocationPropertyToCloudFormation(properties.definitionS3Location),DefinitionString:cdk().stringToCloudFormation(properties.definitionString),DefinitionSubstitutions:cdk().hashMapper(cdk().stringToCloudFormation)(properties.definitionSubstitutions),EncryptionConfiguration:convertCfnStateMachineEncryptionConfigurationPropertyToCloudFormation(properties.encryptionConfiguration),LoggingConfiguration:convertCfnStateMachineLoggingConfigurationPropertyToCloudFormation(properties.loggingConfiguration),RoleArn:cdk().stringToCloudFormation(properties.roleArn),StateMachineName:cdk().stringToCloudFormation(properties.stateMachineName),StateMachineType:cdk().stringToCloudFormation(properties.stateMachineType),Tags:cdk().listMapper(convertCfnStateMachineTagsEntryPropertyToCloudFormation)(properties.tags),TracingConfiguration:convertCfnStateMachineTracingConfigurationPropertyToCloudFormation(properties.tracingConfiguration)}):properties}function CfnStateMachinePropsFromCloudFormation(properties){if(cdk().isResolvableObject(properties))return new(cfn_parse()).FromCloudFormationResult(properties);if(properties=properties??{},!(properties&&typeof properties=="object"&&!Array.isArray(properties)))return new(cfn_parse()).FromCloudFormationResult(properties);const ret=new(cfn_parse()).FromCloudFormationPropertyObject;return ret.addPropertyResult("definition","Definition",properties.Definition!=null?cfn_parse().FromCloudFormation.getAny(properties.Definition):void 0),ret.addPropertyResult("definitionS3Location","DefinitionS3Location",properties.DefinitionS3Location!=null?CfnStateMachineS3LocationPropertyFromCloudFormation(properties.DefinitionS3Location):void 0),ret.addPropertyResult("definitionString","DefinitionString",properties.DefinitionString!=null?cfn_parse().FromCloudFormation.getString(properties.DefinitionString):void 0),ret.addPropertyResult("definitionSubstitutions","DefinitionSubstitutions",properties.DefinitionSubstitutions!=null?cfn_parse().FromCloudFormation.getMap(cfn_parse().FromCloudFormation.getString)(properties.DefinitionSubstitutions):void 0),ret.addPropertyResult("encryptionConfiguration","EncryptionConfiguration",properties.EncryptionConfiguration!=null?CfnStateMachineEncryptionConfigurationPropertyFromCloudFormation(properties.EncryptionConfiguration):void 0),ret.addPropertyResult("loggingConfiguration","LoggingConfiguration",properties.LoggingConfiguration!=null?CfnStateMachineLoggingConfigurationPropertyFromCloudFormation(properties.LoggingConfiguration):void 0),ret.addPropertyResult("roleArn","RoleArn",properties.RoleArn!=null?cfn_parse().FromCloudFormation.getString(properties.RoleArn):void 0),ret.addPropertyResult("stateMachineName","StateMachineName",properties.StateMachineName!=null?cfn_parse().FromCloudFormation.getString(properties.StateMachineName):void 0),ret.addPropertyResult("stateMachineType","StateMachineType",properties.StateMachineType!=null?cfn_parse().FromCloudFormation.getString(properties.StateMachineType):void 0),ret.addPropertyResult("tags","Tags",properties.Tags!=null?cfn_parse().FromCloudFormation.getArray(CfnStateMachineTagsEntryPropertyFromCloudFormation)(properties.Tags):void 0),ret.addPropertyResult("tracingConfiguration","TracingConfiguration",properties.TracingConfiguration!=null?CfnStateMachineTracingConfigurationPropertyFromCloudFormation(properties.TracingConfiguration):void 0),ret.addUnrecognizedPropertiesAsExtra(properties),ret}class CfnStateMachineAlias extends cdk().CfnResource{static _fromCloudFormation(scope,id,resourceAttributes,options){resourceAttributes=resourceAttributes||{};const resourceProperties=options.parser.parseValue(resourceAttributes.Properties),propsResult=CfnStateMachineAliasPropsFromCloudFormation(resourceProperties);if(cdk().isResolvableObject(propsResult.value))throw new(cdk_errors()).ValidationError("Unexpected IResolvable",scope);const ret=new CfnStateMachineAlias(scope,id,propsResult.value);for(const[propKey,propVal]of Object.entries(propsResult.extraProperties))ret.addPropertyOverride(propKey,propVal);return options.parser.handleAttributes(ret,resourceAttributes,id),ret}constructor(scope,id,props={}){super(scope,id,{type:CfnStateMachineAlias.CFN_RESOURCE_TYPE_NAME,properties:props});try{jsiiDeprecationWarnings().aws_cdk_lib_aws_stepfunctions_CfnStateMachineAliasProps(props)}catch(error){throw process.env.JSII_DEBUG!=="1"&&error.name==="DeprecationError"&&Error.captureStackTrace(error,CfnStateMachineAlias),error}this.attrArn=cdk().Token.asString(this.getAtt("Arn",cdk().ResolutionTypeHint.STRING)),this.deploymentPreference=props.deploymentPreference,this.description=props.description,this.name=props.name,this.routingConfiguration=props.routingConfiguration}get cfnProperties(){return{deploymentPreference:this.deploymentPreference,description:this.description,name:this.name,routingConfiguration:this.routingConfiguration}}inspect(inspector){try{jsiiDeprecationWarnings().aws_cdk_lib_TreeInspector(inspector)}catch(error){throw process.env.JSII_DEBUG!=="1"&&error.name==="DeprecationError"&&Error.captureStackTrace(error,this.inspect),error}inspector.addAttribute("aws:cdk:cloudformation:type",CfnStateMachineAlias.CFN_RESOURCE_TYPE_NAME),inspector.addAttribute("aws:cdk:cloudformation:props",this.cfnProperties)}renderProperties(props){return convertCfnStateMachineAliasPropsToCloudFormation(props)}}exports.CfnStateMachineAlias=CfnStateMachineAlias,_c=JSII_RTTI_SYMBOL_1,CfnStateMachineAlias[_c]={fqn:"aws-cdk-lib.aws_stepfunctions.CfnStateMachineAlias",version:"2.201.0"},CfnStateMachineAlias.CFN_RESOURCE_TYPE_NAME="AWS::StepFunctions::StateMachineAlias";function CfnStateMachineAliasRoutingConfigurationVersionPropertyValidator(properties){if(!cdk().canInspect(properties))return cdk().VALIDATION_SUCCESS;const errors=new(cdk()).ValidationResults;return properties&&typeof properties=="object"&&!Array.isArray(properties)||errors.collect(new(cdk()).ValidationResult("Expected an object, but received: "+JSON.stringify(properties))),errors.collect(cdk().propertyValidator("stateMachineVersionArn",cdk().requiredValidator)(properties.stateMachineVersionArn)),errors.collect(cdk().propertyValidator("stateMachineVersionArn",cdk().validateString)(properties.stateMachineVersionArn)),errors.collect(cdk().propertyValidator("weight",cdk().requiredValidator)(properties.weight)),errors.collect(cdk().propertyValidator("weight",cdk().validateNumber)(properties.weight)),errors.wrap('supplied properties not correct for "RoutingConfigurationVersionProperty"')}function convertCfnStateMachineAliasRoutingConfigurationVersionPropertyToCloudFormation(properties){return cdk().canInspect(properties)?(CfnStateMachineAliasRoutingConfigurationVersionPropertyValidator(properties).assertSuccess(),{StateMachineVersionArn:cdk().stringToCloudFormation(properties.stateMachineVersionArn),Weight:cdk().numberToCloudFormation(properties.weight)}):properties}function CfnStateMachineAliasRoutingConfigurationVersionPropertyFromCloudFormation(properties){if(cdk().isResolvableObject(properties))return new(cfn_parse()).FromCloudFormationResult(properties);if(properties=properties??{},!(properties&&typeof properties=="object"&&!Array.isArray(properties)))return new(cfn_parse()).FromCloudFormationResult(properties);const ret=new(cfn_parse()).FromCloudFormationPropertyObject;return ret.addPropertyResult("stateMachineVersionArn","StateMachineVersionArn",properties.StateMachineVersionArn!=null?cfn_parse().FromCloudFormation.getString(properties.StateMachineVersionArn):void 0),ret.addPropertyResult("weight","Weight",properties.Weight!=null?cfn_parse().FromCloudFormation.getNumber(properties.Weight):void 0),ret.addUnrecognizedPropertiesAsExtra(properties),ret}function CfnStateMachineAliasDeploymentPreferencePropertyValidator(properties){if(!cdk().canInspect(properties))return cdk().VALIDATION_SUCCESS;const errors=new(cdk()).ValidationResults;return properties&&typeof properties=="object"&&!Array.isArray(properties)||errors.collect(new(cdk()).ValidationResult("Expected an object, but received: "+JSON.stringify(properties))),errors.collect(cdk().propertyValidator("alarms",cdk().listValidator(cdk().validateString))(properties.alarms)),errors.collect(cdk().propertyValidator("interval",cdk().validateNumber)(properties.interval)),errors.collect(cdk().propertyValidator("percentage",cdk().validateNumber)(properties.percentage)),errors.collect(cdk().propertyValidator("stateMachineVersionArn",cdk().requiredValidator)(properties.stateMachineVersionArn)),errors.collect(cdk().propertyValidator("stateMachineVersionArn",cdk().validateString)(properties.stateMachineVersionArn)),errors.collect(cdk().propertyValidator("type",cdk().requiredValidator)(properties.type)),errors.collect(cdk().propertyValidator("type",cdk().validateString)(properties.type)),errors.wrap('supplied properties not correct for "DeploymentPreferenceProperty"')}function convertCfnStateMachineAliasDeploymentPreferencePropertyToCloudFormation(properties){return cdk().canInspect(properties)?(CfnStateMachineAliasDeploymentPreferencePropertyValidator(properties).assertSuccess(),{Alarms:cdk().listMapper(cdk().stringToCloudFormation)(properties.alarms),Interval:cdk().numberToCloudFormation(properties.interval),Percentage:cdk().numberToCloudFormation(properties.percentage),StateMachineVersionArn:cdk().stringToCloudFormation(properties.stateMachineVersionArn),Type:cdk().stringToCloudFormation(properties.type)}):properties}function CfnStateMachineAliasDeploymentPreferencePropertyFromCloudFormation(properties){if(cdk().isResolvableObject(properties))return new(cfn_parse()).FromCloudFormationResult(properties);if(properties=properties??{},!(properties&&typeof properties=="object"&&!Array.isArray(properties)))return new(cfn_parse()).FromCloudFormationResult(properties);const ret=new(cfn_parse()).FromCloudFormationPropertyObject;return ret.addPropertyResult("alarms","Alarms",properties.Alarms!=null?cfn_parse().FromCloudFormation.getArray(cfn_parse().FromCloudFormation.getString)(properties.Alarms):void 0),ret.addPropertyResult("interval","Interval",properties.Interval!=null?cfn_parse().FromCloudFormation.getNumber(properties.Interval):void 0),ret.addPropertyResult("percentage","Percentage",properties.Percentage!=null?cfn_parse().FromCloudFormation.getNumber(properties.Percentage):void 0),ret.addPropertyResult("stateMachineVersionArn","StateMachineVersionArn",properties.StateMachineVersionArn!=null?cfn_parse().FromCloudFormation.getString(properties.StateMachineVersionArn):void 0),ret.addPropertyResult("type","Type",properties.Type!=null?cfn_parse().FromCloudFormation.getString(properties.Type):void 0),ret.addUnrecognizedPropertiesAsExtra(properties),ret}function CfnStateMachineAliasPropsValidator(properties){if(!cdk().canInspect(properties))return cdk().VALIDATION_SUCCESS;const errors=new(cdk()).ValidationResults;return properties&&typeof properties=="object"&&!Array.isArray(properties)||errors.collect(new(cdk()).ValidationResult("Expected an object, but received: "+JSON.stringify(properties))),errors.collect(cdk().propertyValidator("deploymentPreference",CfnStateMachineAliasDeploymentPreferencePropertyValidator)(properties.deploymentPreference)),errors.collect(cdk().propertyValidator("description",cdk().validateString)(properties.description)),errors.collect(cdk().propertyValidator("name",cdk().validateString)(properties.name)),errors.collect(cdk().propertyValidator("routingConfiguration",cdk().listValidator(CfnStateMachineAliasRoutingConfigurationVersionPropertyValidator))(properties.routingConfiguration)),errors.wrap('supplied properties not correct for "CfnStateMachineAliasProps"')}function convertCfnStateMachineAliasPropsToCloudFormation(properties){return cdk().canInspect(properties)?(CfnStateMachineAliasPropsValidator(properties).assertSuccess(),{DeploymentPreference:convertCfnStateMachineAliasDeploymentPreferencePropertyToCloudFormation(properties.deploymentPreference),Description:cdk().stringToCloudFormation(properties.description),Name:cdk().stringToCloudFormation(properties.name),RoutingConfiguration:cdk().listMapper(convertCfnStateMachineAliasRoutingConfigurationVersionPropertyToCloudFormation)(properties.routingConfiguration)}):properties}function CfnStateMachineAliasPropsFromCloudFormation(properties){if(cdk().isResolvableObject(properties))return new(cfn_parse()).FromCloudFormationResult(properties);if(properties=properties??{},!(properties&&typeof properties=="object"&&!Array.isArray(properties)))return new(cfn_parse()).FromCloudFormationResult(properties);const ret=new(cfn_parse()).FromCloudFormationPropertyObject;return ret.addPropertyResult("deploymentPreference","DeploymentPreference",properties.DeploymentPreference!=null?CfnStateMachineAliasDeploymentPreferencePropertyFromCloudFormation(properties.DeploymentPreference):void 0),ret.addPropertyResult("description","Description",properties.Description!=null?cfn_parse().FromCloudFormation.getString(properties.Description):void 0),ret.addPropertyResult("name","Name",properties.Name!=null?cfn_parse().FromCloudFormation.getString(properties.Name):void 0),ret.addPropertyResult("routingConfiguration","RoutingConfiguration",properties.RoutingConfiguration!=null?cfn_parse().FromCloudFormation.getArray(CfnStateMachineAliasRoutingConfigurationVersionPropertyFromCloudFormation)(properties.RoutingConfiguration):void 0),ret.addUnrecognizedPropertiesAsExtra(properties),ret}class CfnStateMachineVersion extends cdk().CfnResource{static _fromCloudFormation(scope,id,resourceAttributes,options){resourceAttributes=resourceAttributes||{};const resourceProperties=options.parser.parseValue(resourceAttributes.Properties),propsResult=CfnStateMachineVersionPropsFromCloudFormation(resourceProperties);if(cdk().isResolvableObject(propsResult.value))throw new(cdk_errors()).ValidationError("Unexpected IResolvable",scope);const ret=new CfnStateMachineVersion(scope,id,propsResult.value);for(const[propKey,propVal]of Object.entries(propsResult.extraProperties))ret.addPropertyOverride(propKey,propVal);return options.parser.handleAttributes(ret,resourceAttributes,id),ret}constructor(scope,id,props){super(scope,id,{type:CfnStateMachineVersion.CFN_RESOURCE_TYPE_NAME,properties:props});try{jsiiDeprecationWarnings().aws_cdk_lib_aws_stepfunctions_CfnStateMachineVersionProps(props)}catch(error){throw process.env.JSII_DEBUG!=="1"&&error.name==="DeprecationError"&&Error.captureStackTrace(error,CfnStateMachineVersion),error}cdk().requireProperty(props,"stateMachineArn",this),this.attrArn=cdk().Token.asString(this.getAtt("Arn",cdk().ResolutionTypeHint.STRING)),this.description=props.description,this.stateMachineArn=props.stateMachineArn,this.stateMachineRevisionId=props.stateMachineRevisionId}get cfnProperties(){return{description:this.description,stateMachineArn:this.stateMachineArn,stateMachineRevisionId:this.stateMachineRevisionId}}inspect(inspector){try{jsiiDeprecationWarnings().aws_cdk_lib_TreeInspector(inspector)}catch(error){throw process.env.JSII_DEBUG!=="1"&&error.name==="DeprecationError"&&Error.captureStackTrace(error,this.inspect),error}inspector.addAttribute("aws:cdk:cloudformation:type",CfnStateMachineVersion.CFN_RESOURCE_TYPE_NAME),inspector.addAttribute("aws:cdk:cloudformation:props",this.cfnProperties)}renderProperties(props){return convertCfnStateMachineVersionPropsToCloudFormation(props)}}exports.CfnStateMachineVersion=CfnStateMachineVersion,_d=JSII_RTTI_SYMBOL_1,CfnStateMachineVersion[_d]={fqn:"aws-cdk-lib.aws_stepfunctions.CfnStateMachineVersion",version:"2.201.0"},CfnStateMachineVersion.CFN_RESOURCE_TYPE_NAME="AWS::StepFunctions::StateMachineVersion";function CfnStateMachineVersionPropsValidator(properties){if(!cdk().canInspect(properties))return cdk().VALIDATION_SUCCESS;const errors=new(cdk()).ValidationResults;return properties&&typeof properties=="object"&&!Array.isArray(properties)||errors.collect(new(cdk()).ValidationResult("Expected an object, but received: "+JSON.stringify(properties))),errors.collect(cdk().propertyValidator("description",cdk().validateString)(properties.description)),errors.collect(cdk().propertyValidator("stateMachineArn",cdk().requiredValidator)(properties.stateMachineArn)),errors.collect(cdk().propertyValidator("stateMachineArn",cdk().validateString)(properties.stateMachineArn)),errors.collect(cdk().propertyValidator("stateMachineRevisionId",cdk().validateString)(properties.stateMachineRevisionId)),errors.wrap('supplied properties not correct for "CfnStateMachineVersionProps"')}function convertCfnStateMachineVersionPropsToCloudFormation(properties){return cdk().canInspect(properties)?(CfnStateMachineVersionPropsValidator(properties).assertSuccess(),{Description:cdk().stringToCloudFormation(properties.description),StateMachineArn:cdk().stringToCloudFormation(properties.stateMachineArn),StateMachineRevisionId:cdk().stringToCloudFormation(properties.stateMachineRevisionId)}):properties}function CfnStateMachineVersionPropsFromCloudFormation(properties){if(cdk().isResolvableObject(properties))return new(cfn_parse()).FromCloudFormationResult(properties);if(properties=properties??{},!(properties&&typeof properties=="object"&&!Array.isArray(properties)))return new(cfn_parse()).FromCloudFormationResult(properties);const ret=new(cfn_parse()).FromCloudFormationPropertyObject;return ret.addPropertyResult("description","Description",properties.Description!=null?cfn_parse().FromCloudFormation.getString(properties.Description):void 0),ret.addPropertyResult("stateMachineArn","StateMachineArn",properties.StateMachineArn!=null?cfn_parse().FromCloudFormation.getString(properties.StateMachineArn):void 0),ret.addPropertyResult("stateMachineRevisionId","StateMachineRevisionId",properties.StateMachineRevisionId!=null?cfn_parse().FromCloudFormation.getString(properties.StateMachineRevisionId):void 0),ret.addUnrecognizedPropertiesAsExtra(properties),ret}
