import { EncryptionConfiguration } from '../encryption-configuration';
export declare function noEmptyObject<A>(o: Record<string, A>): Record<string, A> | undefined;
export declare function buildEncryptionConfiguration(encryptionConfiguration?: EncryptionConfiguration): {
    type: string;
    kmsKeyId?: undefined;
    kmsDataKeyReusePeriodSeconds?: undefined;
} | {
    kmsKeyId: string;
    kmsDataKeyReusePeriodSeconds: number;
    type: string;
} | undefined;
