"use strict";var __createBinding=exports&&exports.__createBinding||(Object.create?function(o,m,k,k2){k2===void 0&&(k2=k);var desc=Object.getOwnPropertyDescriptor(m,k);(!desc||("get"in desc?!m.__esModule:desc.writable||desc.configurable))&&(desc={enumerable:!0,get:function(){return m[k]}}),Object.defineProperty(o,k2,desc)}:function(o,m,k,k2){k2===void 0&&(k2=k),o[k2]=m[k]}),__exportStar=exports&&exports.__exportStar||function(m,exports2){for(var p in m)p!=="default"&&!Object.prototype.hasOwnProperty.call(exports2,p)&&__createBinding(exports2,m,p)};Object.defineProperty(exports,"__esModule",{value:!0});var _noFold;exports.CfnAssignment=void 0,Object.defineProperty(exports,_noFold="CfnAssignment",{enumerable:!0,configurable:!0,get:()=>require("./sso.generated").CfnAssignment}),exports.CfnInstanceAccessControlAttributeConfiguration=void 0,Object.defineProperty(exports,_noFold="CfnInstanceAccessControlAttributeConfiguration",{enumerable:!0,configurable:!0,get:()=>require("./sso.generated").CfnInstanceAccessControlAttributeConfiguration}),exports.CfnPermissionSet=void 0,Object.defineProperty(exports,_noFold="CfnPermissionSet",{enumerable:!0,configurable:!0,get:()=>require("./sso.generated").CfnPermissionSet}),exports.CfnApplication=void 0,Object.defineProperty(exports,_noFold="CfnApplication",{enumerable:!0,configurable:!0,get:()=>require("./sso.generated").CfnApplication}),exports.CfnApplicationAssignment=void 0,Object.defineProperty(exports,_noFold="CfnApplicationAssignment",{enumerable:!0,configurable:!0,get:()=>require("./sso.generated").CfnApplicationAssignment}),exports.CfnInstance=void 0,Object.defineProperty(exports,_noFold="CfnInstance",{enumerable:!0,configurable:!0,get:()=>require("./sso.generated").CfnInstance});
