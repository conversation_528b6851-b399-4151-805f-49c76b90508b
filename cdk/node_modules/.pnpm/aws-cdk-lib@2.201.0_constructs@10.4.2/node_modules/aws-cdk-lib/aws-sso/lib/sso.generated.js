"use strict";var _a,_b,_c,_d,_e,_f;Object.defineProperty(exports,"__esModule",{value:!0}),exports.CfnInstance=exports.CfnApplicationAssignment=exports.CfnApplication=exports.CfnPermissionSet=exports.CfnInstanceAccessControlAttributeConfiguration=exports.CfnAssignment=void 0;var jsiiDeprecationWarnings=()=>{var tmp=require("../../.warnings.jsii.js");return jsiiDeprecationWarnings=()=>tmp,tmp};const JSII_RTTI_SYMBOL_1=Symbol.for("jsii.rtti");var cdk=()=>{var tmp=require("../../core");return cdk=()=>tmp,tmp},cfn_parse=()=>{var tmp=require("../../core/lib/helpers-internal");return cfn_parse=()=>tmp,tmp},cdk_errors=()=>{var tmp=require("../../core/lib/errors");return cdk_errors=()=>tmp,tmp};class CfnAssignment extends cdk().CfnResource{static _fromCloudFormation(scope,id,resourceAttributes,options){resourceAttributes=resourceAttributes||{};const resourceProperties=options.parser.parseValue(resourceAttributes.Properties),propsResult=CfnAssignmentPropsFromCloudFormation(resourceProperties);if(cdk().isResolvableObject(propsResult.value))throw new(cdk_errors()).ValidationError("Unexpected IResolvable",scope);const ret=new CfnAssignment(scope,id,propsResult.value);for(const[propKey,propVal]of Object.entries(propsResult.extraProperties))ret.addPropertyOverride(propKey,propVal);return options.parser.handleAttributes(ret,resourceAttributes,id),ret}constructor(scope,id,props){super(scope,id,{type:CfnAssignment.CFN_RESOURCE_TYPE_NAME,properties:props});try{jsiiDeprecationWarnings().aws_cdk_lib_aws_sso_CfnAssignmentProps(props)}catch(error){throw process.env.JSII_DEBUG!=="1"&&error.name==="DeprecationError"&&Error.captureStackTrace(error,CfnAssignment),error}cdk().requireProperty(props,"instanceArn",this),cdk().requireProperty(props,"permissionSetArn",this),cdk().requireProperty(props,"principalId",this),cdk().requireProperty(props,"principalType",this),cdk().requireProperty(props,"targetId",this),cdk().requireProperty(props,"targetType",this),this.instanceArn=props.instanceArn,this.permissionSetArn=props.permissionSetArn,this.principalId=props.principalId,this.principalType=props.principalType,this.targetId=props.targetId,this.targetType=props.targetType}get cfnProperties(){return{instanceArn:this.instanceArn,permissionSetArn:this.permissionSetArn,principalId:this.principalId,principalType:this.principalType,targetId:this.targetId,targetType:this.targetType}}inspect(inspector){try{jsiiDeprecationWarnings().aws_cdk_lib_TreeInspector(inspector)}catch(error){throw process.env.JSII_DEBUG!=="1"&&error.name==="DeprecationError"&&Error.captureStackTrace(error,this.inspect),error}inspector.addAttribute("aws:cdk:cloudformation:type",CfnAssignment.CFN_RESOURCE_TYPE_NAME),inspector.addAttribute("aws:cdk:cloudformation:props",this.cfnProperties)}renderProperties(props){return convertCfnAssignmentPropsToCloudFormation(props)}}exports.CfnAssignment=CfnAssignment,_a=JSII_RTTI_SYMBOL_1,CfnAssignment[_a]={fqn:"aws-cdk-lib.aws_sso.CfnAssignment",version:"2.201.0"},CfnAssignment.CFN_RESOURCE_TYPE_NAME="AWS::SSO::Assignment";function CfnAssignmentPropsValidator(properties){if(!cdk().canInspect(properties))return cdk().VALIDATION_SUCCESS;const errors=new(cdk()).ValidationResults;return properties&&typeof properties=="object"&&!Array.isArray(properties)||errors.collect(new(cdk()).ValidationResult("Expected an object, but received: "+JSON.stringify(properties))),errors.collect(cdk().propertyValidator("instanceArn",cdk().requiredValidator)(properties.instanceArn)),errors.collect(cdk().propertyValidator("instanceArn",cdk().validateString)(properties.instanceArn)),errors.collect(cdk().propertyValidator("permissionSetArn",cdk().requiredValidator)(properties.permissionSetArn)),errors.collect(cdk().propertyValidator("permissionSetArn",cdk().validateString)(properties.permissionSetArn)),errors.collect(cdk().propertyValidator("principalId",cdk().requiredValidator)(properties.principalId)),errors.collect(cdk().propertyValidator("principalId",cdk().validateString)(properties.principalId)),errors.collect(cdk().propertyValidator("principalType",cdk().requiredValidator)(properties.principalType)),errors.collect(cdk().propertyValidator("principalType",cdk().validateString)(properties.principalType)),errors.collect(cdk().propertyValidator("targetId",cdk().requiredValidator)(properties.targetId)),errors.collect(cdk().propertyValidator("targetId",cdk().validateString)(properties.targetId)),errors.collect(cdk().propertyValidator("targetType",cdk().requiredValidator)(properties.targetType)),errors.collect(cdk().propertyValidator("targetType",cdk().validateString)(properties.targetType)),errors.wrap('supplied properties not correct for "CfnAssignmentProps"')}function convertCfnAssignmentPropsToCloudFormation(properties){return cdk().canInspect(properties)?(CfnAssignmentPropsValidator(properties).assertSuccess(),{InstanceArn:cdk().stringToCloudFormation(properties.instanceArn),PermissionSetArn:cdk().stringToCloudFormation(properties.permissionSetArn),PrincipalId:cdk().stringToCloudFormation(properties.principalId),PrincipalType:cdk().stringToCloudFormation(properties.principalType),TargetId:cdk().stringToCloudFormation(properties.targetId),TargetType:cdk().stringToCloudFormation(properties.targetType)}):properties}function CfnAssignmentPropsFromCloudFormation(properties){if(cdk().isResolvableObject(properties))return new(cfn_parse()).FromCloudFormationResult(properties);if(properties=properties??{},!(properties&&typeof properties=="object"&&!Array.isArray(properties)))return new(cfn_parse()).FromCloudFormationResult(properties);const ret=new(cfn_parse()).FromCloudFormationPropertyObject;return ret.addPropertyResult("instanceArn","InstanceArn",properties.InstanceArn!=null?cfn_parse().FromCloudFormation.getString(properties.InstanceArn):void 0),ret.addPropertyResult("permissionSetArn","PermissionSetArn",properties.PermissionSetArn!=null?cfn_parse().FromCloudFormation.getString(properties.PermissionSetArn):void 0),ret.addPropertyResult("principalId","PrincipalId",properties.PrincipalId!=null?cfn_parse().FromCloudFormation.getString(properties.PrincipalId):void 0),ret.addPropertyResult("principalType","PrincipalType",properties.PrincipalType!=null?cfn_parse().FromCloudFormation.getString(properties.PrincipalType):void 0),ret.addPropertyResult("targetId","TargetId",properties.TargetId!=null?cfn_parse().FromCloudFormation.getString(properties.TargetId):void 0),ret.addPropertyResult("targetType","TargetType",properties.TargetType!=null?cfn_parse().FromCloudFormation.getString(properties.TargetType):void 0),ret.addUnrecognizedPropertiesAsExtra(properties),ret}class CfnInstanceAccessControlAttributeConfiguration extends cdk().CfnResource{static _fromCloudFormation(scope,id,resourceAttributes,options){resourceAttributes=resourceAttributes||{};const resourceProperties=options.parser.parseValue(resourceAttributes.Properties),propsResult=CfnInstanceAccessControlAttributeConfigurationPropsFromCloudFormation(resourceProperties);if(cdk().isResolvableObject(propsResult.value))throw new(cdk_errors()).ValidationError("Unexpected IResolvable",scope);const ret=new CfnInstanceAccessControlAttributeConfiguration(scope,id,propsResult.value);for(const[propKey,propVal]of Object.entries(propsResult.extraProperties))ret.addPropertyOverride(propKey,propVal);return options.parser.handleAttributes(ret,resourceAttributes,id),ret}constructor(scope,id,props){super(scope,id,{type:CfnInstanceAccessControlAttributeConfiguration.CFN_RESOURCE_TYPE_NAME,properties:props});try{jsiiDeprecationWarnings().aws_cdk_lib_aws_sso_CfnInstanceAccessControlAttributeConfigurationProps(props)}catch(error){throw process.env.JSII_DEBUG!=="1"&&error.name==="DeprecationError"&&Error.captureStackTrace(error,CfnInstanceAccessControlAttributeConfiguration),error}cdk().requireProperty(props,"instanceArn",this),this.accessControlAttributes=props.accessControlAttributes,this.instanceAccessControlAttributeConfiguration=props.instanceAccessControlAttributeConfiguration,this.instanceArn=props.instanceArn}get cfnProperties(){return{accessControlAttributes:this.accessControlAttributes,instanceAccessControlAttributeConfiguration:this.instanceAccessControlAttributeConfiguration,instanceArn:this.instanceArn}}inspect(inspector){try{jsiiDeprecationWarnings().aws_cdk_lib_TreeInspector(inspector)}catch(error){throw process.env.JSII_DEBUG!=="1"&&error.name==="DeprecationError"&&Error.captureStackTrace(error,this.inspect),error}inspector.addAttribute("aws:cdk:cloudformation:type",CfnInstanceAccessControlAttributeConfiguration.CFN_RESOURCE_TYPE_NAME),inspector.addAttribute("aws:cdk:cloudformation:props",this.cfnProperties)}renderProperties(props){return convertCfnInstanceAccessControlAttributeConfigurationPropsToCloudFormation(props)}}exports.CfnInstanceAccessControlAttributeConfiguration=CfnInstanceAccessControlAttributeConfiguration,_b=JSII_RTTI_SYMBOL_1,CfnInstanceAccessControlAttributeConfiguration[_b]={fqn:"aws-cdk-lib.aws_sso.CfnInstanceAccessControlAttributeConfiguration",version:"2.201.0"},CfnInstanceAccessControlAttributeConfiguration.CFN_RESOURCE_TYPE_NAME="AWS::SSO::InstanceAccessControlAttributeConfiguration";function CfnInstanceAccessControlAttributeConfigurationAccessControlAttributeValuePropertyValidator(properties){if(!cdk().canInspect(properties))return cdk().VALIDATION_SUCCESS;const errors=new(cdk()).ValidationResults;return properties&&typeof properties=="object"&&!Array.isArray(properties)||errors.collect(new(cdk()).ValidationResult("Expected an object, but received: "+JSON.stringify(properties))),errors.collect(cdk().propertyValidator("source",cdk().requiredValidator)(properties.source)),errors.collect(cdk().propertyValidator("source",cdk().listValidator(cdk().validateString))(properties.source)),errors.wrap('supplied properties not correct for "AccessControlAttributeValueProperty"')}function convertCfnInstanceAccessControlAttributeConfigurationAccessControlAttributeValuePropertyToCloudFormation(properties){return cdk().canInspect(properties)?(CfnInstanceAccessControlAttributeConfigurationAccessControlAttributeValuePropertyValidator(properties).assertSuccess(),{Source:cdk().listMapper(cdk().stringToCloudFormation)(properties.source)}):properties}function CfnInstanceAccessControlAttributeConfigurationAccessControlAttributeValuePropertyFromCloudFormation(properties){if(cdk().isResolvableObject(properties))return new(cfn_parse()).FromCloudFormationResult(properties);if(properties=properties??{},!(properties&&typeof properties=="object"&&!Array.isArray(properties)))return new(cfn_parse()).FromCloudFormationResult(properties);const ret=new(cfn_parse()).FromCloudFormationPropertyObject;return ret.addPropertyResult("source","Source",properties.Source!=null?cfn_parse().FromCloudFormation.getArray(cfn_parse().FromCloudFormation.getString)(properties.Source):void 0),ret.addUnrecognizedPropertiesAsExtra(properties),ret}function CfnInstanceAccessControlAttributeConfigurationAccessControlAttributePropertyValidator(properties){if(!cdk().canInspect(properties))return cdk().VALIDATION_SUCCESS;const errors=new(cdk()).ValidationResults;return properties&&typeof properties=="object"&&!Array.isArray(properties)||errors.collect(new(cdk()).ValidationResult("Expected an object, but received: "+JSON.stringify(properties))),errors.collect(cdk().propertyValidator("key",cdk().requiredValidator)(properties.key)),errors.collect(cdk().propertyValidator("key",cdk().validateString)(properties.key)),errors.collect(cdk().propertyValidator("value",cdk().requiredValidator)(properties.value)),errors.collect(cdk().propertyValidator("value",CfnInstanceAccessControlAttributeConfigurationAccessControlAttributeValuePropertyValidator)(properties.value)),errors.wrap('supplied properties not correct for "AccessControlAttributeProperty"')}function convertCfnInstanceAccessControlAttributeConfigurationAccessControlAttributePropertyToCloudFormation(properties){return cdk().canInspect(properties)?(CfnInstanceAccessControlAttributeConfigurationAccessControlAttributePropertyValidator(properties).assertSuccess(),{Key:cdk().stringToCloudFormation(properties.key),Value:convertCfnInstanceAccessControlAttributeConfigurationAccessControlAttributeValuePropertyToCloudFormation(properties.value)}):properties}function CfnInstanceAccessControlAttributeConfigurationAccessControlAttributePropertyFromCloudFormation(properties){if(cdk().isResolvableObject(properties))return new(cfn_parse()).FromCloudFormationResult(properties);if(properties=properties??{},!(properties&&typeof properties=="object"&&!Array.isArray(properties)))return new(cfn_parse()).FromCloudFormationResult(properties);const ret=new(cfn_parse()).FromCloudFormationPropertyObject;return ret.addPropertyResult("key","Key",properties.Key!=null?cfn_parse().FromCloudFormation.getString(properties.Key):void 0),ret.addPropertyResult("value","Value",properties.Value!=null?CfnInstanceAccessControlAttributeConfigurationAccessControlAttributeValuePropertyFromCloudFormation(properties.Value):void 0),ret.addUnrecognizedPropertiesAsExtra(properties),ret}function CfnInstanceAccessControlAttributeConfigurationInstanceAccessControlAttributeConfigurationPropertyValidator(properties){if(!cdk().canInspect(properties))return cdk().VALIDATION_SUCCESS;const errors=new(cdk()).ValidationResults;return properties&&typeof properties=="object"&&!Array.isArray(properties)||errors.collect(new(cdk()).ValidationResult("Expected an object, but received: "+JSON.stringify(properties))),errors.collect(cdk().propertyValidator("accessControlAttributes",cdk().requiredValidator)(properties.accessControlAttributes)),errors.collect(cdk().propertyValidator("accessControlAttributes",cdk().listValidator(CfnInstanceAccessControlAttributeConfigurationAccessControlAttributePropertyValidator))(properties.accessControlAttributes)),errors.wrap('supplied properties not correct for "InstanceAccessControlAttributeConfigurationProperty"')}function convertCfnInstanceAccessControlAttributeConfigurationInstanceAccessControlAttributeConfigurationPropertyToCloudFormation(properties){return cdk().canInspect(properties)?(CfnInstanceAccessControlAttributeConfigurationInstanceAccessControlAttributeConfigurationPropertyValidator(properties).assertSuccess(),{AccessControlAttributes:cdk().listMapper(convertCfnInstanceAccessControlAttributeConfigurationAccessControlAttributePropertyToCloudFormation)(properties.accessControlAttributes)}):properties}function CfnInstanceAccessControlAttributeConfigurationInstanceAccessControlAttributeConfigurationPropertyFromCloudFormation(properties){if(cdk().isResolvableObject(properties))return new(cfn_parse()).FromCloudFormationResult(properties);if(properties=properties??{},!(properties&&typeof properties=="object"&&!Array.isArray(properties)))return new(cfn_parse()).FromCloudFormationResult(properties);const ret=new(cfn_parse()).FromCloudFormationPropertyObject;return ret.addPropertyResult("accessControlAttributes","AccessControlAttributes",properties.AccessControlAttributes!=null?cfn_parse().FromCloudFormation.getArray(CfnInstanceAccessControlAttributeConfigurationAccessControlAttributePropertyFromCloudFormation)(properties.AccessControlAttributes):void 0),ret.addUnrecognizedPropertiesAsExtra(properties),ret}function CfnInstanceAccessControlAttributeConfigurationPropsValidator(properties){if(!cdk().canInspect(properties))return cdk().VALIDATION_SUCCESS;const errors=new(cdk()).ValidationResults;return properties&&typeof properties=="object"&&!Array.isArray(properties)||errors.collect(new(cdk()).ValidationResult("Expected an object, but received: "+JSON.stringify(properties))),errors.collect(cdk().propertyValidator("accessControlAttributes",cdk().listValidator(CfnInstanceAccessControlAttributeConfigurationAccessControlAttributePropertyValidator))(properties.accessControlAttributes)),errors.collect(cdk().propertyValidator("instanceAccessControlAttributeConfiguration",CfnInstanceAccessControlAttributeConfigurationInstanceAccessControlAttributeConfigurationPropertyValidator)(properties.instanceAccessControlAttributeConfiguration)),errors.collect(cdk().propertyValidator("instanceArn",cdk().requiredValidator)(properties.instanceArn)),errors.collect(cdk().propertyValidator("instanceArn",cdk().validateString)(properties.instanceArn)),errors.wrap('supplied properties not correct for "CfnInstanceAccessControlAttributeConfigurationProps"')}function convertCfnInstanceAccessControlAttributeConfigurationPropsToCloudFormation(properties){return cdk().canInspect(properties)?(CfnInstanceAccessControlAttributeConfigurationPropsValidator(properties).assertSuccess(),{AccessControlAttributes:cdk().listMapper(convertCfnInstanceAccessControlAttributeConfigurationAccessControlAttributePropertyToCloudFormation)(properties.accessControlAttributes),InstanceAccessControlAttributeConfiguration:convertCfnInstanceAccessControlAttributeConfigurationInstanceAccessControlAttributeConfigurationPropertyToCloudFormation(properties.instanceAccessControlAttributeConfiguration),InstanceArn:cdk().stringToCloudFormation(properties.instanceArn)}):properties}function CfnInstanceAccessControlAttributeConfigurationPropsFromCloudFormation(properties){if(cdk().isResolvableObject(properties))return new(cfn_parse()).FromCloudFormationResult(properties);if(properties=properties??{},!(properties&&typeof properties=="object"&&!Array.isArray(properties)))return new(cfn_parse()).FromCloudFormationResult(properties);const ret=new(cfn_parse()).FromCloudFormationPropertyObject;return ret.addPropertyResult("accessControlAttributes","AccessControlAttributes",properties.AccessControlAttributes!=null?cfn_parse().FromCloudFormation.getArray(CfnInstanceAccessControlAttributeConfigurationAccessControlAttributePropertyFromCloudFormation)(properties.AccessControlAttributes):void 0),ret.addPropertyResult("instanceAccessControlAttributeConfiguration","InstanceAccessControlAttributeConfiguration",properties.InstanceAccessControlAttributeConfiguration!=null?CfnInstanceAccessControlAttributeConfigurationInstanceAccessControlAttributeConfigurationPropertyFromCloudFormation(properties.InstanceAccessControlAttributeConfiguration):void 0),ret.addPropertyResult("instanceArn","InstanceArn",properties.InstanceArn!=null?cfn_parse().FromCloudFormation.getString(properties.InstanceArn):void 0),ret.addUnrecognizedPropertiesAsExtra(properties),ret}class CfnPermissionSet extends cdk().CfnResource{static _fromCloudFormation(scope,id,resourceAttributes,options){resourceAttributes=resourceAttributes||{};const resourceProperties=options.parser.parseValue(resourceAttributes.Properties),propsResult=CfnPermissionSetPropsFromCloudFormation(resourceProperties);if(cdk().isResolvableObject(propsResult.value))throw new(cdk_errors()).ValidationError("Unexpected IResolvable",scope);const ret=new CfnPermissionSet(scope,id,propsResult.value);for(const[propKey,propVal]of Object.entries(propsResult.extraProperties))ret.addPropertyOverride(propKey,propVal);return options.parser.handleAttributes(ret,resourceAttributes,id),ret}constructor(scope,id,props){super(scope,id,{type:CfnPermissionSet.CFN_RESOURCE_TYPE_NAME,properties:props});try{jsiiDeprecationWarnings().aws_cdk_lib_aws_sso_CfnPermissionSetProps(props)}catch(error){throw process.env.JSII_DEBUG!=="1"&&error.name==="DeprecationError"&&Error.captureStackTrace(error,CfnPermissionSet),error}cdk().requireProperty(props,"instanceArn",this),cdk().requireProperty(props,"name",this),this.attrPermissionSetArn=cdk().Token.asString(this.getAtt("PermissionSetArn",cdk().ResolutionTypeHint.STRING)),this.customerManagedPolicyReferences=props.customerManagedPolicyReferences,this.description=props.description,this.inlinePolicy=props.inlinePolicy,this.instanceArn=props.instanceArn,this.managedPolicies=props.managedPolicies,this.name=props.name,this.permissionsBoundary=props.permissionsBoundary,this.relayStateType=props.relayStateType,this.sessionDuration=props.sessionDuration,this.tags=new(cdk()).TagManager(cdk().TagType.STANDARD,"AWS::SSO::PermissionSet",props.tags,{tagPropertyName:"tags"}),this.tagsRaw=props.tags}get cfnProperties(){return{customerManagedPolicyReferences:this.customerManagedPolicyReferences,description:this.description,inlinePolicy:this.inlinePolicy,instanceArn:this.instanceArn,managedPolicies:this.managedPolicies,name:this.name,permissionsBoundary:this.permissionsBoundary,relayStateType:this.relayStateType,sessionDuration:this.sessionDuration,tags:this.tags.renderTags()}}inspect(inspector){try{jsiiDeprecationWarnings().aws_cdk_lib_TreeInspector(inspector)}catch(error){throw process.env.JSII_DEBUG!=="1"&&error.name==="DeprecationError"&&Error.captureStackTrace(error,this.inspect),error}inspector.addAttribute("aws:cdk:cloudformation:type",CfnPermissionSet.CFN_RESOURCE_TYPE_NAME),inspector.addAttribute("aws:cdk:cloudformation:props",this.cfnProperties)}renderProperties(props){return convertCfnPermissionSetPropsToCloudFormation(props)}}exports.CfnPermissionSet=CfnPermissionSet,_c=JSII_RTTI_SYMBOL_1,CfnPermissionSet[_c]={fqn:"aws-cdk-lib.aws_sso.CfnPermissionSet",version:"2.201.0"},CfnPermissionSet.CFN_RESOURCE_TYPE_NAME="AWS::SSO::PermissionSet";function CfnPermissionSetCustomerManagedPolicyReferencePropertyValidator(properties){if(!cdk().canInspect(properties))return cdk().VALIDATION_SUCCESS;const errors=new(cdk()).ValidationResults;return properties&&typeof properties=="object"&&!Array.isArray(properties)||errors.collect(new(cdk()).ValidationResult("Expected an object, but received: "+JSON.stringify(properties))),errors.collect(cdk().propertyValidator("name",cdk().requiredValidator)(properties.name)),errors.collect(cdk().propertyValidator("name",cdk().validateString)(properties.name)),errors.collect(cdk().propertyValidator("path",cdk().validateString)(properties.path)),errors.wrap('supplied properties not correct for "CustomerManagedPolicyReferenceProperty"')}function convertCfnPermissionSetCustomerManagedPolicyReferencePropertyToCloudFormation(properties){return cdk().canInspect(properties)?(CfnPermissionSetCustomerManagedPolicyReferencePropertyValidator(properties).assertSuccess(),{Name:cdk().stringToCloudFormation(properties.name),Path:cdk().stringToCloudFormation(properties.path)}):properties}function CfnPermissionSetCustomerManagedPolicyReferencePropertyFromCloudFormation(properties){if(cdk().isResolvableObject(properties))return new(cfn_parse()).FromCloudFormationResult(properties);if(properties=properties??{},!(properties&&typeof properties=="object"&&!Array.isArray(properties)))return new(cfn_parse()).FromCloudFormationResult(properties);const ret=new(cfn_parse()).FromCloudFormationPropertyObject;return ret.addPropertyResult("name","Name",properties.Name!=null?cfn_parse().FromCloudFormation.getString(properties.Name):void 0),ret.addPropertyResult("path","Path",properties.Path!=null?cfn_parse().FromCloudFormation.getString(properties.Path):void 0),ret.addUnrecognizedPropertiesAsExtra(properties),ret}function CfnPermissionSetPermissionsBoundaryPropertyValidator(properties){if(!cdk().canInspect(properties))return cdk().VALIDATION_SUCCESS;const errors=new(cdk()).ValidationResults;return properties&&typeof properties=="object"&&!Array.isArray(properties)||errors.collect(new(cdk()).ValidationResult("Expected an object, but received: "+JSON.stringify(properties))),errors.collect(cdk().propertyValidator("customerManagedPolicyReference",CfnPermissionSetCustomerManagedPolicyReferencePropertyValidator)(properties.customerManagedPolicyReference)),errors.collect(cdk().propertyValidator("managedPolicyArn",cdk().validateString)(properties.managedPolicyArn)),errors.wrap('supplied properties not correct for "PermissionsBoundaryProperty"')}function convertCfnPermissionSetPermissionsBoundaryPropertyToCloudFormation(properties){return cdk().canInspect(properties)?(CfnPermissionSetPermissionsBoundaryPropertyValidator(properties).assertSuccess(),{CustomerManagedPolicyReference:convertCfnPermissionSetCustomerManagedPolicyReferencePropertyToCloudFormation(properties.customerManagedPolicyReference),ManagedPolicyArn:cdk().stringToCloudFormation(properties.managedPolicyArn)}):properties}function CfnPermissionSetPermissionsBoundaryPropertyFromCloudFormation(properties){if(cdk().isResolvableObject(properties))return new(cfn_parse()).FromCloudFormationResult(properties);if(properties=properties??{},!(properties&&typeof properties=="object"&&!Array.isArray(properties)))return new(cfn_parse()).FromCloudFormationResult(properties);const ret=new(cfn_parse()).FromCloudFormationPropertyObject;return ret.addPropertyResult("customerManagedPolicyReference","CustomerManagedPolicyReference",properties.CustomerManagedPolicyReference!=null?CfnPermissionSetCustomerManagedPolicyReferencePropertyFromCloudFormation(properties.CustomerManagedPolicyReference):void 0),ret.addPropertyResult("managedPolicyArn","ManagedPolicyArn",properties.ManagedPolicyArn!=null?cfn_parse().FromCloudFormation.getString(properties.ManagedPolicyArn):void 0),ret.addUnrecognizedPropertiesAsExtra(properties),ret}function CfnPermissionSetPropsValidator(properties){if(!cdk().canInspect(properties))return cdk().VALIDATION_SUCCESS;const errors=new(cdk()).ValidationResults;return properties&&typeof properties=="object"&&!Array.isArray(properties)||errors.collect(new(cdk()).ValidationResult("Expected an object, but received: "+JSON.stringify(properties))),errors.collect(cdk().propertyValidator("customerManagedPolicyReferences",cdk().listValidator(CfnPermissionSetCustomerManagedPolicyReferencePropertyValidator))(properties.customerManagedPolicyReferences)),errors.collect(cdk().propertyValidator("description",cdk().validateString)(properties.description)),errors.collect(cdk().propertyValidator("inlinePolicy",cdk().validateObject)(properties.inlinePolicy)),errors.collect(cdk().propertyValidator("instanceArn",cdk().requiredValidator)(properties.instanceArn)),errors.collect(cdk().propertyValidator("instanceArn",cdk().validateString)(properties.instanceArn)),errors.collect(cdk().propertyValidator("managedPolicies",cdk().listValidator(cdk().validateString))(properties.managedPolicies)),errors.collect(cdk().propertyValidator("name",cdk().requiredValidator)(properties.name)),errors.collect(cdk().propertyValidator("name",cdk().validateString)(properties.name)),errors.collect(cdk().propertyValidator("permissionsBoundary",CfnPermissionSetPermissionsBoundaryPropertyValidator)(properties.permissionsBoundary)),errors.collect(cdk().propertyValidator("relayStateType",cdk().validateString)(properties.relayStateType)),errors.collect(cdk().propertyValidator("sessionDuration",cdk().validateString)(properties.sessionDuration)),errors.collect(cdk().propertyValidator("tags",cdk().listValidator(cdk().validateCfnTag))(properties.tags)),errors.wrap('supplied properties not correct for "CfnPermissionSetProps"')}function convertCfnPermissionSetPropsToCloudFormation(properties){return cdk().canInspect(properties)?(CfnPermissionSetPropsValidator(properties).assertSuccess(),{CustomerManagedPolicyReferences:cdk().listMapper(convertCfnPermissionSetCustomerManagedPolicyReferencePropertyToCloudFormation)(properties.customerManagedPolicyReferences),Description:cdk().stringToCloudFormation(properties.description),InlinePolicy:cdk().objectToCloudFormation(properties.inlinePolicy),InstanceArn:cdk().stringToCloudFormation(properties.instanceArn),ManagedPolicies:cdk().listMapper(cdk().stringToCloudFormation)(properties.managedPolicies),Name:cdk().stringToCloudFormation(properties.name),PermissionsBoundary:convertCfnPermissionSetPermissionsBoundaryPropertyToCloudFormation(properties.permissionsBoundary),RelayStateType:cdk().stringToCloudFormation(properties.relayStateType),SessionDuration:cdk().stringToCloudFormation(properties.sessionDuration),Tags:cdk().listMapper(cdk().cfnTagToCloudFormation)(properties.tags)}):properties}function CfnPermissionSetPropsFromCloudFormation(properties){if(cdk().isResolvableObject(properties))return new(cfn_parse()).FromCloudFormationResult(properties);if(properties=properties??{},!(properties&&typeof properties=="object"&&!Array.isArray(properties)))return new(cfn_parse()).FromCloudFormationResult(properties);const ret=new(cfn_parse()).FromCloudFormationPropertyObject;return ret.addPropertyResult("customerManagedPolicyReferences","CustomerManagedPolicyReferences",properties.CustomerManagedPolicyReferences!=null?cfn_parse().FromCloudFormation.getArray(CfnPermissionSetCustomerManagedPolicyReferencePropertyFromCloudFormation)(properties.CustomerManagedPolicyReferences):void 0),ret.addPropertyResult("description","Description",properties.Description!=null?cfn_parse().FromCloudFormation.getString(properties.Description):void 0),ret.addPropertyResult("inlinePolicy","InlinePolicy",properties.InlinePolicy!=null?cfn_parse().FromCloudFormation.getAny(properties.InlinePolicy):void 0),ret.addPropertyResult("instanceArn","InstanceArn",properties.InstanceArn!=null?cfn_parse().FromCloudFormation.getString(properties.InstanceArn):void 0),ret.addPropertyResult("managedPolicies","ManagedPolicies",properties.ManagedPolicies!=null?cfn_parse().FromCloudFormation.getArray(cfn_parse().FromCloudFormation.getString)(properties.ManagedPolicies):void 0),ret.addPropertyResult("name","Name",properties.Name!=null?cfn_parse().FromCloudFormation.getString(properties.Name):void 0),ret.addPropertyResult("permissionsBoundary","PermissionsBoundary",properties.PermissionsBoundary!=null?CfnPermissionSetPermissionsBoundaryPropertyFromCloudFormation(properties.PermissionsBoundary):void 0),ret.addPropertyResult("relayStateType","RelayStateType",properties.RelayStateType!=null?cfn_parse().FromCloudFormation.getString(properties.RelayStateType):void 0),ret.addPropertyResult("sessionDuration","SessionDuration",properties.SessionDuration!=null?cfn_parse().FromCloudFormation.getString(properties.SessionDuration):void 0),ret.addPropertyResult("tags","Tags",properties.Tags!=null?cfn_parse().FromCloudFormation.getArray(cfn_parse().FromCloudFormation.getCfnTag)(properties.Tags):void 0),ret.addUnrecognizedPropertiesAsExtra(properties),ret}class CfnApplication extends cdk().CfnResource{static _fromCloudFormation(scope,id,resourceAttributes,options){resourceAttributes=resourceAttributes||{};const resourceProperties=options.parser.parseValue(resourceAttributes.Properties),propsResult=CfnApplicationPropsFromCloudFormation(resourceProperties);if(cdk().isResolvableObject(propsResult.value))throw new(cdk_errors()).ValidationError("Unexpected IResolvable",scope);const ret=new CfnApplication(scope,id,propsResult.value);for(const[propKey,propVal]of Object.entries(propsResult.extraProperties))ret.addPropertyOverride(propKey,propVal);return options.parser.handleAttributes(ret,resourceAttributes,id),ret}constructor(scope,id,props){super(scope,id,{type:CfnApplication.CFN_RESOURCE_TYPE_NAME,properties:props});try{jsiiDeprecationWarnings().aws_cdk_lib_aws_sso_CfnApplicationProps(props)}catch(error){throw process.env.JSII_DEBUG!=="1"&&error.name==="DeprecationError"&&Error.captureStackTrace(error,CfnApplication),error}cdk().requireProperty(props,"applicationProviderArn",this),cdk().requireProperty(props,"instanceArn",this),cdk().requireProperty(props,"name",this),this.attrApplicationArn=cdk().Token.asString(this.getAtt("ApplicationArn",cdk().ResolutionTypeHint.STRING)),this.applicationProviderArn=props.applicationProviderArn,this.cdkTagManager=new(cdk()).TagManager(cdk().TagType.STANDARD,"AWS::SSO::Application",void 0,{tagPropertyName:"tags"}),this.description=props.description,this.instanceArn=props.instanceArn,this.name=props.name,this.portalOptions=props.portalOptions,this.status=props.status,this.tags=props.tags}get cfnProperties(){return{applicationProviderArn:this.applicationProviderArn,tags:this.cdkTagManager.renderTags(this.tags),description:this.description,instanceArn:this.instanceArn,name:this.name,portalOptions:this.portalOptions,status:this.status}}inspect(inspector){try{jsiiDeprecationWarnings().aws_cdk_lib_TreeInspector(inspector)}catch(error){throw process.env.JSII_DEBUG!=="1"&&error.name==="DeprecationError"&&Error.captureStackTrace(error,this.inspect),error}inspector.addAttribute("aws:cdk:cloudformation:type",CfnApplication.CFN_RESOURCE_TYPE_NAME),inspector.addAttribute("aws:cdk:cloudformation:props",this.cfnProperties)}renderProperties(props){return convertCfnApplicationPropsToCloudFormation(props)}}exports.CfnApplication=CfnApplication,_d=JSII_RTTI_SYMBOL_1,CfnApplication[_d]={fqn:"aws-cdk-lib.aws_sso.CfnApplication",version:"2.201.0"},CfnApplication.CFN_RESOURCE_TYPE_NAME="AWS::SSO::Application";function CfnApplicationSignInOptionsPropertyValidator(properties){if(!cdk().canInspect(properties))return cdk().VALIDATION_SUCCESS;const errors=new(cdk()).ValidationResults;return properties&&typeof properties=="object"&&!Array.isArray(properties)||errors.collect(new(cdk()).ValidationResult("Expected an object, but received: "+JSON.stringify(properties))),errors.collect(cdk().propertyValidator("applicationUrl",cdk().validateString)(properties.applicationUrl)),errors.collect(cdk().propertyValidator("origin",cdk().requiredValidator)(properties.origin)),errors.collect(cdk().propertyValidator("origin",cdk().validateString)(properties.origin)),errors.wrap('supplied properties not correct for "SignInOptionsProperty"')}function convertCfnApplicationSignInOptionsPropertyToCloudFormation(properties){return cdk().canInspect(properties)?(CfnApplicationSignInOptionsPropertyValidator(properties).assertSuccess(),{ApplicationUrl:cdk().stringToCloudFormation(properties.applicationUrl),Origin:cdk().stringToCloudFormation(properties.origin)}):properties}function CfnApplicationSignInOptionsPropertyFromCloudFormation(properties){if(cdk().isResolvableObject(properties))return new(cfn_parse()).FromCloudFormationResult(properties);if(properties=properties??{},!(properties&&typeof properties=="object"&&!Array.isArray(properties)))return new(cfn_parse()).FromCloudFormationResult(properties);const ret=new(cfn_parse()).FromCloudFormationPropertyObject;return ret.addPropertyResult("applicationUrl","ApplicationUrl",properties.ApplicationUrl!=null?cfn_parse().FromCloudFormation.getString(properties.ApplicationUrl):void 0),ret.addPropertyResult("origin","Origin",properties.Origin!=null?cfn_parse().FromCloudFormation.getString(properties.Origin):void 0),ret.addUnrecognizedPropertiesAsExtra(properties),ret}function CfnApplicationPortalOptionsConfigurationPropertyValidator(properties){if(!cdk().canInspect(properties))return cdk().VALIDATION_SUCCESS;const errors=new(cdk()).ValidationResults;return properties&&typeof properties=="object"&&!Array.isArray(properties)||errors.collect(new(cdk()).ValidationResult("Expected an object, but received: "+JSON.stringify(properties))),errors.collect(cdk().propertyValidator("signInOptions",CfnApplicationSignInOptionsPropertyValidator)(properties.signInOptions)),errors.collect(cdk().propertyValidator("visibility",cdk().validateString)(properties.visibility)),errors.wrap('supplied properties not correct for "PortalOptionsConfigurationProperty"')}function convertCfnApplicationPortalOptionsConfigurationPropertyToCloudFormation(properties){return cdk().canInspect(properties)?(CfnApplicationPortalOptionsConfigurationPropertyValidator(properties).assertSuccess(),{SignInOptions:convertCfnApplicationSignInOptionsPropertyToCloudFormation(properties.signInOptions),Visibility:cdk().stringToCloudFormation(properties.visibility)}):properties}function CfnApplicationPortalOptionsConfigurationPropertyFromCloudFormation(properties){if(cdk().isResolvableObject(properties))return new(cfn_parse()).FromCloudFormationResult(properties);if(properties=properties??{},!(properties&&typeof properties=="object"&&!Array.isArray(properties)))return new(cfn_parse()).FromCloudFormationResult(properties);const ret=new(cfn_parse()).FromCloudFormationPropertyObject;return ret.addPropertyResult("signInOptions","SignInOptions",properties.SignInOptions!=null?CfnApplicationSignInOptionsPropertyFromCloudFormation(properties.SignInOptions):void 0),ret.addPropertyResult("visibility","Visibility",properties.Visibility!=null?cfn_parse().FromCloudFormation.getString(properties.Visibility):void 0),ret.addUnrecognizedPropertiesAsExtra(properties),ret}function CfnApplicationPropsValidator(properties){if(!cdk().canInspect(properties))return cdk().VALIDATION_SUCCESS;const errors=new(cdk()).ValidationResults;return properties&&typeof properties=="object"&&!Array.isArray(properties)||errors.collect(new(cdk()).ValidationResult("Expected an object, but received: "+JSON.stringify(properties))),errors.collect(cdk().propertyValidator("applicationProviderArn",cdk().requiredValidator)(properties.applicationProviderArn)),errors.collect(cdk().propertyValidator("applicationProviderArn",cdk().validateString)(properties.applicationProviderArn)),errors.collect(cdk().propertyValidator("description",cdk().validateString)(properties.description)),errors.collect(cdk().propertyValidator("instanceArn",cdk().requiredValidator)(properties.instanceArn)),errors.collect(cdk().propertyValidator("instanceArn",cdk().validateString)(properties.instanceArn)),errors.collect(cdk().propertyValidator("name",cdk().requiredValidator)(properties.name)),errors.collect(cdk().propertyValidator("name",cdk().validateString)(properties.name)),errors.collect(cdk().propertyValidator("portalOptions",CfnApplicationPortalOptionsConfigurationPropertyValidator)(properties.portalOptions)),errors.collect(cdk().propertyValidator("status",cdk().validateString)(properties.status)),errors.collect(cdk().propertyValidator("tags",cdk().listValidator(cdk().validateCfnTag))(properties.tags)),errors.wrap('supplied properties not correct for "CfnApplicationProps"')}function convertCfnApplicationPropsToCloudFormation(properties){return cdk().canInspect(properties)?(CfnApplicationPropsValidator(properties).assertSuccess(),{ApplicationProviderArn:cdk().stringToCloudFormation(properties.applicationProviderArn),Description:cdk().stringToCloudFormation(properties.description),InstanceArn:cdk().stringToCloudFormation(properties.instanceArn),Name:cdk().stringToCloudFormation(properties.name),PortalOptions:convertCfnApplicationPortalOptionsConfigurationPropertyToCloudFormation(properties.portalOptions),Status:cdk().stringToCloudFormation(properties.status),Tags:cdk().listMapper(cdk().cfnTagToCloudFormation)(properties.tags)}):properties}function CfnApplicationPropsFromCloudFormation(properties){if(cdk().isResolvableObject(properties))return new(cfn_parse()).FromCloudFormationResult(properties);if(properties=properties??{},!(properties&&typeof properties=="object"&&!Array.isArray(properties)))return new(cfn_parse()).FromCloudFormationResult(properties);const ret=new(cfn_parse()).FromCloudFormationPropertyObject;return ret.addPropertyResult("applicationProviderArn","ApplicationProviderArn",properties.ApplicationProviderArn!=null?cfn_parse().FromCloudFormation.getString(properties.ApplicationProviderArn):void 0),ret.addPropertyResult("description","Description",properties.Description!=null?cfn_parse().FromCloudFormation.getString(properties.Description):void 0),ret.addPropertyResult("instanceArn","InstanceArn",properties.InstanceArn!=null?cfn_parse().FromCloudFormation.getString(properties.InstanceArn):void 0),ret.addPropertyResult("name","Name",properties.Name!=null?cfn_parse().FromCloudFormation.getString(properties.Name):void 0),ret.addPropertyResult("portalOptions","PortalOptions",properties.PortalOptions!=null?CfnApplicationPortalOptionsConfigurationPropertyFromCloudFormation(properties.PortalOptions):void 0),ret.addPropertyResult("status","Status",properties.Status!=null?cfn_parse().FromCloudFormation.getString(properties.Status):void 0),ret.addPropertyResult("tags","Tags",properties.Tags!=null?cfn_parse().FromCloudFormation.getArray(cfn_parse().FromCloudFormation.getCfnTag)(properties.Tags):void 0),ret.addUnrecognizedPropertiesAsExtra(properties),ret}class CfnApplicationAssignment extends cdk().CfnResource{static _fromCloudFormation(scope,id,resourceAttributes,options){resourceAttributes=resourceAttributes||{};const resourceProperties=options.parser.parseValue(resourceAttributes.Properties),propsResult=CfnApplicationAssignmentPropsFromCloudFormation(resourceProperties);if(cdk().isResolvableObject(propsResult.value))throw new(cdk_errors()).ValidationError("Unexpected IResolvable",scope);const ret=new CfnApplicationAssignment(scope,id,propsResult.value);for(const[propKey,propVal]of Object.entries(propsResult.extraProperties))ret.addPropertyOverride(propKey,propVal);return options.parser.handleAttributes(ret,resourceAttributes,id),ret}constructor(scope,id,props){super(scope,id,{type:CfnApplicationAssignment.CFN_RESOURCE_TYPE_NAME,properties:props});try{jsiiDeprecationWarnings().aws_cdk_lib_aws_sso_CfnApplicationAssignmentProps(props)}catch(error){throw process.env.JSII_DEBUG!=="1"&&error.name==="DeprecationError"&&Error.captureStackTrace(error,CfnApplicationAssignment),error}cdk().requireProperty(props,"applicationArn",this),cdk().requireProperty(props,"principalId",this),cdk().requireProperty(props,"principalType",this),this.applicationArn=props.applicationArn,this.principalId=props.principalId,this.principalType=props.principalType}get cfnProperties(){return{applicationArn:this.applicationArn,principalId:this.principalId,principalType:this.principalType}}inspect(inspector){try{jsiiDeprecationWarnings().aws_cdk_lib_TreeInspector(inspector)}catch(error){throw process.env.JSII_DEBUG!=="1"&&error.name==="DeprecationError"&&Error.captureStackTrace(error,this.inspect),error}inspector.addAttribute("aws:cdk:cloudformation:type",CfnApplicationAssignment.CFN_RESOURCE_TYPE_NAME),inspector.addAttribute("aws:cdk:cloudformation:props",this.cfnProperties)}renderProperties(props){return convertCfnApplicationAssignmentPropsToCloudFormation(props)}}exports.CfnApplicationAssignment=CfnApplicationAssignment,_e=JSII_RTTI_SYMBOL_1,CfnApplicationAssignment[_e]={fqn:"aws-cdk-lib.aws_sso.CfnApplicationAssignment",version:"2.201.0"},CfnApplicationAssignment.CFN_RESOURCE_TYPE_NAME="AWS::SSO::ApplicationAssignment";function CfnApplicationAssignmentPropsValidator(properties){if(!cdk().canInspect(properties))return cdk().VALIDATION_SUCCESS;const errors=new(cdk()).ValidationResults;return properties&&typeof properties=="object"&&!Array.isArray(properties)||errors.collect(new(cdk()).ValidationResult("Expected an object, but received: "+JSON.stringify(properties))),errors.collect(cdk().propertyValidator("applicationArn",cdk().requiredValidator)(properties.applicationArn)),errors.collect(cdk().propertyValidator("applicationArn",cdk().validateString)(properties.applicationArn)),errors.collect(cdk().propertyValidator("principalId",cdk().requiredValidator)(properties.principalId)),errors.collect(cdk().propertyValidator("principalId",cdk().validateString)(properties.principalId)),errors.collect(cdk().propertyValidator("principalType",cdk().requiredValidator)(properties.principalType)),errors.collect(cdk().propertyValidator("principalType",cdk().validateString)(properties.principalType)),errors.wrap('supplied properties not correct for "CfnApplicationAssignmentProps"')}function convertCfnApplicationAssignmentPropsToCloudFormation(properties){return cdk().canInspect(properties)?(CfnApplicationAssignmentPropsValidator(properties).assertSuccess(),{ApplicationArn:cdk().stringToCloudFormation(properties.applicationArn),PrincipalId:cdk().stringToCloudFormation(properties.principalId),PrincipalType:cdk().stringToCloudFormation(properties.principalType)}):properties}function CfnApplicationAssignmentPropsFromCloudFormation(properties){if(cdk().isResolvableObject(properties))return new(cfn_parse()).FromCloudFormationResult(properties);if(properties=properties??{},!(properties&&typeof properties=="object"&&!Array.isArray(properties)))return new(cfn_parse()).FromCloudFormationResult(properties);const ret=new(cfn_parse()).FromCloudFormationPropertyObject;return ret.addPropertyResult("applicationArn","ApplicationArn",properties.ApplicationArn!=null?cfn_parse().FromCloudFormation.getString(properties.ApplicationArn):void 0),ret.addPropertyResult("principalId","PrincipalId",properties.PrincipalId!=null?cfn_parse().FromCloudFormation.getString(properties.PrincipalId):void 0),ret.addPropertyResult("principalType","PrincipalType",properties.PrincipalType!=null?cfn_parse().FromCloudFormation.getString(properties.PrincipalType):void 0),ret.addUnrecognizedPropertiesAsExtra(properties),ret}class CfnInstance extends cdk().CfnResource{static _fromCloudFormation(scope,id,resourceAttributes,options){resourceAttributes=resourceAttributes||{};const resourceProperties=options.parser.parseValue(resourceAttributes.Properties),propsResult=CfnInstancePropsFromCloudFormation(resourceProperties);if(cdk().isResolvableObject(propsResult.value))throw new(cdk_errors()).ValidationError("Unexpected IResolvable",scope);const ret=new CfnInstance(scope,id,propsResult.value);for(const[propKey,propVal]of Object.entries(propsResult.extraProperties))ret.addPropertyOverride(propKey,propVal);return options.parser.handleAttributes(ret,resourceAttributes,id),ret}constructor(scope,id,props={}){super(scope,id,{type:CfnInstance.CFN_RESOURCE_TYPE_NAME,properties:props});try{jsiiDeprecationWarnings().aws_cdk_lib_aws_sso_CfnInstanceProps(props)}catch(error){throw process.env.JSII_DEBUG!=="1"&&error.name==="DeprecationError"&&Error.captureStackTrace(error,CfnInstance),error}this.attrIdentityStoreId=cdk().Token.asString(this.getAtt("IdentityStoreId",cdk().ResolutionTypeHint.STRING)),this.attrInstanceArn=cdk().Token.asString(this.getAtt("InstanceArn",cdk().ResolutionTypeHint.STRING)),this.attrOwnerAccountId=cdk().Token.asString(this.getAtt("OwnerAccountId",cdk().ResolutionTypeHint.STRING)),this.attrStatus=cdk().Token.asString(this.getAtt("Status",cdk().ResolutionTypeHint.STRING)),this.cdkTagManager=new(cdk()).TagManager(cdk().TagType.STANDARD,"AWS::SSO::Instance",void 0,{tagPropertyName:"tags"}),this.name=props.name,this.tags=props.tags}get cfnProperties(){return{tags:this.cdkTagManager.renderTags(this.tags),name:this.name}}inspect(inspector){try{jsiiDeprecationWarnings().aws_cdk_lib_TreeInspector(inspector)}catch(error){throw process.env.JSII_DEBUG!=="1"&&error.name==="DeprecationError"&&Error.captureStackTrace(error,this.inspect),error}inspector.addAttribute("aws:cdk:cloudformation:type",CfnInstance.CFN_RESOURCE_TYPE_NAME),inspector.addAttribute("aws:cdk:cloudformation:props",this.cfnProperties)}renderProperties(props){return convertCfnInstancePropsToCloudFormation(props)}}exports.CfnInstance=CfnInstance,_f=JSII_RTTI_SYMBOL_1,CfnInstance[_f]={fqn:"aws-cdk-lib.aws_sso.CfnInstance",version:"2.201.0"},CfnInstance.CFN_RESOURCE_TYPE_NAME="AWS::SSO::Instance";function CfnInstancePropsValidator(properties){if(!cdk().canInspect(properties))return cdk().VALIDATION_SUCCESS;const errors=new(cdk()).ValidationResults;return properties&&typeof properties=="object"&&!Array.isArray(properties)||errors.collect(new(cdk()).ValidationResult("Expected an object, but received: "+JSON.stringify(properties))),errors.collect(cdk().propertyValidator("name",cdk().validateString)(properties.name)),errors.collect(cdk().propertyValidator("tags",cdk().listValidator(cdk().validateCfnTag))(properties.tags)),errors.wrap('supplied properties not correct for "CfnInstanceProps"')}function convertCfnInstancePropsToCloudFormation(properties){return cdk().canInspect(properties)?(CfnInstancePropsValidator(properties).assertSuccess(),{Name:cdk().stringToCloudFormation(properties.name),Tags:cdk().listMapper(cdk().cfnTagToCloudFormation)(properties.tags)}):properties}function CfnInstancePropsFromCloudFormation(properties){if(cdk().isResolvableObject(properties))return new(cfn_parse()).FromCloudFormationResult(properties);if(properties=properties??{},!(properties&&typeof properties=="object"&&!Array.isArray(properties)))return new(cfn_parse()).FromCloudFormationResult(properties);const ret=new(cfn_parse()).FromCloudFormationPropertyObject;return ret.addPropertyResult("name","Name",properties.Name!=null?cfn_parse().FromCloudFormation.getString(properties.Name):void 0),ret.addPropertyResult("tags","Tags",properties.Tags!=null?cfn_parse().FromCloudFormation.getArray(cfn_parse().FromCloudFormation.getCfnTag)(properties.Tags):void 0),ret.addUnrecognizedPropertiesAsExtra(properties),ret}
