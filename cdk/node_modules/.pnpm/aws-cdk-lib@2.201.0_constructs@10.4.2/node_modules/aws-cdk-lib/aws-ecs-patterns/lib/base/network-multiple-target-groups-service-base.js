"use strict";var _a;Object.defineProperty(exports,"__esModule",{value:!0}),exports.NetworkMultipleTargetGroupsServiceBase=void 0;var jsiiDeprecationWarnings=()=>{var tmp=require("../../../.warnings.jsii.js");return jsiiDeprecationWarnings=()=>tmp,tmp};const JSII_RTTI_SYMBOL_1=Symbol.for("jsii.rtti");var constructs_1=()=>{var tmp=require("constructs");return constructs_1=()=>tmp,tmp},aws_ecs_1=()=>{var tmp=require("../../../aws-ecs");return aws_ecs_1=()=>tmp,tmp},aws_elasticloadbalancingv2_1=()=>{var tmp=require("../../../aws-elasticloadbalancingv2");return aws_elasticloadbalancingv2_1=()=>tmp,tmp},aws_route53_1=()=>{var tmp=require("../../../aws-route53");return aws_route53_1=()=>tmp,tmp},aws_route53_targets_1=()=>{var tmp=require("../../../aws-route53-targets");return aws_route53_targets_1=()=>tmp,tmp},core_1=()=>{var tmp=require("../../../core");return core_1=()=>tmp,tmp};class NetworkMultipleTargetGroupsServiceBase extends constructs_1().Construct{constructor(scope,id,props={}){super(scope,id),this.listeners=new Array,this.targetGroups=new Array,this.loadBalancers=new Array;try{jsiiDeprecationWarnings().aws_cdk_lib_aws_ecs_patterns_NetworkMultipleTargetGroupsServiceBaseProps(props)}catch(error){throw process.env.JSII_DEBUG!=="1"&&error.name==="DeprecationError"&&Error.captureStackTrace(error,NetworkMultipleTargetGroupsServiceBase),error}if(this.validateInput(props),this.cluster=props.cluster||this.getDefaultCluster(this,props.vpc),this.desiredCount=props.desiredCount||1,this.internalDesiredCount=props.desiredCount,props.taskImageOptions&&(this.logDriver=this.createLogDriver(props.taskImageOptions.enableLogging,props.taskImageOptions.logDriver)),props.loadBalancers){for(const lbProps of props.loadBalancers){const lb=this.createLoadBalancer(lbProps.name,lbProps.publicLoadBalancer);this.loadBalancers.push(lb);for(const listenerProps of lbProps.listeners){const listener=this.createListener(listenerProps.name,lb,listenerProps.port||80);this.listeners.push(listener)}this.createDomainName(lb,lbProps.domainName,lbProps.domainZone),new(core_1()).CfnOutput(this,`LoadBalancerDNS${lb.node.id}`,{value:lb.loadBalancerDnsName})}this.loadBalancer=this.loadBalancers[0],this.listener=this.listeners[0]}else this.loadBalancer=this.createLoadBalancer("LB"),this.listener=this.createListener("PublicListener",this.loadBalancer,80),this.createDomainName(this.loadBalancer),new(core_1()).CfnOutput(this,"LoadBalancerDNS",{value:this.loadBalancer.loadBalancerDnsName})}getDefaultCluster(scope,vpc){try{jsiiDeprecationWarnings().aws_cdk_lib_aws_ec2_IVpc(vpc)}catch(error){throw process.env.JSII_DEBUG!=="1"&&error.name==="DeprecationError"&&Error.captureStackTrace(error,this.getDefaultCluster),error}const DEFAULT_CLUSTER_ID=`EcsDefaultClusterMnL3mNNYN${vpc?vpc.node.id:""}`,stack=core_1().Stack.of(scope);return stack.node.tryFindChild(DEFAULT_CLUSTER_ID)||new(aws_ecs_1()).Cluster(stack,DEFAULT_CLUSTER_ID,{vpc})}createAWSLogDriver(prefix){return new(aws_ecs_1()).AwsLogDriver({streamPrefix:prefix})}findListener(name){if(!name)return this.listener;for(const listener of this.listeners)if(listener.node.id===name)return listener;throw new(core_1()).ValidationError(`Listener ${name} is not defined. Did you define listener with name ${name}?`,this)}registerECSTargets(service,container,targets){try{jsiiDeprecationWarnings().aws_cdk_lib_aws_ecs_BaseService(service),jsiiDeprecationWarnings().aws_cdk_lib_aws_ecs_ContainerDefinition(container)}catch(error){throw process.env.JSII_DEBUG!=="1"&&error.name==="DeprecationError"&&Error.captureStackTrace(error,this.registerECSTargets),error}for(const targetProps of targets){const targetGroup=this.findListener(targetProps.listener).addTargets(`ECSTargetGroup${container.containerName}${targetProps.containerPort}`,{port:targetProps.containerPort??80,targets:[service.loadBalancerTarget({containerName:container.containerName,containerPort:targetProps.containerPort})]});this.targetGroups.push(targetGroup)}if(this.targetGroups.length===0)throw new(core_1()).ValidationError("At least one target group should be specified.",this);return this.targetGroups[0]}addPortMappingForTargets(container,targets){try{jsiiDeprecationWarnings().aws_cdk_lib_aws_ecs_ContainerDefinition(container)}catch(error){throw process.env.JSII_DEBUG!=="1"&&error.name==="DeprecationError"&&Error.captureStackTrace(error,this.addPortMappingForTargets),error}for(const target of targets)container.findPortMapping(target.containerPort,aws_ecs_1().Protocol.TCP)||container.addPortMappings({containerPort:target.containerPort})}createLogDriver(enableLoggingProp,logDriverProp){return logDriverProp??(enableLoggingProp??!0?this.createAWSLogDriver(this.node.id):void 0)}validateInput(props){if(props.cluster&&props.vpc)throw new(core_1()).ValidationError("You can only specify either vpc or cluster. Alternatively, you can leave both blank",this);if(props.desiredCount!==void 0&&props.desiredCount<1)throw new(core_1()).ValidationError("You must specify a desiredCount greater than 0",this);if(props.loadBalancers){if(props.loadBalancers.length===0)throw new(core_1()).ValidationError("At least one load balancer must be specified",this);for(const lbProps of props.loadBalancers)if(lbProps.listeners.length===0)throw new(core_1()).ValidationError("At least one listener must be specified",this)}}createLoadBalancer(name,publicLoadBalancer){const internetFacing=publicLoadBalancer??!0,lbProps={vpc:this.cluster.vpc,internetFacing};return new(aws_elasticloadbalancingv2_1()).NetworkLoadBalancer(this,name,lbProps)}createListener(name,lb,port){return lb.addListener(name,{port})}createDomainName(loadBalancer,name,zone){if(typeof name<"u"){if(typeof zone>"u")throw new(core_1()).ValidationError("A Route53 hosted domain zone name is required to configure the specified domain name",this);new(aws_route53_1()).ARecord(this,`DNS${loadBalancer.node.id}`,{zone,recordName:name,target:aws_route53_1().RecordTarget.fromAlias(new(aws_route53_targets_1()).LoadBalancerTarget(loadBalancer))})}}}exports.NetworkMultipleTargetGroupsServiceBase=NetworkMultipleTargetGroupsServiceBase,_a=JSII_RTTI_SYMBOL_1,NetworkMultipleTargetGroupsServiceBase[_a]={fqn:"aws-cdk-lib.aws_ecs_patterns.NetworkMultipleTargetGroupsServiceBase",version:"2.201.0"};
