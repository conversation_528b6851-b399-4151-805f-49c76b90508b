"use strict";var __decorate=exports&&exports.__decorate||function(decorators,target,key,desc){var c=arguments.length,r=c<3?target:desc===null?desc=Object.getOwnPropertyDescriptor(target,key):desc,d;if(typeof Reflect=="object"&&typeof Reflect.decorate=="function")r=Reflect.decorate(decorators,target,key,desc);else for(var i=decorators.length-1;i>=0;i--)(d=decorators[i])&&(r=(c<3?d(r):c>3?d(target,key,r):d(target,key))||r);return c>3&&r&&Object.defineProperty(target,key,r),r},_a;Object.defineProperty(exports,"__esModule",{value:!0}),exports.NetworkLoadBalancedFargateService=void 0;var jsiiDeprecationWarnings=()=>{var tmp=require("../../../.warnings.jsii.js");return jsiiDeprecationWarnings=()=>tmp,tmp};const JSII_RTTI_SYMBOL_1=Symbol.for("jsii.rtti");var aws_ecs_1=()=>{var tmp=require("../../../aws-ecs");return aws_ecs_1=()=>tmp,tmp},core_1=()=>{var tmp=require("../../../core");return core_1=()=>tmp,tmp},prop_injectable_1=()=>{var tmp=require("../../../core/lib/prop-injectable");return prop_injectable_1=()=>tmp,tmp},cxapi=()=>{var tmp=require("../../../cx-api");return cxapi=()=>tmp,tmp},network_load_balanced_service_base_1=()=>{var tmp=require("../base/network-load-balanced-service-base");return network_load_balanced_service_base_1=()=>tmp,tmp};let NetworkLoadBalancedFargateService=class NetworkLoadBalancedFargateService2 extends network_load_balanced_service_base_1().NetworkLoadBalancedServiceBase{constructor(scope,id,props={}){super(scope,id,props);try{jsiiDeprecationWarnings().aws_cdk_lib_aws_ecs_patterns_NetworkLoadBalancedFargateServiceProps(props)}catch(error){throw process.env.JSII_DEBUG!=="1"&&error.name==="DeprecationError"&&Error.captureStackTrace(error,NetworkLoadBalancedFargateService2),error}if(this.assignPublicIp=props.assignPublicIp??!1,props.taskDefinition&&props.taskImageOptions)throw new(core_1()).ValidationError("You must specify either a taskDefinition or an image, not both.",this);if(props.taskDefinition)this.taskDefinition=props.taskDefinition;else if(props.taskImageOptions){const taskImageOptions=props.taskImageOptions;this.taskDefinition=new(aws_ecs_1()).FargateTaskDefinition(this,"TaskDef",{memoryLimitMiB:props.memoryLimitMiB,cpu:props.cpu,ephemeralStorageGiB:props.ephemeralStorageGiB,executionRole:taskImageOptions.executionRole,taskRole:taskImageOptions.taskRole,family:taskImageOptions.family,runtimePlatform:props.runtimePlatform});const enableLogging=taskImageOptions.enableLogging??!0,logDriver=taskImageOptions.logDriver??(enableLogging?this.createAWSLogDriver(this.node.id):void 0),containerName=taskImageOptions.containerName??"web";this.taskDefinition.addContainer(containerName,{image:taskImageOptions.image,logging:logDriver,environment:taskImageOptions.environment,secrets:taskImageOptions.secrets,dockerLabels:taskImageOptions.dockerLabels}).addPortMappings({containerPort:taskImageOptions.containerPort||80})}else throw new(core_1()).ValidationError("You must specify one of: taskDefinition or image",this);const desiredCount=core_1().FeatureFlags.of(this).isEnabled(cxapi().ECS_REMOVE_DEFAULT_DESIRED_COUNT)?this.internalDesiredCount:this.desiredCount;this.service=new(aws_ecs_1()).FargateService(this,"Service",{cluster:this.cluster,desiredCount,taskDefinition:this.taskDefinition,assignPublicIp:this.assignPublicIp,serviceName:props.serviceName,healthCheckGracePeriod:props.healthCheckGracePeriod,minHealthyPercent:props.minHealthyPercent,maxHealthyPercent:props.maxHealthyPercent,propagateTags:props.propagateTags,enableECSManagedTags:props.enableECSManagedTags,cloudMapOptions:props.cloudMapOptions,platformVersion:props.platformVersion,deploymentController:props.deploymentController,circuitBreaker:props.circuitBreaker,securityGroups:props.securityGroups,vpcSubnets:props.taskSubnets,enableExecuteCommand:props.enableExecuteCommand,capacityProviderStrategies:props.capacityProviderStrategies}),this.addServiceAsTarget(this.service)}};exports.NetworkLoadBalancedFargateService=NetworkLoadBalancedFargateService,_a=JSII_RTTI_SYMBOL_1,NetworkLoadBalancedFargateService[_a]={fqn:"aws-cdk-lib.aws_ecs_patterns.NetworkLoadBalancedFargateService",version:"2.201.0"},NetworkLoadBalancedFargateService.PROPERTY_INJECTION_ID="aws-cdk-lib.aws-ecs-patterns.NetworkLoadBalancedFargateService",exports.NetworkLoadBalancedFargateService=NetworkLoadBalancedFargateService=__decorate([prop_injectable_1().propertyInjectable],NetworkLoadBalancedFargateService);
