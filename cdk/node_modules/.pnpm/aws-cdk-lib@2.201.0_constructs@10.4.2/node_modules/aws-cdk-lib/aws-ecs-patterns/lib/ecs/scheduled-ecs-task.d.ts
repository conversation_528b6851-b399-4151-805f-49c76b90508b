import { Construct } from 'constructs';
import { Ec2TaskDefinition } from '../../../aws-ecs';
import { EcsTask } from '../../../aws-events-targets';
import { ScheduledTaskBase, ScheduledTaskBaseProps, ScheduledTaskImageProps } from '../base/scheduled-task-base';
/**
 * The properties for the ScheduledEc2Task task.
 */
export interface ScheduledEc2TaskProps extends ScheduledTaskBaseProps {
    /**
     * The properties to define if using an existing TaskDefinition in this construct.
     * ScheduledEc2TaskDefinitionOptions or ScheduledEc2TaskImageOptions must be defined, but not both.
     *
     * @default none
     */
    readonly scheduledEc2TaskDefinitionOptions?: ScheduledEc2TaskDefinitionOptions;
    /**
     * The properties to define if the construct is to create a TaskDefinition.
     * ScheduledEc2TaskDefinitionOptions or ScheduledEc2TaskImageOptions must be defined, but not both.
     *
     * @default none
     */
    readonly scheduledEc2TaskImageOptions?: ScheduledEc2TaskImageOptions;
}
/**
 * The properties for the ScheduledEc2Task using an image.
 */
export interface ScheduledEc2TaskImageOptions extends ScheduledTaskImageProps {
    /**
     * The minimum number of CPU units to reserve for the container.
     *
     * @default none
     */
    readonly cpu?: number;
    /**
     * The hard limit (in MiB) of memory to present to the container.
     *
     * If your container attempts to exceed the allocated memory, the container
     * is terminated.
     *
     * At least one of memoryLimitMiB and memoryReservationMiB is required for non-Fargate services.
     *
     * @default - No memory limit.
     */
    readonly memoryLimitMiB?: number;
    /**
     * The soft limit (in MiB) of memory to reserve for the container.
     *
     * When system memory is under contention, Docker attempts to keep the
     * container memory within the limit. If the container requires more memory,
     * it can consume up to the value specified by the Memory property or all of
     * the available memory on the container instance—whichever comes first.
     *
     * At least one of memoryLimitMiB and memoryReservationMiB is required for non-Fargate services.
     *
     * @default - No memory reserved.
     */
    readonly memoryReservationMiB?: number;
}
/**
 * The properties for the ScheduledEc2Task using a task definition.
 */
export interface ScheduledEc2TaskDefinitionOptions {
    /**
     * The task definition to use for tasks in the service. One of image or taskDefinition must be specified.
     *
     * [disable-awslint:ref-via-interface]
     *
     * @default - none
     */
    readonly taskDefinition: Ec2TaskDefinition;
}
/**
 * A scheduled EC2 task that will be initiated off of CloudWatch Events.
 */
export declare class ScheduledEc2Task extends ScheduledTaskBase {
    /**
     * The EC2 task definition in this construct.
     */
    readonly taskDefinition: Ec2TaskDefinition;
    /**
     * The ECS task in this construct.
     */
    readonly task: EcsTask;
    /**
     * Constructs a new instance of the ScheduledEc2Task class.
     */
    constructor(scope: Construct, id: string, props: ScheduledEc2TaskProps);
}
