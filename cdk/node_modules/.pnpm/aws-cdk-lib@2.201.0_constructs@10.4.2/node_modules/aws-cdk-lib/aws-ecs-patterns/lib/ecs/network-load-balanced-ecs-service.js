"use strict";var _a;Object.defineProperty(exports,"__esModule",{value:!0}),exports.NetworkLoadBalancedEc2Service=void 0;var jsiiDeprecationWarnings=()=>{var tmp=require("../../../.warnings.jsii.js");return jsiiDeprecationWarnings=()=>tmp,tmp};const JSII_RTTI_SYMBOL_1=Symbol.for("jsii.rtti");var aws_ecs_1=()=>{var tmp=require("../../../aws-ecs");return aws_ecs_1=()=>tmp,tmp},core_1=()=>{var tmp=require("../../../core");return core_1=()=>tmp,tmp},cxapi=()=>{var tmp=require("../../../cx-api");return cxapi=()=>tmp,tmp},network_load_balanced_service_base_1=()=>{var tmp=require("../base/network-load-balanced-service-base");return network_load_balanced_service_base_1=()=>tmp,tmp};class NetworkLoadBalancedEc2Service extends network_load_balanced_service_base_1().NetworkLoadBalancedServiceBase{constructor(scope,id,props={}){super(scope,id,props);try{jsiiDeprecationWarnings().aws_cdk_lib_aws_ecs_patterns_NetworkLoadBalancedEc2ServiceProps(props)}catch(error){throw process.env.JSII_DEBUG!=="1"&&error.name==="DeprecationError"&&Error.captureStackTrace(error,NetworkLoadBalancedEc2Service),error}if(props.taskDefinition&&props.taskImageOptions)throw new(core_1()).ValidationError("You must specify either a taskDefinition or an image, not both.",this);if(props.taskDefinition)this.taskDefinition=props.taskDefinition;else if(props.taskImageOptions){const taskImageOptions=props.taskImageOptions;this.taskDefinition=new(aws_ecs_1()).Ec2TaskDefinition(this,"TaskDef",{executionRole:taskImageOptions.executionRole,taskRole:taskImageOptions.taskRole,family:taskImageOptions.family});const enableLogging=taskImageOptions.enableLogging??!0,logDriver=taskImageOptions.logDriver??(enableLogging?this.createAWSLogDriver(this.node.id):void 0),containerName=taskImageOptions.containerName??"web";this.taskDefinition.addContainer(containerName,{image:taskImageOptions.image,cpu:props.cpu,memoryLimitMiB:props.memoryLimitMiB,memoryReservationMiB:props.memoryReservationMiB,environment:taskImageOptions.environment,secrets:taskImageOptions.secrets,logging:logDriver,dockerLabels:taskImageOptions.dockerLabels}).addPortMappings({containerPort:taskImageOptions.containerPort||80})}else throw new(core_1()).ValidationError("You must specify one of: taskDefinition or image",this);const desiredCount=core_1().FeatureFlags.of(this).isEnabled(cxapi().ECS_REMOVE_DEFAULT_DESIRED_COUNT)?this.internalDesiredCount:this.desiredCount;this.service=new(aws_ecs_1()).Ec2Service(this,"Service",{cluster:this.cluster,desiredCount,taskDefinition:this.taskDefinition,assignPublicIp:!1,serviceName:props.serviceName,healthCheckGracePeriod:props.healthCheckGracePeriod,minHealthyPercent:props.minHealthyPercent,maxHealthyPercent:props.maxHealthyPercent,propagateTags:props.propagateTags,enableECSManagedTags:props.enableECSManagedTags,cloudMapOptions:props.cloudMapOptions,deploymentController:props.deploymentController,circuitBreaker:props.circuitBreaker,enableExecuteCommand:props.enableExecuteCommand,placementConstraints:props.placementConstraints,placementStrategies:props.placementStrategies,capacityProviderStrategies:props.capacityProviderStrategies}),this.addServiceAsTarget(this.service)}}exports.NetworkLoadBalancedEc2Service=NetworkLoadBalancedEc2Service,_a=JSII_RTTI_SYMBOL_1,NetworkLoadBalancedEc2Service[_a]={fqn:"aws-cdk-lib.aws_ecs_patterns.NetworkLoadBalancedEc2Service",version:"2.201.0"};
