"use strict";var __decorate=exports&&exports.__decorate||function(decorators,target,key,desc){var c=arguments.length,r=c<3?target:desc===null?desc=Object.getOwnPropertyDescriptor(target,key):desc,d;if(typeof Reflect=="object"&&typeof Reflect.decorate=="function")r=Reflect.decorate(decorators,target,key,desc);else for(var i=decorators.length-1;i>=0;i--)(d=decorators[i])&&(r=(c<3?d(r):c>3?d(target,key,r):d(target,key))||r);return c>3&&r&&Object.defineProperty(target,key,r),r},_a;Object.defineProperty(exports,"__esModule",{value:!0}),exports.ApplicationLoadBalancedFargateService=void 0;var jsiiDeprecationWarnings=()=>{var tmp=require("../../../.warnings.jsii.js");return jsiiDeprecationWarnings=()=>tmp,tmp};const JSII_RTTI_SYMBOL_1=Symbol.for("jsii.rtti");var aws_ecs_1=()=>{var tmp=require("../../../aws-ecs");return aws_ecs_1=()=>tmp,tmp},core_1=()=>{var tmp=require("../../../core");return core_1=()=>tmp,tmp},prop_injectable_1=()=>{var tmp=require("../../../core/lib/prop-injectable");return prop_injectable_1=()=>tmp,tmp},cxapi=()=>{var tmp=require("../../../cx-api");return cxapi=()=>tmp,tmp},application_load_balanced_service_base_1=()=>{var tmp=require("../base/application-load-balanced-service-base");return application_load_balanced_service_base_1=()=>tmp,tmp};let ApplicationLoadBalancedFargateService=class ApplicationLoadBalancedFargateService2 extends application_load_balanced_service_base_1().ApplicationLoadBalancedServiceBase{constructor(scope,id,props={}){super(scope,id,props);try{jsiiDeprecationWarnings().aws_cdk_lib_aws_ecs_patterns_ApplicationLoadBalancedFargateServiceProps(props)}catch(error){throw process.env.JSII_DEBUG!=="1"&&error.name==="DeprecationError"&&Error.captureStackTrace(error,ApplicationLoadBalancedFargateService2),error}if(this.assignPublicIp=props.assignPublicIp??!1,props.taskDefinition&&props.taskImageOptions)throw new(core_1()).ValidationError("You must specify either a taskDefinition or an image, not both.",this);if(props.taskDefinition)this.taskDefinition=props.taskDefinition;else if(props.taskImageOptions){const taskImageOptions=props.taskImageOptions;this.taskDefinition=new(aws_ecs_1()).FargateTaskDefinition(this,"TaskDef",{memoryLimitMiB:props.memoryLimitMiB,cpu:props.cpu,ephemeralStorageGiB:props.ephemeralStorageGiB,executionRole:taskImageOptions.executionRole,taskRole:taskImageOptions.taskRole,family:taskImageOptions.family,runtimePlatform:props.runtimePlatform});const enableLogging=taskImageOptions.enableLogging??!0,logDriver=taskImageOptions.logDriver!==void 0?taskImageOptions.logDriver:enableLogging?this.createAWSLogDriver(this.node.id):void 0;this.validateContainerCpu(this.taskDefinition.cpu,props.containerCpu),this.validateContainerMemoryLimitMiB(this.taskDefinition.memoryMiB,props.containerMemoryLimitMiB);const containerName=taskImageOptions.containerName??"web";this.taskDefinition.addContainer(containerName,{image:taskImageOptions.image,healthCheck:props.healthCheck,logging:logDriver,environment:taskImageOptions.environment,secrets:taskImageOptions.secrets,dockerLabels:taskImageOptions.dockerLabels,command:taskImageOptions.command,entryPoint:taskImageOptions.entryPoint,cpu:props.containerCpu,memoryLimitMiB:props.containerMemoryLimitMiB}).addPortMappings({containerPort:taskImageOptions.containerPort||80})}else throw new(core_1()).ValidationError("You must specify one of: taskDefinition or image",this);if(this.validateHealthyPercentage("minHealthyPercent",props.minHealthyPercent),this.validateHealthyPercentage("maxHealthyPercent",props.maxHealthyPercent),props.minHealthyPercent&&!core_1().Token.isUnresolved(props.minHealthyPercent)&&props.maxHealthyPercent&&!core_1().Token.isUnresolved(props.maxHealthyPercent)&&props.minHealthyPercent>=props.maxHealthyPercent)throw new(core_1()).ValidationError("Minimum healthy percent must be less than maximum healthy percent.",this);const desiredCount=core_1().FeatureFlags.of(this).isEnabled(cxapi().ECS_REMOVE_DEFAULT_DESIRED_COUNT)?this.internalDesiredCount:this.desiredCount;this.service=new(aws_ecs_1()).FargateService(this,"Service",{cluster:this.cluster,desiredCount,taskDefinition:this.taskDefinition,assignPublicIp:this.assignPublicIp,serviceName:props.serviceName,healthCheckGracePeriod:props.healthCheckGracePeriod,minHealthyPercent:props.minHealthyPercent,maxHealthyPercent:props.maxHealthyPercent,propagateTags:props.propagateTags,enableECSManagedTags:props.enableECSManagedTags,cloudMapOptions:props.cloudMapOptions,platformVersion:props.platformVersion,deploymentController:props.deploymentController,circuitBreaker:props.circuitBreaker,securityGroups:props.securityGroups,vpcSubnets:props.taskSubnets,enableExecuteCommand:props.enableExecuteCommand,capacityProviderStrategies:props.capacityProviderStrategies}),this.addServiceAsTarget(this.service)}validateHealthyPercentage(name,value){if(!(value===void 0||core_1().Token.isUnresolved(value))&&(!Number.isInteger(value)||value<0))throw new(core_1()).ValidationError(`${name}: Must be a non-negative integer; received ${value}`,this)}validateContainerCpu(cpu,containerCpu){if(!(containerCpu===void 0||core_1().Token.isUnresolved(containerCpu)||core_1().Token.isUnresolved(cpu))){if(containerCpu>cpu)throw new(core_1()).ValidationError(`containerCpu must be less than to cpu; received containerCpu: ${containerCpu}, cpu: ${cpu}`,this);if(containerCpu<0||!Number.isInteger(containerCpu))throw new(core_1()).ValidationError(`containerCpu must be a non-negative integer; received ${containerCpu}`,this)}}validateContainerMemoryLimitMiB(memoryLimitMiB,containerMemoryLimitMiB){if(!(containerMemoryLimitMiB===void 0||core_1().Token.isUnresolved(containerMemoryLimitMiB)||core_1().Token.isUnresolved(memoryLimitMiB))){if(containerMemoryLimitMiB>memoryLimitMiB)throw new(core_1()).ValidationError(`containerMemoryLimitMiB must be less than to memoryLimitMiB; received containerMemoryLimitMiB: ${containerMemoryLimitMiB}, memoryLimitMiB: ${memoryLimitMiB}`,this);if(containerMemoryLimitMiB<=0||!Number.isInteger(containerMemoryLimitMiB))throw new(core_1()).ValidationError(`containerMemoryLimitMiB must be a positive integer; received ${containerMemoryLimitMiB}`,this)}}};exports.ApplicationLoadBalancedFargateService=ApplicationLoadBalancedFargateService,_a=JSII_RTTI_SYMBOL_1,ApplicationLoadBalancedFargateService[_a]={fqn:"aws-cdk-lib.aws_ecs_patterns.ApplicationLoadBalancedFargateService",version:"2.201.0"},ApplicationLoadBalancedFargateService.PROPERTY_INJECTION_ID="aws-cdk-lib.aws-ecs-patterns.ApplicationLoadBalancedFargateService",exports.ApplicationLoadBalancedFargateService=ApplicationLoadBalancedFargateService=__decorate([prop_injectable_1().propertyInjectable],ApplicationLoadBalancedFargateService);
