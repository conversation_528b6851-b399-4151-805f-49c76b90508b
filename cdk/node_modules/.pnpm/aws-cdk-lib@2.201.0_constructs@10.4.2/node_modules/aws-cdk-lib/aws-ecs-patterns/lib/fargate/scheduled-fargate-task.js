"use strict";var _a;Object.defineProperty(exports,"__esModule",{value:!0}),exports.ScheduledFargateTask=void 0;var jsiiDeprecationWarnings=()=>{var tmp=require("../../../.warnings.jsii.js");return jsiiDeprecationWarnings=()=>tmp,tmp};const JSII_RTTI_SYMBOL_1=Symbol.for("jsii.rtti");var aws_ecs_1=()=>{var tmp=require("../../../aws-ecs");return aws_ecs_1=()=>tmp,tmp},aws_events_targets_1=()=>{var tmp=require("../../../aws-events-targets");return aws_events_targets_1=()=>tmp,tmp},core_1=()=>{var tmp=require("../../../core");return core_1=()=>tmp,tmp},scheduled_task_base_1=()=>{var tmp=require("../base/scheduled-task-base");return scheduled_task_base_1=()=>tmp,tmp};class ScheduledFargateTask extends scheduled_task_base_1().ScheduledTaskBase{constructor(scope,id,props){super(scope,id,props);try{jsiiDeprecationWarnings().aws_cdk_lib_aws_ecs_patterns_ScheduledFargateTaskProps(props)}catch(error){throw process.env.JSII_DEBUG!=="1"&&error.name==="DeprecationError"&&Error.captureStackTrace(error,ScheduledFargateTask),error}if(props.scheduledFargateTaskDefinitionOptions&&props.scheduledFargateTaskImageOptions)throw new(core_1()).ValidationError("You must specify either a scheduledFargateTaskDefinitionOptions or scheduledFargateTaskOptions, not both.",this);if(props.scheduledFargateTaskDefinitionOptions)this.taskDefinition=props.scheduledFargateTaskDefinitionOptions.taskDefinition;else if(props.scheduledFargateTaskImageOptions){const taskImageOptions=props.scheduledFargateTaskImageOptions,containerName=taskImageOptions.containerName??"ScheduledContainer";this.taskDefinition=new(aws_ecs_1()).FargateTaskDefinition(this,"ScheduledTaskDef",{memoryLimitMiB:taskImageOptions.memoryLimitMiB||512,cpu:taskImageOptions.cpu||256,ephemeralStorageGiB:taskImageOptions.ephemeralStorageGiB}),this.taskDefinition.addContainer(containerName,{image:taskImageOptions.image,command:taskImageOptions.command,environment:taskImageOptions.environment,secrets:taskImageOptions.secrets,logging:taskImageOptions.logDriver??this.createAWSLogDriver(this.node.id)})}else throw new(core_1()).ValidationError("You must specify one of: taskDefinition or image",this);props.taskDefinition&&core_1().Annotations.of(this).addWarningV2("@aws-cdk/aws-ecs-patterns:propertyIgnored","Property 'taskDefinition' is ignored, use 'scheduledFargateTaskDefinitionOptions' or 'scheduledFargateTaskImageOptions' instead."),props.cpu&&core_1().Annotations.of(this).addWarningV2("@aws-cdk/aws-ecs-patterns:propertyIgnored","Property 'cpu' is ignored, use 'scheduledFargateTaskImageOptions.cpu' instead."),props.memoryLimitMiB&&core_1().Annotations.of(this).addWarningV2("@aws-cdk/aws-ecs-patterns:propertyIgnored","Property 'memoryLimitMiB' is ignored, use 'scheduledFargateTaskImageOptions.memoryLimitMiB' instead."),props.ephemeralStorageGiB&&core_1().Annotations.of(this).addWarningV2("@aws-cdk/aws-ecs-patterns:propertyIgnored","Property 'ephemeralStorageGiB' is ignored, use 'scheduledFargateTaskImageOptions.ephemeralStorageGiB' instead."),props.runtimePlatform&&core_1().Annotations.of(this).addWarningV2("@aws-cdk/aws-ecs-patterns:propertyIgnored","Property 'runtimePlatform' is ignored."),this.task=new(aws_events_targets_1()).EcsTask({cluster:this.cluster,taskDefinition:this.taskDefinition,taskCount:this.desiredTaskCount,subnetSelection:this.subnetSelection,platformVersion:props.platformVersion,securityGroups:props.securityGroups,propagateTags:props.propagateTags,tags:props.tags}),this.addTaskAsTarget(this.task)}}exports.ScheduledFargateTask=ScheduledFargateTask,_a=JSII_RTTI_SYMBOL_1,ScheduledFargateTask[_a]={fqn:"aws-cdk-lib.aws_ecs_patterns.ScheduledFargateTask",version:"2.201.0"};
