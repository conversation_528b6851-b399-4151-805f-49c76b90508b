"use strict";var _a;Object.defineProperty(exports,"__esModule",{value:!0}),exports.QueueProcessingEc2Service=void 0;var jsiiDeprecationWarnings=()=>{var tmp=require("../../../.warnings.jsii.js");return jsiiDeprecationWarnings=()=>tmp,tmp};const JSII_RTTI_SYMBOL_1=Symbol.for("jsii.rtti");var aws_ecs_1=()=>{var tmp=require("../../../aws-ecs");return aws_ecs_1=()=>tmp,tmp},core_1=()=>{var tmp=require("../../../core");return core_1=()=>tmp,tmp},cxapi=()=>{var tmp=require("../../../cx-api");return cxapi=()=>tmp,tmp},queue_processing_service_base_1=()=>{var tmp=require("../base/queue-processing-service-base");return queue_processing_service_base_1=()=>tmp,tmp};class QueueProcessingEc2Service extends queue_processing_service_base_1().QueueProcessingServiceBase{constructor(scope,id,props={}){super(scope,id,props);try{jsiiDeprecationWarnings().aws_cdk_lib_aws_ecs_patterns_QueueProcessingEc2ServiceProps(props)}catch(error){throw process.env.JSII_DEBUG!=="1"&&error.name==="DeprecationError"&&Error.captureStackTrace(error,QueueProcessingEc2Service),error}if(!props.image)throw new(core_1()).ValidationError("image must be specified for EC2 queue processing service",this);const containerName=props.containerName??"QueueProcessingContainer";this.taskDefinition=new(aws_ecs_1()).Ec2TaskDefinition(this,"QueueProcessingTaskDef",{family:props.family}),this.taskDefinition.addContainer(containerName,{image:props.image,memoryLimitMiB:props.memoryLimitMiB,memoryReservationMiB:props.memoryReservationMiB,cpu:props.cpu,gpuCount:props.gpuCount,command:props.command,environment:this.environment,secrets:this.secrets,logging:this.logDriver});const desiredCount=core_1().FeatureFlags.of(this).isEnabled(cxapi().ECS_REMOVE_DEFAULT_DESIRED_COUNT)?void 0:this.desiredCount;this.service=new(aws_ecs_1()).Ec2Service(this,"QueueProcessingService",{cluster:this.cluster,desiredCount,taskDefinition:this.taskDefinition,serviceName:props.serviceName,minHealthyPercent:props.minHealthyPercent,maxHealthyPercent:props.maxHealthyPercent,propagateTags:props.propagateTags,enableECSManagedTags:props.enableECSManagedTags,deploymentController:props.deploymentController,circuitBreaker:props.circuitBreaker,capacityProviderStrategies:props.capacityProviderStrategies,enableExecuteCommand:props.enableExecuteCommand,placementConstraints:props.placementConstraints,placementStrategies:props.placementStrategies}),this.configureAutoscalingForService(this.service),this.grantPermissionsToService(this.service)}}exports.QueueProcessingEc2Service=QueueProcessingEc2Service,_a=JSII_RTTI_SYMBOL_1,QueueProcessingEc2Service[_a]={fqn:"aws-cdk-lib.aws_ecs_patterns.QueueProcessingEc2Service",version:"2.201.0"};
