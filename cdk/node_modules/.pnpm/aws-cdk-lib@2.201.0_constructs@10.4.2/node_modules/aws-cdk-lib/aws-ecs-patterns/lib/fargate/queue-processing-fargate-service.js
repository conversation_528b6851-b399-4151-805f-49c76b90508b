"use strict";var _a;Object.defineProperty(exports,"__esModule",{value:!0}),exports.QueueProcessingFargateService=void 0;var jsiiDeprecationWarnings=()=>{var tmp=require("../../../.warnings.jsii.js");return jsiiDeprecationWarnings=()=>tmp,tmp};const JSII_RTTI_SYMBOL_1=Symbol.for("jsii.rtti");var aws_ecs_1=()=>{var tmp=require("../../../aws-ecs");return aws_ecs_1=()=>tmp,tmp},core_1=()=>{var tmp=require("../../../core");return core_1=()=>tmp,tmp},cxapi=()=>{var tmp=require("../../../cx-api");return cxapi=()=>tmp,tmp},queue_processing_service_base_1=()=>{var tmp=require("../base/queue-processing-service-base");return queue_processing_service_base_1=()=>tmp,tmp};class QueueProcessingFargateService extends queue_processing_service_base_1().QueueProcessingServiceBase{constructor(scope,id,props={}){super(scope,id,props);try{jsiiDeprecationWarnings().aws_cdk_lib_aws_ecs_patterns_QueueProcessingFargateServiceProps(props)}catch(error){throw process.env.JSII_DEBUG!=="1"&&error.name==="DeprecationError"&&Error.captureStackTrace(error,QueueProcessingFargateService),error}if(props.taskDefinition&&props.image)throw new(core_1()).ValidationError("You must specify only one of taskDefinition or image",this);if(props.taskDefinition)this.taskDefinition=props.taskDefinition;else if(props.image){this.taskDefinition=new(aws_ecs_1()).FargateTaskDefinition(this,"QueueProcessingTaskDef",{memoryLimitMiB:props.memoryLimitMiB||512,cpu:props.cpu||256,ephemeralStorageGiB:props.ephemeralStorageGiB,family:props.family,runtimePlatform:props.runtimePlatform});const containerName=props.containerName??"QueueProcessingContainer";this.taskDefinition.addContainer(containerName,{image:props.image,command:props.command,environment:this.environment,secrets:this.secrets,logging:this.logDriver,healthCheck:props.healthCheck})}else throw new(core_1()).ValidationError("You must specify one of: taskDefinition or image",this);const desiredCount=core_1().FeatureFlags.of(this).isEnabled(cxapi().ECS_REMOVE_DEFAULT_DESIRED_COUNT)?void 0:this.desiredCount;this.service=new(aws_ecs_1()).FargateService(this,"QueueProcessingFargateService",{cluster:this.cluster,desiredCount,taskDefinition:this.taskDefinition,serviceName:props.serviceName,minHealthyPercent:props.minHealthyPercent,maxHealthyPercent:props.maxHealthyPercent,propagateTags:props.propagateTags,enableECSManagedTags:props.enableECSManagedTags,platformVersion:props.platformVersion,deploymentController:props.deploymentController,securityGroups:props.securityGroups,vpcSubnets:props.taskSubnets,assignPublicIp:props.assignPublicIp,circuitBreaker:props.circuitBreaker,capacityProviderStrategies:props.capacityProviderStrategies,enableExecuteCommand:props.enableExecuteCommand,healthCheckGracePeriod:props.healthCheckGracePeriod}),this.configureAutoscalingForService(this.service),this.grantPermissionsToService(this.service)}}exports.QueueProcessingFargateService=QueueProcessingFargateService,_a=JSII_RTTI_SYMBOL_1,QueueProcessingFargateService[_a]={fqn:"aws-cdk-lib.aws_ecs_patterns.QueueProcessingFargateService",version:"2.201.0"};
