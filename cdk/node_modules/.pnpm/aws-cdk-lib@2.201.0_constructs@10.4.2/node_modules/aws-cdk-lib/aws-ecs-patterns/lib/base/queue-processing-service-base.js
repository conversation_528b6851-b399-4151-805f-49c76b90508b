"use strict";var _a;Object.defineProperty(exports,"__esModule",{value:!0}),exports.QueueProcessingServiceBase=void 0;var jsiiDeprecationWarnings=()=>{var tmp=require("../../../.warnings.jsii.js");return jsiiDeprecationWarnings=()=>tmp,tmp};const JSII_RTTI_SYMBOL_1=Symbol.for("jsii.rtti");var constructs_1=()=>{var tmp=require("constructs");return constructs_1=()=>tmp,tmp},aws_ecs_1=()=>{var tmp=require("../../../aws-ecs");return aws_ecs_1=()=>tmp,tmp},aws_sqs_1=()=>{var tmp=require("../../../aws-sqs");return aws_sqs_1=()=>tmp,tmp},core_1=()=>{var tmp=require("../../../core");return core_1=()=>tmp,tmp},cxapi=()=>{var tmp=require("../../../cx-api");return cxapi=()=>tmp,tmp};class QueueProcessingServiceBase extends constructs_1().Construct{constructor(scope,id,props){super(scope,id);try{jsiiDeprecationWarnings().aws_cdk_lib_aws_ecs_patterns_QueueProcessingServiceBaseProps(props)}catch(error){throw process.env.JSII_DEBUG!=="1"&&error.name==="DeprecationError"&&Error.captureStackTrace(error,QueueProcessingServiceBase),error}if(props.cluster&&props.vpc)throw new(core_1()).ValidationError("You can only specify either vpc or cluster. Alternatively, you can leave both blank",this);if(this.cluster=props.cluster||this.getDefaultCluster(this,props.vpc),props.queue&&(props.retentionPeriod||props.visibilityTimeout||props.maxReceiveCount)){const errorProps=["retentionPeriod","visibilityTimeout","maxReceiveCount"].filter(prop=>props.hasOwnProperty(prop));throw new(core_1()).ValidationError(`${errorProps.join(", ")} can be set only when queue is not set. Specify them in the QueueProps of the queue`,this)}props.queue?this.sqsQueue=props.queue:(this.deadLetterQueue=new(aws_sqs_1()).Queue(this,"EcsProcessingDeadLetterQueue",{retentionPeriod:props.retentionPeriod||core_1().Duration.days(14)}),this.sqsQueue=new(aws_sqs_1()).Queue(this,"EcsProcessingQueue",{visibilityTimeout:props.visibilityTimeout,deadLetterQueue:{queue:this.deadLetterQueue,maxReceiveCount:props.maxReceiveCount||3}}),new(core_1()).CfnOutput(this,"SQSDeadLetterQueue",{value:this.deadLetterQueue.queueName}),new(core_1()).CfnOutput(this,"SQSDeadLetterQueueArn",{value:this.deadLetterQueue.queueArn}));const defaultScalingSteps=[{upper:0,change:-1},{lower:100,change:1},{lower:500,change:5}];if(this.scalingSteps=props.scalingSteps??defaultScalingSteps,props.cooldown&&props.cooldown.toSeconds()>999999999)throw new(core_1()).ValidationError(`cooldown cannot be more than 999999999, found: ${props.cooldown.toSeconds()}`,this);this.cooldown=props.cooldown;const enableLogging=props.enableLogging??!0;if(this.logDriver=props.logDriver??(enableLogging?this.createAWSLogDriver(this.node.id):void 0),this.environment={...props.environment||{},QUEUE_NAME:this.sqsQueue.queueName},this.secrets=props.secrets,this.disableCpuBasedScaling=props.disableCpuBasedScaling??!1,this.cpuTargetUtilizationPercent=props.cpuTargetUtilizationPercent??50,this.desiredCount=props.desiredTaskCount??1,core_1().FeatureFlags.of(this).isEnabled(cxapi().ECS_REMOVE_DEFAULT_DESIRED_COUNT)?props.desiredTaskCount!=null?(this.minCapacity=props.minScalingCapacity??this.desiredCount,this.maxCapacity=props.maxScalingCapacity||2*this.desiredCount):(this.minCapacity=props.minScalingCapacity??1,this.maxCapacity=props.maxScalingCapacity||2):(this.minCapacity=props.minScalingCapacity??this.desiredCount,this.maxCapacity=props.maxScalingCapacity||2*this.desiredCount),!this.desiredCount&&!this.maxCapacity)throw new(core_1()).ValidationError("maxScalingCapacity must be set and greater than 0 if desiredCount is 0",this);new(core_1()).CfnOutput(this,"SQSQueue",{value:this.sqsQueue.queueName}),new(core_1()).CfnOutput(this,"SQSQueueArn",{value:this.sqsQueue.queueArn})}configureAutoscalingForService(service){try{jsiiDeprecationWarnings().aws_cdk_lib_aws_ecs_BaseService(service)}catch(error){throw process.env.JSII_DEBUG!=="1"&&error.name==="DeprecationError"&&Error.captureStackTrace(error,this.configureAutoscalingForService),error}const scalingTarget=service.autoScaleTaskCount({maxCapacity:this.maxCapacity,minCapacity:this.minCapacity});this.disableCpuBasedScaling||scalingTarget.scaleOnCpuUtilization("CpuScaling",{targetUtilizationPercent:this.cpuTargetUtilizationPercent}),scalingTarget.scaleOnMetric("QueueMessagesVisibleScaling",{metric:this.sqsQueue.metricApproximateNumberOfMessagesVisible(),scalingSteps:this.scalingSteps,cooldown:this.cooldown})}grantPermissionsToService(service){try{jsiiDeprecationWarnings().aws_cdk_lib_aws_ecs_BaseService(service)}catch(error){throw process.env.JSII_DEBUG!=="1"&&error.name==="DeprecationError"&&Error.captureStackTrace(error,this.grantPermissionsToService),error}this.sqsQueue.grantConsumeMessages(service.taskDefinition.taskRole)}getDefaultCluster(scope,vpc){try{jsiiDeprecationWarnings().aws_cdk_lib_aws_ec2_IVpc(vpc)}catch(error){throw process.env.JSII_DEBUG!=="1"&&error.name==="DeprecationError"&&Error.captureStackTrace(error,this.getDefaultCluster),error}const DEFAULT_CLUSTER_ID=`EcsDefaultClusterMnL3mNNYN${vpc?vpc.node.id:""}`,stack=core_1().Stack.of(scope);return stack.node.tryFindChild(DEFAULT_CLUSTER_ID)||new(aws_ecs_1()).Cluster(stack,DEFAULT_CLUSTER_ID,{vpc})}createAWSLogDriver(prefix){return new(aws_ecs_1()).AwsLogDriver({streamPrefix:prefix})}}exports.QueueProcessingServiceBase=QueueProcessingServiceBase,_a=JSII_RTTI_SYMBOL_1,QueueProcessingServiceBase[_a]={fqn:"aws-cdk-lib.aws_ecs_patterns.QueueProcessingServiceBase",version:"2.201.0"};
