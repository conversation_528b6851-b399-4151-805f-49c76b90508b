"use strict";var __decorate=exports&&exports.__decorate||function(decorators,target,key,desc){var c=arguments.length,r=c<3?target:desc===null?desc=Object.getOwnPropertyDescriptor(target,key):desc,d;if(typeof Reflect=="object"&&typeof Reflect.decorate=="function")r=Reflect.decorate(decorators,target,key,desc);else for(var i=decorators.length-1;i>=0;i--)(d=decorators[i])&&(r=(c<3?d(r):c>3?d(target,key,r):d(target,key))||r);return c>3&&r&&Object.defineProperty(target,key,r),r},_a;Object.defineProperty(exports,"__esModule",{value:!0}),exports.NetworkMultipleTargetGroupsFargateService=void 0;var jsiiDeprecationWarnings=()=>{var tmp=require("../../../.warnings.jsii.js");return jsiiDeprecationWarnings=()=>tmp,tmp};const JSII_RTTI_SYMBOL_1=Symbol.for("jsii.rtti");var aws_ecs_1=()=>{var tmp=require("../../../aws-ecs");return aws_ecs_1=()=>tmp,tmp},core_1=()=>{var tmp=require("../../../core");return core_1=()=>tmp,tmp},prop_injectable_1=()=>{var tmp=require("../../../core/lib/prop-injectable");return prop_injectable_1=()=>tmp,tmp},cxapi=()=>{var tmp=require("../../../cx-api");return cxapi=()=>tmp,tmp},network_multiple_target_groups_service_base_1=()=>{var tmp=require("../base/network-multiple-target-groups-service-base");return network_multiple_target_groups_service_base_1=()=>tmp,tmp};let NetworkMultipleTargetGroupsFargateService=class NetworkMultipleTargetGroupsFargateService2 extends network_multiple_target_groups_service_base_1().NetworkMultipleTargetGroupsServiceBase{constructor(scope,id,props={}){super(scope,id,props);try{jsiiDeprecationWarnings().aws_cdk_lib_aws_ecs_patterns_NetworkMultipleTargetGroupsFargateServiceProps(props)}catch(error){throw process.env.JSII_DEBUG!=="1"&&error.name==="DeprecationError"&&Error.captureStackTrace(error,NetworkMultipleTargetGroupsFargateService2),error}if(this.assignPublicIp=props.assignPublicIp??!1,props.taskDefinition&&props.taskImageOptions)throw new(core_1()).ValidationError("You must specify only one of TaskDefinition or TaskImageOptions.",this);if(props.taskDefinition)this.taskDefinition=props.taskDefinition;else if(props.taskImageOptions){const taskImageOptions=props.taskImageOptions;this.taskDefinition=new(aws_ecs_1()).FargateTaskDefinition(this,"TaskDef",{memoryLimitMiB:props.memoryLimitMiB,cpu:props.cpu,ephemeralStorageGiB:props.ephemeralStorageGiB,executionRole:taskImageOptions.executionRole,taskRole:taskImageOptions.taskRole,family:taskImageOptions.family,runtimePlatform:props.runtimePlatform});const containerName=taskImageOptions.containerName??"web",container=this.taskDefinition.addContainer(containerName,{image:taskImageOptions.image,logging:this.logDriver,environment:taskImageOptions.environment,secrets:taskImageOptions.secrets,dockerLabels:taskImageOptions.dockerLabels});if(taskImageOptions.containerPorts)for(const containerPort of taskImageOptions.containerPorts)container.addPortMappings({containerPort})}else throw new(core_1()).ValidationError("You must specify one of: taskDefinition or image",this);if(!this.taskDefinition.defaultContainer)throw new(core_1()).ValidationError("At least one essential container must be specified",this);if(this.taskDefinition.defaultContainer.portMappings.length===0&&this.taskDefinition.defaultContainer.addPortMappings({containerPort:80}),this.service=this.createFargateService(props),props.targetGroups)this.addPortMappingForTargets(this.taskDefinition.defaultContainer,props.targetGroups),this.targetGroup=this.registerECSTargets(this.service,this.taskDefinition.defaultContainer,props.targetGroups);else{const containerPort=this.taskDefinition.defaultContainer.portMappings[0].containerPort;if(!containerPort)throw new(core_1()).ValidationError("The first port mapping added to the default container must expose a single port",this);this.targetGroup=this.listener.addTargets("ECS",{targets:[this.service],port:containerPort})}}createFargateService(props){const desiredCount=core_1().FeatureFlags.of(this).isEnabled(cxapi().ECS_REMOVE_DEFAULT_DESIRED_COUNT)?this.internalDesiredCount:this.desiredCount;return new(aws_ecs_1()).FargateService(this,"Service",{cluster:this.cluster,desiredCount,taskDefinition:this.taskDefinition,assignPublicIp:this.assignPublicIp,serviceName:props.serviceName,healthCheckGracePeriod:props.healthCheckGracePeriod,propagateTags:props.propagateTags,enableECSManagedTags:props.enableECSManagedTags,cloudMapOptions:props.cloudMapOptions,platformVersion:props.platformVersion,enableExecuteCommand:props.enableExecuteCommand,minHealthyPercent:props.minHealthyPercent,maxHealthyPercent:props.maxHealthyPercent})}};exports.NetworkMultipleTargetGroupsFargateService=NetworkMultipleTargetGroupsFargateService,_a=JSII_RTTI_SYMBOL_1,NetworkMultipleTargetGroupsFargateService[_a]={fqn:"aws-cdk-lib.aws_ecs_patterns.NetworkMultipleTargetGroupsFargateService",version:"2.201.0"},NetworkMultipleTargetGroupsFargateService.PROPERTY_INJECTION_ID="aws-cdk-lib.aws-ecs-patterns.NetworkMultipleTargetGroupsFargateService",exports.NetworkMultipleTargetGroupsFargateService=NetworkMultipleTargetGroupsFargateService=__decorate([prop_injectable_1().propertyInjectable],NetworkMultipleTargetGroupsFargateService);
