"use strict";var __createBinding=exports&&exports.__createBinding||(Object.create?function(o,m,k,k2){k2===void 0&&(k2=k);var desc=Object.getOwnPropertyDescriptor(m,k);(!desc||("get"in desc?!m.__esModule:desc.writable||desc.configurable))&&(desc={enumerable:!0,get:function(){return m[k]}}),Object.defineProperty(o,k2,desc)}:function(o,m,k,k2){k2===void 0&&(k2=k),o[k2]=m[k]}),__exportStar=exports&&exports.__exportStar||function(m,exports2){for(var p in m)p!=="default"&&!Object.prototype.hasOwnProperty.call(exports2,p)&&__createBinding(exports2,m,p)};Object.defineProperty(exports,"__esModule",{value:!0});var _noFold;exports.ApiBase=void 0,Object.defineProperty(exports,_noFold="ApiBase",{enumerable:!0,configurable:!0,get:()=>require("./lib").ApiBase}),exports.AppsyncFunction=void 0,Object.defineProperty(exports,_noFold="AppsyncFunction",{enumerable:!0,configurable:!0,get:()=>require("./lib").AppsyncFunction}),exports.CfnApiCache=void 0,Object.defineProperty(exports,_noFold="CfnApiCache",{enumerable:!0,configurable:!0,get:()=>require("./lib").CfnApiCache}),exports.CfnApiKey=void 0,Object.defineProperty(exports,_noFold="CfnApiKey",{enumerable:!0,configurable:!0,get:()=>require("./lib").CfnApiKey}),exports.CfnDataSource=void 0,Object.defineProperty(exports,_noFold="CfnDataSource",{enumerable:!0,configurable:!0,get:()=>require("./lib").CfnDataSource}),exports.CfnDomainName=void 0,Object.defineProperty(exports,_noFold="CfnDomainName",{enumerable:!0,configurable:!0,get:()=>require("./lib").CfnDomainName}),exports.CfnDomainNameApiAssociation=void 0,Object.defineProperty(exports,_noFold="CfnDomainNameApiAssociation",{enumerable:!0,configurable:!0,get:()=>require("./lib").CfnDomainNameApiAssociation}),exports.CfnFunctionConfiguration=void 0,Object.defineProperty(exports,_noFold="CfnFunctionConfiguration",{enumerable:!0,configurable:!0,get:()=>require("./lib").CfnFunctionConfiguration}),exports.CfnGraphQLApi=void 0,Object.defineProperty(exports,_noFold="CfnGraphQLApi",{enumerable:!0,configurable:!0,get:()=>require("./lib").CfnGraphQLApi}),exports.CfnGraphQLSchema=void 0,Object.defineProperty(exports,_noFold="CfnGraphQLSchema",{enumerable:!0,configurable:!0,get:()=>require("./lib").CfnGraphQLSchema}),exports.CfnResolver=void 0,Object.defineProperty(exports,_noFold="CfnResolver",{enumerable:!0,configurable:!0,get:()=>require("./lib").CfnResolver}),exports.CfnSourceApiAssociation=void 0,Object.defineProperty(exports,_noFold="CfnSourceApiAssociation",{enumerable:!0,configurable:!0,get:()=>require("./lib").CfnSourceApiAssociation}),exports.CfnApi=void 0,Object.defineProperty(exports,_noFold="CfnApi",{enumerable:!0,configurable:!0,get:()=>require("./lib").CfnApi}),exports.CfnChannelNamespace=void 0,Object.defineProperty(exports,_noFold="CfnChannelNamespace",{enumerable:!0,configurable:!0,get:()=>require("./lib").CfnChannelNamespace}),exports.CONTEXT_ARGUMENTS_CACHING_KEY=void 0,Object.defineProperty(exports,_noFold="CONTEXT_ARGUMENTS_CACHING_KEY",{enumerable:!0,configurable:!0,get:()=>require("./lib").CONTEXT_ARGUMENTS_CACHING_KEY}),exports.CONTEXT_SOURCE_CACHING_KEY=void 0,Object.defineProperty(exports,_noFold="CONTEXT_SOURCE_CACHING_KEY",{enumerable:!0,configurable:!0,get:()=>require("./lib").CONTEXT_SOURCE_CACHING_KEY}),exports.CONTEXT_IDENTITY_CACHING_KEY=void 0,Object.defineProperty(exports,_noFold="CONTEXT_IDENTITY_CACHING_KEY",{enumerable:!0,configurable:!0,get:()=>require("./lib").CONTEXT_IDENTITY_CACHING_KEY}),exports.BASE_CACHING_KEYS=void 0,Object.defineProperty(exports,_noFold="BASE_CACHING_KEYS",{enumerable:!0,configurable:!0,get:()=>require("./lib").BASE_CACHING_KEYS}),exports.KeyCondition=void 0,Object.defineProperty(exports,_noFold="KeyCondition",{enumerable:!0,configurable:!0,get:()=>require("./lib").KeyCondition}),exports.Assign=void 0,Object.defineProperty(exports,_noFold="Assign",{enumerable:!0,configurable:!0,get:()=>require("./lib").Assign}),exports.PartitionKeyStep=void 0,Object.defineProperty(exports,_noFold="PartitionKeyStep",{enumerable:!0,configurable:!0,get:()=>require("./lib").PartitionKeyStep}),exports.SortKeyStep=void 0,Object.defineProperty(exports,_noFold="SortKeyStep",{enumerable:!0,configurable:!0,get:()=>require("./lib").SortKeyStep}),exports.PrimaryKey=void 0,Object.defineProperty(exports,_noFold="PrimaryKey",{enumerable:!0,configurable:!0,get:()=>require("./lib").PrimaryKey}),exports.PartitionKey=void 0,Object.defineProperty(exports,_noFold="PartitionKey",{enumerable:!0,configurable:!0,get:()=>require("./lib").PartitionKey}),exports.AttributeValues=void 0,Object.defineProperty(exports,_noFold="AttributeValues",{enumerable:!0,configurable:!0,get:()=>require("./lib").AttributeValues}),exports.AttributeValuesStep=void 0,Object.defineProperty(exports,_noFold="AttributeValuesStep",{enumerable:!0,configurable:!0,get:()=>require("./lib").AttributeValuesStep}),exports.Values=void 0,Object.defineProperty(exports,_noFold="Values",{enumerable:!0,configurable:!0,get:()=>require("./lib").Values}),exports.BaseDataSource=void 0,Object.defineProperty(exports,_noFold="BaseDataSource",{enumerable:!0,configurable:!0,get:()=>require("./lib").BaseDataSource}),exports.BackedDataSource=void 0,Object.defineProperty(exports,_noFold="BackedDataSource",{enumerable:!0,configurable:!0,get:()=>require("./lib").BackedDataSource}),exports.NoneDataSource=void 0,Object.defineProperty(exports,_noFold="NoneDataSource",{enumerable:!0,configurable:!0,get:()=>require("./lib").NoneDataSource}),exports.DynamoDbDataSource=void 0,Object.defineProperty(exports,_noFold="DynamoDbDataSource",{enumerable:!0,configurable:!0,get:()=>require("./lib").DynamoDbDataSource}),exports.HttpDataSource=void 0,Object.defineProperty(exports,_noFold="HttpDataSource",{enumerable:!0,configurable:!0,get:()=>require("./lib").HttpDataSource}),exports.EventBridgeDataSource=void 0,Object.defineProperty(exports,_noFold="EventBridgeDataSource",{enumerable:!0,configurable:!0,get:()=>require("./lib").EventBridgeDataSource}),exports.LambdaDataSource=void 0,Object.defineProperty(exports,_noFold="LambdaDataSource",{enumerable:!0,configurable:!0,get:()=>require("./lib").LambdaDataSource}),exports.RdsDataSource=void 0,Object.defineProperty(exports,_noFold="RdsDataSource",{enumerable:!0,configurable:!0,get:()=>require("./lib").RdsDataSource}),exports.ElasticsearchDataSource=void 0,Object.defineProperty(exports,_noFold="ElasticsearchDataSource",{enumerable:!0,configurable:!0,get:()=>require("./lib").ElasticsearchDataSource}),exports.OpenSearchDataSource=void 0,Object.defineProperty(exports,_noFold="OpenSearchDataSource",{enumerable:!0,configurable:!0,get:()=>require("./lib").OpenSearchDataSource}),exports.AppSyncDataSourceType=void 0,Object.defineProperty(exports,_noFold="AppSyncDataSourceType",{enumerable:!0,configurable:!0,get:()=>require("./lib").AppSyncDataSourceType}),exports.LambdaInvokeType=void 0,Object.defineProperty(exports,_noFold="LambdaInvokeType",{enumerable:!0,configurable:!0,get:()=>require("./lib").LambdaInvokeType}),exports.AppSyncBaseDataSource=void 0,Object.defineProperty(exports,_noFold="AppSyncBaseDataSource",{enumerable:!0,configurable:!0,get:()=>require("./lib").AppSyncBaseDataSource}),exports.AppSyncBackedDataSource=void 0,Object.defineProperty(exports,_noFold="AppSyncBackedDataSource",{enumerable:!0,configurable:!0,get:()=>require("./lib").AppSyncBackedDataSource}),exports.AppSyncDynamoDbDataSource=void 0,Object.defineProperty(exports,_noFold="AppSyncDynamoDbDataSource",{enumerable:!0,configurable:!0,get:()=>require("./lib").AppSyncDynamoDbDataSource}),exports.AppSyncHttpDataSource=void 0,Object.defineProperty(exports,_noFold="AppSyncHttpDataSource",{enumerable:!0,configurable:!0,get:()=>require("./lib").AppSyncHttpDataSource}),exports.AppSyncEventBridgeDataSource=void 0,Object.defineProperty(exports,_noFold="AppSyncEventBridgeDataSource",{enumerable:!0,configurable:!0,get:()=>require("./lib").AppSyncEventBridgeDataSource}),exports.AppSyncLambdaDataSource=void 0,Object.defineProperty(exports,_noFold="AppSyncLambdaDataSource",{enumerable:!0,configurable:!0,get:()=>require("./lib").AppSyncLambdaDataSource}),exports.AppSyncRdsDataSource=void 0,Object.defineProperty(exports,_noFold="AppSyncRdsDataSource",{enumerable:!0,configurable:!0,get:()=>require("./lib").AppSyncRdsDataSource}),exports.AppSyncOpenSearchDataSource=void 0,Object.defineProperty(exports,_noFold="AppSyncOpenSearchDataSource",{enumerable:!0,configurable:!0,get:()=>require("./lib").AppSyncOpenSearchDataSource}),exports.MappingTemplate=void 0,Object.defineProperty(exports,_noFold="MappingTemplate",{enumerable:!0,configurable:!0,get:()=>require("./lib").MappingTemplate}),exports.Resolver=void 0,Object.defineProperty(exports,_noFold="Resolver",{enumerable:!0,configurable:!0,get:()=>require("./lib").Resolver}),exports.SchemaFile=void 0,Object.defineProperty(exports,_noFold="SchemaFile",{enumerable:!0,configurable:!0,get:()=>require("./lib").SchemaFile}),exports.UserPoolDefaultAction=void 0,Object.defineProperty(exports,_noFold="UserPoolDefaultAction",{enumerable:!0,configurable:!0,get:()=>require("./lib").UserPoolDefaultAction}),exports.FieldLogLevel=void 0,Object.defineProperty(exports,_noFold="FieldLogLevel",{enumerable:!0,configurable:!0,get:()=>require("./lib").FieldLogLevel}),exports.Definition=void 0,Object.defineProperty(exports,_noFold="Definition",{enumerable:!0,configurable:!0,get:()=>require("./lib").Definition}),exports.IntrospectionConfig=void 0,Object.defineProperty(exports,_noFold="IntrospectionConfig",{enumerable:!0,configurable:!0,get:()=>require("./lib").IntrospectionConfig}),exports.GraphqlApi=void 0,Object.defineProperty(exports,_noFold="GraphqlApi",{enumerable:!0,configurable:!0,get:()=>require("./lib").GraphqlApi}),exports.IamResource=void 0,Object.defineProperty(exports,_noFold="IamResource",{enumerable:!0,configurable:!0,get:()=>require("./lib").IamResource}),exports.Visibility=void 0,Object.defineProperty(exports,_noFold="Visibility",{enumerable:!0,configurable:!0,get:()=>require("./lib").Visibility}),exports.AuthorizationType=void 0,Object.defineProperty(exports,_noFold="AuthorizationType",{enumerable:!0,configurable:!0,get:()=>require("./lib").AuthorizationType}),exports.GraphqlApiBase=void 0,Object.defineProperty(exports,_noFold="GraphqlApiBase",{enumerable:!0,configurable:!0,get:()=>require("./lib").GraphqlApiBase}),exports.Code=void 0,Object.defineProperty(exports,_noFold="Code",{enumerable:!0,configurable:!0,get:()=>require("./lib").Code}),exports.AssetCode=void 0,Object.defineProperty(exports,_noFold="AssetCode",{enumerable:!0,configurable:!0,get:()=>require("./lib").AssetCode}),exports.InlineCode=void 0,Object.defineProperty(exports,_noFold="InlineCode",{enumerable:!0,configurable:!0,get:()=>require("./lib").InlineCode}),exports.FunctionRuntimeFamily=void 0,Object.defineProperty(exports,_noFold="FunctionRuntimeFamily",{enumerable:!0,configurable:!0,get:()=>require("./lib").FunctionRuntimeFamily}),exports.FunctionRuntime=void 0,Object.defineProperty(exports,_noFold="FunctionRuntime",{enumerable:!0,configurable:!0,get:()=>require("./lib").FunctionRuntime}),exports.MergeType=void 0,Object.defineProperty(exports,_noFold="MergeType",{enumerable:!0,configurable:!0,get:()=>require("./lib").MergeType}),exports.SourceApiAssociation=void 0,Object.defineProperty(exports,_noFold="SourceApiAssociation",{enumerable:!0,configurable:!0,get:()=>require("./lib").SourceApiAssociation}),exports.addSourceGraphQLPermission=void 0,Object.defineProperty(exports,_noFold="addSourceGraphQLPermission",{enumerable:!0,configurable:!0,get:()=>require("./lib").addSourceGraphQLPermission}),exports.addSourceApiAutoMergePermission=void 0,Object.defineProperty(exports,_noFold="addSourceApiAutoMergePermission",{enumerable:!0,configurable:!0,get:()=>require("./lib").addSourceApiAutoMergePermission}),exports.AppSyncEventResource=void 0,Object.defineProperty(exports,_noFold="AppSyncEventResource",{enumerable:!0,configurable:!0,get:()=>require("./lib").AppSyncEventResource}),exports.AppSyncFieldLogLevel=void 0,Object.defineProperty(exports,_noFold="AppSyncFieldLogLevel",{enumerable:!0,configurable:!0,get:()=>require("./lib").AppSyncFieldLogLevel}),exports.AppSyncAuthorizationType=void 0,Object.defineProperty(exports,_noFold="AppSyncAuthorizationType",{enumerable:!0,configurable:!0,get:()=>require("./lib").AppSyncAuthorizationType}),exports.createAPIKey=void 0,Object.defineProperty(exports,_noFold="createAPIKey",{enumerable:!0,configurable:!0,get:()=>require("./lib").createAPIKey}),exports.EventApiBase=void 0,Object.defineProperty(exports,_noFold="EventApiBase",{enumerable:!0,configurable:!0,get:()=>require("./lib").EventApiBase}),exports.EventApi=void 0,Object.defineProperty(exports,_noFold="EventApi",{enumerable:!0,configurable:!0,get:()=>require("./lib").EventApi}),exports.HandlerBehavior=void 0,Object.defineProperty(exports,_noFold="HandlerBehavior",{enumerable:!0,configurable:!0,get:()=>require("./lib").HandlerBehavior}),exports.ChannelNamespace=void 0,Object.defineProperty(exports,_noFold="ChannelNamespace",{enumerable:!0,configurable:!0,get:()=>require("./lib").ChannelNamespace});
