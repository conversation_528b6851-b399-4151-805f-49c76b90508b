"use strict";Object.defineProperty(exports,"__esModule",{value:!0}),exports.AppSyncMetrics=void 0;class AppSyncMetrics{static _4XxErrorSum(dimensions){return{namespace:"AWS/AppSync",metricName:"4XXError",dimensionsMap:dimensions,statistic:"Sum"}}static _5XxErrorSum(dimensions){return{namespace:"AWS/AppSync",metricName:"5XXError",dimensionsMap:dimensions,statistic:"Sum"}}static latencyAverage(dimensions){return{namespace:"AWS/AppSync",metricName:"Latency",dimensionsMap:dimensions,statistic:"Average"}}}exports.AppSyncMetrics=AppSyncMetrics;
