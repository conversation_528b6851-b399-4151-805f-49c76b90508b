"use strict";var _a,_b;Object.defineProperty(exports,"__esModule",{value:!0}),exports.GraphqlApiBase=exports.AuthorizationType=exports.Visibility=exports.IamResource=void 0;var jsiiDeprecationWarnings=()=>{var tmp=require("../../.warnings.jsii.js");return jsiiDeprecationWarnings=()=>tmp,tmp};const JSII_RTTI_SYMBOL_1=Symbol.for("jsii.rtti");var data_source_1=()=>{var tmp=require("./data-source");return data_source_1=()=>tmp,tmp},resolver_1=()=>{var tmp=require("./resolver");return resolver_1=()=>tmp,tmp},aws_iam_1=()=>{var tmp=require("../../aws-iam");return aws_iam_1=()=>tmp,tmp},core_1=()=>{var tmp=require("../../core");return core_1=()=>tmp,tmp};class IamResource{static custom(...arns){if(arns.length===0)throw new(core_1()).UnscopedValidationError("At least 1 custom ARN must be provided.");return new IamResource(arns)}static ofType(type,...fields){const arns=fields.length?fields.map(field=>`types/${type}/fields/${field}`):[`types/${type}/*`];return new IamResource(arns)}static all(){return new IamResource(["*"])}constructor(arns){this.arns=arns}resourceArns(api){try{jsiiDeprecationWarnings().aws_cdk_lib_aws_appsync_GraphqlApiBase(api)}catch(error){throw process.env.JSII_DEBUG!=="1"&&error.name==="DeprecationError"&&Error.captureStackTrace(error,this.resourceArns),error}return this.arns.map(arn=>core_1().Stack.of(api).formatArn({service:"appsync",resource:`apis/${api.apiId}`,arnFormat:core_1().ArnFormat.SLASH_RESOURCE_NAME,resourceName:`${arn}`}))}}exports.IamResource=IamResource,_a=JSII_RTTI_SYMBOL_1,IamResource[_a]={fqn:"aws-cdk-lib.aws_appsync.IamResource",version:"2.201.0"};var Visibility;(function(Visibility2){Visibility2.GLOBAL="GLOBAL",Visibility2.PRIVATE="PRIVATE"})(Visibility||(exports.Visibility=Visibility={}));var AuthorizationType;(function(AuthorizationType2){AuthorizationType2.API_KEY="API_KEY",AuthorizationType2.IAM="AWS_IAM",AuthorizationType2.USER_POOL="AMAZON_COGNITO_USER_POOLS",AuthorizationType2.OIDC="OPENID_CONNECT",AuthorizationType2.LAMBDA="AWS_LAMBDA"})(AuthorizationType||(exports.AuthorizationType=AuthorizationType={}));class GraphqlApiBase extends core_1().Resource{addNoneDataSource(id,options){try{jsiiDeprecationWarnings().aws_cdk_lib_aws_appsync_DataSourceOptions(options)}catch(error){throw process.env.JSII_DEBUG!=="1"&&error.name==="DeprecationError"&&Error.captureStackTrace(error,this.addNoneDataSource),error}return new(data_source_1()).NoneDataSource(this,id,{api:this,name:options?.name,description:options?.description})}addDynamoDbDataSource(id,table,options){try{jsiiDeprecationWarnings().aws_cdk_lib_aws_dynamodb_ITable(table),jsiiDeprecationWarnings().aws_cdk_lib_aws_appsync_DataSourceOptions(options)}catch(error){throw process.env.JSII_DEBUG!=="1"&&error.name==="DeprecationError"&&Error.captureStackTrace(error,this.addDynamoDbDataSource),error}return new(data_source_1()).DynamoDbDataSource(this,id,{api:this,table,name:options?.name,description:options?.description})}addHttpDataSource(id,endpoint,options){try{jsiiDeprecationWarnings().aws_cdk_lib_aws_appsync_HttpDataSourceOptions(options)}catch(error){throw process.env.JSII_DEBUG!=="1"&&error.name==="DeprecationError"&&Error.captureStackTrace(error,this.addHttpDataSource),error}return new(data_source_1()).HttpDataSource(this,id,{api:this,endpoint,name:options?.name,description:options?.description,authorizationConfig:options?.authorizationConfig})}addLambdaDataSource(id,lambdaFunction,options){try{jsiiDeprecationWarnings().aws_cdk_lib_aws_lambda_IFunction(lambdaFunction),jsiiDeprecationWarnings().aws_cdk_lib_aws_appsync_DataSourceOptions(options)}catch(error){throw process.env.JSII_DEBUG!=="1"&&error.name==="DeprecationError"&&Error.captureStackTrace(error,this.addLambdaDataSource),error}return new(data_source_1()).LambdaDataSource(this,id,{api:this,lambdaFunction,name:options?.name,description:options?.description})}addRdsDataSource(id,serverlessCluster,secretStore,databaseName,options){try{jsiiDeprecationWarnings().aws_cdk_lib_aws_rds_IServerlessCluster(serverlessCluster),jsiiDeprecationWarnings().aws_cdk_lib_aws_secretsmanager_ISecret(secretStore),jsiiDeprecationWarnings().aws_cdk_lib_aws_appsync_DataSourceOptions(options)}catch(error){throw process.env.JSII_DEBUG!=="1"&&error.name==="DeprecationError"&&Error.captureStackTrace(error,this.addRdsDataSource),error}return new(data_source_1()).RdsDataSource(this,id,{api:this,name:options?.name,description:options?.description,serverlessCluster,secretStore,databaseName})}addRdsDataSourceV2(id,serverlessCluster,secretStore,databaseName,options){try{jsiiDeprecationWarnings().aws_cdk_lib_aws_rds_IDatabaseCluster(serverlessCluster),jsiiDeprecationWarnings().aws_cdk_lib_aws_secretsmanager_ISecret(secretStore),jsiiDeprecationWarnings().aws_cdk_lib_aws_appsync_DataSourceOptions(options)}catch(error){throw process.env.JSII_DEBUG!=="1"&&error.name==="DeprecationError"&&Error.captureStackTrace(error,this.addRdsDataSourceV2),error}return new(data_source_1()).RdsDataSource(this,id,{api:this,name:options?.name,description:options?.description,serverlessCluster,secretStore,databaseName})}addElasticsearchDataSource(id,domain,options){try{jsiiDeprecationWarnings().print("aws-cdk-lib.aws_appsync.GraphqlApiBase#addElasticsearchDataSource","- use `addOpenSearchDataSource`"),jsiiDeprecationWarnings().aws_cdk_lib_aws_elasticsearch_IDomain(domain),jsiiDeprecationWarnings().aws_cdk_lib_aws_appsync_DataSourceOptions(options)}catch(error){throw process.env.JSII_DEBUG!=="1"&&error.name==="DeprecationError"&&Error.captureStackTrace(error,this.addElasticsearchDataSource),error}return new(data_source_1()).ElasticsearchDataSource(this,id,{api:this,name:options?.name,description:options?.description,domain})}addEventBridgeDataSource(id,eventBus,options){try{jsiiDeprecationWarnings().aws_cdk_lib_aws_events_IEventBus(eventBus),jsiiDeprecationWarnings().aws_cdk_lib_aws_appsync_DataSourceOptions(options)}catch(error){throw process.env.JSII_DEBUG!=="1"&&error.name==="DeprecationError"&&Error.captureStackTrace(error,this.addEventBridgeDataSource),error}return new(data_source_1()).EventBridgeDataSource(this,id,{api:this,eventBus,name:options?.name,description:options?.description})}addOpenSearchDataSource(id,domain,options){try{jsiiDeprecationWarnings().aws_cdk_lib_aws_opensearchservice_IDomain(domain),jsiiDeprecationWarnings().aws_cdk_lib_aws_appsync_DataSourceOptions(options)}catch(error){throw process.env.JSII_DEBUG!=="1"&&error.name==="DeprecationError"&&Error.captureStackTrace(error,this.addOpenSearchDataSource),error}return new(data_source_1()).OpenSearchDataSource(this,id,{api:this,name:options?.name,description:options?.description,domain})}createResolver(id,props){try{jsiiDeprecationWarnings().aws_cdk_lib_aws_appsync_ExtendedResolverProps(props)}catch(error){throw process.env.JSII_DEBUG!=="1"&&error.name==="DeprecationError"&&Error.captureStackTrace(error,this.createResolver),error}return new(resolver_1()).Resolver(this,id,{api:this,...props})}addSchemaDependency(construct){try{jsiiDeprecationWarnings().aws_cdk_lib_CfnResource(construct)}catch(error){throw process.env.JSII_DEBUG!=="1"&&error.name==="DeprecationError"&&Error.captureStackTrace(error,this.addSchemaDependency),error}return!1}grant(grantee,resources,...actions){try{jsiiDeprecationWarnings().aws_cdk_lib_aws_iam_IGrantable(grantee),jsiiDeprecationWarnings().aws_cdk_lib_aws_appsync_IamResource(resources)}catch(error){throw process.env.JSII_DEBUG!=="1"&&error.name==="DeprecationError"&&Error.captureStackTrace(error,this.grant),error}return aws_iam_1().Grant.addToPrincipal({grantee,actions,resourceArns:resources.resourceArns(this),scope:this})}grantMutation(grantee,...fields){try{jsiiDeprecationWarnings().aws_cdk_lib_aws_iam_IGrantable(grantee)}catch(error){throw process.env.JSII_DEBUG!=="1"&&error.name==="DeprecationError"&&Error.captureStackTrace(error,this.grantMutation),error}return this.grant(grantee,IamResource.ofType("Mutation",...fields),"appsync:GraphQL")}grantQuery(grantee,...fields){try{jsiiDeprecationWarnings().aws_cdk_lib_aws_iam_IGrantable(grantee)}catch(error){throw process.env.JSII_DEBUG!=="1"&&error.name==="DeprecationError"&&Error.captureStackTrace(error,this.grantQuery),error}return this.grant(grantee,IamResource.ofType("Query",...fields),"appsync:GraphQL")}grantSubscription(grantee,...fields){try{jsiiDeprecationWarnings().aws_cdk_lib_aws_iam_IGrantable(grantee)}catch(error){throw process.env.JSII_DEBUG!=="1"&&error.name==="DeprecationError"&&Error.captureStackTrace(error,this.grantSubscription),error}return this.grant(grantee,IamResource.ofType("Subscription",...fields),"appsync:GraphQL")}}exports.GraphqlApiBase=GraphqlApiBase,_b=JSII_RTTI_SYMBOL_1,GraphqlApiBase[_b]={fqn:"aws-cdk-lib.aws_appsync.GraphqlApiBase",version:"2.201.0"};
