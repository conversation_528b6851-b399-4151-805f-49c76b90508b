"use strict";var __createBinding=exports&&exports.__createBinding||(Object.create?function(o,m,k,k2){k2===void 0&&(k2=k);var desc=Object.getOwnPropertyDescriptor(m,k);(!desc||("get"in desc?!m.__esModule:desc.writable||desc.configurable))&&(desc={enumerable:!0,get:function(){return m[k]}}),Object.defineProperty(o,k2,desc)}:function(o,m,k,k2){k2===void 0&&(k2=k),o[k2]=m[k]}),__exportStar=exports&&exports.__exportStar||function(m,exports2){for(var p in m)p!=="default"&&!Object.prototype.hasOwnProperty.call(exports2,p)&&__createBinding(exports2,m,p)};Object.defineProperty(exports,"__esModule",{value:!0});var _noFold;exports.ApiBase=void 0,Object.defineProperty(exports,_noFold="ApiBase",{enumerable:!0,configurable:!0,get:()=>require("./api-base").ApiBase}),exports.AppsyncFunction=void 0,Object.defineProperty(exports,_noFold="AppsyncFunction",{enumerable:!0,configurable:!0,get:()=>require("./appsync-function").AppsyncFunction}),exports.CfnApiCache=void 0,Object.defineProperty(exports,_noFold="CfnApiCache",{enumerable:!0,configurable:!0,get:()=>require("./appsync.generated").CfnApiCache}),exports.CfnApiKey=void 0,Object.defineProperty(exports,_noFold="CfnApiKey",{enumerable:!0,configurable:!0,get:()=>require("./appsync.generated").CfnApiKey}),exports.CfnDataSource=void 0,Object.defineProperty(exports,_noFold="CfnDataSource",{enumerable:!0,configurable:!0,get:()=>require("./appsync.generated").CfnDataSource}),exports.CfnDomainName=void 0,Object.defineProperty(exports,_noFold="CfnDomainName",{enumerable:!0,configurable:!0,get:()=>require("./appsync.generated").CfnDomainName}),exports.CfnDomainNameApiAssociation=void 0,Object.defineProperty(exports,_noFold="CfnDomainNameApiAssociation",{enumerable:!0,configurable:!0,get:()=>require("./appsync.generated").CfnDomainNameApiAssociation}),exports.CfnFunctionConfiguration=void 0,Object.defineProperty(exports,_noFold="CfnFunctionConfiguration",{enumerable:!0,configurable:!0,get:()=>require("./appsync.generated").CfnFunctionConfiguration}),exports.CfnGraphQLApi=void 0,Object.defineProperty(exports,_noFold="CfnGraphQLApi",{enumerable:!0,configurable:!0,get:()=>require("./appsync.generated").CfnGraphQLApi}),exports.CfnGraphQLSchema=void 0,Object.defineProperty(exports,_noFold="CfnGraphQLSchema",{enumerable:!0,configurable:!0,get:()=>require("./appsync.generated").CfnGraphQLSchema}),exports.CfnResolver=void 0,Object.defineProperty(exports,_noFold="CfnResolver",{enumerable:!0,configurable:!0,get:()=>require("./appsync.generated").CfnResolver}),exports.CfnSourceApiAssociation=void 0,Object.defineProperty(exports,_noFold="CfnSourceApiAssociation",{enumerable:!0,configurable:!0,get:()=>require("./appsync.generated").CfnSourceApiAssociation}),exports.CfnApi=void 0,Object.defineProperty(exports,_noFold="CfnApi",{enumerable:!0,configurable:!0,get:()=>require("./appsync.generated").CfnApi}),exports.CfnChannelNamespace=void 0,Object.defineProperty(exports,_noFold="CfnChannelNamespace",{enumerable:!0,configurable:!0,get:()=>require("./appsync.generated").CfnChannelNamespace}),exports.CONTEXT_ARGUMENTS_CACHING_KEY=void 0,Object.defineProperty(exports,_noFold="CONTEXT_ARGUMENTS_CACHING_KEY",{enumerable:!0,configurable:!0,get:()=>require("./caching-key").CONTEXT_ARGUMENTS_CACHING_KEY}),exports.CONTEXT_SOURCE_CACHING_KEY=void 0,Object.defineProperty(exports,_noFold="CONTEXT_SOURCE_CACHING_KEY",{enumerable:!0,configurable:!0,get:()=>require("./caching-key").CONTEXT_SOURCE_CACHING_KEY}),exports.CONTEXT_IDENTITY_CACHING_KEY=void 0,Object.defineProperty(exports,_noFold="CONTEXT_IDENTITY_CACHING_KEY",{enumerable:!0,configurable:!0,get:()=>require("./caching-key").CONTEXT_IDENTITY_CACHING_KEY}),exports.BASE_CACHING_KEYS=void 0,Object.defineProperty(exports,_noFold="BASE_CACHING_KEYS",{enumerable:!0,configurable:!0,get:()=>require("./caching-key").BASE_CACHING_KEYS}),exports.KeyCondition=void 0,Object.defineProperty(exports,_noFold="KeyCondition",{enumerable:!0,configurable:!0,get:()=>require("./key").KeyCondition}),exports.Assign=void 0,Object.defineProperty(exports,_noFold="Assign",{enumerable:!0,configurable:!0,get:()=>require("./key").Assign}),exports.PartitionKeyStep=void 0,Object.defineProperty(exports,_noFold="PartitionKeyStep",{enumerable:!0,configurable:!0,get:()=>require("./key").PartitionKeyStep}),exports.SortKeyStep=void 0,Object.defineProperty(exports,_noFold="SortKeyStep",{enumerable:!0,configurable:!0,get:()=>require("./key").SortKeyStep}),exports.PrimaryKey=void 0,Object.defineProperty(exports,_noFold="PrimaryKey",{enumerable:!0,configurable:!0,get:()=>require("./key").PrimaryKey}),exports.PartitionKey=void 0,Object.defineProperty(exports,_noFold="PartitionKey",{enumerable:!0,configurable:!0,get:()=>require("./key").PartitionKey}),exports.AttributeValues=void 0,Object.defineProperty(exports,_noFold="AttributeValues",{enumerable:!0,configurable:!0,get:()=>require("./key").AttributeValues}),exports.AttributeValuesStep=void 0,Object.defineProperty(exports,_noFold="AttributeValuesStep",{enumerable:!0,configurable:!0,get:()=>require("./key").AttributeValuesStep}),exports.Values=void 0,Object.defineProperty(exports,_noFold="Values",{enumerable:!0,configurable:!0,get:()=>require("./key").Values}),exports.BaseDataSource=void 0,Object.defineProperty(exports,_noFold="BaseDataSource",{enumerable:!0,configurable:!0,get:()=>require("./data-source").BaseDataSource}),exports.BackedDataSource=void 0,Object.defineProperty(exports,_noFold="BackedDataSource",{enumerable:!0,configurable:!0,get:()=>require("./data-source").BackedDataSource}),exports.NoneDataSource=void 0,Object.defineProperty(exports,_noFold="NoneDataSource",{enumerable:!0,configurable:!0,get:()=>require("./data-source").NoneDataSource}),exports.DynamoDbDataSource=void 0,Object.defineProperty(exports,_noFold="DynamoDbDataSource",{enumerable:!0,configurable:!0,get:()=>require("./data-source").DynamoDbDataSource}),exports.HttpDataSource=void 0,Object.defineProperty(exports,_noFold="HttpDataSource",{enumerable:!0,configurable:!0,get:()=>require("./data-source").HttpDataSource}),exports.EventBridgeDataSource=void 0,Object.defineProperty(exports,_noFold="EventBridgeDataSource",{enumerable:!0,configurable:!0,get:()=>require("./data-source").EventBridgeDataSource}),exports.LambdaDataSource=void 0,Object.defineProperty(exports,_noFold="LambdaDataSource",{enumerable:!0,configurable:!0,get:()=>require("./data-source").LambdaDataSource}),exports.RdsDataSource=void 0,Object.defineProperty(exports,_noFold="RdsDataSource",{enumerable:!0,configurable:!0,get:()=>require("./data-source").RdsDataSource}),exports.ElasticsearchDataSource=void 0,Object.defineProperty(exports,_noFold="ElasticsearchDataSource",{enumerable:!0,configurable:!0,get:()=>require("./data-source").ElasticsearchDataSource}),exports.OpenSearchDataSource=void 0,Object.defineProperty(exports,_noFold="OpenSearchDataSource",{enumerable:!0,configurable:!0,get:()=>require("./data-source").OpenSearchDataSource}),exports.AppSyncDataSourceType=void 0,Object.defineProperty(exports,_noFold="AppSyncDataSourceType",{enumerable:!0,configurable:!0,get:()=>require("./data-source-common").AppSyncDataSourceType}),exports.LambdaInvokeType=void 0,Object.defineProperty(exports,_noFold="LambdaInvokeType",{enumerable:!0,configurable:!0,get:()=>require("./data-source-common").LambdaInvokeType}),exports.AppSyncBaseDataSource=void 0,Object.defineProperty(exports,_noFold="AppSyncBaseDataSource",{enumerable:!0,configurable:!0,get:()=>require("./data-source-common").AppSyncBaseDataSource}),exports.AppSyncBackedDataSource=void 0,Object.defineProperty(exports,_noFold="AppSyncBackedDataSource",{enumerable:!0,configurable:!0,get:()=>require("./data-source-common").AppSyncBackedDataSource}),exports.AppSyncDynamoDbDataSource=void 0,Object.defineProperty(exports,_noFold="AppSyncDynamoDbDataSource",{enumerable:!0,configurable:!0,get:()=>require("./data-source-common").AppSyncDynamoDbDataSource}),exports.AppSyncHttpDataSource=void 0,Object.defineProperty(exports,_noFold="AppSyncHttpDataSource",{enumerable:!0,configurable:!0,get:()=>require("./data-source-common").AppSyncHttpDataSource}),exports.AppSyncEventBridgeDataSource=void 0,Object.defineProperty(exports,_noFold="AppSyncEventBridgeDataSource",{enumerable:!0,configurable:!0,get:()=>require("./data-source-common").AppSyncEventBridgeDataSource}),exports.AppSyncLambdaDataSource=void 0,Object.defineProperty(exports,_noFold="AppSyncLambdaDataSource",{enumerable:!0,configurable:!0,get:()=>require("./data-source-common").AppSyncLambdaDataSource}),exports.AppSyncRdsDataSource=void 0,Object.defineProperty(exports,_noFold="AppSyncRdsDataSource",{enumerable:!0,configurable:!0,get:()=>require("./data-source-common").AppSyncRdsDataSource}),exports.AppSyncOpenSearchDataSource=void 0,Object.defineProperty(exports,_noFold="AppSyncOpenSearchDataSource",{enumerable:!0,configurable:!0,get:()=>require("./data-source-common").AppSyncOpenSearchDataSource}),exports.MappingTemplate=void 0,Object.defineProperty(exports,_noFold="MappingTemplate",{enumerable:!0,configurable:!0,get:()=>require("./mapping-template").MappingTemplate}),exports.Resolver=void 0,Object.defineProperty(exports,_noFold="Resolver",{enumerable:!0,configurable:!0,get:()=>require("./resolver").Resolver}),exports.SchemaFile=void 0,Object.defineProperty(exports,_noFold="SchemaFile",{enumerable:!0,configurable:!0,get:()=>require("./schema").SchemaFile}),exports.UserPoolDefaultAction=void 0,Object.defineProperty(exports,_noFold="UserPoolDefaultAction",{enumerable:!0,configurable:!0,get:()=>require("./graphqlapi").UserPoolDefaultAction}),exports.FieldLogLevel=void 0,Object.defineProperty(exports,_noFold="FieldLogLevel",{enumerable:!0,configurable:!0,get:()=>require("./graphqlapi").FieldLogLevel}),exports.Definition=void 0,Object.defineProperty(exports,_noFold="Definition",{enumerable:!0,configurable:!0,get:()=>require("./graphqlapi").Definition}),exports.IntrospectionConfig=void 0,Object.defineProperty(exports,_noFold="IntrospectionConfig",{enumerable:!0,configurable:!0,get:()=>require("./graphqlapi").IntrospectionConfig}),exports.GraphqlApi=void 0,Object.defineProperty(exports,_noFold="GraphqlApi",{enumerable:!0,configurable:!0,get:()=>require("./graphqlapi").GraphqlApi}),exports.IamResource=void 0,Object.defineProperty(exports,_noFold="IamResource",{enumerable:!0,configurable:!0,get:()=>require("./graphqlapi-base").IamResource}),exports.Visibility=void 0,Object.defineProperty(exports,_noFold="Visibility",{enumerable:!0,configurable:!0,get:()=>require("./graphqlapi-base").Visibility}),exports.AuthorizationType=void 0,Object.defineProperty(exports,_noFold="AuthorizationType",{enumerable:!0,configurable:!0,get:()=>require("./graphqlapi-base").AuthorizationType}),exports.GraphqlApiBase=void 0,Object.defineProperty(exports,_noFold="GraphqlApiBase",{enumerable:!0,configurable:!0,get:()=>require("./graphqlapi-base").GraphqlApiBase}),exports.Code=void 0,Object.defineProperty(exports,_noFold="Code",{enumerable:!0,configurable:!0,get:()=>require("./code").Code}),exports.AssetCode=void 0,Object.defineProperty(exports,_noFold="AssetCode",{enumerable:!0,configurable:!0,get:()=>require("./code").AssetCode}),exports.InlineCode=void 0,Object.defineProperty(exports,_noFold="InlineCode",{enumerable:!0,configurable:!0,get:()=>require("./code").InlineCode}),exports.FunctionRuntimeFamily=void 0,Object.defineProperty(exports,_noFold="FunctionRuntimeFamily",{enumerable:!0,configurable:!0,get:()=>require("./runtime").FunctionRuntimeFamily}),exports.FunctionRuntime=void 0,Object.defineProperty(exports,_noFold="FunctionRuntime",{enumerable:!0,configurable:!0,get:()=>require("./runtime").FunctionRuntime}),exports.MergeType=void 0,Object.defineProperty(exports,_noFold="MergeType",{enumerable:!0,configurable:!0,get:()=>require("./source-api-association").MergeType}),exports.SourceApiAssociation=void 0,Object.defineProperty(exports,_noFold="SourceApiAssociation",{enumerable:!0,configurable:!0,get:()=>require("./source-api-association").SourceApiAssociation}),exports.addSourceGraphQLPermission=void 0,Object.defineProperty(exports,_noFold="addSourceGraphQLPermission",{enumerable:!0,configurable:!0,get:()=>require("./source-api-association").addSourceGraphQLPermission}),exports.addSourceApiAutoMergePermission=void 0,Object.defineProperty(exports,_noFold="addSourceApiAutoMergePermission",{enumerable:!0,configurable:!0,get:()=>require("./source-api-association").addSourceApiAutoMergePermission}),exports.AppSyncEventResource=void 0,Object.defineProperty(exports,_noFold="AppSyncEventResource",{enumerable:!0,configurable:!0,get:()=>require("./appsync-common").AppSyncEventResource}),exports.AppSyncFieldLogLevel=void 0,Object.defineProperty(exports,_noFold="AppSyncFieldLogLevel",{enumerable:!0,configurable:!0,get:()=>require("./appsync-common").AppSyncFieldLogLevel}),exports.AppSyncAuthorizationType=void 0,Object.defineProperty(exports,_noFold="AppSyncAuthorizationType",{enumerable:!0,configurable:!0,get:()=>require("./auth-config").AppSyncAuthorizationType}),exports.createAPIKey=void 0,Object.defineProperty(exports,_noFold="createAPIKey",{enumerable:!0,configurable:!0,get:()=>require("./auth-config").createAPIKey}),exports.EventApiBase=void 0,Object.defineProperty(exports,_noFold="EventApiBase",{enumerable:!0,configurable:!0,get:()=>require("./eventapi").EventApiBase}),exports.EventApi=void 0,Object.defineProperty(exports,_noFold="EventApi",{enumerable:!0,configurable:!0,get:()=>require("./eventapi").EventApi}),exports.HandlerBehavior=void 0,Object.defineProperty(exports,_noFold="HandlerBehavior",{enumerable:!0,configurable:!0,get:()=>require("./channel-namespace").HandlerBehavior}),exports.ChannelNamespace=void 0,Object.defineProperty(exports,_noFold="ChannelNamespace",{enumerable:!0,configurable:!0,get:()=>require("./channel-namespace").ChannelNamespace});
