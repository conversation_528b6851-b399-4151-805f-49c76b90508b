import { Construct } from 'constructs';
import { BaseAppsyncFunctionProps, AppsyncFunction } from './appsync-function';
import { CfnDataSource } from './appsync.generated';
import { IGraphqlApi } from './graphqlapi-base';
import { BaseResolverProps, Resolver } from './resolver';
import { ITable } from '../../aws-dynamodb';
import { IDomain as IElasticsearchDomain } from '../../aws-elasticsearch';
import { IEventBus } from '../../aws-events';
import { IGrantable, IPrincipal, IRole } from '../../aws-iam';
import { IFunction } from '../../aws-lambda';
import { IDomain as IOpenSearchDomain } from '../../aws-opensearchservice';
import { IServerlessCluster, IDatabaseCluster } from '../../aws-rds';
import { ISecret } from '../../aws-secretsmanager';
import { IResolvable } from '../../core';
/**
 * Base properties for an AppSync datasource
 */
export interface BaseDataSourceProps {
    /**
     * The API to attach this data source to
     */
    readonly api: IGraphqlApi;
    /**
     * The name of the data source
     *
     * @default - id of data source
     */
    readonly name?: string;
    /**
     * the description of the data source
     *
     * @default - None
     */
    readonly description?: string;
}
/**
 * properties for an AppSync datasource backed by a resource
 */
export interface BackedDataSourceProps extends BaseDataSourceProps {
    /**
     * The IAM service role to be assumed by AppSync to interact with the data source
     *
     * @default -  Create a new role
     */
    readonly serviceRole?: IRole;
}
/**
 * props used by implementations of BaseDataSource to provide configuration. Should not be used directly.
 */
export interface ExtendedDataSourceProps {
    /**
     * the type of the AppSync datasource
     */
    readonly type: string;
    /**
     * configuration for DynamoDB Datasource
     *
     * @default - No config
     */
    readonly dynamoDbConfig?: CfnDataSource.DynamoDBConfigProperty | IResolvable;
    /**
     * configuration for Elasticsearch data source
     *
     * @deprecated - use `openSearchConfig`
     * @default - No config
     */
    readonly elasticsearchConfig?: CfnDataSource.ElasticsearchConfigProperty | IResolvable;
    /**
     * configuration for OpenSearch data source
     *
     * @default - No config
     */
    readonly openSearchServiceConfig?: CfnDataSource.OpenSearchServiceConfigProperty | IResolvable;
    /**
     * configuration for HTTP Datasource
     *
     * @default - No config
     */
    readonly httpConfig?: CfnDataSource.HttpConfigProperty | IResolvable;
    /**
     * configuration for EventBridge Datasource
     *
     * @default - No config
     */
    readonly eventBridgeConfig?: CfnDataSource.EventBridgeConfigProperty | IResolvable;
    /**
     * configuration for Lambda Datasource
     *
     * @default - No config
     */
    readonly lambdaConfig?: CfnDataSource.LambdaConfigProperty | IResolvable;
    /**
     * configuration for RDS Datasource
     *
     * @default - No config
     */
    readonly relationalDatabaseConfig?: CfnDataSource.RelationalDatabaseConfigProperty | IResolvable;
}
/**
 * Abstract AppSync datasource implementation. Do not use directly but use subclasses for concrete datasources
 */
export declare abstract class BaseDataSource extends Construct {
    /**
     * the name of the data source
     */
    readonly name: string;
    /**
     * the underlying CFN data source resource
     */
    readonly ds: CfnDataSource;
    protected api: IGraphqlApi;
    protected serviceRole?: IRole;
    constructor(scope: Construct, id: string, props: BackedDataSourceProps, extended: ExtendedDataSourceProps);
    /**
     * creates a new resolver for this datasource and API using the given properties
     */
    createResolver(id: string, props: BaseResolverProps): Resolver;
    /**
     * creates a new appsync function for this datasource and API using the given properties
     */
    createFunction(id: string, props: BaseAppsyncFunctionProps): AppsyncFunction;
}
/**
 * Abstract AppSync datasource implementation. Do not use directly but use subclasses for resource backed datasources
 */
export declare abstract class BackedDataSource extends BaseDataSource implements IGrantable {
    /**
     * the principal of the data source to be IGrantable
     */
    readonly grantPrincipal: IPrincipal;
    constructor(scope: Construct, id: string, props: BackedDataSourceProps, extended: ExtendedDataSourceProps);
}
/**
 * Properties for an AppSync dummy datasource
 */
export interface NoneDataSourceProps extends BaseDataSourceProps {
}
/**
 * An AppSync dummy datasource
 */
export declare class NoneDataSource extends BaseDataSource {
    constructor(scope: Construct, id: string, props: NoneDataSourceProps);
}
/**
 * Properties for an AppSync DynamoDB datasource
 */
export interface DynamoDbDataSourceProps extends BackedDataSourceProps {
    /**
     * The DynamoDB table backing this data source
     */
    readonly table: ITable;
    /**
     * Specify whether this DS is read only or has read and write permissions to the DynamoDB table
     *
     * @default false
     */
    readonly readOnlyAccess?: boolean;
    /**
     * use credentials of caller to access DynamoDB
     *
     * @default false
     */
    readonly useCallerCredentials?: boolean;
}
/**
 * An AppSync datasource backed by a DynamoDB table
 */
export declare class DynamoDbDataSource extends BackedDataSource {
    /**
     * Uniquely identifies this class.
     */
    static readonly PROPERTY_INJECTION_ID: string;
    constructor(scope: Construct, id: string, props: DynamoDbDataSourceProps);
}
/**
 * The authorization config in case the HTTP endpoint requires authorization
 */
export interface AwsIamConfig {
    /**
     * The signing region for AWS IAM authorization
     */
    readonly signingRegion: string;
    /**
     * The signing service name for AWS IAM authorization
     */
    readonly signingServiceName: string;
}
/**
 * Properties for an AppSync http datasource
 */
export interface HttpDataSourceProps extends BackedDataSourceProps {
    /**
     * The http endpoint
     */
    readonly endpoint: string;
    /**
     * The authorization config in case the HTTP endpoint requires authorization
     *
     * @default - none
     *
     */
    readonly authorizationConfig?: AwsIamConfig;
}
/**
 * An AppSync datasource backed by a http endpoint
 */
export declare class HttpDataSource extends BackedDataSource {
    /**
     * Uniquely identifies this class.
     */
    static readonly PROPERTY_INJECTION_ID: string;
    constructor(scope: Construct, id: string, props: HttpDataSourceProps);
}
/**
 * Properties for an AppSync EventBridge datasource
 */
export interface EventBridgeDataSourceProps extends BackedDataSourceProps {
    /**
     * The EventBridge EventBus
     */
    readonly eventBus: IEventBus;
}
/**
 * An AppSync datasource backed by EventBridge
 */
export declare class EventBridgeDataSource extends BackedDataSource {
    /**
     * Uniquely identifies this class.
     */
    static readonly PROPERTY_INJECTION_ID: string;
    constructor(scope: Construct, id: string, props: EventBridgeDataSourceProps);
}
/**
 * Properties for an AppSync Lambda datasource
 */
export interface LambdaDataSourceProps extends BackedDataSourceProps {
    /**
     * The Lambda function to call to interact with this data source
     */
    readonly lambdaFunction: IFunction;
}
/**
 * An AppSync datasource backed by a Lambda function
 */
export declare class LambdaDataSource extends BackedDataSource {
    /**
     * Uniquely identifies this class.
     */
    static readonly PROPERTY_INJECTION_ID: string;
    constructor(scope: Construct, id: string, props: LambdaDataSourceProps);
}
/**
 * Properties for an AppSync RDS datasource Aurora Serverless V1
 */
export interface RdsDataSourceProps extends BackedDataSourceProps {
    /**
     * The serverless cluster to call to interact with this data source
     */
    readonly serverlessCluster: IServerlessCluster;
    /**
     * The secret containing the credentials for the database
     */
    readonly secretStore: ISecret;
    /**
     * The name of the database to use within the cluster
     *
     * @default - None
     */
    readonly databaseName?: string;
}
/**
 * Properties for an AppSync RDS datasource Aurora Serverless V2
 */
export interface RdsDataSourcePropsV2 extends BackedDataSourceProps {
    /**
     * The serverless cluster to call to interact with this data source
     */
    readonly serverlessCluster: IDatabaseCluster;
    /**
     * The secret containing the credentials for the database
     */
    readonly secretStore: ISecret;
    /**
     * The name of the database to use within the cluster
     *
     * @default - None
     */
    readonly databaseName?: string;
}
/**
 * An AppSync datasource backed by RDS
 */
export declare class RdsDataSource extends BackedDataSource {
    /**
     * Uniquely identifies this class.
     */
    static readonly PROPERTY_INJECTION_ID: string;
    constructor(scope: Construct, id: string, props: RdsDataSourceProps);
}
/**
 * Properties for the Elasticsearch Data Source
 *
 * @deprecated - use `OpenSearchDataSourceProps` with `OpenSearchDataSource`
 */
export interface ElasticsearchDataSourceProps extends BackedDataSourceProps {
    /**
     * The elasticsearch domain containing the endpoint for the data source
     */
    readonly domain: IElasticsearchDomain;
}
/**
 * An Appsync datasource backed by Elasticsearch
 *
 * @deprecated - use `OpenSearchDataSource`
 */
export declare class ElasticsearchDataSource extends BackedDataSource {
    constructor(scope: Construct, id: string, props: ElasticsearchDataSourceProps);
}
/**
 * Properties for the OpenSearch Data Source
 */
export interface OpenSearchDataSourceProps extends BackedDataSourceProps {
    /**
     * The OpenSearch domain containing the endpoint for the data source
     */
    readonly domain: IOpenSearchDomain;
}
/**
 * An Appsync datasource backed by OpenSearch
 */
export declare class OpenSearchDataSource extends BackedDataSource {
    /**
     * Uniquely identifies this class.
     */
    static readonly PROPERTY_INJECTION_ID: string;
    constructor(scope: Construct, id: string, props: OpenSearchDataSourceProps);
}
