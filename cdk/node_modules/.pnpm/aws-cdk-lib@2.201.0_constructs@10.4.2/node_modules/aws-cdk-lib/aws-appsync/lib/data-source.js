"use strict";var __decorate=exports&&exports.__decorate||function(decorators,target,key,desc){var c=arguments.length,r=c<3?target:desc===null?desc=Object.getOwnPropertyDescriptor(target,key):desc,d;if(typeof Reflect=="object"&&typeof Reflect.decorate=="function")r=Reflect.decorate(decorators,target,key,desc);else for(var i=decorators.length-1;i>=0;i--)(d=decorators[i])&&(r=(c<3?d(r):c>3?d(target,key,r):d(target,key))||r);return c>3&&r&&Object.defineProperty(target,key,r),r},_a,_b,_c,_d,_e,_f,_g,_h,_j,_k;Object.defineProperty(exports,"__esModule",{value:!0}),exports.OpenSearchDataSource=exports.ElasticsearchDataSource=exports.RdsDataSource=exports.LambdaDataSource=exports.EventBridgeDataSource=exports.HttpDataSource=exports.DynamoDbDataSource=exports.NoneDataSource=exports.BackedDataSource=exports.BaseDataSource=void 0;var jsiiDeprecationWarnings=()=>{var tmp=require("../../.warnings.jsii.js");return jsiiDeprecationWarnings=()=>tmp,tmp};const JSII_RTTI_SYMBOL_1=Symbol.for("jsii.rtti");var constructs_1=()=>{var tmp=require("constructs");return constructs_1=()=>tmp,tmp},appsync_function_1=()=>{var tmp=require("./appsync-function");return appsync_function_1=()=>tmp,tmp},appsync_generated_1=()=>{var tmp=require("./appsync.generated");return appsync_generated_1=()=>tmp,tmp},resolver_1=()=>{var tmp=require("./resolver");return resolver_1=()=>tmp,tmp},aws_iam_1=()=>{var tmp=require("../../aws-iam");return aws_iam_1=()=>tmp,tmp},core_1=()=>{var tmp=require("../../core");return core_1=()=>tmp,tmp},prop_injectable_1=()=>{var tmp=require("../../core/lib/prop-injectable");return prop_injectable_1=()=>tmp,tmp};class BaseDataSource extends constructs_1().Construct{constructor(scope,id,props,extended){super(scope,id);try{jsiiDeprecationWarnings().aws_cdk_lib_aws_appsync_BackedDataSourceProps(props),jsiiDeprecationWarnings().aws_cdk_lib_aws_appsync_ExtendedDataSourceProps(extended)}catch(error){throw process.env.JSII_DEBUG!=="1"&&error.name==="DeprecationError"&&Error.captureStackTrace(error,BaseDataSource),error}extended.type!=="NONE"&&(this.serviceRole=props.serviceRole||new(aws_iam_1()).Role(this,"ServiceRole",{assumedBy:new(aws_iam_1()).ServicePrincipal("appsync.amazonaws.com")}));const name=props.name??id,supportedName=core_1().Token.isUnresolved(name)?name:name.replace(/[\W]+/g,"");this.ds=new(appsync_generated_1()).CfnDataSource(this,"Resource",{apiId:props.api.apiId,name:supportedName,description:props.description,serviceRoleArn:this.serviceRole?.roleArn,...extended}),this.name=supportedName,this.api=props.api}createResolver(id,props){try{jsiiDeprecationWarnings().aws_cdk_lib_aws_appsync_BaseResolverProps(props)}catch(error){throw process.env.JSII_DEBUG!=="1"&&error.name==="DeprecationError"&&Error.captureStackTrace(error,this.createResolver),error}return new(resolver_1()).Resolver(this.api,id,{api:this.api,dataSource:this,...props})}createFunction(id,props){try{jsiiDeprecationWarnings().aws_cdk_lib_aws_appsync_BaseAppsyncFunctionProps(props)}catch(error){throw process.env.JSII_DEBUG!=="1"&&error.name==="DeprecationError"&&Error.captureStackTrace(error,this.createFunction),error}return new(appsync_function_1()).AppsyncFunction(this.api,id,{api:this.api,dataSource:this,...props})}}exports.BaseDataSource=BaseDataSource,_a=JSII_RTTI_SYMBOL_1,BaseDataSource[_a]={fqn:"aws-cdk-lib.aws_appsync.BaseDataSource",version:"2.201.0"};class BackedDataSource extends BaseDataSource{constructor(scope,id,props,extended){super(scope,id,props,extended);try{jsiiDeprecationWarnings().aws_cdk_lib_aws_appsync_BackedDataSourceProps(props),jsiiDeprecationWarnings().aws_cdk_lib_aws_appsync_ExtendedDataSourceProps(extended)}catch(error){throw process.env.JSII_DEBUG!=="1"&&error.name==="DeprecationError"&&Error.captureStackTrace(error,BackedDataSource),error}this.grantPrincipal=this.serviceRole}}exports.BackedDataSource=BackedDataSource,_b=JSII_RTTI_SYMBOL_1,BackedDataSource[_b]={fqn:"aws-cdk-lib.aws_appsync.BackedDataSource",version:"2.201.0"};class NoneDataSource extends BaseDataSource{constructor(scope,id,props){super(scope,id,props,{type:"NONE"});try{jsiiDeprecationWarnings().aws_cdk_lib_aws_appsync_NoneDataSourceProps(props)}catch(error){throw process.env.JSII_DEBUG!=="1"&&error.name==="DeprecationError"&&Error.captureStackTrace(error,NoneDataSource),error}}}exports.NoneDataSource=NoneDataSource,_c=JSII_RTTI_SYMBOL_1,NoneDataSource[_c]={fqn:"aws-cdk-lib.aws_appsync.NoneDataSource",version:"2.201.0"};let DynamoDbDataSource=class DynamoDbDataSource2 extends BackedDataSource{constructor(scope,id,props){super(scope,id,props,{type:"AMAZON_DYNAMODB",dynamoDbConfig:{tableName:props.table.tableName,awsRegion:props.table.env.region,useCallerCredentials:props.useCallerCredentials}});try{jsiiDeprecationWarnings().aws_cdk_lib_aws_appsync_DynamoDbDataSourceProps(props)}catch(error){throw process.env.JSII_DEBUG!=="1"&&error.name==="DeprecationError"&&Error.captureStackTrace(error,DynamoDbDataSource2),error}props.readOnlyAccess?props.table.grantReadData(this):props.table.grantReadWriteData(this)}};exports.DynamoDbDataSource=DynamoDbDataSource,_d=JSII_RTTI_SYMBOL_1,DynamoDbDataSource[_d]={fqn:"aws-cdk-lib.aws_appsync.DynamoDbDataSource",version:"2.201.0"},DynamoDbDataSource.PROPERTY_INJECTION_ID="aws-cdk-lib.aws-appsync.DynamoDbDataSource",exports.DynamoDbDataSource=DynamoDbDataSource=__decorate([prop_injectable_1().propertyInjectable],DynamoDbDataSource);let HttpDataSource=class HttpDataSource2 extends BackedDataSource{constructor(scope,id,props){try{jsiiDeprecationWarnings().aws_cdk_lib_aws_appsync_HttpDataSourceProps(props)}catch(error){throw process.env.JSII_DEBUG!=="1"&&error.name==="DeprecationError"&&Error.captureStackTrace(error,HttpDataSource2),error}const authorizationConfig=props.authorizationConfig?{authorizationType:"AWS_IAM",awsIamConfig:props.authorizationConfig}:void 0;super(scope,id,props,{type:"HTTP",httpConfig:{endpoint:props.endpoint,authorizationConfig}})}};exports.HttpDataSource=HttpDataSource,_e=JSII_RTTI_SYMBOL_1,HttpDataSource[_e]={fqn:"aws-cdk-lib.aws_appsync.HttpDataSource",version:"2.201.0"},HttpDataSource.PROPERTY_INJECTION_ID="aws-cdk-lib.aws-appsync.HttpDataSource",exports.HttpDataSource=HttpDataSource=__decorate([prop_injectable_1().propertyInjectable],HttpDataSource);let EventBridgeDataSource=class EventBridgeDataSource2 extends BackedDataSource{constructor(scope,id,props){super(scope,id,props,{type:"AMAZON_EVENTBRIDGE",eventBridgeConfig:{eventBusArn:props.eventBus.eventBusArn}});try{jsiiDeprecationWarnings().aws_cdk_lib_aws_appsync_EventBridgeDataSourceProps(props)}catch(error){throw process.env.JSII_DEBUG!=="1"&&error.name==="DeprecationError"&&Error.captureStackTrace(error,EventBridgeDataSource2),error}props.eventBus.grantPutEventsTo(this)}};exports.EventBridgeDataSource=EventBridgeDataSource,_f=JSII_RTTI_SYMBOL_1,EventBridgeDataSource[_f]={fqn:"aws-cdk-lib.aws_appsync.EventBridgeDataSource",version:"2.201.0"},EventBridgeDataSource.PROPERTY_INJECTION_ID="aws-cdk-lib.aws-appsync.EventBridgeDataSource",exports.EventBridgeDataSource=EventBridgeDataSource=__decorate([prop_injectable_1().propertyInjectable],EventBridgeDataSource);let LambdaDataSource=class LambdaDataSource2 extends BackedDataSource{constructor(scope,id,props){super(scope,id,props,{type:"AWS_LAMBDA",lambdaConfig:{lambdaFunctionArn:props.lambdaFunction.functionArn}});try{jsiiDeprecationWarnings().aws_cdk_lib_aws_appsync_LambdaDataSourceProps(props)}catch(error){throw process.env.JSII_DEBUG!=="1"&&error.name==="DeprecationError"&&Error.captureStackTrace(error,LambdaDataSource2),error}props.lambdaFunction.grantInvoke(this)}};exports.LambdaDataSource=LambdaDataSource,_g=JSII_RTTI_SYMBOL_1,LambdaDataSource[_g]={fqn:"aws-cdk-lib.aws_appsync.LambdaDataSource",version:"2.201.0"},LambdaDataSource.PROPERTY_INJECTION_ID="aws-cdk-lib.aws-appsync.LambdaDataSource",exports.LambdaDataSource=LambdaDataSource=__decorate([prop_injectable_1().propertyInjectable],LambdaDataSource);let RdsDataSource=class RdsDataSource2 extends BackedDataSource{constructor(scope,id,props){super(scope,id,props,{type:"RELATIONAL_DATABASE",relationalDatabaseConfig:{rdsHttpEndpointConfig:{awsRegion:props.serverlessCluster.env.region,dbClusterIdentifier:core_1().Lazy.string({produce:()=>core_1().Stack.of(this).formatArn({service:"rds",resource:`cluster:${props.serverlessCluster.clusterIdentifier}`})}),awsSecretStoreArn:props.secretStore.secretArn,databaseName:props.databaseName},relationalDatabaseSourceType:"RDS_HTTP_ENDPOINT"}});try{jsiiDeprecationWarnings().aws_cdk_lib_aws_appsync_RdsDataSourceProps(props)}catch(error){throw process.env.JSII_DEBUG!=="1"&&error.name==="DeprecationError"&&Error.captureStackTrace(error,RdsDataSource2),error}const clusterArn=core_1().Stack.of(this).formatArn({service:"rds",resource:`cluster:${props.serverlessCluster.clusterIdentifier}`});props.secretStore.grantRead(this),props.serverlessCluster.grantDataApiAccess(this),aws_iam_1().Grant.addToPrincipal({grantee:this,actions:["rds-data:DeleteItems","rds-data:ExecuteSql","rds-data:GetItems","rds-data:InsertItems","rds-data:UpdateItems"],resourceArns:[clusterArn,`${clusterArn}:*`],scope:this})}};exports.RdsDataSource=RdsDataSource,_h=JSII_RTTI_SYMBOL_1,RdsDataSource[_h]={fqn:"aws-cdk-lib.aws_appsync.RdsDataSource",version:"2.201.0"},RdsDataSource.PROPERTY_INJECTION_ID="aws-cdk-lib.aws-appsync.RdsDataSource",exports.RdsDataSource=RdsDataSource=__decorate([prop_injectable_1().propertyInjectable],RdsDataSource);class ElasticsearchDataSource extends BackedDataSource{constructor(scope,id,props){super(scope,id,props,{type:"AMAZON_ELASTICSEARCH",elasticsearchConfig:{awsRegion:props.domain.env.region,endpoint:`https://${props.domain.domainEndpoint}`}});try{jsiiDeprecationWarnings().print("aws-cdk-lib.aws_appsync.ElasticsearchDataSource","- use `OpenSearchDataSource`"),jsiiDeprecationWarnings().aws_cdk_lib_aws_appsync_ElasticsearchDataSourceProps(props)}catch(error){throw process.env.JSII_DEBUG!=="1"&&error.name==="DeprecationError"&&Error.captureStackTrace(error,ElasticsearchDataSource),error}props.domain.grantReadWrite(this)}}exports.ElasticsearchDataSource=ElasticsearchDataSource,_j=JSII_RTTI_SYMBOL_1,ElasticsearchDataSource[_j]={fqn:"aws-cdk-lib.aws_appsync.ElasticsearchDataSource",version:"2.201.0"};let OpenSearchDataSource=class OpenSearchDataSource2 extends BackedDataSource{constructor(scope,id,props){super(scope,id,props,{type:"AMAZON_OPENSEARCH_SERVICE",openSearchServiceConfig:{awsRegion:props.domain.env.region,endpoint:`https://${props.domain.domainEndpoint}`}});try{jsiiDeprecationWarnings().aws_cdk_lib_aws_appsync_OpenSearchDataSourceProps(props)}catch(error){throw process.env.JSII_DEBUG!=="1"&&error.name==="DeprecationError"&&Error.captureStackTrace(error,OpenSearchDataSource2),error}props.domain.grantReadWrite(this)}};exports.OpenSearchDataSource=OpenSearchDataSource,_k=JSII_RTTI_SYMBOL_1,OpenSearchDataSource[_k]={fqn:"aws-cdk-lib.aws_appsync.OpenSearchDataSource",version:"2.201.0"},OpenSearchDataSource.PROPERTY_INJECTION_ID="aws-cdk-lib.aws-appsync.OpenSearchDataSource",exports.OpenSearchDataSource=OpenSearchDataSource=__decorate([prop_injectable_1().propertyInjectable],OpenSearchDataSource);
