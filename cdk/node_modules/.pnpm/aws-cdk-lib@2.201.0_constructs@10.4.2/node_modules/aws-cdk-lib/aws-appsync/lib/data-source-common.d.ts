import { Construct } from 'constructs';
import { IApi } from './api-base';
import { CfnDataSource } from './appsync.generated';
import { ITable } from '../../aws-dynamodb';
import { IEventBus } from '../../aws-events';
import { IRole, IPrincipal, IGrantable } from '../../aws-iam';
import { IFunction } from '../../aws-lambda';
import { IDomain } from '../../aws-opensearchservice';
import { IDatabaseCluster, IServerlessCluster } from '../../aws-rds';
import { ISecret } from '../../aws-secretsmanager';
import { IResolvable } from '../../core';
/**
 * Valid data source types for AppSync
 */
export declare enum AppSyncDataSourceType {
    /**
     * Lambda data source type
     */
    LAMBDA = "AWS_LAMBDA",
    /**
     * DynamoDB data source type
     */
    DYNAMODB = "AMAZON_DYNAMODB",
    /**
     * EventBridge data source type
     */
    EVENTBRIDGE = "AMAZON_EVENTBRIDGE",
    /**
     * OpenSearch service data source type
     */
    OPENSEARCH_SERVICE = "AMAZON_OPENSEARCH_SERVICE",
    /**
     * HTTP data source type
     */
    HTTP = "HTTP",
    /**
     * Relational DB data source type
     */
    RELATIONAL_DATABASE = "RELATIONAL_DATABASE",
    /**
     * Bedrock runtime data source type
     */
    BEDROCK = "AMAZON_BEDROCK_RUNTIME",
    /**
     * Elasticsearch data source type
     */
    ELASTICSEARCH = "AMAZON_ELASTICSEARCH"
}
/**
 * Invoke types for direct Lambda data sources
 */
export declare enum LambdaInvokeType {
    /**
     * Invoke function asynchronously
     */
    EVENT = "EVENT",
    /**
     * Invoke function synchronously
     */
    REQUEST_RESPONSE = "REQUEST_RESPONSE"
}
/**
 * Base properties for an AppSync datasource
 */
export interface AppSyncBaseDataSourceProps {
    /**
     * The API to attach this data source to
     */
    readonly api: IApi;
    /**
     * The name of the data source. The only allowed pattern is: {[_A-Za-z][_0-9A-Za-z]*}.
     * Any invalid characters will be automatically removed.
     *
     * @default - id of data source
     */
    readonly name?: string;
    /**
     * The description of the data source
     *
     * @default - None
     */
    readonly description?: string;
}
/**
 * Properties for an AppSync datasource backed by a resource
 */
export interface AppSyncBackedDataSourceProps extends AppSyncBaseDataSourceProps {
    /**
     * The IAM service role to be assumed by AppSync to interact with the data source
     *
     * @default -  Create a new role
     */
    readonly serviceRole?: IRole;
}
/**
 * Props used by implementations of BaseDataSource to provide configuration. Should not be used directly.
 */
export interface AppSyncExtendedDataSourceProps {
    /**
     * The type of the AppSync datasource
     */
    readonly type: AppSyncDataSourceType;
    /**
     * Configuration for DynamoDB Datasource
     *
     * @default - No config
     */
    readonly dynamoDbConfig?: CfnDataSource.DynamoDBConfigProperty | IResolvable;
    /**
     * Configuration for OpenSearch data source
     *
     * @default - No config
     */
    readonly openSearchServiceConfig?: CfnDataSource.OpenSearchServiceConfigProperty | IResolvable;
    /**
     * Configuration for HTTP Datasource
     *
     * @default - No config
     */
    readonly httpConfig?: CfnDataSource.HttpConfigProperty | IResolvable;
    /**
     * Configuration for EventBridge Datasource
     *
     * @default - No config
     */
    readonly eventBridgeConfig?: CfnDataSource.EventBridgeConfigProperty | IResolvable;
    /**
     * Configuration for Lambda Datasource
     *
     * @default - No config
     */
    readonly lambdaConfig?: CfnDataSource.LambdaConfigProperty | IResolvable;
    /**
     * Configuration for RDS Datasource
     *
     * @default - No config
     */
    readonly relationalDatabaseConfig?: CfnDataSource.RelationalDatabaseConfigProperty | IResolvable;
}
/**
 * Abstract AppSync datasource implementation. Do not use directly but use subclasses for concrete datasources
 */
export declare abstract class AppSyncBaseDataSource extends Construct {
    /**
     * The name of the data source
     */
    readonly name: string;
    /**
     * The underlying CFN data source resource
     */
    readonly resource: CfnDataSource;
    protected api: IApi;
    protected serviceRole?: IRole;
    constructor(scope: Construct, id: string, props: AppSyncBackedDataSourceProps, extended: AppSyncExtendedDataSourceProps);
}
/**
 * Abstract AppSync datasource implementation. Do not use directly but use subclasses for resource backed datasources
 */
export declare abstract class AppSyncBackedDataSource extends AppSyncBaseDataSource implements IGrantable {
    /**
     * The principal of the data source to be IGrantable
     */
    readonly grantPrincipal: IPrincipal;
    constructor(scope: Construct, id: string, props: AppSyncBackedDataSourceProps, extended: AppSyncExtendedDataSourceProps);
}
/**
 * Properties for an AppSync DynamoDB datasource
 */
export interface AppSyncDynamoDbDataSourceProps extends AppSyncBackedDataSourceProps {
    /**
     * The DynamoDB table backing this data source
     */
    readonly table: ITable;
    /**
     * Specify whether this Data Source is read only or has read and write permissions to the DynamoDB table
     *
     * @default false
     */
    readonly readOnlyAccess?: boolean;
    /**
     * Use credentials of caller to access DynamoDB
     *
     * @default false
     */
    readonly useCallerCredentials?: boolean;
}
/**
 * An AppSync datasource backed by a DynamoDB table
 */
export declare class AppSyncDynamoDbDataSource extends AppSyncBackedDataSource {
    constructor(scope: Construct, id: string, props: AppSyncDynamoDbDataSourceProps);
}
/**
 * The authorization config in case the HTTP endpoint requires authorization
 */
export interface AppSyncAwsIamConfig {
    /**
     * The signing region for AWS IAM authorization
     */
    readonly signingRegion: string;
    /**
     * The signing service name for AWS IAM authorization
     */
    readonly signingServiceName: string;
}
/**
 * Optional configuration for data sources
 */
export interface AppSyncDataSourceOptions {
    /**
     * The name of the data source, overrides the id given by CDK
     *
     * @default - generated by CDK given the id
     */
    readonly name?: string;
    /**
     * The description of the data source
     *
     * @default - No description
     */
    readonly description?: string;
}
/**
 * Optional configuration for Http data sources
 */
export interface AppSyncHttpDataSourceOptions extends AppSyncDataSourceOptions {
    /**
     * The authorization config in case the HTTP endpoint requires authorization
     *
     * @default - none
     */
    readonly authorizationConfig?: AppSyncAwsIamConfig;
}
/**
 * Properties for an AppSync http datasource
 */
export interface AppSyncHttpDataSourceProps extends AppSyncBackedDataSourceProps {
    /**
     * The http endpoint
     */
    readonly endpoint: string;
    /**
     * The authorization config in case the HTTP endpoint requires authorization
     *
     * @default - none
     */
    readonly authorizationConfig?: AppSyncAwsIamConfig;
}
/**
 * An AppSync datasource backed by a http endpoint
 */
export declare class AppSyncHttpDataSource extends AppSyncBackedDataSource {
    constructor(scope: Construct, id: string, props: AppSyncHttpDataSourceProps);
}
/**
 * Properties for an AppSync EventBridge datasource
 */
export interface AppSyncEventBridgeDataSourceProps extends AppSyncBackedDataSourceProps {
    /**
     * The EventBridge EventBus
     */
    readonly eventBus: IEventBus;
}
/**
 * An AppSync datasource backed by EventBridge
 */
export declare class AppSyncEventBridgeDataSource extends AppSyncBackedDataSource {
    constructor(scope: Construct, id: string, props: AppSyncEventBridgeDataSourceProps);
}
/**
 * Properties for an AppSync Lambda datasource
 */
export interface AppSyncLambdaDataSourceProps extends AppSyncBackedDataSourceProps {
    /**
     * The Lambda function to call to interact with this data source
     */
    readonly lambdaFunction: IFunction;
}
/**
 * An AppSync datasource backed by a Lambda function
 */
export declare class AppSyncLambdaDataSource extends AppSyncBackedDataSource {
    constructor(scope: Construct, id: string, props: AppSyncLambdaDataSourceProps);
}
/**
 * Properties for an AppSync RDS datasource Aurora Serverless V1
 */
export interface AppSyncRdsDataSourceProps extends AppSyncBackedDataSourceProps {
    /**
     * The serverless cluster to call to interact with this data source
     */
    readonly serverlessCluster: IServerlessCluster;
    /**
     * The secret containing the credentials for the database
     */
    readonly secretStore: ISecret;
    /**
     * The name of the database to use within the cluster
     *
     * @default - None
     */
    readonly databaseName?: string;
}
/**
 * Properties for an AppSync RDS datasource Aurora Serverless V2
 */
export interface AppSyncRdsDataSourcePropsV2 extends AppSyncBackedDataSourceProps {
    /**
     * The serverless cluster to call to interact with this data source
     */
    readonly serverlessCluster: IDatabaseCluster;
    /**
     * The secret containing the credentials for the database
     */
    readonly secretStore: ISecret;
    /**
     * The name of the database to use within the cluster
     *
     * @default - None
     */
    readonly databaseName?: string;
}
/**
 * An AppSync datasource backed by RDS
 */
export declare class AppSyncRdsDataSource extends AppSyncBackedDataSource {
    constructor(scope: Construct, id: string, props: AppSyncRdsDataSourceProps);
}
/**
 * Properties for the OpenSearch Data Source
 */
export interface AppSyncOpenSearchDataSourceProps extends AppSyncBackedDataSourceProps {
    /**
     * The OpenSearch domain containing the endpoint for the data source
     */
    readonly domain: IDomain;
}
/**
 * An Appsync datasource backed by OpenSearch
 */
export declare class AppSyncOpenSearchDataSource extends AppSyncBackedDataSource {
    constructor(scope: Construct, id: string, props: AppSyncOpenSearchDataSourceProps);
}
