import { IResource, Resource } from '../../core';
/**
 * Interface for an API
 */
export interface IApi extends IResource {
    /**
     * The unique identifier for the AWS AppSync Api generated by the service.
     *
     * @attribute
     */
    readonly apiId: string;
    /**
     * The ARN of the AWS AppSync Api.
     *
     * @attribute
     */
    readonly apiArn: string;
}
/**
 * Base Class for API
 */
export declare abstract class ApiBase extends Resource implements IApi {
    /**
     * The unique identifier for the AWS AppSync Api generated by the service.
     */
    abstract readonly apiId: string;
    /**
     * The ARN of the AWS AppSync Api.
     */
    abstract readonly apiArn: string;
}
