"use strict";var __decorate=exports&&exports.__decorate||function(decorators,target,key,desc){var c=arguments.length,r=c<3?target:desc===null?desc=Object.getOwnPropertyDescriptor(target,key):desc,d;if(typeof Reflect=="object"&&typeof Reflect.decorate=="function")r=Reflect.decorate(decorators,target,key,desc);else for(var i=decorators.length-1;i>=0;i--)(d=decorators[i])&&(r=(c<3?d(r):c>3?d(target,key,r):d(target,key))||r);return c>3&&r&&Object.defineProperty(target,key,r),r},_a,_b;Object.defineProperty(exports,"__esModule",{value:!0}),exports.EventApi=exports.EventApiBase=void 0;var jsiiDeprecationWarnings=()=>{var tmp=require("../../.warnings.jsii.js");return jsiiDeprecationWarnings=()=>tmp,tmp};const JSII_RTTI_SYMBOL_1=Symbol.for("jsii.rtti");var api_base_1=()=>{var tmp=require("./api-base");return api_base_1=()=>tmp,tmp},appsync_common_1=()=>{var tmp=require("./appsync-common");return appsync_common_1=()=>tmp,tmp},appsync_generated_1=()=>{var tmp=require("./appsync.generated");return appsync_generated_1=()=>tmp,tmp},auth_config_1=()=>{var tmp=require("./auth-config");return auth_config_1=()=>tmp,tmp},channel_namespace_1=()=>{var tmp=require("./channel-namespace");return channel_namespace_1=()=>tmp,tmp},data_source_common_1=()=>{var tmp=require("./data-source-common");return data_source_common_1=()=>tmp,tmp},aws_iam_1=()=>{var tmp=require("../../aws-iam");return aws_iam_1=()=>tmp,tmp},aws_logs_1=()=>{var tmp=require("../../aws-logs");return aws_logs_1=()=>tmp,tmp},core_1=()=>{var tmp=require("../../core");return core_1=()=>tmp,tmp},metadata_resource_1=()=>{var tmp=require("../../core/lib/metadata-resource");return metadata_resource_1=()=>tmp,tmp},prop_injectable_1=()=>{var tmp=require("../../core/lib/prop-injectable");return prop_injectable_1=()=>tmp,tmp};class AppSyncEventApiAuthConfig{setupOpenIdConnectConfig(config){if(config)return{authTtl:config.tokenExpiryFromAuth,clientId:config.clientId,iatTtl:config.tokenExpiryFromIssue,issuer:config.oidcProvider}}setupCognitoConfig(config){if(config)return{userPoolId:config.userPool.userPoolId,awsRegion:config.userPool.env.region,appIdClientRegex:config.appIdClientRegex}}setupLambdaAuthorizerConfig(config){if(config)return{authorizerResultTtlInSeconds:config.resultsCacheTtl?.toSeconds(),authorizerUri:config.handler.functionArn,identityValidationExpression:config.validationRegex}}}class EventApiBase extends api_base_1().ApiBase{addChannelNamespace(id,options){try{jsiiDeprecationWarnings().aws_cdk_lib_aws_appsync_ChannelNamespaceOptions(options)}catch(error){throw process.env.JSII_DEBUG!=="1"&&error.name==="DeprecationError"&&Error.captureStackTrace(error,this.addChannelNamespace),error}return new(channel_namespace_1()).ChannelNamespace(this,id,{api:this,channelNamespaceName:options?.channelNamespaceName??id,...options})}addDynamoDbDataSource(id,table,options){try{jsiiDeprecationWarnings().aws_cdk_lib_aws_dynamodb_ITable(table),jsiiDeprecationWarnings().aws_cdk_lib_aws_appsync_AppSyncDataSourceOptions(options)}catch(error){throw process.env.JSII_DEBUG!=="1"&&error.name==="DeprecationError"&&Error.captureStackTrace(error,this.addDynamoDbDataSource),error}return new(data_source_common_1()).AppSyncDynamoDbDataSource(this,id,{api:this,table,name:options?.name,description:options?.description})}addHttpDataSource(id,endpoint,options){try{jsiiDeprecationWarnings().aws_cdk_lib_aws_appsync_AppSyncHttpDataSourceOptions(options)}catch(error){throw process.env.JSII_DEBUG!=="1"&&error.name==="DeprecationError"&&Error.captureStackTrace(error,this.addHttpDataSource),error}return new(data_source_common_1()).AppSyncHttpDataSource(this,id,{api:this,endpoint,name:options?.name,description:options?.description,authorizationConfig:options?.authorizationConfig})}addLambdaDataSource(id,lambdaFunction,options){try{jsiiDeprecationWarnings().aws_cdk_lib_aws_lambda_IFunction(lambdaFunction),jsiiDeprecationWarnings().aws_cdk_lib_aws_appsync_AppSyncDataSourceOptions(options)}catch(error){throw process.env.JSII_DEBUG!=="1"&&error.name==="DeprecationError"&&Error.captureStackTrace(error,this.addLambdaDataSource),error}return new(data_source_common_1()).AppSyncLambdaDataSource(this,id,{api:this,lambdaFunction,name:options?.name,description:options?.description})}addRdsDataSource(id,serverlessCluster,secretStore,databaseName,options){try{jsiiDeprecationWarnings().aws_cdk_lib_aws_secretsmanager_ISecret(secretStore),jsiiDeprecationWarnings().aws_cdk_lib_aws_appsync_AppSyncDataSourceOptions(options)}catch(error){throw process.env.JSII_DEBUG!=="1"&&error.name==="DeprecationError"&&Error.captureStackTrace(error,this.addRdsDataSource),error}return new(data_source_common_1()).AppSyncRdsDataSource(this,id,{api:this,name:options?.name,description:options?.description,serverlessCluster,secretStore,databaseName})}addEventBridgeDataSource(id,eventBus,options){try{jsiiDeprecationWarnings().aws_cdk_lib_aws_events_IEventBus(eventBus),jsiiDeprecationWarnings().aws_cdk_lib_aws_appsync_AppSyncDataSourceOptions(options)}catch(error){throw process.env.JSII_DEBUG!=="1"&&error.name==="DeprecationError"&&Error.captureStackTrace(error,this.addEventBridgeDataSource),error}return new(data_source_common_1()).AppSyncEventBridgeDataSource(this,id,{api:this,eventBus,name:options?.name,description:options?.description})}addOpenSearchDataSource(id,domain,options){try{jsiiDeprecationWarnings().aws_cdk_lib_aws_opensearchservice_IDomain(domain),jsiiDeprecationWarnings().aws_cdk_lib_aws_appsync_AppSyncDataSourceOptions(options)}catch(error){throw process.env.JSII_DEBUG!=="1"&&error.name==="DeprecationError"&&Error.captureStackTrace(error,this.addOpenSearchDataSource),error}return new(data_source_common_1()).AppSyncOpenSearchDataSource(this,id,{api:this,name:options?.name,description:options?.description,domain})}grant(grantee,resources,...actions){try{jsiiDeprecationWarnings().aws_cdk_lib_aws_iam_IGrantable(grantee),jsiiDeprecationWarnings().aws_cdk_lib_aws_appsync_AppSyncEventResource(resources)}catch(error){throw process.env.JSII_DEBUG!=="1"&&error.name==="DeprecationError"&&Error.captureStackTrace(error,this.grant),error}if(!this.authProviderTypes.includes(auth_config_1().AppSyncAuthorizationType.IAM))throw new(core_1()).ValidationError("Cannot use grant method because IAM Authorization mode is missing in the auth providers on this API.",this);return aws_iam_1().Grant.addToPrincipal({grantee,actions,resourceArns:resources.resourceArns(this),scope:this})}grantPublish(grantee){try{jsiiDeprecationWarnings().aws_cdk_lib_aws_iam_IGrantable(grantee)}catch(error){throw process.env.JSII_DEBUG!=="1"&&error.name==="DeprecationError"&&Error.captureStackTrace(error,this.grantPublish),error}return this.grant(grantee,appsync_common_1().AppSyncEventResource.allChannelNamespaces(),"appsync:EventPublish")}grantSubscribe(grantee){try{jsiiDeprecationWarnings().aws_cdk_lib_aws_iam_IGrantable(grantee)}catch(error){throw process.env.JSII_DEBUG!=="1"&&error.name==="DeprecationError"&&Error.captureStackTrace(error,this.grantSubscribe),error}return this.grant(grantee,appsync_common_1().AppSyncEventResource.allChannelNamespaces(),"appsync:EventSubscribe")}grantPublishAndSubscribe(grantee){try{jsiiDeprecationWarnings().aws_cdk_lib_aws_iam_IGrantable(grantee)}catch(error){throw process.env.JSII_DEBUG!=="1"&&error.name==="DeprecationError"&&Error.captureStackTrace(error,this.grantPublishAndSubscribe),error}return this.grant(grantee,appsync_common_1().AppSyncEventResource.allChannelNamespaces(),"appsync:EventPublish","appsync:EventSubscribe")}grantConnect(grantee){try{jsiiDeprecationWarnings().aws_cdk_lib_aws_iam_IGrantable(grantee)}catch(error){throw process.env.JSII_DEBUG!=="1"&&error.name==="DeprecationError"&&Error.captureStackTrace(error,this.grantConnect),error}return this.grant(grantee,appsync_common_1().AppSyncEventResource.forAPI(),"appsync:EventConnect")}}exports.EventApiBase=EventApiBase,_a=JSII_RTTI_SYMBOL_1,EventApiBase[_a]={fqn:"aws-cdk-lib.aws_appsync.EventApiBase",version:"2.201.0"};let EventApi=class EventApi2 extends EventApiBase{static fromEventApiAttributes(scope,id,attrs){try{jsiiDeprecationWarnings().aws_cdk_lib_aws_appsync_EventApiAttributes(attrs)}catch(error){throw process.env.JSII_DEBUG!=="1"&&error.name==="DeprecationError"&&Error.captureStackTrace(error,this.fromEventApiAttributes),error}const arn=attrs.apiArn??core_1().Stack.of(scope).formatArn({service:"appsync",resource:"apis",resourceName:attrs.apiId});class Import extends EventApiBase{constructor(){super(...arguments),this.apiId=attrs.apiId,this.apiArn=arn,this.httpDns=attrs.httpDns,this.realtimeDns=attrs.realtimeDns,this.authProviderTypes=attrs.authProviderTypes??[]}}return new Import(scope,id)}constructor(scope,id,props){try{jsiiDeprecationWarnings().aws_cdk_lib_aws_appsync_EventApiProps(props)}catch(error){throw process.env.JSII_DEBUG!=="1"&&error.name==="DeprecationError"&&Error.captureStackTrace(error,EventApi2),error}if(props.apiName!==void 0&&!core_1().Token.isUnresolved(props.apiName)&&(props.apiName.length<1||props.apiName.length>50))throw new(core_1()).ValidationError(`\`apiName\` must be between 1 and 50 characters, got: ${props.apiName.length} characters.`,scope);super(scope,id,{physicalName:props.apiName??core_1().Lazy.string({produce:()=>core_1().Names.uniqueResourceName(this,{maxLength:50,separator:"-"})})}),this.apiKeys={},(0,metadata_resource_1().addConstructMetadata)(this,props);const defaultAuthProviders=[{authorizationType:auth_config_1().AppSyncAuthorizationType.API_KEY}],authProviders=props.authorizationConfig?.authProviders??defaultAuthProviders;this.authProviderTypes=this.setupAuthProviderTypes(authProviders);const connectionAuthModeTypes=props.authorizationConfig?.connectionAuthModeTypes??this.authProviderTypes,defaultPublishAuthModeTypes=props.authorizationConfig?.defaultPublishAuthModeTypes??this.authProviderTypes,defaultSubscribeAuthModeTypes=props.authorizationConfig?.defaultSubscribeAuthModeTypes??this.authProviderTypes;this.connectionModeTypes=connectionAuthModeTypes,this.defaultPublishModeTypes=defaultPublishAuthModeTypes,this.defaultSubscribeModeTypes=defaultSubscribeAuthModeTypes,this.validateEventApiConfiguration(props,authProviders),this.eventConfig={authProviders:this.mapAuthorizationProviders(authProviders),connectionAuthModes:this.mapAuthorizationConfig(connectionAuthModeTypes),defaultPublishAuthModes:this.mapAuthorizationConfig(defaultPublishAuthModeTypes),defaultSubscribeAuthModes:this.mapAuthorizationConfig(defaultSubscribeAuthModeTypes),logConfig:this.setupLogConfig(props.logConfig)},this.api=new(appsync_generated_1()).CfnApi(this,"Resource",{name:this.physicalName,ownerContact:props.ownerContact,eventConfig:this.eventConfig}),this.apiId=this.api.attrApiId,this.apiArn=this.api.attrApiArn,this.httpDns=this.api.attrDnsHttp,this.realtimeDns=this.api.attrDnsRealtime;const apiKeyConfigs=authProviders.filter(mode=>mode.authorizationType===auth_config_1().AppSyncAuthorizationType.API_KEY);for(const mode of apiKeyConfigs)this.apiKeys[mode.apiKeyConfig?.name??"Default"]=(0,auth_config_1().createAPIKey)(this,this.apiId,mode.apiKeyConfig);authProviders.some(mode=>mode.authorizationType===auth_config_1().AppSyncAuthorizationType.LAMBDA)&&authProviders.find(mode=>mode.authorizationType===auth_config_1().AppSyncAuthorizationType.LAMBDA&&mode.lambdaAuthorizerConfig)?.lambdaAuthorizerConfig?.handler.addPermission(`${id}-appsync`,{principal:new(aws_iam_1()).ServicePrincipal("appsync.amazonaws.com"),action:"lambda:InvokeFunction",sourceArn:this.apiArn}),props.domainName&&(this.domainNameResource=new(appsync_generated_1()).CfnDomainName(this,"DomainName",{domainName:props.domainName.domainName,certificateArn:props.domainName.certificate.certificateArn,description:`domain for ${props.apiName} Event API`}),new(appsync_generated_1()).CfnDomainNameApiAssociation(this,"DomainAssociation",{domainName:props.domainName.domainName,apiId:this.apiId}).addDependency(this.domainNameResource));const logGroupName=`/aws/appsync/apis/${this.apiId}`;if(props.logConfig){const logRetention=new(aws_logs_1()).LogRetention(this,"LogRetention",{logGroupName,retention:props.logConfig?.retention??aws_logs_1().RetentionDays.INFINITE});this.logGroup=aws_logs_1().LogGroup.fromLogGroupArn(this,"LogGroup",logRetention.logGroupArn)}else this.logGroup=aws_logs_1().LogGroup.fromLogGroupName(this,"LogGroup",logGroupName)}validateEventApiConfiguration(props,authProviders){this.validateOwnerContact(props.ownerContact),this.validateAuthorizationProps(authProviders),this.validateAuthorizationConfig(authProviders,this.connectionModeTypes),this.validateAuthorizationConfig(authProviders,this.defaultPublishModeTypes),this.validateAuthorizationConfig(authProviders,this.defaultSubscribeModeTypes)}validateOwnerContact(ownerContact){if(ownerContact===void 0||core_1().Token.isUnresolved(ownerContact))return;if(ownerContact.length<1||ownerContact.length>256)throw new(core_1()).ValidationError(`\`ownerContact\` must be between 1 and 256 characters, got: ${ownerContact.length} characters.`,this);if(!/^[A-Za-z0-9_\-\ \.]+$/.test(ownerContact))throw new(core_1()).ValidationError(`\`ownerContact\` must contain only alphanumeric characters, underscores, hyphens, spaces, and periods, got: ${ownerContact}`,this)}setupLogConfig(config){if(!config)return;const logsRoleArn=config.role?.roleArn??new(aws_iam_1()).Role(this,"ApiLogsRole",{assumedBy:new(aws_iam_1()).ServicePrincipal("appsync.amazonaws.com"),managedPolicies:[aws_iam_1().ManagedPolicy.fromAwsManagedPolicyName("service-role/AWSAppSyncPushToCloudWatchLogs")]}).roleArn,fieldLogLevel=config.fieldLogLevel??appsync_common_1().AppSyncFieldLogLevel.NONE;return{cloudWatchLogsRoleArn:logsRoleArn,logLevel:fieldLogLevel}}setupAuthProviderTypes(authProviders){return!authProviders||authProviders.length===0?[auth_config_1().AppSyncAuthorizationType.API_KEY]:authProviders.map(mode=>mode.authorizationType)}mapAuthorizationProviders(authProviders){const authConfig=new AppSyncEventApiAuthConfig;return authProviders.reduce((acc,mode)=>(acc.push({authType:mode.authorizationType,cognitoConfig:authConfig.setupCognitoConfig(mode.cognitoConfig),openIdConnectConfig:authConfig.setupOpenIdConnectConfig(mode.openIdConnectConfig),lambdaAuthorizerConfig:authConfig.setupLambdaAuthorizerConfig(mode.lambdaAuthorizerConfig)}),acc),[])}mapAuthorizationConfig(authModes){return authModes.map(mode=>({authType:mode}))}validateAuthorizationProps(authProviders){const keyConfigs=authProviders.filter(mode=>mode.authorizationType===auth_config_1().AppSyncAuthorizationType.API_KEY),someWithNoNames=keyConfigs.some(config=>!config.apiKeyConfig?.name);if(keyConfigs.length>1&&someWithNoNames)throw new(core_1()).ValidationError("You must specify key names when configuring more than 1 API key.",this);if(authProviders.filter(authProvider=>authProvider.authorizationType===auth_config_1().AppSyncAuthorizationType.LAMBDA).length>1)throw new(core_1()).ValidationError("You can only have a single AWS Lambda function configured to authorize your API. See https://docs.aws.amazon.com/appsync/latest/devguide/security.html",this);if(authProviders.filter(authProvider=>authProvider.authorizationType===auth_config_1().AppSyncAuthorizationType.IAM).length>1)throw new(core_1()).ValidationError("You can't duplicate IAM configuration. See https://docs.aws.amazon.com/appsync/latest/devguide/security.html",this);authProviders.map(authProvider=>{if(authProvider.authorizationType===auth_config_1().AppSyncAuthorizationType.OIDC&&!authProvider.openIdConnectConfig)throw new(core_1()).ValidationError("OPENID_CONNECT authorization type is specified but OIDC Authorizer Configuration is missing in the AuthProvider",this);if(authProvider.authorizationType===auth_config_1().AppSyncAuthorizationType.USER_POOL&&!authProvider.cognitoConfig)throw new(core_1()).ValidationError("AMAZON_COGNITO_USER_POOLS authorization type is specified but Cognito Authorizer Configuration is missing in the AuthProvider",this);if(authProvider.authorizationType===auth_config_1().AppSyncAuthorizationType.LAMBDA&&!authProvider.lambdaAuthorizerConfig)throw new(core_1()).ValidationError("AWS_LAMBDA authorization type is specified but Lambda Authorizer Configuration is missing in the AuthProvider",this)})}validateAuthorizationConfig(authProviders,authTypes){for(const authType of authTypes)if(!authProviders.find(authProvider=>authProvider.authorizationType===authType))throw new(core_1()).ValidationError(`Missing authorization configuration for ${authType}`,this);if(authTypes.length===0)throw new(core_1()).ValidationError("Empty AuthModeTypes array is not allowed, if specifying, you must specify a valid mode",this)}get appSyncDomainName(){if(!this.domainNameResource)throw new(core_1()).ValidationError("Cannot retrieve the appSyncDomainName without a domainName configuration",this);return this.domainNameResource.attrAppSyncDomainName}get customHttpEndpoint(){if(!this.domainNameResource)throw new(core_1()).ValidationError("Cannot retrieve the appSyncDomainName without a domainName configuration",this);return`https://${this.domainNameResource.attrDomainName}/event`}get customRealtimeEndpoint(){if(!this.domainNameResource)throw new(core_1()).ValidationError("Cannot retrieve the appSyncDomainName without a domainName configuration",this);return`wss://${this.domainNameResource.attrDomainName}/event/realtime`}};exports.EventApi=EventApi,_b=JSII_RTTI_SYMBOL_1,EventApi[_b]={fqn:"aws-cdk-lib.aws_appsync.EventApi",version:"2.201.0"},EventApi.PROPERTY_INJECTION_ID="aws-cdk-lib.aws-appsync.EventApi",exports.EventApi=EventApi=__decorate([prop_injectable_1().propertyInjectable],EventApi);
