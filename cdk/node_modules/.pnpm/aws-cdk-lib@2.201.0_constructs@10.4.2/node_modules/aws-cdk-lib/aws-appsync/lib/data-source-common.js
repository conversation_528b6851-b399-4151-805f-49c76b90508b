"use strict";var _a,_b,_c,_d,_e,_f,_g,_h;Object.defineProperty(exports,"__esModule",{value:!0}),exports.AppSyncOpenSearchDataSource=exports.AppSyncRdsDataSource=exports.AppSyncLambdaDataSource=exports.AppSyncEventBridgeDataSource=exports.AppSyncHttpDataSource=exports.AppSyncDynamoDbDataSource=exports.AppSyncBackedDataSource=exports.AppSyncBaseDataSource=exports.LambdaInvokeType=exports.AppSyncDataSourceType=void 0;var jsiiDeprecationWarnings=()=>{var tmp=require("../../.warnings.jsii.js");return jsiiDeprecationWarnings=()=>tmp,tmp};const JSII_RTTI_SYMBOL_1=Symbol.for("jsii.rtti");var constructs_1=()=>{var tmp=require("constructs");return constructs_1=()=>tmp,tmp},appsync_generated_1=()=>{var tmp=require("./appsync.generated");return appsync_generated_1=()=>tmp,tmp},aws_iam_1=()=>{var tmp=require("../../aws-iam");return aws_iam_1=()=>tmp,tmp},core_1=()=>{var tmp=require("../../core");return core_1=()=>tmp,tmp},AppSyncDataSourceType;(function(AppSyncDataSourceType2){AppSyncDataSourceType2.LAMBDA="AWS_LAMBDA",AppSyncDataSourceType2.DYNAMODB="AMAZON_DYNAMODB",AppSyncDataSourceType2.EVENTBRIDGE="AMAZON_EVENTBRIDGE",AppSyncDataSourceType2.OPENSEARCH_SERVICE="AMAZON_OPENSEARCH_SERVICE",AppSyncDataSourceType2.HTTP="HTTP",AppSyncDataSourceType2.RELATIONAL_DATABASE="RELATIONAL_DATABASE",AppSyncDataSourceType2.BEDROCK="AMAZON_BEDROCK_RUNTIME",AppSyncDataSourceType2.ELASTICSEARCH="AMAZON_ELASTICSEARCH"})(AppSyncDataSourceType||(exports.AppSyncDataSourceType=AppSyncDataSourceType={}));var LambdaInvokeType;(function(LambdaInvokeType2){LambdaInvokeType2.EVENT="EVENT",LambdaInvokeType2.REQUEST_RESPONSE="REQUEST_RESPONSE"})(LambdaInvokeType||(exports.LambdaInvokeType=LambdaInvokeType={}));class AppSyncBaseDataSource extends constructs_1().Construct{constructor(scope,id,props,extended){super(scope,id);try{jsiiDeprecationWarnings().aws_cdk_lib_aws_appsync_AppSyncBackedDataSourceProps(props),jsiiDeprecationWarnings().aws_cdk_lib_aws_appsync_AppSyncExtendedDataSourceProps(extended)}catch(error){throw process.env.JSII_DEBUG!=="1"&&error.name==="DeprecationError"&&Error.captureStackTrace(error,AppSyncBaseDataSource),error}this.serviceRole=props.serviceRole||new(aws_iam_1()).Role(this,"ServiceRole",{assumedBy:new(aws_iam_1()).ServicePrincipal("appsync.amazonaws.com")});const name=props.name??id,supportedName=core_1().Token.isUnresolved(name)?name:name.replace(/[\W]+/g,"");this.resource=new(appsync_generated_1()).CfnDataSource(this,"Resource",{apiId:props.api.apiId,name:supportedName,description:props.description,serviceRoleArn:this.serviceRole?.roleArn,...extended}),this.name=supportedName,this.api=props.api}}exports.AppSyncBaseDataSource=AppSyncBaseDataSource,_a=JSII_RTTI_SYMBOL_1,AppSyncBaseDataSource[_a]={fqn:"aws-cdk-lib.aws_appsync.AppSyncBaseDataSource",version:"2.201.0"};class AppSyncBackedDataSource extends AppSyncBaseDataSource{constructor(scope,id,props,extended){super(scope,id,props,extended);try{jsiiDeprecationWarnings().aws_cdk_lib_aws_appsync_AppSyncBackedDataSourceProps(props),jsiiDeprecationWarnings().aws_cdk_lib_aws_appsync_AppSyncExtendedDataSourceProps(extended)}catch(error){throw process.env.JSII_DEBUG!=="1"&&error.name==="DeprecationError"&&Error.captureStackTrace(error,AppSyncBackedDataSource),error}this.grantPrincipal=this.serviceRole}}exports.AppSyncBackedDataSource=AppSyncBackedDataSource,_b=JSII_RTTI_SYMBOL_1,AppSyncBackedDataSource[_b]={fqn:"aws-cdk-lib.aws_appsync.AppSyncBackedDataSource",version:"2.201.0"};class AppSyncDynamoDbDataSource extends AppSyncBackedDataSource{constructor(scope,id,props){super(scope,id,props,{type:AppSyncDataSourceType.DYNAMODB,dynamoDbConfig:{tableName:props.table.tableName,awsRegion:props.table.env.region,useCallerCredentials:props.useCallerCredentials}});try{jsiiDeprecationWarnings().aws_cdk_lib_aws_appsync_AppSyncDynamoDbDataSourceProps(props)}catch(error){throw process.env.JSII_DEBUG!=="1"&&error.name==="DeprecationError"&&Error.captureStackTrace(error,AppSyncDynamoDbDataSource),error}props.readOnlyAccess?props.table.grantReadData(this):props.table.grantReadWriteData(this)}}exports.AppSyncDynamoDbDataSource=AppSyncDynamoDbDataSource,_c=JSII_RTTI_SYMBOL_1,AppSyncDynamoDbDataSource[_c]={fqn:"aws-cdk-lib.aws_appsync.AppSyncDynamoDbDataSource",version:"2.201.0"};class AppSyncHttpDataSource extends AppSyncBackedDataSource{constructor(scope,id,props){try{jsiiDeprecationWarnings().aws_cdk_lib_aws_appsync_AppSyncHttpDataSourceProps(props)}catch(error){throw process.env.JSII_DEBUG!=="1"&&error.name==="DeprecationError"&&Error.captureStackTrace(error,AppSyncHttpDataSource),error}const authorizationConfig=props.authorizationConfig?{authorizationType:"AWS_IAM",awsIamConfig:props.authorizationConfig}:void 0;super(scope,id,props,{type:AppSyncDataSourceType.HTTP,httpConfig:{endpoint:props.endpoint,authorizationConfig}})}}exports.AppSyncHttpDataSource=AppSyncHttpDataSource,_d=JSII_RTTI_SYMBOL_1,AppSyncHttpDataSource[_d]={fqn:"aws-cdk-lib.aws_appsync.AppSyncHttpDataSource",version:"2.201.0"};class AppSyncEventBridgeDataSource extends AppSyncBackedDataSource{constructor(scope,id,props){super(scope,id,props,{type:AppSyncDataSourceType.EVENTBRIDGE,eventBridgeConfig:{eventBusArn:props.eventBus.eventBusArn}});try{jsiiDeprecationWarnings().aws_cdk_lib_aws_appsync_AppSyncEventBridgeDataSourceProps(props)}catch(error){throw process.env.JSII_DEBUG!=="1"&&error.name==="DeprecationError"&&Error.captureStackTrace(error,AppSyncEventBridgeDataSource),error}props.eventBus.grantPutEventsTo(this)}}exports.AppSyncEventBridgeDataSource=AppSyncEventBridgeDataSource,_e=JSII_RTTI_SYMBOL_1,AppSyncEventBridgeDataSource[_e]={fqn:"aws-cdk-lib.aws_appsync.AppSyncEventBridgeDataSource",version:"2.201.0"};class AppSyncLambdaDataSource extends AppSyncBackedDataSource{constructor(scope,id,props){super(scope,id,props,{type:AppSyncDataSourceType.LAMBDA,lambdaConfig:{lambdaFunctionArn:props.lambdaFunction.functionArn}});try{jsiiDeprecationWarnings().aws_cdk_lib_aws_appsync_AppSyncLambdaDataSourceProps(props)}catch(error){throw process.env.JSII_DEBUG!=="1"&&error.name==="DeprecationError"&&Error.captureStackTrace(error,AppSyncLambdaDataSource),error}props.lambdaFunction.grantInvoke(this)}}exports.AppSyncLambdaDataSource=AppSyncLambdaDataSource,_f=JSII_RTTI_SYMBOL_1,AppSyncLambdaDataSource[_f]={fqn:"aws-cdk-lib.aws_appsync.AppSyncLambdaDataSource",version:"2.201.0"};class AppSyncRdsDataSource extends AppSyncBackedDataSource{constructor(scope,id,props){super(scope,id,props,{type:AppSyncDataSourceType.RELATIONAL_DATABASE,relationalDatabaseConfig:{rdsHttpEndpointConfig:{awsRegion:props.serverlessCluster.env.region,dbClusterIdentifier:core_1().Lazy.string({produce:()=>core_1().Stack.of(this).formatArn({service:"rds",resource:`cluster:${props.serverlessCluster.clusterIdentifier}`})}),awsSecretStoreArn:props.secretStore.secretArn,databaseName:props.databaseName},relationalDatabaseSourceType:"RDS_HTTP_ENDPOINT"}});try{jsiiDeprecationWarnings().aws_cdk_lib_aws_appsync_AppSyncRdsDataSourceProps(props)}catch(error){throw process.env.JSII_DEBUG!=="1"&&error.name==="DeprecationError"&&Error.captureStackTrace(error,AppSyncRdsDataSource),error}const clusterArn=core_1().Stack.of(this).formatArn({service:"rds",resource:`cluster:${props.serverlessCluster.clusterIdentifier}`});props.secretStore.grantRead(this),props.serverlessCluster.grantDataApiAccess(this),aws_iam_1().Grant.addToPrincipal({grantee:this,actions:["rds-data:DeleteItems","rds-data:ExecuteSql","rds-data:GetItems","rds-data:InsertItems","rds-data:UpdateItems"],resourceArns:[clusterArn,`${clusterArn}:*`],scope:this})}}exports.AppSyncRdsDataSource=AppSyncRdsDataSource,_g=JSII_RTTI_SYMBOL_1,AppSyncRdsDataSource[_g]={fqn:"aws-cdk-lib.aws_appsync.AppSyncRdsDataSource",version:"2.201.0"};class AppSyncOpenSearchDataSource extends AppSyncBackedDataSource{constructor(scope,id,props){super(scope,id,props,{type:AppSyncDataSourceType.OPENSEARCH_SERVICE,openSearchServiceConfig:{awsRegion:props.domain.env.region,endpoint:`https://${props.domain.domainEndpoint}`}});try{jsiiDeprecationWarnings().aws_cdk_lib_aws_appsync_AppSyncOpenSearchDataSourceProps(props)}catch(error){throw process.env.JSII_DEBUG!=="1"&&error.name==="DeprecationError"&&Error.captureStackTrace(error,AppSyncOpenSearchDataSource),error}props.domain.grantReadWrite(this)}}exports.AppSyncOpenSearchDataSource=AppSyncOpenSearchDataSource,_h=JSII_RTTI_SYMBOL_1,AppSyncOpenSearchDataSource[_h]={fqn:"aws-cdk-lib.aws_appsync.AppSyncOpenSearchDataSource",version:"2.201.0"};
