"use strict";var _a,_b,_c,_d,_e,_f,_g,_h,_j;Object.defineProperty(exports,"__esModule",{value:!0}),exports.Values=exports.AttributeValuesStep=exports.AttributeValues=exports.PartitionKey=exports.PrimaryKey=exports.SortKeyStep=exports.PartitionKeyStep=exports.Assign=exports.KeyCondition=void 0;var jsiiDeprecationWarnings=()=>{var tmp=require("../../.warnings.jsii.js");return jsiiDeprecationWarnings=()=>tmp,tmp};const JSII_RTTI_SYMBOL_1=Symbol.for("jsii.rtti");var private_1=()=>{var tmp=require("./private");return private_1=()=>tmp,tmp};class KeyCondition{static eq(keyName,arg){return new KeyCondition(new(private_1()).BinaryCondition(keyName,"=",arg))}static lt(keyName,arg){return new KeyCondition(new(private_1()).BinaryCondition(keyName,"<",arg))}static le(keyName,arg){return new KeyCondition(new(private_1()).BinaryCondition(keyName,"<=",arg))}static gt(keyName,arg){return new KeyCondition(new(private_1()).BinaryCondition(keyName,">",arg))}static ge(keyName,arg){return new KeyCondition(new(private_1()).BinaryCondition(keyName,">=",arg))}static beginsWith(keyName,arg){return new KeyCondition(new(private_1()).BeginsWith(keyName,arg))}static between(keyName,arg1,arg2){return new KeyCondition(new(private_1()).Between(keyName,arg1,arg2))}constructor(cond){this.cond=cond}and(keyCond){try{jsiiDeprecationWarnings().aws_cdk_lib_aws_appsync_KeyCondition(keyCond)}catch(error){throw process.env.JSII_DEBUG!=="1"&&error.name==="DeprecationError"&&Error.captureStackTrace(error,this.and),error}return new KeyCondition(this.cond.and(keyCond.cond))}renderTemplate(){return`"query" : {
              "expression" : "${this.cond.renderCondition()}",
              "expressionNames" : {
                ${this.cond.renderExpressionNames()}
              },
              "expressionValues" : {
                ${this.cond.renderExpressionValues()}
              }
            }`}}exports.KeyCondition=KeyCondition,_a=JSII_RTTI_SYMBOL_1,KeyCondition[_a]={fqn:"aws-cdk-lib.aws_appsync.KeyCondition",version:"2.201.0"};class Assign{constructor(attr,arg){this.attr=attr,this.arg=arg}renderAsAssignment(){return`"${this.attr}" : $util.dynamodb.toDynamoDBJson(${this.arg})`}putInMap(map){return`$util.qr($${map}.put("${this.attr}", ${this.arg}))`}}exports.Assign=Assign,_b=JSII_RTTI_SYMBOL_1,Assign[_b]={fqn:"aws-cdk-lib.aws_appsync.Assign",version:"2.201.0"};class PartitionKeyStep{constructor(key){this.key=key}is(val){return new PartitionKey(new Assign(this.key,`$ctx.args.${val}`))}auto(){return new PartitionKey(new Assign(this.key,"$util.autoId()"))}}exports.PartitionKeyStep=PartitionKeyStep,_c=JSII_RTTI_SYMBOL_1,PartitionKeyStep[_c]={fqn:"aws-cdk-lib.aws_appsync.PartitionKeyStep",version:"2.201.0"};class SortKeyStep{constructor(pkey,skey){this.pkey=pkey,this.skey=skey;try{jsiiDeprecationWarnings().aws_cdk_lib_aws_appsync_Assign(pkey)}catch(error){throw process.env.JSII_DEBUG!=="1"&&error.name==="DeprecationError"&&Error.captureStackTrace(error,SortKeyStep),error}}is(val){return new PrimaryKey(this.pkey,new Assign(this.skey,`$ctx.args.${val}`))}auto(){return new PrimaryKey(this.pkey,new Assign(this.skey,"$util.autoId()"))}}exports.SortKeyStep=SortKeyStep,_d=JSII_RTTI_SYMBOL_1,SortKeyStep[_d]={fqn:"aws-cdk-lib.aws_appsync.SortKeyStep",version:"2.201.0"};class PrimaryKey{static partition(key){return new PartitionKeyStep(key)}constructor(pkey,skey){this.pkey=pkey,this.skey=skey;try{jsiiDeprecationWarnings().aws_cdk_lib_aws_appsync_Assign(pkey),jsiiDeprecationWarnings().aws_cdk_lib_aws_appsync_Assign(skey)}catch(error){throw process.env.JSII_DEBUG!=="1"&&error.name==="DeprecationError"&&Error.captureStackTrace(error,PrimaryKey),error}}renderTemplate(){const assignments=[this.pkey.renderAsAssignment()];return this.skey&&assignments.push(this.skey.renderAsAssignment()),`"key" : {
      ${assignments.join(",")}
    }`}}exports.PrimaryKey=PrimaryKey,_e=JSII_RTTI_SYMBOL_1,PrimaryKey[_e]={fqn:"aws-cdk-lib.aws_appsync.PrimaryKey",version:"2.201.0"};class PartitionKey extends PrimaryKey{constructor(pkey){super(pkey);try{jsiiDeprecationWarnings().aws_cdk_lib_aws_appsync_Assign(pkey)}catch(error){throw process.env.JSII_DEBUG!=="1"&&error.name==="DeprecationError"&&Error.captureStackTrace(error,PartitionKey),error}}sort(key){return new SortKeyStep(this.pkey,key)}}exports.PartitionKey=PartitionKey,_f=JSII_RTTI_SYMBOL_1,PartitionKey[_f]={fqn:"aws-cdk-lib.aws_appsync.PartitionKey",version:"2.201.0"};class AttributeValues{constructor(container,assignments=[]){this.container=container,this.assignments=assignments}attribute(attr){return new AttributeValuesStep(attr,this.container,this.assignments)}renderVariables(){return`#set($input = ${this.container})
      ${this.assignments.map(a=>a.putInMap("input")).join(`
`)}`}renderTemplate(){return'"attributeValues": $util.dynamodb.toMapValuesJson($input)'}}exports.AttributeValues=AttributeValues,_g=JSII_RTTI_SYMBOL_1,AttributeValues[_g]={fqn:"aws-cdk-lib.aws_appsync.AttributeValues",version:"2.201.0"};class AttributeValuesStep{constructor(attr,container,assignments){this.attr=attr,this.container=container,this.assignments=assignments}is(val){return this.assignments.push(new Assign(this.attr,val)),new AttributeValues(this.container,this.assignments)}}exports.AttributeValuesStep=AttributeValuesStep,_h=JSII_RTTI_SYMBOL_1,AttributeValuesStep[_h]={fqn:"aws-cdk-lib.aws_appsync.AttributeValuesStep",version:"2.201.0"};class Values{static projecting(arg){return new AttributeValues("$ctx.args"+(arg?`.${arg}`:""))}static attribute(attr){return new AttributeValues("{}").attribute(attr)}}exports.Values=Values,_j=JSII_RTTI_SYMBOL_1,Values[_j]={fqn:"aws-cdk-lib.aws_appsync.Values",version:"2.201.0"};
