"use strict";var __decorate=exports&&exports.__decorate||function(decorators,target,key,desc){var c=arguments.length,r=c<3?target:desc===null?desc=Object.getOwnPropertyDescriptor(target,key):desc,d;if(typeof Reflect=="object"&&typeof Reflect.decorate=="function")r=Reflect.decorate(decorators,target,key,desc);else for(var i=decorators.length-1;i>=0;i--)(d=decorators[i])&&(r=(c<3?d(r):c>3?d(target,key,r):d(target,key))||r);return c>3&&r&&Object.defineProperty(target,key,r),r},_a;Object.defineProperty(exports,"__esModule",{value:!0}),exports.SourceApiAssociation=exports.MergeType=void 0,exports.addSourceGraphQLPermission=addSourceGraphQLPermission,exports.addSourceApiAutoMergePermission=addSourceApiAutoMergePermission;var jsiiDeprecationWarnings=()=>{var tmp=require("../../.warnings.jsii.js");return jsiiDeprecationWarnings=()=>tmp,tmp};const JSII_RTTI_SYMBOL_1=Symbol.for("jsii.rtti");var appsync_generated_1=()=>{var tmp=require("./appsync.generated");return appsync_generated_1=()=>tmp,tmp},aws_iam_1=()=>{var tmp=require("../../aws-iam");return aws_iam_1=()=>tmp,tmp},core_1=()=>{var tmp=require("../../core");return core_1=()=>tmp,tmp},metadata_resource_1=()=>{var tmp=require("../../core/lib/metadata-resource");return metadata_resource_1=()=>tmp,tmp},prop_injectable_1=()=>{var tmp=require("../../core/lib/prop-injectable");return prop_injectable_1=()=>tmp,tmp},MergeType;(function(MergeType2){MergeType2.MANUAL_MERGE="MANUAL_MERGE",MergeType2.AUTO_MERGE="AUTO_MERGE"})(MergeType||(exports.MergeType=MergeType={}));let SourceApiAssociation=class SourceApiAssociation2 extends core_1().Resource{static fromSourceApiAssociationAttributes(scope,id,attrs){try{jsiiDeprecationWarnings().aws_cdk_lib_aws_appsync_SourceApiAssociationAttributes(attrs)}catch(error){throw process.env.JSII_DEBUG!=="1"&&error.name==="DeprecationError"&&Error.captureStackTrace(error,this.fromSourceApiAssociationAttributes),error}class Import extends core_1().Resource{constructor(s,i){super(s,i),this.associationId=core_1().Lazy.stringValue({produce:()=>core_1().Fn.select(3,core_1().Fn.split("/",attrs.associationArn))}),this.associationArn=attrs.associationArn,this.sourceApi=attrs.sourceApi,this.mergedApi=attrs.mergedApi}}return new Import(scope,id)}constructor(scope,id,props){super(scope,id);try{jsiiDeprecationWarnings().aws_cdk_lib_aws_appsync_SourceApiAssociationProps(props)}catch(error){throw process.env.JSII_DEBUG!=="1"&&error.name==="DeprecationError"&&Error.captureStackTrace(error,SourceApiAssociation2),error}(0,metadata_resource_1().addConstructMetadata)(this,props),this.mergeType=props.mergeType??MergeType.AUTO_MERGE,this.mergedApiExecutionRole=props.mergedApiExecutionRole,this.sourceApi=props.sourceApi,this.mergedApi=props.mergedApi,this.association=new(appsync_generated_1()).CfnSourceApiAssociation(this,"Resource",{sourceApiIdentifier:this.sourceApi.arn,mergedApiIdentifier:this.mergedApi.arn,sourceApiAssociationConfig:{mergeType:this.mergeType},description:props.description}),this.sourceApi.addSchemaDependency(this.association),this.associationId=this.association.attrAssociationId,this.associationArn=this.association.attrAssociationArn,addSourceGraphQLPermission(this.association,this.mergedApiExecutionRole),this.mergeType===MergeType.AUTO_MERGE&&addSourceApiAutoMergePermission(this.association,this.mergedApiExecutionRole)}};exports.SourceApiAssociation=SourceApiAssociation,_a=JSII_RTTI_SYMBOL_1,SourceApiAssociation[_a]={fqn:"aws-cdk-lib.aws_appsync.SourceApiAssociation",version:"2.201.0"},SourceApiAssociation.PROPERTY_INJECTION_ID="aws-cdk-lib.aws-appsync.SourceApiAssociation",exports.SourceApiAssociation=SourceApiAssociation=__decorate([prop_injectable_1().propertyInjectable],SourceApiAssociation);function addSourceGraphQLPermission(sourceApiAssociation,mergedApiExecutionRole){return mergedApiExecutionRole.addToPrincipalPolicy(new(aws_iam_1()).PolicyStatement({effect:aws_iam_1().Effect.ALLOW,actions:["appsync:SourceGraphQL"],resources:[sourceApiAssociation.attrSourceApiArn,sourceApiAssociation.attrSourceApiArn.concat("/*")]}))}function addSourceApiAutoMergePermission(sourceApiAssociation,mergedApiExecutionRole){return mergedApiExecutionRole.addToPrincipalPolicy(new(aws_iam_1()).PolicyStatement({effect:aws_iam_1().Effect.ALLOW,actions:["appsync:StartSchemaMerge"],resources:[sourceApiAssociation.attrAssociationArn]}))}
