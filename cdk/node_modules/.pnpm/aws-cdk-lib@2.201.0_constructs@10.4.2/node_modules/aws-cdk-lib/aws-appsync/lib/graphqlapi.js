"use strict";var __decorate=exports&&exports.__decorate||function(decorators,target,key,desc){var c=arguments.length,r=c<3?target:desc===null?desc=Object.getOwnPropertyDescriptor(target,key):desc,d;if(typeof Reflect=="object"&&typeof Reflect.decorate=="function")r=Reflect.decorate(decorators,target,key,desc);else for(var i=decorators.length-1;i>=0;i--)(d=decorators[i])&&(r=(c<3?d(r):c>3?d(target,key,r):d(target,key))||r);return c>3&&r&&Object.defineProperty(target,key,r),r},_a,_b;Object.defineProperty(exports,"__esModule",{value:!0}),exports.GraphqlApi=exports.IntrospectionConfig=exports.Definition=exports.FieldLogLevel=exports.UserPoolDefaultAction=void 0;var jsiiDeprecationWarnings=()=>{var tmp=require("../../.warnings.jsii.js");return jsiiDeprecationWarnings=()=>tmp,tmp};const JSII_RTTI_SYMBOL_1=Symbol.for("jsii.rtti");var appsync_generated_1=()=>{var tmp=require("./appsync.generated");return appsync_generated_1=()=>tmp,tmp},graphqlapi_base_1=()=>{var tmp=require("./graphqlapi-base");return graphqlapi_base_1=()=>tmp,tmp},schema_1=()=>{var tmp=require("./schema");return schema_1=()=>tmp,tmp},source_api_association_1=()=>{var tmp=require("./source-api-association");return source_api_association_1=()=>tmp,tmp},aws_iam_1=()=>{var tmp=require("../../aws-iam");return aws_iam_1=()=>tmp,tmp},aws_logs_1=()=>{var tmp=require("../../aws-logs");return aws_logs_1=()=>tmp,tmp},core_1=()=>{var tmp=require("../../core");return core_1=()=>tmp,tmp},metadata_resource_1=()=>{var tmp=require("../../core/lib/metadata-resource");return metadata_resource_1=()=>tmp,tmp},prop_injectable_1=()=>{var tmp=require("../../core/lib/prop-injectable");return prop_injectable_1=()=>tmp,tmp},cxapi=()=>{var tmp=require("../../cx-api");return cxapi=()=>tmp,tmp},UserPoolDefaultAction;(function(UserPoolDefaultAction2){UserPoolDefaultAction2.ALLOW="ALLOW",UserPoolDefaultAction2.DENY="DENY"})(UserPoolDefaultAction||(exports.UserPoolDefaultAction=UserPoolDefaultAction={}));var FieldLogLevel;(function(FieldLogLevel2){FieldLogLevel2.NONE="NONE",FieldLogLevel2.ERROR="ERROR",FieldLogLevel2.INFO="INFO",FieldLogLevel2.DEBUG="DEBUG",FieldLogLevel2.ALL="ALL"})(FieldLogLevel||(exports.FieldLogLevel=FieldLogLevel={}));class Definition{static fromSchema(schema){try{jsiiDeprecationWarnings().aws_cdk_lib_aws_appsync_ISchema(schema)}catch(error){throw process.env.JSII_DEBUG!=="1"&&error.name==="DeprecationError"&&Error.captureStackTrace(error,this.fromSchema),error}return{schema}}static fromFile(filePath){return this.fromSchema(schema_1().SchemaFile.fromAsset(filePath))}static fromSourceApis(sourceApiOptions){try{jsiiDeprecationWarnings().aws_cdk_lib_aws_appsync_SourceApiOptions(sourceApiOptions)}catch(error){throw process.env.JSII_DEBUG!=="1"&&error.name==="DeprecationError"&&Error.captureStackTrace(error,this.fromSourceApis),error}return{sourceApiOptions}}}exports.Definition=Definition,_a=JSII_RTTI_SYMBOL_1,Definition[_a]={fqn:"aws-cdk-lib.aws_appsync.Definition",version:"2.201.0"};var IntrospectionConfig;(function(IntrospectionConfig2){IntrospectionConfig2.ENABLED="ENABLED",IntrospectionConfig2.DISABLED="DISABLED"})(IntrospectionConfig||(exports.IntrospectionConfig=IntrospectionConfig={}));let GraphqlApi=class GraphqlApi2 extends graphqlapi_base_1().GraphqlApiBase{static fromGraphqlApiAttributes(scope,id,attrs){try{jsiiDeprecationWarnings().aws_cdk_lib_aws_appsync_GraphqlApiAttributes(attrs)}catch(error){throw process.env.JSII_DEBUG!=="1"&&error.name==="DeprecationError"&&Error.captureStackTrace(error,this.fromGraphqlApiAttributes),error}const arn=attrs.graphqlApiArn??core_1().Stack.of(scope).formatArn({service:"appsync",resource:`apis/${attrs.graphqlApiId}`});class Import extends graphqlapi_base_1().GraphqlApiBase{constructor(s,i){super(s,i),this.apiId=attrs.graphqlApiId,this.arn=arn,this.graphQLEndpointArn=attrs.graphQLEndpointArn??"",this.visibility=attrs.visibility??graphqlapi_base_1().Visibility.GLOBAL,this.modes=attrs.modes??[]}}return new Import(scope,id)}get schema(){if(this.definition.schema)return this.definition.schema;throw new(core_1()).ValidationError("Schema does not exist for AppSync merged APIs.",this)}constructor(scope,id,props){super(scope,id),this.environmentVariables={};try{jsiiDeprecationWarnings().aws_cdk_lib_aws_appsync_GraphqlApiProps(props)}catch(error){throw process.env.JSII_DEBUG!=="1"&&error.name==="DeprecationError"&&Error.captureStackTrace(error,GraphqlApi2),error}(0,metadata_resource_1().addConstructMetadata)(this,props);const defaultMode=props.authorizationConfig?.defaultAuthorization??{authorizationType:graphqlapi_base_1().AuthorizationType.API_KEY},additionalModes=props.authorizationConfig?.additionalAuthorizationModes??[],modes=[defaultMode,...additionalModes];if(this.modes=modes.map(mode=>mode.authorizationType),this.validateAuthorizationProps(modes),!props.schema&&!props.definition)throw new(core_1()).ValidationError("You must specify a GraphQL schema or source APIs in property definition.",this);if(props.schema!==void 0==(props.definition!==void 0))throw new(core_1()).ValidationError("You cannot specify both properties schema and definition.",this);if(props.queryDepthLimit!==void 0&&(props.queryDepthLimit<0||props.queryDepthLimit>75))throw new(core_1()).ValidationError("You must specify a query depth limit between 0 and 75.",this);if(props.resolverCountLimit!==void 0&&(props.resolverCountLimit<0||props.resolverCountLimit>1e4))throw new(core_1()).ValidationError("You must specify a resolver count limit between 0 and 10000.",this);if(!core_1().Token.isUnresolved(props.ownerContact)&&props.ownerContact!==void 0&&props.ownerContact.length>256)throw new(core_1()).ValidationError("You must specify `ownerContact` as a string of 256 characters or less.",this);if(this.definition=props.schema?Definition.fromSchema(props.schema):props.definition,this.definition.sourceApiOptions&&this.setupMergedApiExecutionRole(this.definition.sourceApiOptions),props.environmentVariables!==void 0&&Object.entries(props.environmentVariables).forEach(([key,value])=>{this.addEnvironmentVariable(key,value)}),this.node.addValidation({validate:()=>this.validateEnvironmentVariables()}),this.visibility=props.visibility??graphqlapi_base_1().Visibility.GLOBAL,this.api=new(appsync_generated_1()).CfnGraphQLApi(this,"Resource",{name:props.name,authenticationType:defaultMode.authorizationType,logConfig:this.setupLogConfig(props.logConfig),openIdConnectConfig:this.setupOpenIdConnectConfig(defaultMode.openIdConnectConfig),userPoolConfig:this.setupUserPoolConfig(defaultMode.userPoolConfig),lambdaAuthorizerConfig:this.setupLambdaAuthorizerConfig(defaultMode.lambdaAuthorizerConfig),additionalAuthenticationProviders:this.setupAdditionalAuthorizationModes(additionalModes),xrayEnabled:props.xrayEnabled,visibility:props.visibility,mergedApiExecutionRoleArn:this.mergedApiExecutionRole?.roleArn,apiType:this.definition.sourceApiOptions?"MERGED":void 0,introspectionConfig:props.introspectionConfig,queryDepthLimit:props.queryDepthLimit,resolverCountLimit:props.resolverCountLimit,environmentVariables:core_1().Lazy.any({produce:()=>this.renderEnvironmentVariables()}),ownerContact:props.ownerContact}),this.apiId=this.api.attrApiId,this.arn=this.api.attrArn,this.graphqlUrl=this.api.attrGraphQlUrl,this.name=this.api.name,this.graphQLEndpointArn=this.api.attrGraphQlEndpointArn,this.definition.schema?this.schemaResource=new(appsync_generated_1()).CfnGraphQLSchema(this,"Schema",this.definition.schema.bind(this)):this.setupSourceApiAssociations(),props.domainName&&(this.domainNameResource=new(appsync_generated_1()).CfnDomainName(this,"DomainName",{domainName:props.domainName.domainName,certificateArn:props.domainName.certificate.certificateArn,description:`domain for ${this.name} at ${this.graphqlUrl}`}),new(appsync_generated_1()).CfnDomainNameApiAssociation(this,"DomainAssociation",{domainName:props.domainName.domainName,apiId:this.apiId}).addDependency(this.domainNameResource)),modes.some(mode=>mode.authorizationType===graphqlapi_base_1().AuthorizationType.API_KEY)){const config=modes.find(mode=>mode.authorizationType===graphqlapi_base_1().AuthorizationType.API_KEY&&mode.apiKeyConfig)?.apiKeyConfig;this.apiKeyResource=this.createAPIKey(config),this.schemaResource&&this.apiKeyResource.addDependency(this.schemaResource),this.apiKey=this.apiKeyResource.attrApiKey}if(modes.some(mode=>mode.authorizationType===graphqlapi_base_1().AuthorizationType.LAMBDA)){const config=modes.find(mode=>mode.authorizationType===graphqlapi_base_1().AuthorizationType.LAMBDA&&mode.lambdaAuthorizerConfig)?.lambdaAuthorizerConfig;core_1().FeatureFlags.of(this).isEnabled(cxapi().APPSYNC_GRAPHQLAPI_SCOPE_LAMBDA_FUNCTION_PERMISSION)?config?.handler.addPermission(`${id}-appsync`,{principal:new(aws_iam_1()).ServicePrincipal("appsync.amazonaws.com"),action:"lambda:InvokeFunction",sourceArn:this.arn}):config?.handler.addPermission(`${id}-appsync`,{principal:new(aws_iam_1()).ServicePrincipal("appsync.amazonaws.com"),action:"lambda:InvokeFunction"})}const logGroupName=`/aws/appsync/apis/${this.apiId}`;if(props.logConfig){const logRetention=new(aws_logs_1()).LogRetention(this,"LogRetention",{logGroupName,retention:props.logConfig?.retention??aws_logs_1().RetentionDays.INFINITE});this.logGroup=aws_logs_1().LogGroup.fromLogGroupArn(this,"LogGroup",logRetention.logGroupArn)}else this.logGroup=aws_logs_1().LogGroup.fromLogGroupName(this,"LogGroup",logGroupName)}setupSourceApiAssociations(){this.definition.sourceApiOptions?.sourceApis.forEach(sourceApiConfig=>{const mergeType=sourceApiConfig.mergeType??source_api_association_1().MergeType.AUTO_MERGE;let sourceApiIdentifier=sourceApiConfig.sourceApi.apiId,mergedApiIdentifier=this.apiId;core_1().FeatureFlags.of(this).isEnabled(cxapi().APPSYNC_ENABLE_USE_ARN_IDENTIFIER_SOURCE_API_ASSOCIATION)&&(sourceApiIdentifier=sourceApiConfig.sourceApi.arn,mergedApiIdentifier=this.arn);const association=new(appsync_generated_1()).CfnSourceApiAssociation(this,`${sourceApiConfig.sourceApi.node.id}Association`,{sourceApiIdentifier,mergedApiIdentifier,sourceApiAssociationConfig:{mergeType},description:sourceApiConfig.description});sourceApiConfig.sourceApi.addSchemaDependency(association);const executionRole=this.mergedApiExecutionRole;(0,source_api_association_1().addSourceGraphQLPermission)(association,executionRole),mergeType===source_api_association_1().MergeType.AUTO_MERGE&&(0,source_api_association_1().addSourceApiAutoMergePermission)(association,executionRole)})}setupMergedApiExecutionRole(sourceApiOptions){sourceApiOptions.mergedApiExecutionRole?this.mergedApiExecutionRole=sourceApiOptions.mergedApiExecutionRole:this.mergedApiExecutionRole=new(aws_iam_1()).Role(this,"MergedApiExecutionRole",{assumedBy:new(aws_iam_1()).ServicePrincipal("appsync.amazonaws.com")})}validateAuthorizationProps(modes){if(modes.filter(mode=>mode.authorizationType===graphqlapi_base_1().AuthorizationType.LAMBDA).length>1)throw new(core_1()).ValidationError("You can only have a single AWS Lambda function configured to authorize your API.",this);if(modes.map(mode=>{if(mode.authorizationType===graphqlapi_base_1().AuthorizationType.OIDC&&!mode.openIdConnectConfig)throw new(core_1()).ValidationError("Missing OIDC Configuration",this);if(mode.authorizationType===graphqlapi_base_1().AuthorizationType.USER_POOL&&!mode.userPoolConfig)throw new(core_1()).ValidationError("Missing User Pool Configuration",this);if(mode.authorizationType===graphqlapi_base_1().AuthorizationType.LAMBDA&&!mode.lambdaAuthorizerConfig)throw new(core_1()).ValidationError("Missing Lambda Configuration",this)}),modes.filter(mode=>mode.authorizationType===graphqlapi_base_1().AuthorizationType.API_KEY).length>1)throw new(core_1()).ValidationError("You can't duplicate API_KEY configuration. See https://docs.aws.amazon.com/appsync/latest/devguide/security.html",this);if(modes.filter(mode=>mode.authorizationType===graphqlapi_base_1().AuthorizationType.IAM).length>1)throw new(core_1()).ValidationError("You can't duplicate IAM configuration. See https://docs.aws.amazon.com/appsync/latest/devguide/security.html",this)}addSchemaDependency(construct){try{jsiiDeprecationWarnings().aws_cdk_lib_CfnResource(construct)}catch(error){throw process.env.JSII_DEBUG!=="1"&&error.name==="DeprecationError"&&Error.captureStackTrace(error,this.addSchemaDependency),error}return this.schemaResource&&construct.addDependency(this.schemaResource),!0}addEnvironmentVariable(key,value){if(this.definition.sourceApiOptions)throw new(core_1()).ValidationError("Environment variables are not supported for merged APIs",this);if(!core_1().Token.isUnresolved(key)&&!/^[A-Za-z]+\w*$/.test(key))throw new(core_1()).ValidationError(`Key '${key}' must begin with a letter and can only contain letters, numbers, and underscores`,this);if(!core_1().Token.isUnresolved(key)&&(key.length<2||key.length>64))throw new(core_1()).ValidationError(`Key '${key}' must be between 2 and 64 characters long, got ${key.length}`,this);if(!core_1().Token.isUnresolved(value)&&value.length>512)throw new(core_1()).ValidationError(`Value for '${key}' is too long. Values can be up to 512 characters long, got ${value.length}`,this);this.environmentVariables[key]=value}validateEnvironmentVariables(){const errors=[],entries=Object.entries(this.environmentVariables);return entries.length>50&&errors.push(`Only 50 environment variables can be set, got ${entries.length}`),errors}renderEnvironmentVariables(){return Object.entries(this.environmentVariables).length>0?this.environmentVariables:void 0}setupLogConfig(config){if(!config)return;const logsRoleArn=config.role?.roleArn??new(aws_iam_1()).Role(this,"ApiLogsRole",{assumedBy:new(aws_iam_1()).ServicePrincipal("appsync.amazonaws.com"),managedPolicies:[aws_iam_1().ManagedPolicy.fromAwsManagedPolicyName("service-role/AWSAppSyncPushToCloudWatchLogs")]}).roleArn,fieldLogLevel=config.fieldLogLevel??FieldLogLevel.NONE;return{cloudWatchLogsRoleArn:logsRoleArn,excludeVerboseContent:config.excludeVerboseContent,fieldLogLevel}}setupOpenIdConnectConfig(config){if(config)return{authTtl:config.tokenExpiryFromAuth,clientId:config.clientId,iatTtl:config.tokenExpiryFromIssue,issuer:config.oidcProvider}}setupUserPoolConfig(config){if(config)return{userPoolId:config.userPool.userPoolId,awsRegion:config.userPool.env.region,appIdClientRegex:config.appIdClientRegex,defaultAction:config.defaultAction||UserPoolDefaultAction.ALLOW}}setupLambdaAuthorizerConfig(config){if(config)return{authorizerResultTtlInSeconds:config.resultsCacheTtl?.toSeconds(),authorizerUri:config.handler.functionArn,identityValidationExpression:config.validationRegex}}setupAdditionalAuthorizationModes(modes){if(!(!modes||modes.length===0))return modes.reduce((acc,mode)=>[...acc,{authenticationType:mode.authorizationType,userPoolConfig:this.setupUserPoolConfig(mode.userPoolConfig),openIdConnectConfig:this.setupOpenIdConnectConfig(mode.openIdConnectConfig),lambdaAuthorizerConfig:this.setupLambdaAuthorizerConfig(mode.lambdaAuthorizerConfig)}],[])}createAPIKey(config){if(config?.expires?.isBefore(core_1().Duration.days(1))||config?.expires?.isAfter(core_1().Duration.days(365)))throw Error("API key expiration must be between 1 and 365 days.");const expires=config?.expires?config?.expires.toEpoch():void 0;return new(appsync_generated_1()).CfnApiKey(this,`${config?.name||"Default"}ApiKey`,{expires,description:config?.description,apiId:this.apiId})}get appSyncDomainName(){if(!this.domainNameResource)throw new(core_1()).ValidationError("Cannot retrieve the appSyncDomainName without a domainName configuration",this);return this.domainNameResource.attrAppSyncDomainName}};exports.GraphqlApi=GraphqlApi,_b=JSII_RTTI_SYMBOL_1,GraphqlApi[_b]={fqn:"aws-cdk-lib.aws_appsync.GraphqlApi",version:"2.201.0"},GraphqlApi.PROPERTY_INJECTION_ID="aws-cdk-lib.aws-appsync.GraphqlApi",__decorate([(0,metadata_resource_1().MethodMetadata)()],GraphqlApi.prototype,"addSchemaDependency",null),__decorate([(0,metadata_resource_1().MethodMetadata)()],GraphqlApi.prototype,"addEnvironmentVariable",null),exports.GraphqlApi=GraphqlApi=__decorate([prop_injectable_1().propertyInjectable],GraphqlApi);
