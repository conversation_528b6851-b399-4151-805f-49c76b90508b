import { DynamoDbDataSource, HttpDataSource, LambdaDataSource, NoneDataSource, RdsDataSource, AwsIamConfig, ElasticsearchDataSource, OpenSearchDataSource, EventBridgeDataSource } from './data-source';
import { Resolver, ExtendedResolverProps } from './resolver';
import { ITable } from '../../aws-dynamodb';
import { IDomain as IElasticsearchDomain } from '../../aws-elasticsearch';
import { IEventBus } from '../../aws-events';
import { Grant, IGrantable } from '../../aws-iam';
import { IFunction } from '../../aws-lambda';
import { IDomain as IOpenSearchDomain } from '../../aws-opensearchservice';
import { IDatabaseCluster, IServerlessCluster } from '../../aws-rds';
import { ISecret } from '../../aws-secretsmanager';
import { CfnResource, IResource, Resource } from '../../core';
/**
 * Optional configuration for data sources
 */
export interface DataSourceOptions {
    /**
     * The name of the data source, overrides the id given by cdk
     *
     * @default - generated by cdk given the id
     */
    readonly name?: string;
    /**
     * The description of the data source
     *
     * @default - No description
     */
    readonly description?: string;
}
/**
 * Optional configuration for Http data sources
 */
export interface HttpDataSourceOptions extends DataSourceOptions {
    /**
     * The authorization config in case the HTTP endpoint requires authorization
     *
     * @default - none
     */
    readonly authorizationConfig?: AwsIamConfig;
}
/**
 * A class used to generate resource arns for AppSync
 */
export declare class IamResource {
    /**
     * Generate the resource names given custom arns
     *
     * @param arns The custom arns that need to be permissioned
     *
     * Example: custom('/types/Query/fields/getExample')
     */
    static custom(...arns: string[]): IamResource;
    /**
     * Generate the resource names given a type and fields
     *
     * @param type The type that needs to be allowed
     * @param fields The fields that need to be allowed, if empty grant permissions to ALL fields
     *
     * Example: ofType('Query', 'GetExample')
     */
    static ofType(type: string, ...fields: string[]): IamResource;
    /**
     * Generate the resource names that accepts all types: `*`
     */
    static all(): IamResource;
    private arns;
    private constructor();
    /**
     * Return the Resource ARN
     *
     * @param api The GraphQL API to give permissions
     */
    resourceArns(api: GraphqlApiBase): string[];
}
/**
 * Visibility type for a GraphQL API
 */
export declare enum Visibility {
    /**
     * Public, open to the internet
     */
    GLOBAL = "GLOBAL",
    /**
     * Only accessible through a VPC
     */
    PRIVATE = "PRIVATE"
}
/**
 * enum with all possible values for AppSync authorization type
 */
export declare enum AuthorizationType {
    /**
     * API Key authorization type
     */
    API_KEY = "API_KEY",
    /**
     * AWS IAM authorization type. Can be used with Cognito Identity Pool federated credentials
     */
    IAM = "AWS_IAM",
    /**
     * Cognito User Pool authorization type
     */
    USER_POOL = "AMAZON_COGNITO_USER_POOLS",
    /**
     * OpenID Connect authorization type
     */
    OIDC = "OPENID_CONNECT",
    /**
     * Lambda authorization type
     */
    LAMBDA = "AWS_LAMBDA"
}
/**
 * Interface for GraphQL
 */
export interface IGraphqlApi extends IResource {
    /**
     * an unique AWS AppSync GraphQL API identifier
     * i.e. 'lxz775lwdrgcndgz3nurvac7oa'
     *
     * @attribute
     */
    readonly apiId: string;
    /**
     * the ARN of the API
     *
     * @attribute
     */
    readonly arn: string;
    /**
     * The GraphQL endpoint ARN
     */
    readonly graphQLEndpointArn: string;
    /**
     * the visibility of the API
     */
    readonly visibility: Visibility;
    /**
     * The Authorization Types for this GraphQL Api
     */
    readonly modes: AuthorizationType[];
    /**
     * add a new dummy data source to this API. Useful for pipeline resolvers
     * and for backend changes that don't require a data source.
     *
     * @param id The data source's id
     * @param options The optional configuration for this data source
     */
    addNoneDataSource(id: string, options?: DataSourceOptions): NoneDataSource;
    /**
     * add a new DynamoDB data source to this API
     *
     * @param id The data source's id
     * @param table The DynamoDB table backing this data source
     * @param options The optional configuration for this data source
     */
    addDynamoDbDataSource(id: string, table: ITable, options?: DataSourceOptions): DynamoDbDataSource;
    /**
     * add a new http data source to this API
     *
     * @param id The data source's id
     * @param endpoint The http endpoint
     * @param options The optional configuration for this data source
     */
    addHttpDataSource(id: string, endpoint: string, options?: HttpDataSourceOptions): HttpDataSource;
    /**
     * Add an EventBridge data source to this api
     * @param id The data source's id
     * @param eventBus The EventBridge EventBus on which to put events
     * @param options The optional configuration for this data source
     */
    addEventBridgeDataSource(id: string, eventBus: IEventBus, options?: DataSourceOptions): EventBridgeDataSource;
    /**
     * add a new Lambda data source to this API
     *
     * @param id The data source's id
     * @param lambdaFunction The Lambda function to call to interact with this data source
     * @param options The optional configuration for this data source
     */
    addLambdaDataSource(id: string, lambdaFunction: IFunction, options?: DataSourceOptions): LambdaDataSource;
    /**
     * add a new Rds data source to this API
     *
     * @param id The data source's id
     * @param serverlessCluster The serverless cluster to interact with this data source
     * @param secretStore The secret store that contains the username and password for the serverless cluster
     * @param databaseName The optional name of the database to use within the cluster
     * @param options The optional configuration for this data source
     */
    addRdsDataSource(id: string, serverlessCluster: IServerlessCluster, secretStore: ISecret, databaseName?: string, options?: DataSourceOptions): RdsDataSource;
    /**
     * add a new Rds Serverless V2 data source to this API
     *
     * @param id The data source's id
     * @param serverlessCluster The serverless V2 cluster to interact with this data source
     * @param secretStore The secret store that contains the username and password for the serverless cluster
     * @param databaseName The optional name of the database to use within the cluster
     * @param options The optional configuration for this data source
     */
    addRdsDataSourceV2(id: string, serverlessCluster: IDatabaseCluster, secretStore: ISecret, databaseName?: string, options?: DataSourceOptions): RdsDataSource;
    /**
     * add a new elasticsearch data source to this API
     *
     * @deprecated - use `addOpenSearchDataSource`
     * @param id The data source's id
     * @param domain The elasticsearch domain for this data source
     * @param options The optional configuration for this data source
     */
    addElasticsearchDataSource(id: string, domain: IElasticsearchDomain, options?: DataSourceOptions): ElasticsearchDataSource;
    /**
     * Add a new OpenSearch data source to this API
     *
     * @param id The data source's id
     * @param domain The OpenSearch domain for this data source
     * @param options The optional configuration for this data source
     */
    addOpenSearchDataSource(id: string, domain: IOpenSearchDomain, options?: DataSourceOptions): OpenSearchDataSource;
    /**
     * creates a new resolver for this datasource and API using the given properties
     */
    createResolver(id: string, props: ExtendedResolverProps): Resolver;
    /**
     * Add schema dependency if not imported
     *
     * @param construct the dependee
     */
    addSchemaDependency(construct: CfnResource): boolean;
    /**
     * Adds an IAM policy statement associated with this GraphQLApi to an IAM
     * principal's policy.
     *
     * @param grantee The principal
     * @param resources The set of resources to allow (i.e. ...:[region]:[accountId]:apis/GraphQLId/...)
     * @param actions The actions that should be granted to the principal (i.e. appsync:graphql )
     */
    grant(grantee: IGrantable, resources: IamResource, ...actions: string[]): Grant;
    /**
     * Adds an IAM policy statement for Mutation access to this GraphQLApi to an IAM
     * principal's policy.
     *
     * @param grantee The principal
     * @param fields The fields to grant access to that are Mutations (leave blank for all)
     */
    grantMutation(grantee: IGrantable, ...fields: string[]): Grant;
    /**
     * Adds an IAM policy statement for Query access to this GraphQLApi to an IAM
     * principal's policy.
     *
     * @param grantee The principal
     * @param fields The fields to grant access to that are Queries (leave blank for all)
     */
    grantQuery(grantee: IGrantable, ...fields: string[]): Grant;
    /**
     * Adds an IAM policy statement for Subscription access to this GraphQLApi to an IAM
     * principal's policy.
     *
     * @param grantee The principal
     * @param fields The fields to grant access to that are Subscriptions (leave blank for all)
     */
    grantSubscription(grantee: IGrantable, ...fields: string[]): Grant;
}
/**
 * Base Class for GraphQL API
 */
export declare abstract class GraphqlApiBase extends Resource implements IGraphqlApi {
    /**
     * an unique AWS AppSync GraphQL API identifier
     * i.e. 'lxz775lwdrgcndgz3nurvac7oa'
     */
    abstract readonly apiId: string;
    /**
     * The GraphQL endpoint ARN
     */
    abstract readonly graphQLEndpointArn: string;
    /**
     * The visibility of the API
     */
    abstract readonly visibility: Visibility;
    /**
     * the ARN of the API
     */
    abstract readonly arn: string;
    /**
     * The Authorization Types for this GraphQL Api
     */
    abstract readonly modes: AuthorizationType[];
    /**
     * add a new dummy data source to this API. Useful for pipeline resolvers
     * and for backend changes that don't require a data source.
     *
     * @param id The data source's id
     * @param options The optional configuration for this data source
     */
    addNoneDataSource(id: string, options?: DataSourceOptions): NoneDataSource;
    /**
     * add a new DynamoDB data source to this API
     *
     * @param id The data source's id
     * @param table The DynamoDB table backing this data source
     * @param options The optional configuration for this data source
     */
    addDynamoDbDataSource(id: string, table: ITable, options?: DataSourceOptions): DynamoDbDataSource;
    /**
     * add a new http data source to this API
     *
     * @param id The data source's id
     * @param endpoint The http endpoint
     * @param options The optional configuration for this data source
     */
    addHttpDataSource(id: string, endpoint: string, options?: HttpDataSourceOptions): HttpDataSource;
    /**
     * add a new Lambda data source to this API
     *
     * @param id The data source's id
     * @param lambdaFunction The Lambda function to call to interact with this data source
     * @param options The optional configuration for this data source
     */
    addLambdaDataSource(id: string, lambdaFunction: IFunction, options?: DataSourceOptions): LambdaDataSource;
    /**
     * add a new Rds data source to this API
     * @param id The data source's id
     * @param serverlessCluster The serverless cluster to interact with this data source
     * @param secretStore The secret store that contains the username and password for the serverless cluster
     * @param databaseName The optional name of the database to use within the cluster
     * @param options The optional configuration for this data source
     */
    addRdsDataSource(id: string, serverlessCluster: IServerlessCluster, secretStore: ISecret, databaseName?: string, options?: DataSourceOptions): RdsDataSource;
    /**
     * add a new Rds data source to this API
     * @param id The data source's id
     * @param serverlessCluster The serverless V2 cluster to interact with this data source
     * @param secretStore The secret store that contains the username and password for the serverless cluster
     * @param databaseName The optional name of the database to use within the cluster
     * @param options The optional configuration for this data source
     */
    addRdsDataSourceV2(id: string, serverlessCluster: IDatabaseCluster, secretStore: ISecret, databaseName?: string, options?: DataSourceOptions): RdsDataSource;
    /**
     * add a new elasticsearch data source to this API
     *
     * @deprecated - use `addOpenSearchDataSource`
     * @param id The data source's id
     * @param domain The elasticsearch domain for this data source
     * @param options The optional configuration for this data source
     */
    addElasticsearchDataSource(id: string, domain: IElasticsearchDomain, options?: DataSourceOptions): ElasticsearchDataSource;
    /**
     * Add an EventBridge data source to this api
     * @param id The data source's id
     * @param eventBus The EventBridge EventBus on which to put events
     * @param options The optional configuration for this data source
     */
    addEventBridgeDataSource(id: string, eventBus: IEventBus, options?: DataSourceOptions): EventBridgeDataSource;
    /**
     * add a new OpenSearch data source to this API
     *
     * @param id The data source's id
     * @param domain The OpenSearch domain for this data source
     * @param options The optional configuration for this data source
     */
    addOpenSearchDataSource(id: string, domain: IOpenSearchDomain, options?: DataSourceOptions): OpenSearchDataSource;
    /**
     * creates a new resolver for this datasource and API using the given properties
     */
    createResolver(id: string, props: ExtendedResolverProps): Resolver;
    /**
     * Add schema dependency if not imported
     *
     * @param construct the dependee
     */
    addSchemaDependency(construct: CfnResource): boolean;
    /**
     * Adds an IAM policy statement associated with this GraphQLApi to an IAM
     * principal's policy.
     *
     * @param grantee The principal
     * @param resources The set of resources to allow (i.e. ...:[region]:[accountId]:apis/GraphQLId/...)
     * @param actions The actions that should be granted to the principal (i.e. appsync:graphql )
     */
    grant(grantee: IGrantable, resources: IamResource, ...actions: string[]): Grant;
    /**
     * Adds an IAM policy statement for Mutation access to this GraphQLApi to an IAM
     * principal's policy.
     *
     * @param grantee The principal
     * @param fields The fields to grant access to that are Mutations (leave blank for all)
     */
    grantMutation(grantee: IGrantable, ...fields: string[]): Grant;
    /**
     * Adds an IAM policy statement for Query access to this GraphQLApi to an IAM
     * principal's policy.
     *
     * @param grantee The principal
     * @param fields The fields to grant access to that are Queries (leave blank for all)
     */
    grantQuery(grantee: IGrantable, ...fields: string[]): Grant;
    /**
     * Adds an IAM policy statement for Subscription access to this GraphQLApi to an IAM
     * principal's policy.
     *
     * @param grantee The principal
     * @param fields The fields to grant access to that are Subscriptions (leave blank for all)
     */
    grantSubscription(grantee: IGrantable, ...fields: string[]): Grant;
}
