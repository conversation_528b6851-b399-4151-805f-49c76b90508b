"use strict";var _a;Object.defineProperty(exports,"__esModule",{value:!0}),exports.AppSyncFieldLogLevel=exports.AppSyncEventResource=void 0;var jsiiDeprecationWarnings=()=>{var tmp=require("../../.warnings.jsii.js");return jsiiDeprecationWarnings=()=>tmp,tmp};const JSII_RTTI_SYMBOL_1=Symbol.for("jsii.rtti");var core_1=()=>{var tmp=require("../../core");return core_1=()=>tmp,tmp};class AppSyncEventResource{static forAPI(){return new AppSyncEventResource([""])}static ofChannelNamespace(channelNamespace){const arns=[`channelNamespace/${channelNamespace}`];return new AppSyncEventResource(arns)}static all(){return new AppSyncEventResource(["*"])}static allChannelNamespaces(){const arns=["channelNamespace/*"];return new AppSyncEventResource(arns)}constructor(arns){this.arns=arns}resourceArns(api){try{jsiiDeprecationWarnings().aws_cdk_lib_aws_appsync_EventApiBase(api)}catch(error){throw process.env.JSII_DEBUG!=="1"&&error.name==="DeprecationError"&&Error.captureStackTrace(error,this.resourceArns),error}return this.arns.map(arn=>arn===""?core_1().Stack.of(api).formatArn({service:"appsync",resource:`apis/${api.apiId}`,arnFormat:core_1().ArnFormat.NO_RESOURCE_NAME}):core_1().Stack.of(api).formatArn({service:"appsync",resource:`apis/${api.apiId}`,arnFormat:core_1().ArnFormat.SLASH_RESOURCE_NAME,resourceName:arn}))}}exports.AppSyncEventResource=AppSyncEventResource,_a=JSII_RTTI_SYMBOL_1,AppSyncEventResource[_a]={fqn:"aws-cdk-lib.aws_appsync.AppSyncEventResource",version:"2.201.0"};var AppSyncFieldLogLevel;(function(AppSyncFieldLogLevel2){AppSyncFieldLogLevel2.NONE="NONE",AppSyncFieldLogLevel2.ERROR="ERROR",AppSyncFieldLogLevel2.INFO="INFO",AppSyncFieldLogLevel2.DEBUG="DEBUG",AppSyncFieldLogLevel2.ALL="ALL"})(AppSyncFieldLogLevel||(exports.AppSyncFieldLogLevel=AppSyncFieldLogLevel={}));
