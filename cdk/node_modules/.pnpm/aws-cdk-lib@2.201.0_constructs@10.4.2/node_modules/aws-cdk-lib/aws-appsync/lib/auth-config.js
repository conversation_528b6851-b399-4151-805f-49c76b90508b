"use strict";Object.defineProperty(exports,"__esModule",{value:!0}),exports.AppSyncAuthorizationType=void 0,exports.createAPIKey=createAPIKey;var appsync_generated_1=()=>{var tmp=require("./appsync.generated");return appsync_generated_1=()=>tmp,tmp},core_1=()=>{var tmp=require("../../core");return core_1=()=>tmp,tmp},AppSyncAuthorizationType;(function(AppSyncAuthorizationType2){AppSyncAuthorizationType2.API_KEY="API_KEY",AppSyncAuthorizationType2.IAM="AWS_IAM",AppSyncAuthorizationType2.USER_POOL="AMAZON_COGNITO_USER_POOLS",AppSyncAuthorizationType2.OIDC="OPENID_CONNECT",AppSyncAuthorizationType2.LAMBDA="AWS_LAMBDA"})(AppSyncAuthorizationType||(exports.AppSyncAuthorizationType=AppSyncAuthorizationType={}));function createAPIKey(scope,apiId,config){if(config?.expires?.isBefore(core_1().Duration.days(1))||config?.expires?.isAfter(core_1().Duration.days(365)))throw Error("API key expiration must be between 1 and 365 days.");const expires=config?.expires?config?.expires.toEpoch():void 0;return new(appsync_generated_1()).CfnApiKey(scope,`${config?.name||"Default"}ApiKey`,{expires,description:config?.description,apiId})}
