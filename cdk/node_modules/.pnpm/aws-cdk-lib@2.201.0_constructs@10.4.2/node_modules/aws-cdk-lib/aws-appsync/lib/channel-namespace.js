"use strict";var __decorate=exports&&exports.__decorate||function(decorators,target,key,desc){var c=arguments.length,r=c<3?target:desc===null?desc=Object.getOwnPropertyDescriptor(target,key):desc,d;if(typeof Reflect=="object"&&typeof Reflect.decorate=="function")r=Reflect.decorate(decorators,target,key,desc);else for(var i=decorators.length-1;i>=0;i--)(d=decorators[i])&&(r=(c<3?d(r):c>3?d(target,key,r):d(target,key))||r);return c>3&&r&&Object.defineProperty(target,key,r),r},_a;Object.defineProperty(exports,"__esModule",{value:!0}),exports.ChannelNamespace=exports.HandlerBehavior=void 0;var jsiiDeprecationWarnings=()=>{var tmp=require("../../.warnings.jsii.js");return jsiiDeprecationWarnings=()=>tmp,tmp};const JSII_RTTI_SYMBOL_1=Symbol.for("jsii.rtti");var appsync_common_1=()=>{var tmp=require("./appsync-common");return appsync_common_1=()=>tmp,tmp},appsync_generated_1=()=>{var tmp=require("./appsync.generated");return appsync_generated_1=()=>tmp,tmp},data_source_common_1=()=>{var tmp=require("./data-source-common");return data_source_common_1=()=>tmp,tmp},core_1=()=>{var tmp=require("../../core");return core_1=()=>tmp,tmp},metadata_resource_1=()=>{var tmp=require("../../core/lib/metadata-resource");return metadata_resource_1=()=>tmp,tmp},prop_injectable_1=()=>{var tmp=require("../../core/lib/prop-injectable");return prop_injectable_1=()=>tmp,tmp},HandlerBehavior;(function(HandlerBehavior2){HandlerBehavior2.CODE="CODE",HandlerBehavior2.DIRECT="DIRECT"})(HandlerBehavior||(exports.HandlerBehavior=HandlerBehavior={}));let ChannelNamespace=class ChannelNamespace2 extends core_1().Resource{static fromChannelNamespaceArn(scope,id,channelNamespaceArn){class Import extends core_1().Resource{constructor(){super(...arguments),this.channelNamespaceArn=channelNamespaceArn}}return new Import(scope,id)}constructor(scope,id,props){try{jsiiDeprecationWarnings().aws_cdk_lib_aws_appsync_ChannelNamespaceProps(props)}catch(error){throw process.env.JSII_DEBUG!=="1"&&error.name==="DeprecationError"&&Error.captureStackTrace(error,ChannelNamespace2),error}if(props.channelNamespaceName!==void 0&&!core_1().Token.isUnresolved(props.channelNamespaceName)&&(props.channelNamespaceName.length<1||props.channelNamespaceName.length>50))throw new(core_1()).ValidationError(`\`channelNamespaceName\` must be between 1 and 50 characters, got: ${props.channelNamespaceName.length} characters.`,scope);super(scope,id,{physicalName:props.channelNamespaceName??id}),(0,metadata_resource_1().addConstructMetadata)(this,props),this.validateHandlerConfig(props);const code=props.code?.bind(this);let handlerConfig={};props.publishHandlerConfig&&(handlerConfig={onPublish:{behavior:props.publishHandlerConfig?.direct?HandlerBehavior.DIRECT:HandlerBehavior.CODE,integration:{dataSourceName:props.publishHandlerConfig?.dataSource?.name||""}}},handlerConfig.onPublish.behavior===HandlerBehavior.DIRECT&&(handlerConfig.onPublish.integration.lambdaConfig={invokeType:props.publishHandlerConfig?.lambdaInvokeType||data_source_common_1().LambdaInvokeType.REQUEST_RESPONSE})),props.subscribeHandlerConfig&&(handlerConfig={...handlerConfig,onSubscribe:{behavior:props.subscribeHandlerConfig?.direct?HandlerBehavior.DIRECT:HandlerBehavior.CODE,integration:{dataSourceName:props.subscribeHandlerConfig?.dataSource?.name||""}}},handlerConfig.onSubscribe.behavior===HandlerBehavior.DIRECT&&(handlerConfig.onSubscribe.integration.lambdaConfig={invokeType:props.subscribeHandlerConfig?.lambdaInvokeType||data_source_common_1().LambdaInvokeType.REQUEST_RESPONSE})),this.validateAuthorizationConfig(props.api.authProviderTypes,props.authorizationConfig?.publishAuthModeTypes??[]),this.validateAuthorizationConfig(props.api.authProviderTypes,props.authorizationConfig?.subscribeAuthModeTypes??[]),this.channelNamespace=new(appsync_generated_1()).CfnChannelNamespace(this,"Resource",{name:this.physicalName,apiId:props.api.apiId,codeHandlers:code?.inlineCode,codeS3Location:code?.s3Location,handlerConfigs:handlerConfig,publishAuthModes:props.authorizationConfig?.publishAuthModeTypes?.map(mode=>({authType:mode})),subscribeAuthModes:props.authorizationConfig?.subscribeAuthModeTypes?.map(mode=>({authType:mode}))}),props.publishHandlerConfig?.dataSource&&this.channelNamespace.addDependency(props.publishHandlerConfig.dataSource.resource),props.subscribeHandlerConfig?.dataSource&&this.channelNamespace.addDependency(props.subscribeHandlerConfig.dataSource.resource),this.channelNamespaceArn=this.channelNamespace.attrChannelNamespaceArn,this.api=props.api}grantSubscribe(grantee){try{jsiiDeprecationWarnings().aws_cdk_lib_aws_iam_IGrantable(grantee)}catch(error){throw process.env.JSII_DEBUG!=="1"&&error.name==="DeprecationError"&&Error.captureStackTrace(error,this.grantSubscribe),error}return this.api.grant(grantee,appsync_common_1().AppSyncEventResource.ofChannelNamespace(this.channelNamespace.name),"appsync:EventSubscribe")}grantPublish(grantee){try{jsiiDeprecationWarnings().aws_cdk_lib_aws_iam_IGrantable(grantee)}catch(error){throw process.env.JSII_DEBUG!=="1"&&error.name==="DeprecationError"&&Error.captureStackTrace(error,this.grantPublish),error}return this.api.grant(grantee,appsync_common_1().AppSyncEventResource.ofChannelNamespace(this.channelNamespace.name),"appsync:EventPublish")}grantPublishAndSubscribe(grantee){try{jsiiDeprecationWarnings().aws_cdk_lib_aws_iam_IGrantable(grantee)}catch(error){throw process.env.JSII_DEBUG!=="1"&&error.name==="DeprecationError"&&Error.captureStackTrace(error,this.grantPublishAndSubscribe),error}return this.api.grant(grantee,appsync_common_1().AppSyncEventResource.ofChannelNamespace(this.channelNamespace.name),"appsync:EventPublish","appsync:EventSubscribe")}validateAuthorizationConfig(apiAuthProviders,channelAuthModeTypes){for(const mode of channelAuthModeTypes)if(!apiAuthProviders.find(authProvider=>authProvider===mode))throw new(core_1()).ValidationError(`API is missing authorization configuration for ${mode}`,this)}validateHandlerConfig(props){if(!(!props?.publishHandlerConfig&&!props?.subscribeHandlerConfig)){if(props.publishHandlerConfig?.direct){if(!props.publishHandlerConfig.dataSource)throw new(core_1()).ValidationError("No data source provided. AWS_LAMBDA data source is required for Direct handler behavior type",this);if(props.publishHandlerConfig.dataSource.resource.type!==data_source_common_1().AppSyncDataSourceType.LAMBDA)throw new(core_1()).ValidationError("Direct integration is only supported for AWS_LAMBDA data sources.",this)}if(props.subscribeHandlerConfig?.direct){if(!props.subscribeHandlerConfig.dataSource)throw new(core_1()).ValidationError("No data source provided. AWS_LAMBDA data source is required for Direct handler behavior type",this);if(props.subscribeHandlerConfig.dataSource.resource.type!==data_source_common_1().AppSyncDataSourceType.LAMBDA)throw new(core_1()).ValidationError("Direct integration is only supported for AWS_LAMBDA data sources.",this)}if(props.publishHandlerConfig?.direct&&props.subscribeHandlerConfig?.direct&&props.code)throw new(core_1()).ValidationError("Code handlers are not supported when both publish and subscribe use the Direct data source behavior",this);if(!props.publishHandlerConfig?.direct&&props.publishHandlerConfig?.lambdaInvokeType)throw new(core_1()).ValidationError("LambdaInvokeType is only supported for Direct handler behavior type",this);if(!props.subscribeHandlerConfig?.direct&&props.subscribeHandlerConfig?.lambdaInvokeType)throw new(core_1()).ValidationError("LambdaInvokeType is only supported for Direct handler behavior type",this)}}};exports.ChannelNamespace=ChannelNamespace,_a=JSII_RTTI_SYMBOL_1,ChannelNamespace[_a]={fqn:"aws-cdk-lib.aws_appsync.ChannelNamespace",version:"2.201.0"},ChannelNamespace.PROPERTY_INJECTION_ID="aws-cdk-lib.aws-appsync.ChannelNamespace",__decorate([(0,metadata_resource_1().MethodMetadata)()],ChannelNamespace.prototype,"grantSubscribe",null),__decorate([(0,metadata_resource_1().MethodMetadata)()],ChannelNamespace.prototype,"grantPublish",null),__decorate([(0,metadata_resource_1().MethodMetadata)()],ChannelNamespace.prototype,"grantPublishAndSubscribe",null),exports.ChannelNamespace=ChannelNamespace=__decorate([prop_injectable_1().propertyInjectable],ChannelNamespace);
