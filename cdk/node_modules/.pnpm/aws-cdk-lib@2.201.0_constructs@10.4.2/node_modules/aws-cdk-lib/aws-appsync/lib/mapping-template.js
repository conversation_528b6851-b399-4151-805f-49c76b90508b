"use strict";var _a;Object.defineProperty(exports,"__esModule",{value:!0}),exports.MappingTemplate=void 0;var jsiiDeprecationWarnings=()=>{var tmp=require("../../.warnings.jsii.js");return jsiiDeprecationWarnings=()=>tmp,tmp};const JSII_RTTI_SYMBOL_1=Symbol.for("jsii.rtti");var fs_1=()=>{var tmp=require("fs");return fs_1=()=>tmp,tmp};class MappingTemplate{static fromString(template){return new StringMappingTemplate(template)}static fromFile(fileName){return new StringMappingTemplate((0,fs_1().readFileSync)(fileName).toString("utf-8"))}static dynamoDbResultList(){return this.fromString("$util.toJson($ctx.result.items)")}static dynamoDbResultItem(){return this.fromString("$util.toJson($ctx.result)")}static dynamoDbScanTable(consistentRead=!1){return this.fromString(`{"version" : "2017-02-28", "operation" : "Scan", "consistentRead": ${consistentRead}}`)}static dynamoDbQuery(cond,indexName,consistentRead=!1){try{jsiiDeprecationWarnings().aws_cdk_lib_aws_appsync_KeyCondition(cond)}catch(error){throw process.env.JSII_DEBUG!=="1"&&error.name==="DeprecationError"&&Error.captureStackTrace(error,this.dynamoDbQuery),error}return this.fromString(`{"version" : "2017-02-28", "operation" : "Query",  "consistentRead": ${consistentRead}, ${indexName?`"index" : "${indexName}", `:""}${cond.renderTemplate()}}`)}static dynamoDbGetItem(keyName,idArg,consistentRead=!1){return this.fromString(`{"version": "2017-02-28", "operation": "GetItem", "consistentRead": ${consistentRead}, "key": {"${keyName}": $util.dynamodb.toDynamoDBJson($ctx.args.${idArg})}}`)}static dynamoDbDeleteItem(keyName,idArg){return this.fromString(`{"version": "2017-02-28", "operation": "DeleteItem", "key": {"${keyName}": $util.dynamodb.toDynamoDBJson($ctx.args.${idArg})}}`)}static dynamoDbPutItem(key,values){try{jsiiDeprecationWarnings().aws_cdk_lib_aws_appsync_PrimaryKey(key),jsiiDeprecationWarnings().aws_cdk_lib_aws_appsync_AttributeValues(values)}catch(error){throw process.env.JSII_DEBUG!=="1"&&error.name==="DeprecationError"&&Error.captureStackTrace(error,this.dynamoDbPutItem),error}return this.fromString(`
      ${values.renderVariables()}
      {
        "version": "2017-02-28",
        "operation": "PutItem",
        ${key.renderTemplate()},
        ${values.renderTemplate()}
      }`)}static lambdaRequest(payload="$util.toJson($ctx)",operation="Invoke"){return this.fromString(`{"version": "2017-02-28", "operation": "${operation}", "payload": ${payload}}`)}static lambdaResult(){return this.fromString("$util.toJson($ctx.result)")}}exports.MappingTemplate=MappingTemplate,_a=JSII_RTTI_SYMBOL_1,MappingTemplate[_a]={fqn:"aws-cdk-lib.aws_appsync.MappingTemplate",version:"2.201.0"};class StringMappingTemplate extends MappingTemplate{constructor(template){super(),this.template=template}renderTemplate(){return this.template}}
