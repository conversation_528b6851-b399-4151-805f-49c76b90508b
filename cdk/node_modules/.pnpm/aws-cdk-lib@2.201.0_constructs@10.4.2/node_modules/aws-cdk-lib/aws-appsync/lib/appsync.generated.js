"use strict";var _a,_b,_c,_d,_e,_f,_g,_h,_j,_k,_l,_m;Object.defineProperty(exports,"__esModule",{value:!0}),exports.CfnChannelNamespace=exports.CfnApi=exports.CfnSourceApiAssociation=exports.CfnResolver=exports.CfnGraphQLSchema=exports.CfnGraphQLApi=exports.CfnFunctionConfiguration=exports.CfnDomainNameApiAssociation=exports.CfnDomainName=exports.CfnDataSource=exports.CfnApiKey=exports.CfnApiCache=void 0;var jsiiDeprecationWarnings=()=>{var tmp=require("../../.warnings.jsii.js");return jsiiDeprecationWarnings=()=>tmp,tmp};const JSII_RTTI_SYMBOL_1=Symbol.for("jsii.rtti");var cdk=()=>{var tmp=require("../../core");return cdk=()=>tmp,tmp},cfn_parse=()=>{var tmp=require("../../core/lib/helpers-internal");return cfn_parse=()=>tmp,tmp},cdk_errors=()=>{var tmp=require("../../core/lib/errors");return cdk_errors=()=>tmp,tmp};class CfnApiCache extends cdk().CfnResource{static _fromCloudFormation(scope,id,resourceAttributes,options){resourceAttributes=resourceAttributes||{};const resourceProperties=options.parser.parseValue(resourceAttributes.Properties),propsResult=CfnApiCachePropsFromCloudFormation(resourceProperties);if(cdk().isResolvableObject(propsResult.value))throw new(cdk_errors()).ValidationError("Unexpected IResolvable",scope);const ret=new CfnApiCache(scope,id,propsResult.value);for(const[propKey,propVal]of Object.entries(propsResult.extraProperties))ret.addPropertyOverride(propKey,propVal);return options.parser.handleAttributes(ret,resourceAttributes,id),ret}constructor(scope,id,props){super(scope,id,{type:CfnApiCache.CFN_RESOURCE_TYPE_NAME,properties:props});try{jsiiDeprecationWarnings().aws_cdk_lib_aws_appsync_CfnApiCacheProps(props)}catch(error){throw process.env.JSII_DEBUG!=="1"&&error.name==="DeprecationError"&&Error.captureStackTrace(error,CfnApiCache),error}cdk().requireProperty(props,"apiCachingBehavior",this),cdk().requireProperty(props,"apiId",this),cdk().requireProperty(props,"ttl",this),cdk().requireProperty(props,"type",this),this.attrId=cdk().Token.asString(this.getAtt("Id",cdk().ResolutionTypeHint.STRING)),this.apiCachingBehavior=props.apiCachingBehavior,this.apiId=props.apiId,this.atRestEncryptionEnabled=props.atRestEncryptionEnabled,this.healthMetricsConfig=props.healthMetricsConfig,this.transitEncryptionEnabled=props.transitEncryptionEnabled,this.ttl=props.ttl,this.type=props.type}get cfnProperties(){return{apiCachingBehavior:this.apiCachingBehavior,apiId:this.apiId,atRestEncryptionEnabled:this.atRestEncryptionEnabled,healthMetricsConfig:this.healthMetricsConfig,transitEncryptionEnabled:this.transitEncryptionEnabled,ttl:this.ttl,type:this.type}}inspect(inspector){try{jsiiDeprecationWarnings().aws_cdk_lib_TreeInspector(inspector)}catch(error){throw process.env.JSII_DEBUG!=="1"&&error.name==="DeprecationError"&&Error.captureStackTrace(error,this.inspect),error}inspector.addAttribute("aws:cdk:cloudformation:type",CfnApiCache.CFN_RESOURCE_TYPE_NAME),inspector.addAttribute("aws:cdk:cloudformation:props",this.cfnProperties)}renderProperties(props){return convertCfnApiCachePropsToCloudFormation(props)}}exports.CfnApiCache=CfnApiCache,_a=JSII_RTTI_SYMBOL_1,CfnApiCache[_a]={fqn:"aws-cdk-lib.aws_appsync.CfnApiCache",version:"2.201.0"},CfnApiCache.CFN_RESOURCE_TYPE_NAME="AWS::AppSync::ApiCache";function CfnApiCachePropsValidator(properties){if(!cdk().canInspect(properties))return cdk().VALIDATION_SUCCESS;const errors=new(cdk()).ValidationResults;return properties&&typeof properties=="object"&&!Array.isArray(properties)||errors.collect(new(cdk()).ValidationResult("Expected an object, but received: "+JSON.stringify(properties))),errors.collect(cdk().propertyValidator("apiCachingBehavior",cdk().requiredValidator)(properties.apiCachingBehavior)),errors.collect(cdk().propertyValidator("apiCachingBehavior",cdk().validateString)(properties.apiCachingBehavior)),errors.collect(cdk().propertyValidator("apiId",cdk().requiredValidator)(properties.apiId)),errors.collect(cdk().propertyValidator("apiId",cdk().validateString)(properties.apiId)),errors.collect(cdk().propertyValidator("atRestEncryptionEnabled",cdk().validateBoolean)(properties.atRestEncryptionEnabled)),errors.collect(cdk().propertyValidator("healthMetricsConfig",cdk().validateString)(properties.healthMetricsConfig)),errors.collect(cdk().propertyValidator("transitEncryptionEnabled",cdk().validateBoolean)(properties.transitEncryptionEnabled)),errors.collect(cdk().propertyValidator("ttl",cdk().requiredValidator)(properties.ttl)),errors.collect(cdk().propertyValidator("ttl",cdk().validateNumber)(properties.ttl)),errors.collect(cdk().propertyValidator("type",cdk().requiredValidator)(properties.type)),errors.collect(cdk().propertyValidator("type",cdk().validateString)(properties.type)),errors.wrap('supplied properties not correct for "CfnApiCacheProps"')}function convertCfnApiCachePropsToCloudFormation(properties){return cdk().canInspect(properties)?(CfnApiCachePropsValidator(properties).assertSuccess(),{ApiCachingBehavior:cdk().stringToCloudFormation(properties.apiCachingBehavior),ApiId:cdk().stringToCloudFormation(properties.apiId),AtRestEncryptionEnabled:cdk().booleanToCloudFormation(properties.atRestEncryptionEnabled),HealthMetricsConfig:cdk().stringToCloudFormation(properties.healthMetricsConfig),TransitEncryptionEnabled:cdk().booleanToCloudFormation(properties.transitEncryptionEnabled),Ttl:cdk().numberToCloudFormation(properties.ttl),Type:cdk().stringToCloudFormation(properties.type)}):properties}function CfnApiCachePropsFromCloudFormation(properties){if(cdk().isResolvableObject(properties))return new(cfn_parse()).FromCloudFormationResult(properties);if(properties=properties??{},!(properties&&typeof properties=="object"&&!Array.isArray(properties)))return new(cfn_parse()).FromCloudFormationResult(properties);const ret=new(cfn_parse()).FromCloudFormationPropertyObject;return ret.addPropertyResult("apiCachingBehavior","ApiCachingBehavior",properties.ApiCachingBehavior!=null?cfn_parse().FromCloudFormation.getString(properties.ApiCachingBehavior):void 0),ret.addPropertyResult("apiId","ApiId",properties.ApiId!=null?cfn_parse().FromCloudFormation.getString(properties.ApiId):void 0),ret.addPropertyResult("atRestEncryptionEnabled","AtRestEncryptionEnabled",properties.AtRestEncryptionEnabled!=null?cfn_parse().FromCloudFormation.getBoolean(properties.AtRestEncryptionEnabled):void 0),ret.addPropertyResult("healthMetricsConfig","HealthMetricsConfig",properties.HealthMetricsConfig!=null?cfn_parse().FromCloudFormation.getString(properties.HealthMetricsConfig):void 0),ret.addPropertyResult("transitEncryptionEnabled","TransitEncryptionEnabled",properties.TransitEncryptionEnabled!=null?cfn_parse().FromCloudFormation.getBoolean(properties.TransitEncryptionEnabled):void 0),ret.addPropertyResult("ttl","Ttl",properties.Ttl!=null?cfn_parse().FromCloudFormation.getNumber(properties.Ttl):void 0),ret.addPropertyResult("type","Type",properties.Type!=null?cfn_parse().FromCloudFormation.getString(properties.Type):void 0),ret.addUnrecognizedPropertiesAsExtra(properties),ret}class CfnApiKey extends cdk().CfnResource{static _fromCloudFormation(scope,id,resourceAttributes,options){resourceAttributes=resourceAttributes||{};const resourceProperties=options.parser.parseValue(resourceAttributes.Properties),propsResult=CfnApiKeyPropsFromCloudFormation(resourceProperties);if(cdk().isResolvableObject(propsResult.value))throw new(cdk_errors()).ValidationError("Unexpected IResolvable",scope);const ret=new CfnApiKey(scope,id,propsResult.value);for(const[propKey,propVal]of Object.entries(propsResult.extraProperties))ret.addPropertyOverride(propKey,propVal);return options.parser.handleAttributes(ret,resourceAttributes,id),ret}constructor(scope,id,props){super(scope,id,{type:CfnApiKey.CFN_RESOURCE_TYPE_NAME,properties:props});try{jsiiDeprecationWarnings().aws_cdk_lib_aws_appsync_CfnApiKeyProps(props)}catch(error){throw process.env.JSII_DEBUG!=="1"&&error.name==="DeprecationError"&&Error.captureStackTrace(error,CfnApiKey),error}cdk().requireProperty(props,"apiId",this),this.attrApiKey=cdk().Token.asString(this.getAtt("ApiKey",cdk().ResolutionTypeHint.STRING)),this.attrApiKeyId=cdk().Token.asString(this.getAtt("ApiKeyId",cdk().ResolutionTypeHint.STRING)),this.attrArn=cdk().Token.asString(this.getAtt("Arn",cdk().ResolutionTypeHint.STRING)),this.apiId=props.apiId,this.description=props.description,this.expires=props.expires}get cfnProperties(){return{apiId:this.apiId,description:this.description,expires:this.expires}}inspect(inspector){try{jsiiDeprecationWarnings().aws_cdk_lib_TreeInspector(inspector)}catch(error){throw process.env.JSII_DEBUG!=="1"&&error.name==="DeprecationError"&&Error.captureStackTrace(error,this.inspect),error}inspector.addAttribute("aws:cdk:cloudformation:type",CfnApiKey.CFN_RESOURCE_TYPE_NAME),inspector.addAttribute("aws:cdk:cloudformation:props",this.cfnProperties)}renderProperties(props){return convertCfnApiKeyPropsToCloudFormation(props)}}exports.CfnApiKey=CfnApiKey,_b=JSII_RTTI_SYMBOL_1,CfnApiKey[_b]={fqn:"aws-cdk-lib.aws_appsync.CfnApiKey",version:"2.201.0"},CfnApiKey.CFN_RESOURCE_TYPE_NAME="AWS::AppSync::ApiKey";function CfnApiKeyPropsValidator(properties){if(!cdk().canInspect(properties))return cdk().VALIDATION_SUCCESS;const errors=new(cdk()).ValidationResults;return properties&&typeof properties=="object"&&!Array.isArray(properties)||errors.collect(new(cdk()).ValidationResult("Expected an object, but received: "+JSON.stringify(properties))),errors.collect(cdk().propertyValidator("apiId",cdk().requiredValidator)(properties.apiId)),errors.collect(cdk().propertyValidator("apiId",cdk().validateString)(properties.apiId)),errors.collect(cdk().propertyValidator("description",cdk().validateString)(properties.description)),errors.collect(cdk().propertyValidator("expires",cdk().validateNumber)(properties.expires)),errors.wrap('supplied properties not correct for "CfnApiKeyProps"')}function convertCfnApiKeyPropsToCloudFormation(properties){return cdk().canInspect(properties)?(CfnApiKeyPropsValidator(properties).assertSuccess(),{ApiId:cdk().stringToCloudFormation(properties.apiId),Description:cdk().stringToCloudFormation(properties.description),Expires:cdk().numberToCloudFormation(properties.expires)}):properties}function CfnApiKeyPropsFromCloudFormation(properties){if(cdk().isResolvableObject(properties))return new(cfn_parse()).FromCloudFormationResult(properties);if(properties=properties??{},!(properties&&typeof properties=="object"&&!Array.isArray(properties)))return new(cfn_parse()).FromCloudFormationResult(properties);const ret=new(cfn_parse()).FromCloudFormationPropertyObject;return ret.addPropertyResult("apiId","ApiId",properties.ApiId!=null?cfn_parse().FromCloudFormation.getString(properties.ApiId):void 0),ret.addPropertyResult("description","Description",properties.Description!=null?cfn_parse().FromCloudFormation.getString(properties.Description):void 0),ret.addPropertyResult("expires","Expires",properties.Expires!=null?cfn_parse().FromCloudFormation.getNumber(properties.Expires):void 0),ret.addUnrecognizedPropertiesAsExtra(properties),ret}class CfnDataSource extends cdk().CfnResource{static _fromCloudFormation(scope,id,resourceAttributes,options){resourceAttributes=resourceAttributes||{};const resourceProperties=options.parser.parseValue(resourceAttributes.Properties),propsResult=CfnDataSourcePropsFromCloudFormation(resourceProperties);if(cdk().isResolvableObject(propsResult.value))throw new(cdk_errors()).ValidationError("Unexpected IResolvable",scope);const ret=new CfnDataSource(scope,id,propsResult.value);for(const[propKey,propVal]of Object.entries(propsResult.extraProperties))ret.addPropertyOverride(propKey,propVal);return options.parser.handleAttributes(ret,resourceAttributes,id),ret}constructor(scope,id,props){super(scope,id,{type:CfnDataSource.CFN_RESOURCE_TYPE_NAME,properties:props});try{jsiiDeprecationWarnings().aws_cdk_lib_aws_appsync_CfnDataSourceProps(props)}catch(error){throw process.env.JSII_DEBUG!=="1"&&error.name==="DeprecationError"&&Error.captureStackTrace(error,CfnDataSource),error}cdk().requireProperty(props,"apiId",this),cdk().requireProperty(props,"name",this),cdk().requireProperty(props,"type",this),this.attrDataSourceArn=cdk().Token.asString(this.getAtt("DataSourceArn",cdk().ResolutionTypeHint.STRING)),this.attrName=cdk().Token.asString(this.getAtt("Name",cdk().ResolutionTypeHint.STRING)),this.apiId=props.apiId,this.description=props.description,this.dynamoDbConfig=props.dynamoDbConfig,this.elasticsearchConfig=props.elasticsearchConfig,this.eventBridgeConfig=props.eventBridgeConfig,this.httpConfig=props.httpConfig,this.lambdaConfig=props.lambdaConfig,this.metricsConfig=props.metricsConfig,this.name=props.name,this.openSearchServiceConfig=props.openSearchServiceConfig,this.relationalDatabaseConfig=props.relationalDatabaseConfig,this.serviceRoleArn=props.serviceRoleArn,this.type=props.type}get cfnProperties(){return{apiId:this.apiId,description:this.description,dynamoDbConfig:this.dynamoDbConfig,elasticsearchConfig:this.elasticsearchConfig,eventBridgeConfig:this.eventBridgeConfig,httpConfig:this.httpConfig,lambdaConfig:this.lambdaConfig,metricsConfig:this.metricsConfig,name:this.name,openSearchServiceConfig:this.openSearchServiceConfig,relationalDatabaseConfig:this.relationalDatabaseConfig,serviceRoleArn:this.serviceRoleArn,type:this.type}}inspect(inspector){try{jsiiDeprecationWarnings().aws_cdk_lib_TreeInspector(inspector)}catch(error){throw process.env.JSII_DEBUG!=="1"&&error.name==="DeprecationError"&&Error.captureStackTrace(error,this.inspect),error}inspector.addAttribute("aws:cdk:cloudformation:type",CfnDataSource.CFN_RESOURCE_TYPE_NAME),inspector.addAttribute("aws:cdk:cloudformation:props",this.cfnProperties)}renderProperties(props){return convertCfnDataSourcePropsToCloudFormation(props)}}exports.CfnDataSource=CfnDataSource,_c=JSII_RTTI_SYMBOL_1,CfnDataSource[_c]={fqn:"aws-cdk-lib.aws_appsync.CfnDataSource",version:"2.201.0"},CfnDataSource.CFN_RESOURCE_TYPE_NAME="AWS::AppSync::DataSource";function CfnDataSourceOpenSearchServiceConfigPropertyValidator(properties){if(!cdk().canInspect(properties))return cdk().VALIDATION_SUCCESS;const errors=new(cdk()).ValidationResults;return properties&&typeof properties=="object"&&!Array.isArray(properties)||errors.collect(new(cdk()).ValidationResult("Expected an object, but received: "+JSON.stringify(properties))),errors.collect(cdk().propertyValidator("awsRegion",cdk().requiredValidator)(properties.awsRegion)),errors.collect(cdk().propertyValidator("awsRegion",cdk().validateString)(properties.awsRegion)),errors.collect(cdk().propertyValidator("endpoint",cdk().requiredValidator)(properties.endpoint)),errors.collect(cdk().propertyValidator("endpoint",cdk().validateString)(properties.endpoint)),errors.wrap('supplied properties not correct for "OpenSearchServiceConfigProperty"')}function convertCfnDataSourceOpenSearchServiceConfigPropertyToCloudFormation(properties){return cdk().canInspect(properties)?(CfnDataSourceOpenSearchServiceConfigPropertyValidator(properties).assertSuccess(),{AwsRegion:cdk().stringToCloudFormation(properties.awsRegion),Endpoint:cdk().stringToCloudFormation(properties.endpoint)}):properties}function CfnDataSourceOpenSearchServiceConfigPropertyFromCloudFormation(properties){if(cdk().isResolvableObject(properties))return new(cfn_parse()).FromCloudFormationResult(properties);if(properties=properties??{},!(properties&&typeof properties=="object"&&!Array.isArray(properties)))return new(cfn_parse()).FromCloudFormationResult(properties);const ret=new(cfn_parse()).FromCloudFormationPropertyObject;return ret.addPropertyResult("awsRegion","AwsRegion",properties.AwsRegion!=null?cfn_parse().FromCloudFormation.getString(properties.AwsRegion):void 0),ret.addPropertyResult("endpoint","Endpoint",properties.Endpoint!=null?cfn_parse().FromCloudFormation.getString(properties.Endpoint):void 0),ret.addUnrecognizedPropertiesAsExtra(properties),ret}function CfnDataSourceEventBridgeConfigPropertyValidator(properties){if(!cdk().canInspect(properties))return cdk().VALIDATION_SUCCESS;const errors=new(cdk()).ValidationResults;return properties&&typeof properties=="object"&&!Array.isArray(properties)||errors.collect(new(cdk()).ValidationResult("Expected an object, but received: "+JSON.stringify(properties))),errors.collect(cdk().propertyValidator("eventBusArn",cdk().requiredValidator)(properties.eventBusArn)),errors.collect(cdk().propertyValidator("eventBusArn",cdk().validateString)(properties.eventBusArn)),errors.wrap('supplied properties not correct for "EventBridgeConfigProperty"')}function convertCfnDataSourceEventBridgeConfigPropertyToCloudFormation(properties){return cdk().canInspect(properties)?(CfnDataSourceEventBridgeConfigPropertyValidator(properties).assertSuccess(),{EventBusArn:cdk().stringToCloudFormation(properties.eventBusArn)}):properties}function CfnDataSourceEventBridgeConfigPropertyFromCloudFormation(properties){if(cdk().isResolvableObject(properties))return new(cfn_parse()).FromCloudFormationResult(properties);if(properties=properties??{},!(properties&&typeof properties=="object"&&!Array.isArray(properties)))return new(cfn_parse()).FromCloudFormationResult(properties);const ret=new(cfn_parse()).FromCloudFormationPropertyObject;return ret.addPropertyResult("eventBusArn","EventBusArn",properties.EventBusArn!=null?cfn_parse().FromCloudFormation.getString(properties.EventBusArn):void 0),ret.addUnrecognizedPropertiesAsExtra(properties),ret}function CfnDataSourceAwsIamConfigPropertyValidator(properties){if(!cdk().canInspect(properties))return cdk().VALIDATION_SUCCESS;const errors=new(cdk()).ValidationResults;return properties&&typeof properties=="object"&&!Array.isArray(properties)||errors.collect(new(cdk()).ValidationResult("Expected an object, but received: "+JSON.stringify(properties))),errors.collect(cdk().propertyValidator("signingRegion",cdk().validateString)(properties.signingRegion)),errors.collect(cdk().propertyValidator("signingServiceName",cdk().validateString)(properties.signingServiceName)),errors.wrap('supplied properties not correct for "AwsIamConfigProperty"')}function convertCfnDataSourceAwsIamConfigPropertyToCloudFormation(properties){return cdk().canInspect(properties)?(CfnDataSourceAwsIamConfigPropertyValidator(properties).assertSuccess(),{SigningRegion:cdk().stringToCloudFormation(properties.signingRegion),SigningServiceName:cdk().stringToCloudFormation(properties.signingServiceName)}):properties}function CfnDataSourceAwsIamConfigPropertyFromCloudFormation(properties){if(cdk().isResolvableObject(properties))return new(cfn_parse()).FromCloudFormationResult(properties);if(properties=properties??{},!(properties&&typeof properties=="object"&&!Array.isArray(properties)))return new(cfn_parse()).FromCloudFormationResult(properties);const ret=new(cfn_parse()).FromCloudFormationPropertyObject;return ret.addPropertyResult("signingRegion","SigningRegion",properties.SigningRegion!=null?cfn_parse().FromCloudFormation.getString(properties.SigningRegion):void 0),ret.addPropertyResult("signingServiceName","SigningServiceName",properties.SigningServiceName!=null?cfn_parse().FromCloudFormation.getString(properties.SigningServiceName):void 0),ret.addUnrecognizedPropertiesAsExtra(properties),ret}function CfnDataSourceAuthorizationConfigPropertyValidator(properties){if(!cdk().canInspect(properties))return cdk().VALIDATION_SUCCESS;const errors=new(cdk()).ValidationResults;return properties&&typeof properties=="object"&&!Array.isArray(properties)||errors.collect(new(cdk()).ValidationResult("Expected an object, but received: "+JSON.stringify(properties))),errors.collect(cdk().propertyValidator("authorizationType",cdk().requiredValidator)(properties.authorizationType)),errors.collect(cdk().propertyValidator("authorizationType",cdk().validateString)(properties.authorizationType)),errors.collect(cdk().propertyValidator("awsIamConfig",CfnDataSourceAwsIamConfigPropertyValidator)(properties.awsIamConfig)),errors.wrap('supplied properties not correct for "AuthorizationConfigProperty"')}function convertCfnDataSourceAuthorizationConfigPropertyToCloudFormation(properties){return cdk().canInspect(properties)?(CfnDataSourceAuthorizationConfigPropertyValidator(properties).assertSuccess(),{AuthorizationType:cdk().stringToCloudFormation(properties.authorizationType),AwsIamConfig:convertCfnDataSourceAwsIamConfigPropertyToCloudFormation(properties.awsIamConfig)}):properties}function CfnDataSourceAuthorizationConfigPropertyFromCloudFormation(properties){if(cdk().isResolvableObject(properties))return new(cfn_parse()).FromCloudFormationResult(properties);if(properties=properties??{},!(properties&&typeof properties=="object"&&!Array.isArray(properties)))return new(cfn_parse()).FromCloudFormationResult(properties);const ret=new(cfn_parse()).FromCloudFormationPropertyObject;return ret.addPropertyResult("authorizationType","AuthorizationType",properties.AuthorizationType!=null?cfn_parse().FromCloudFormation.getString(properties.AuthorizationType):void 0),ret.addPropertyResult("awsIamConfig","AwsIamConfig",properties.AwsIamConfig!=null?CfnDataSourceAwsIamConfigPropertyFromCloudFormation(properties.AwsIamConfig):void 0),ret.addUnrecognizedPropertiesAsExtra(properties),ret}function CfnDataSourceHttpConfigPropertyValidator(properties){if(!cdk().canInspect(properties))return cdk().VALIDATION_SUCCESS;const errors=new(cdk()).ValidationResults;return properties&&typeof properties=="object"&&!Array.isArray(properties)||errors.collect(new(cdk()).ValidationResult("Expected an object, but received: "+JSON.stringify(properties))),errors.collect(cdk().propertyValidator("authorizationConfig",CfnDataSourceAuthorizationConfigPropertyValidator)(properties.authorizationConfig)),errors.collect(cdk().propertyValidator("endpoint",cdk().requiredValidator)(properties.endpoint)),errors.collect(cdk().propertyValidator("endpoint",cdk().validateString)(properties.endpoint)),errors.wrap('supplied properties not correct for "HttpConfigProperty"')}function convertCfnDataSourceHttpConfigPropertyToCloudFormation(properties){return cdk().canInspect(properties)?(CfnDataSourceHttpConfigPropertyValidator(properties).assertSuccess(),{AuthorizationConfig:convertCfnDataSourceAuthorizationConfigPropertyToCloudFormation(properties.authorizationConfig),Endpoint:cdk().stringToCloudFormation(properties.endpoint)}):properties}function CfnDataSourceHttpConfigPropertyFromCloudFormation(properties){if(cdk().isResolvableObject(properties))return new(cfn_parse()).FromCloudFormationResult(properties);if(properties=properties??{},!(properties&&typeof properties=="object"&&!Array.isArray(properties)))return new(cfn_parse()).FromCloudFormationResult(properties);const ret=new(cfn_parse()).FromCloudFormationPropertyObject;return ret.addPropertyResult("authorizationConfig","AuthorizationConfig",properties.AuthorizationConfig!=null?CfnDataSourceAuthorizationConfigPropertyFromCloudFormation(properties.AuthorizationConfig):void 0),ret.addPropertyResult("endpoint","Endpoint",properties.Endpoint!=null?cfn_parse().FromCloudFormation.getString(properties.Endpoint):void 0),ret.addUnrecognizedPropertiesAsExtra(properties),ret}function CfnDataSourceRdsHttpEndpointConfigPropertyValidator(properties){if(!cdk().canInspect(properties))return cdk().VALIDATION_SUCCESS;const errors=new(cdk()).ValidationResults;return properties&&typeof properties=="object"&&!Array.isArray(properties)||errors.collect(new(cdk()).ValidationResult("Expected an object, but received: "+JSON.stringify(properties))),errors.collect(cdk().propertyValidator("awsRegion",cdk().requiredValidator)(properties.awsRegion)),errors.collect(cdk().propertyValidator("awsRegion",cdk().validateString)(properties.awsRegion)),errors.collect(cdk().propertyValidator("awsSecretStoreArn",cdk().requiredValidator)(properties.awsSecretStoreArn)),errors.collect(cdk().propertyValidator("awsSecretStoreArn",cdk().validateString)(properties.awsSecretStoreArn)),errors.collect(cdk().propertyValidator("databaseName",cdk().validateString)(properties.databaseName)),errors.collect(cdk().propertyValidator("dbClusterIdentifier",cdk().requiredValidator)(properties.dbClusterIdentifier)),errors.collect(cdk().propertyValidator("dbClusterIdentifier",cdk().validateString)(properties.dbClusterIdentifier)),errors.collect(cdk().propertyValidator("schema",cdk().validateString)(properties.schema)),errors.wrap('supplied properties not correct for "RdsHttpEndpointConfigProperty"')}function convertCfnDataSourceRdsHttpEndpointConfigPropertyToCloudFormation(properties){return cdk().canInspect(properties)?(CfnDataSourceRdsHttpEndpointConfigPropertyValidator(properties).assertSuccess(),{AwsRegion:cdk().stringToCloudFormation(properties.awsRegion),AwsSecretStoreArn:cdk().stringToCloudFormation(properties.awsSecretStoreArn),DatabaseName:cdk().stringToCloudFormation(properties.databaseName),DbClusterIdentifier:cdk().stringToCloudFormation(properties.dbClusterIdentifier),Schema:cdk().stringToCloudFormation(properties.schema)}):properties}function CfnDataSourceRdsHttpEndpointConfigPropertyFromCloudFormation(properties){if(cdk().isResolvableObject(properties))return new(cfn_parse()).FromCloudFormationResult(properties);if(properties=properties??{},!(properties&&typeof properties=="object"&&!Array.isArray(properties)))return new(cfn_parse()).FromCloudFormationResult(properties);const ret=new(cfn_parse()).FromCloudFormationPropertyObject;return ret.addPropertyResult("awsRegion","AwsRegion",properties.AwsRegion!=null?cfn_parse().FromCloudFormation.getString(properties.AwsRegion):void 0),ret.addPropertyResult("awsSecretStoreArn","AwsSecretStoreArn",properties.AwsSecretStoreArn!=null?cfn_parse().FromCloudFormation.getString(properties.AwsSecretStoreArn):void 0),ret.addPropertyResult("databaseName","DatabaseName",properties.DatabaseName!=null?cfn_parse().FromCloudFormation.getString(properties.DatabaseName):void 0),ret.addPropertyResult("dbClusterIdentifier","DbClusterIdentifier",properties.DbClusterIdentifier!=null?cfn_parse().FromCloudFormation.getString(properties.DbClusterIdentifier):void 0),ret.addPropertyResult("schema","Schema",properties.Schema!=null?cfn_parse().FromCloudFormation.getString(properties.Schema):void 0),ret.addUnrecognizedPropertiesAsExtra(properties),ret}function CfnDataSourceRelationalDatabaseConfigPropertyValidator(properties){if(!cdk().canInspect(properties))return cdk().VALIDATION_SUCCESS;const errors=new(cdk()).ValidationResults;return properties&&typeof properties=="object"&&!Array.isArray(properties)||errors.collect(new(cdk()).ValidationResult("Expected an object, but received: "+JSON.stringify(properties))),errors.collect(cdk().propertyValidator("rdsHttpEndpointConfig",CfnDataSourceRdsHttpEndpointConfigPropertyValidator)(properties.rdsHttpEndpointConfig)),errors.collect(cdk().propertyValidator("relationalDatabaseSourceType",cdk().requiredValidator)(properties.relationalDatabaseSourceType)),errors.collect(cdk().propertyValidator("relationalDatabaseSourceType",cdk().validateString)(properties.relationalDatabaseSourceType)),errors.wrap('supplied properties not correct for "RelationalDatabaseConfigProperty"')}function convertCfnDataSourceRelationalDatabaseConfigPropertyToCloudFormation(properties){return cdk().canInspect(properties)?(CfnDataSourceRelationalDatabaseConfigPropertyValidator(properties).assertSuccess(),{RdsHttpEndpointConfig:convertCfnDataSourceRdsHttpEndpointConfigPropertyToCloudFormation(properties.rdsHttpEndpointConfig),RelationalDatabaseSourceType:cdk().stringToCloudFormation(properties.relationalDatabaseSourceType)}):properties}function CfnDataSourceRelationalDatabaseConfigPropertyFromCloudFormation(properties){if(cdk().isResolvableObject(properties))return new(cfn_parse()).FromCloudFormationResult(properties);if(properties=properties??{},!(properties&&typeof properties=="object"&&!Array.isArray(properties)))return new(cfn_parse()).FromCloudFormationResult(properties);const ret=new(cfn_parse()).FromCloudFormationPropertyObject;return ret.addPropertyResult("rdsHttpEndpointConfig","RdsHttpEndpointConfig",properties.RdsHttpEndpointConfig!=null?CfnDataSourceRdsHttpEndpointConfigPropertyFromCloudFormation(properties.RdsHttpEndpointConfig):void 0),ret.addPropertyResult("relationalDatabaseSourceType","RelationalDatabaseSourceType",properties.RelationalDatabaseSourceType!=null?cfn_parse().FromCloudFormation.getString(properties.RelationalDatabaseSourceType):void 0),ret.addUnrecognizedPropertiesAsExtra(properties),ret}function CfnDataSourceLambdaConfigPropertyValidator(properties){if(!cdk().canInspect(properties))return cdk().VALIDATION_SUCCESS;const errors=new(cdk()).ValidationResults;return properties&&typeof properties=="object"&&!Array.isArray(properties)||errors.collect(new(cdk()).ValidationResult("Expected an object, but received: "+JSON.stringify(properties))),errors.collect(cdk().propertyValidator("lambdaFunctionArn",cdk().requiredValidator)(properties.lambdaFunctionArn)),errors.collect(cdk().propertyValidator("lambdaFunctionArn",cdk().validateString)(properties.lambdaFunctionArn)),errors.wrap('supplied properties not correct for "LambdaConfigProperty"')}function convertCfnDataSourceLambdaConfigPropertyToCloudFormation(properties){return cdk().canInspect(properties)?(CfnDataSourceLambdaConfigPropertyValidator(properties).assertSuccess(),{LambdaFunctionArn:cdk().stringToCloudFormation(properties.lambdaFunctionArn)}):properties}function CfnDataSourceLambdaConfigPropertyFromCloudFormation(properties){if(cdk().isResolvableObject(properties))return new(cfn_parse()).FromCloudFormationResult(properties);if(properties=properties??{},!(properties&&typeof properties=="object"&&!Array.isArray(properties)))return new(cfn_parse()).FromCloudFormationResult(properties);const ret=new(cfn_parse()).FromCloudFormationPropertyObject;return ret.addPropertyResult("lambdaFunctionArn","LambdaFunctionArn",properties.LambdaFunctionArn!=null?cfn_parse().FromCloudFormation.getString(properties.LambdaFunctionArn):void 0),ret.addUnrecognizedPropertiesAsExtra(properties),ret}function CfnDataSourceDeltaSyncConfigPropertyValidator(properties){if(!cdk().canInspect(properties))return cdk().VALIDATION_SUCCESS;const errors=new(cdk()).ValidationResults;return properties&&typeof properties=="object"&&!Array.isArray(properties)||errors.collect(new(cdk()).ValidationResult("Expected an object, but received: "+JSON.stringify(properties))),errors.collect(cdk().propertyValidator("baseTableTtl",cdk().requiredValidator)(properties.baseTableTtl)),errors.collect(cdk().propertyValidator("baseTableTtl",cdk().validateString)(properties.baseTableTtl)),errors.collect(cdk().propertyValidator("deltaSyncTableName",cdk().requiredValidator)(properties.deltaSyncTableName)),errors.collect(cdk().propertyValidator("deltaSyncTableName",cdk().validateString)(properties.deltaSyncTableName)),errors.collect(cdk().propertyValidator("deltaSyncTableTtl",cdk().requiredValidator)(properties.deltaSyncTableTtl)),errors.collect(cdk().propertyValidator("deltaSyncTableTtl",cdk().validateString)(properties.deltaSyncTableTtl)),errors.wrap('supplied properties not correct for "DeltaSyncConfigProperty"')}function convertCfnDataSourceDeltaSyncConfigPropertyToCloudFormation(properties){return cdk().canInspect(properties)?(CfnDataSourceDeltaSyncConfigPropertyValidator(properties).assertSuccess(),{BaseTableTTL:cdk().stringToCloudFormation(properties.baseTableTtl),DeltaSyncTableName:cdk().stringToCloudFormation(properties.deltaSyncTableName),DeltaSyncTableTTL:cdk().stringToCloudFormation(properties.deltaSyncTableTtl)}):properties}function CfnDataSourceDeltaSyncConfigPropertyFromCloudFormation(properties){if(cdk().isResolvableObject(properties))return new(cfn_parse()).FromCloudFormationResult(properties);if(properties=properties??{},!(properties&&typeof properties=="object"&&!Array.isArray(properties)))return new(cfn_parse()).FromCloudFormationResult(properties);const ret=new(cfn_parse()).FromCloudFormationPropertyObject;return ret.addPropertyResult("baseTableTtl","BaseTableTTL",properties.BaseTableTTL!=null?cfn_parse().FromCloudFormation.getString(properties.BaseTableTTL):void 0),ret.addPropertyResult("deltaSyncTableName","DeltaSyncTableName",properties.DeltaSyncTableName!=null?cfn_parse().FromCloudFormation.getString(properties.DeltaSyncTableName):void 0),ret.addPropertyResult("deltaSyncTableTtl","DeltaSyncTableTTL",properties.DeltaSyncTableTTL!=null?cfn_parse().FromCloudFormation.getString(properties.DeltaSyncTableTTL):void 0),ret.addUnrecognizedPropertiesAsExtra(properties),ret}function CfnDataSourceDynamoDBConfigPropertyValidator(properties){if(!cdk().canInspect(properties))return cdk().VALIDATION_SUCCESS;const errors=new(cdk()).ValidationResults;return properties&&typeof properties=="object"&&!Array.isArray(properties)||errors.collect(new(cdk()).ValidationResult("Expected an object, but received: "+JSON.stringify(properties))),errors.collect(cdk().propertyValidator("awsRegion",cdk().requiredValidator)(properties.awsRegion)),errors.collect(cdk().propertyValidator("awsRegion",cdk().validateString)(properties.awsRegion)),errors.collect(cdk().propertyValidator("deltaSyncConfig",CfnDataSourceDeltaSyncConfigPropertyValidator)(properties.deltaSyncConfig)),errors.collect(cdk().propertyValidator("tableName",cdk().requiredValidator)(properties.tableName)),errors.collect(cdk().propertyValidator("tableName",cdk().validateString)(properties.tableName)),errors.collect(cdk().propertyValidator("useCallerCredentials",cdk().validateBoolean)(properties.useCallerCredentials)),errors.collect(cdk().propertyValidator("versioned",cdk().validateBoolean)(properties.versioned)),errors.wrap('supplied properties not correct for "DynamoDBConfigProperty"')}function convertCfnDataSourceDynamoDBConfigPropertyToCloudFormation(properties){return cdk().canInspect(properties)?(CfnDataSourceDynamoDBConfigPropertyValidator(properties).assertSuccess(),{AwsRegion:cdk().stringToCloudFormation(properties.awsRegion),DeltaSyncConfig:convertCfnDataSourceDeltaSyncConfigPropertyToCloudFormation(properties.deltaSyncConfig),TableName:cdk().stringToCloudFormation(properties.tableName),UseCallerCredentials:cdk().booleanToCloudFormation(properties.useCallerCredentials),Versioned:cdk().booleanToCloudFormation(properties.versioned)}):properties}function CfnDataSourceDynamoDBConfigPropertyFromCloudFormation(properties){if(cdk().isResolvableObject(properties))return new(cfn_parse()).FromCloudFormationResult(properties);if(properties=properties??{},!(properties&&typeof properties=="object"&&!Array.isArray(properties)))return new(cfn_parse()).FromCloudFormationResult(properties);const ret=new(cfn_parse()).FromCloudFormationPropertyObject;return ret.addPropertyResult("awsRegion","AwsRegion",properties.AwsRegion!=null?cfn_parse().FromCloudFormation.getString(properties.AwsRegion):void 0),ret.addPropertyResult("deltaSyncConfig","DeltaSyncConfig",properties.DeltaSyncConfig!=null?CfnDataSourceDeltaSyncConfigPropertyFromCloudFormation(properties.DeltaSyncConfig):void 0),ret.addPropertyResult("tableName","TableName",properties.TableName!=null?cfn_parse().FromCloudFormation.getString(properties.TableName):void 0),ret.addPropertyResult("useCallerCredentials","UseCallerCredentials",properties.UseCallerCredentials!=null?cfn_parse().FromCloudFormation.getBoolean(properties.UseCallerCredentials):void 0),ret.addPropertyResult("versioned","Versioned",properties.Versioned!=null?cfn_parse().FromCloudFormation.getBoolean(properties.Versioned):void 0),ret.addUnrecognizedPropertiesAsExtra(properties),ret}function CfnDataSourceElasticsearchConfigPropertyValidator(properties){if(!cdk().canInspect(properties))return cdk().VALIDATION_SUCCESS;const errors=new(cdk()).ValidationResults;return properties&&typeof properties=="object"&&!Array.isArray(properties)||errors.collect(new(cdk()).ValidationResult("Expected an object, but received: "+JSON.stringify(properties))),errors.collect(cdk().propertyValidator("awsRegion",cdk().requiredValidator)(properties.awsRegion)),errors.collect(cdk().propertyValidator("awsRegion",cdk().validateString)(properties.awsRegion)),errors.collect(cdk().propertyValidator("endpoint",cdk().requiredValidator)(properties.endpoint)),errors.collect(cdk().propertyValidator("endpoint",cdk().validateString)(properties.endpoint)),errors.wrap('supplied properties not correct for "ElasticsearchConfigProperty"')}function convertCfnDataSourceElasticsearchConfigPropertyToCloudFormation(properties){return cdk().canInspect(properties)?(CfnDataSourceElasticsearchConfigPropertyValidator(properties).assertSuccess(),{AwsRegion:cdk().stringToCloudFormation(properties.awsRegion),Endpoint:cdk().stringToCloudFormation(properties.endpoint)}):properties}function CfnDataSourceElasticsearchConfigPropertyFromCloudFormation(properties){if(cdk().isResolvableObject(properties))return new(cfn_parse()).FromCloudFormationResult(properties);if(properties=properties??{},!(properties&&typeof properties=="object"&&!Array.isArray(properties)))return new(cfn_parse()).FromCloudFormationResult(properties);const ret=new(cfn_parse()).FromCloudFormationPropertyObject;return ret.addPropertyResult("awsRegion","AwsRegion",properties.AwsRegion!=null?cfn_parse().FromCloudFormation.getString(properties.AwsRegion):void 0),ret.addPropertyResult("endpoint","Endpoint",properties.Endpoint!=null?cfn_parse().FromCloudFormation.getString(properties.Endpoint):void 0),ret.addUnrecognizedPropertiesAsExtra(properties),ret}function CfnDataSourcePropsValidator(properties){if(!cdk().canInspect(properties))return cdk().VALIDATION_SUCCESS;const errors=new(cdk()).ValidationResults;return properties&&typeof properties=="object"&&!Array.isArray(properties)||errors.collect(new(cdk()).ValidationResult("Expected an object, but received: "+JSON.stringify(properties))),errors.collect(cdk().propertyValidator("apiId",cdk().requiredValidator)(properties.apiId)),errors.collect(cdk().propertyValidator("apiId",cdk().validateString)(properties.apiId)),errors.collect(cdk().propertyValidator("description",cdk().validateString)(properties.description)),errors.collect(cdk().propertyValidator("dynamoDbConfig",CfnDataSourceDynamoDBConfigPropertyValidator)(properties.dynamoDbConfig)),errors.collect(cdk().propertyValidator("elasticsearchConfig",CfnDataSourceElasticsearchConfigPropertyValidator)(properties.elasticsearchConfig)),errors.collect(cdk().propertyValidator("eventBridgeConfig",CfnDataSourceEventBridgeConfigPropertyValidator)(properties.eventBridgeConfig)),errors.collect(cdk().propertyValidator("httpConfig",CfnDataSourceHttpConfigPropertyValidator)(properties.httpConfig)),errors.collect(cdk().propertyValidator("lambdaConfig",CfnDataSourceLambdaConfigPropertyValidator)(properties.lambdaConfig)),errors.collect(cdk().propertyValidator("metricsConfig",cdk().validateString)(properties.metricsConfig)),errors.collect(cdk().propertyValidator("name",cdk().requiredValidator)(properties.name)),errors.collect(cdk().propertyValidator("name",cdk().validateString)(properties.name)),errors.collect(cdk().propertyValidator("openSearchServiceConfig",CfnDataSourceOpenSearchServiceConfigPropertyValidator)(properties.openSearchServiceConfig)),errors.collect(cdk().propertyValidator("relationalDatabaseConfig",CfnDataSourceRelationalDatabaseConfigPropertyValidator)(properties.relationalDatabaseConfig)),errors.collect(cdk().propertyValidator("serviceRoleArn",cdk().validateString)(properties.serviceRoleArn)),errors.collect(cdk().propertyValidator("type",cdk().requiredValidator)(properties.type)),errors.collect(cdk().propertyValidator("type",cdk().validateString)(properties.type)),errors.wrap('supplied properties not correct for "CfnDataSourceProps"')}function convertCfnDataSourcePropsToCloudFormation(properties){return cdk().canInspect(properties)?(CfnDataSourcePropsValidator(properties).assertSuccess(),{ApiId:cdk().stringToCloudFormation(properties.apiId),Description:cdk().stringToCloudFormation(properties.description),DynamoDBConfig:convertCfnDataSourceDynamoDBConfigPropertyToCloudFormation(properties.dynamoDbConfig),ElasticsearchConfig:convertCfnDataSourceElasticsearchConfigPropertyToCloudFormation(properties.elasticsearchConfig),EventBridgeConfig:convertCfnDataSourceEventBridgeConfigPropertyToCloudFormation(properties.eventBridgeConfig),HttpConfig:convertCfnDataSourceHttpConfigPropertyToCloudFormation(properties.httpConfig),LambdaConfig:convertCfnDataSourceLambdaConfigPropertyToCloudFormation(properties.lambdaConfig),MetricsConfig:cdk().stringToCloudFormation(properties.metricsConfig),Name:cdk().stringToCloudFormation(properties.name),OpenSearchServiceConfig:convertCfnDataSourceOpenSearchServiceConfigPropertyToCloudFormation(properties.openSearchServiceConfig),RelationalDatabaseConfig:convertCfnDataSourceRelationalDatabaseConfigPropertyToCloudFormation(properties.relationalDatabaseConfig),ServiceRoleArn:cdk().stringToCloudFormation(properties.serviceRoleArn),Type:cdk().stringToCloudFormation(properties.type)}):properties}function CfnDataSourcePropsFromCloudFormation(properties){if(cdk().isResolvableObject(properties))return new(cfn_parse()).FromCloudFormationResult(properties);if(properties=properties??{},!(properties&&typeof properties=="object"&&!Array.isArray(properties)))return new(cfn_parse()).FromCloudFormationResult(properties);const ret=new(cfn_parse()).FromCloudFormationPropertyObject;return ret.addPropertyResult("apiId","ApiId",properties.ApiId!=null?cfn_parse().FromCloudFormation.getString(properties.ApiId):void 0),ret.addPropertyResult("description","Description",properties.Description!=null?cfn_parse().FromCloudFormation.getString(properties.Description):void 0),ret.addPropertyResult("dynamoDbConfig","DynamoDBConfig",properties.DynamoDBConfig!=null?CfnDataSourceDynamoDBConfigPropertyFromCloudFormation(properties.DynamoDBConfig):void 0),ret.addPropertyResult("elasticsearchConfig","ElasticsearchConfig",properties.ElasticsearchConfig!=null?CfnDataSourceElasticsearchConfigPropertyFromCloudFormation(properties.ElasticsearchConfig):void 0),ret.addPropertyResult("eventBridgeConfig","EventBridgeConfig",properties.EventBridgeConfig!=null?CfnDataSourceEventBridgeConfigPropertyFromCloudFormation(properties.EventBridgeConfig):void 0),ret.addPropertyResult("httpConfig","HttpConfig",properties.HttpConfig!=null?CfnDataSourceHttpConfigPropertyFromCloudFormation(properties.HttpConfig):void 0),ret.addPropertyResult("lambdaConfig","LambdaConfig",properties.LambdaConfig!=null?CfnDataSourceLambdaConfigPropertyFromCloudFormation(properties.LambdaConfig):void 0),ret.addPropertyResult("metricsConfig","MetricsConfig",properties.MetricsConfig!=null?cfn_parse().FromCloudFormation.getString(properties.MetricsConfig):void 0),ret.addPropertyResult("name","Name",properties.Name!=null?cfn_parse().FromCloudFormation.getString(properties.Name):void 0),ret.addPropertyResult("openSearchServiceConfig","OpenSearchServiceConfig",properties.OpenSearchServiceConfig!=null?CfnDataSourceOpenSearchServiceConfigPropertyFromCloudFormation(properties.OpenSearchServiceConfig):void 0),ret.addPropertyResult("relationalDatabaseConfig","RelationalDatabaseConfig",properties.RelationalDatabaseConfig!=null?CfnDataSourceRelationalDatabaseConfigPropertyFromCloudFormation(properties.RelationalDatabaseConfig):void 0),ret.addPropertyResult("serviceRoleArn","ServiceRoleArn",properties.ServiceRoleArn!=null?cfn_parse().FromCloudFormation.getString(properties.ServiceRoleArn):void 0),ret.addPropertyResult("type","Type",properties.Type!=null?cfn_parse().FromCloudFormation.getString(properties.Type):void 0),ret.addUnrecognizedPropertiesAsExtra(properties),ret}class CfnDomainName extends cdk().CfnResource{static _fromCloudFormation(scope,id,resourceAttributes,options){resourceAttributes=resourceAttributes||{};const resourceProperties=options.parser.parseValue(resourceAttributes.Properties),propsResult=CfnDomainNamePropsFromCloudFormation(resourceProperties);if(cdk().isResolvableObject(propsResult.value))throw new(cdk_errors()).ValidationError("Unexpected IResolvable",scope);const ret=new CfnDomainName(scope,id,propsResult.value);for(const[propKey,propVal]of Object.entries(propsResult.extraProperties))ret.addPropertyOverride(propKey,propVal);return options.parser.handleAttributes(ret,resourceAttributes,id),ret}constructor(scope,id,props){super(scope,id,{type:CfnDomainName.CFN_RESOURCE_TYPE_NAME,properties:props});try{jsiiDeprecationWarnings().aws_cdk_lib_aws_appsync_CfnDomainNameProps(props)}catch(error){throw process.env.JSII_DEBUG!=="1"&&error.name==="DeprecationError"&&Error.captureStackTrace(error,CfnDomainName),error}cdk().requireProperty(props,"certificateArn",this),cdk().requireProperty(props,"domainName",this),this.attrAppSyncDomainName=cdk().Token.asString(this.getAtt("AppSyncDomainName",cdk().ResolutionTypeHint.STRING)),this.attrDomainName=cdk().Token.asString(this.getAtt("DomainName",cdk().ResolutionTypeHint.STRING)),this.attrDomainNameArn=cdk().Token.asString(this.getAtt("DomainNameArn",cdk().ResolutionTypeHint.STRING)),this.attrHostedZoneId=cdk().Token.asString(this.getAtt("HostedZoneId",cdk().ResolutionTypeHint.STRING)),this.cdkTagManager=new(cdk()).TagManager(cdk().TagType.STANDARD,"AWS::AppSync::DomainName",void 0,{tagPropertyName:"tags"}),this.certificateArn=props.certificateArn,this.description=props.description,this.domainName=props.domainName,this.tags=props.tags}get cfnProperties(){return{tags:this.cdkTagManager.renderTags(this.tags),certificateArn:this.certificateArn,description:this.description,domainName:this.domainName}}inspect(inspector){try{jsiiDeprecationWarnings().aws_cdk_lib_TreeInspector(inspector)}catch(error){throw process.env.JSII_DEBUG!=="1"&&error.name==="DeprecationError"&&Error.captureStackTrace(error,this.inspect),error}inspector.addAttribute("aws:cdk:cloudformation:type",CfnDomainName.CFN_RESOURCE_TYPE_NAME),inspector.addAttribute("aws:cdk:cloudformation:props",this.cfnProperties)}renderProperties(props){return convertCfnDomainNamePropsToCloudFormation(props)}}exports.CfnDomainName=CfnDomainName,_d=JSII_RTTI_SYMBOL_1,CfnDomainName[_d]={fqn:"aws-cdk-lib.aws_appsync.CfnDomainName",version:"2.201.0"},CfnDomainName.CFN_RESOURCE_TYPE_NAME="AWS::AppSync::DomainName";function CfnDomainNamePropsValidator(properties){if(!cdk().canInspect(properties))return cdk().VALIDATION_SUCCESS;const errors=new(cdk()).ValidationResults;return properties&&typeof properties=="object"&&!Array.isArray(properties)||errors.collect(new(cdk()).ValidationResult("Expected an object, but received: "+JSON.stringify(properties))),errors.collect(cdk().propertyValidator("certificateArn",cdk().requiredValidator)(properties.certificateArn)),errors.collect(cdk().propertyValidator("certificateArn",cdk().validateString)(properties.certificateArn)),errors.collect(cdk().propertyValidator("description",cdk().validateString)(properties.description)),errors.collect(cdk().propertyValidator("domainName",cdk().requiredValidator)(properties.domainName)),errors.collect(cdk().propertyValidator("domainName",cdk().validateString)(properties.domainName)),errors.collect(cdk().propertyValidator("tags",cdk().listValidator(cdk().validateCfnTag))(properties.tags)),errors.wrap('supplied properties not correct for "CfnDomainNameProps"')}function convertCfnDomainNamePropsToCloudFormation(properties){return cdk().canInspect(properties)?(CfnDomainNamePropsValidator(properties).assertSuccess(),{CertificateArn:cdk().stringToCloudFormation(properties.certificateArn),Description:cdk().stringToCloudFormation(properties.description),DomainName:cdk().stringToCloudFormation(properties.domainName),Tags:cdk().listMapper(cdk().cfnTagToCloudFormation)(properties.tags)}):properties}function CfnDomainNamePropsFromCloudFormation(properties){if(cdk().isResolvableObject(properties))return new(cfn_parse()).FromCloudFormationResult(properties);if(properties=properties??{},!(properties&&typeof properties=="object"&&!Array.isArray(properties)))return new(cfn_parse()).FromCloudFormationResult(properties);const ret=new(cfn_parse()).FromCloudFormationPropertyObject;return ret.addPropertyResult("certificateArn","CertificateArn",properties.CertificateArn!=null?cfn_parse().FromCloudFormation.getString(properties.CertificateArn):void 0),ret.addPropertyResult("description","Description",properties.Description!=null?cfn_parse().FromCloudFormation.getString(properties.Description):void 0),ret.addPropertyResult("domainName","DomainName",properties.DomainName!=null?cfn_parse().FromCloudFormation.getString(properties.DomainName):void 0),ret.addPropertyResult("tags","Tags",properties.Tags!=null?cfn_parse().FromCloudFormation.getArray(cfn_parse().FromCloudFormation.getCfnTag)(properties.Tags):void 0),ret.addUnrecognizedPropertiesAsExtra(properties),ret}class CfnDomainNameApiAssociation extends cdk().CfnResource{static _fromCloudFormation(scope,id,resourceAttributes,options){resourceAttributes=resourceAttributes||{};const resourceProperties=options.parser.parseValue(resourceAttributes.Properties),propsResult=CfnDomainNameApiAssociationPropsFromCloudFormation(resourceProperties);if(cdk().isResolvableObject(propsResult.value))throw new(cdk_errors()).ValidationError("Unexpected IResolvable",scope);const ret=new CfnDomainNameApiAssociation(scope,id,propsResult.value);for(const[propKey,propVal]of Object.entries(propsResult.extraProperties))ret.addPropertyOverride(propKey,propVal);return options.parser.handleAttributes(ret,resourceAttributes,id),ret}constructor(scope,id,props){super(scope,id,{type:CfnDomainNameApiAssociation.CFN_RESOURCE_TYPE_NAME,properties:props});try{jsiiDeprecationWarnings().aws_cdk_lib_aws_appsync_CfnDomainNameApiAssociationProps(props)}catch(error){throw process.env.JSII_DEBUG!=="1"&&error.name==="DeprecationError"&&Error.captureStackTrace(error,CfnDomainNameApiAssociation),error}cdk().requireProperty(props,"apiId",this),cdk().requireProperty(props,"domainName",this),this.attrApiAssociationIdentifier=cdk().Token.asString(this.getAtt("ApiAssociationIdentifier",cdk().ResolutionTypeHint.STRING)),this.apiId=props.apiId,this.domainName=props.domainName}get cfnProperties(){return{apiId:this.apiId,domainName:this.domainName}}inspect(inspector){try{jsiiDeprecationWarnings().aws_cdk_lib_TreeInspector(inspector)}catch(error){throw process.env.JSII_DEBUG!=="1"&&error.name==="DeprecationError"&&Error.captureStackTrace(error,this.inspect),error}inspector.addAttribute("aws:cdk:cloudformation:type",CfnDomainNameApiAssociation.CFN_RESOURCE_TYPE_NAME),inspector.addAttribute("aws:cdk:cloudformation:props",this.cfnProperties)}renderProperties(props){return convertCfnDomainNameApiAssociationPropsToCloudFormation(props)}}exports.CfnDomainNameApiAssociation=CfnDomainNameApiAssociation,_e=JSII_RTTI_SYMBOL_1,CfnDomainNameApiAssociation[_e]={fqn:"aws-cdk-lib.aws_appsync.CfnDomainNameApiAssociation",version:"2.201.0"},CfnDomainNameApiAssociation.CFN_RESOURCE_TYPE_NAME="AWS::AppSync::DomainNameApiAssociation";function CfnDomainNameApiAssociationPropsValidator(properties){if(!cdk().canInspect(properties))return cdk().VALIDATION_SUCCESS;const errors=new(cdk()).ValidationResults;return properties&&typeof properties=="object"&&!Array.isArray(properties)||errors.collect(new(cdk()).ValidationResult("Expected an object, but received: "+JSON.stringify(properties))),errors.collect(cdk().propertyValidator("apiId",cdk().requiredValidator)(properties.apiId)),errors.collect(cdk().propertyValidator("apiId",cdk().validateString)(properties.apiId)),errors.collect(cdk().propertyValidator("domainName",cdk().requiredValidator)(properties.domainName)),errors.collect(cdk().propertyValidator("domainName",cdk().validateString)(properties.domainName)),errors.wrap('supplied properties not correct for "CfnDomainNameApiAssociationProps"')}function convertCfnDomainNameApiAssociationPropsToCloudFormation(properties){return cdk().canInspect(properties)?(CfnDomainNameApiAssociationPropsValidator(properties).assertSuccess(),{ApiId:cdk().stringToCloudFormation(properties.apiId),DomainName:cdk().stringToCloudFormation(properties.domainName)}):properties}function CfnDomainNameApiAssociationPropsFromCloudFormation(properties){if(cdk().isResolvableObject(properties))return new(cfn_parse()).FromCloudFormationResult(properties);if(properties=properties??{},!(properties&&typeof properties=="object"&&!Array.isArray(properties)))return new(cfn_parse()).FromCloudFormationResult(properties);const ret=new(cfn_parse()).FromCloudFormationPropertyObject;return ret.addPropertyResult("apiId","ApiId",properties.ApiId!=null?cfn_parse().FromCloudFormation.getString(properties.ApiId):void 0),ret.addPropertyResult("domainName","DomainName",properties.DomainName!=null?cfn_parse().FromCloudFormation.getString(properties.DomainName):void 0),ret.addUnrecognizedPropertiesAsExtra(properties),ret}class CfnFunctionConfiguration extends cdk().CfnResource{static _fromCloudFormation(scope,id,resourceAttributes,options){resourceAttributes=resourceAttributes||{};const resourceProperties=options.parser.parseValue(resourceAttributes.Properties),propsResult=CfnFunctionConfigurationPropsFromCloudFormation(resourceProperties);if(cdk().isResolvableObject(propsResult.value))throw new(cdk_errors()).ValidationError("Unexpected IResolvable",scope);const ret=new CfnFunctionConfiguration(scope,id,propsResult.value);for(const[propKey,propVal]of Object.entries(propsResult.extraProperties))ret.addPropertyOverride(propKey,propVal);return options.parser.handleAttributes(ret,resourceAttributes,id),ret}constructor(scope,id,props){super(scope,id,{type:CfnFunctionConfiguration.CFN_RESOURCE_TYPE_NAME,properties:props});try{jsiiDeprecationWarnings().aws_cdk_lib_aws_appsync_CfnFunctionConfigurationProps(props)}catch(error){throw process.env.JSII_DEBUG!=="1"&&error.name==="DeprecationError"&&Error.captureStackTrace(error,CfnFunctionConfiguration),error}cdk().requireProperty(props,"apiId",this),cdk().requireProperty(props,"dataSourceName",this),cdk().requireProperty(props,"name",this),this.attrDataSourceName=cdk().Token.asString(this.getAtt("DataSourceName",cdk().ResolutionTypeHint.STRING)),this.attrFunctionArn=cdk().Token.asString(this.getAtt("FunctionArn",cdk().ResolutionTypeHint.STRING)),this.attrFunctionId=cdk().Token.asString(this.getAtt("FunctionId",cdk().ResolutionTypeHint.STRING)),this.attrName=cdk().Token.asString(this.getAtt("Name",cdk().ResolutionTypeHint.STRING)),this.apiId=props.apiId,this.code=props.code,this.codeS3Location=props.codeS3Location,this.dataSourceName=props.dataSourceName,this.description=props.description,this.functionVersion=props.functionVersion,this.maxBatchSize=props.maxBatchSize,this.name=props.name,this.requestMappingTemplate=props.requestMappingTemplate,this.requestMappingTemplateS3Location=props.requestMappingTemplateS3Location,this.responseMappingTemplate=props.responseMappingTemplate,this.responseMappingTemplateS3Location=props.responseMappingTemplateS3Location,this.runtime=props.runtime,this.syncConfig=props.syncConfig}get cfnProperties(){return{apiId:this.apiId,code:this.code,codeS3Location:this.codeS3Location,dataSourceName:this.dataSourceName,description:this.description,functionVersion:this.functionVersion,maxBatchSize:this.maxBatchSize,name:this.name,requestMappingTemplate:this.requestMappingTemplate,requestMappingTemplateS3Location:this.requestMappingTemplateS3Location,responseMappingTemplate:this.responseMappingTemplate,responseMappingTemplateS3Location:this.responseMappingTemplateS3Location,runtime:this.runtime,syncConfig:this.syncConfig}}inspect(inspector){try{jsiiDeprecationWarnings().aws_cdk_lib_TreeInspector(inspector)}catch(error){throw process.env.JSII_DEBUG!=="1"&&error.name==="DeprecationError"&&Error.captureStackTrace(error,this.inspect),error}inspector.addAttribute("aws:cdk:cloudformation:type",CfnFunctionConfiguration.CFN_RESOURCE_TYPE_NAME),inspector.addAttribute("aws:cdk:cloudformation:props",this.cfnProperties)}renderProperties(props){return convertCfnFunctionConfigurationPropsToCloudFormation(props)}}exports.CfnFunctionConfiguration=CfnFunctionConfiguration,_f=JSII_RTTI_SYMBOL_1,CfnFunctionConfiguration[_f]={fqn:"aws-cdk-lib.aws_appsync.CfnFunctionConfiguration",version:"2.201.0"},CfnFunctionConfiguration.CFN_RESOURCE_TYPE_NAME="AWS::AppSync::FunctionConfiguration";function CfnFunctionConfigurationLambdaConflictHandlerConfigPropertyValidator(properties){if(!cdk().canInspect(properties))return cdk().VALIDATION_SUCCESS;const errors=new(cdk()).ValidationResults;return properties&&typeof properties=="object"&&!Array.isArray(properties)||errors.collect(new(cdk()).ValidationResult("Expected an object, but received: "+JSON.stringify(properties))),errors.collect(cdk().propertyValidator("lambdaConflictHandlerArn",cdk().validateString)(properties.lambdaConflictHandlerArn)),errors.wrap('supplied properties not correct for "LambdaConflictHandlerConfigProperty"')}function convertCfnFunctionConfigurationLambdaConflictHandlerConfigPropertyToCloudFormation(properties){return cdk().canInspect(properties)?(CfnFunctionConfigurationLambdaConflictHandlerConfigPropertyValidator(properties).assertSuccess(),{LambdaConflictHandlerArn:cdk().stringToCloudFormation(properties.lambdaConflictHandlerArn)}):properties}function CfnFunctionConfigurationLambdaConflictHandlerConfigPropertyFromCloudFormation(properties){if(cdk().isResolvableObject(properties))return new(cfn_parse()).FromCloudFormationResult(properties);if(properties=properties??{},!(properties&&typeof properties=="object"&&!Array.isArray(properties)))return new(cfn_parse()).FromCloudFormationResult(properties);const ret=new(cfn_parse()).FromCloudFormationPropertyObject;return ret.addPropertyResult("lambdaConflictHandlerArn","LambdaConflictHandlerArn",properties.LambdaConflictHandlerArn!=null?cfn_parse().FromCloudFormation.getString(properties.LambdaConflictHandlerArn):void 0),ret.addUnrecognizedPropertiesAsExtra(properties),ret}function CfnFunctionConfigurationSyncConfigPropertyValidator(properties){if(!cdk().canInspect(properties))return cdk().VALIDATION_SUCCESS;const errors=new(cdk()).ValidationResults;return properties&&typeof properties=="object"&&!Array.isArray(properties)||errors.collect(new(cdk()).ValidationResult("Expected an object, but received: "+JSON.stringify(properties))),errors.collect(cdk().propertyValidator("conflictDetection",cdk().requiredValidator)(properties.conflictDetection)),errors.collect(cdk().propertyValidator("conflictDetection",cdk().validateString)(properties.conflictDetection)),errors.collect(cdk().propertyValidator("conflictHandler",cdk().validateString)(properties.conflictHandler)),errors.collect(cdk().propertyValidator("lambdaConflictHandlerConfig",CfnFunctionConfigurationLambdaConflictHandlerConfigPropertyValidator)(properties.lambdaConflictHandlerConfig)),errors.wrap('supplied properties not correct for "SyncConfigProperty"')}function convertCfnFunctionConfigurationSyncConfigPropertyToCloudFormation(properties){return cdk().canInspect(properties)?(CfnFunctionConfigurationSyncConfigPropertyValidator(properties).assertSuccess(),{ConflictDetection:cdk().stringToCloudFormation(properties.conflictDetection),ConflictHandler:cdk().stringToCloudFormation(properties.conflictHandler),LambdaConflictHandlerConfig:convertCfnFunctionConfigurationLambdaConflictHandlerConfigPropertyToCloudFormation(properties.lambdaConflictHandlerConfig)}):properties}function CfnFunctionConfigurationSyncConfigPropertyFromCloudFormation(properties){if(cdk().isResolvableObject(properties))return new(cfn_parse()).FromCloudFormationResult(properties);if(properties=properties??{},!(properties&&typeof properties=="object"&&!Array.isArray(properties)))return new(cfn_parse()).FromCloudFormationResult(properties);const ret=new(cfn_parse()).FromCloudFormationPropertyObject;return ret.addPropertyResult("conflictDetection","ConflictDetection",properties.ConflictDetection!=null?cfn_parse().FromCloudFormation.getString(properties.ConflictDetection):void 0),ret.addPropertyResult("conflictHandler","ConflictHandler",properties.ConflictHandler!=null?cfn_parse().FromCloudFormation.getString(properties.ConflictHandler):void 0),ret.addPropertyResult("lambdaConflictHandlerConfig","LambdaConflictHandlerConfig",properties.LambdaConflictHandlerConfig!=null?CfnFunctionConfigurationLambdaConflictHandlerConfigPropertyFromCloudFormation(properties.LambdaConflictHandlerConfig):void 0),ret.addUnrecognizedPropertiesAsExtra(properties),ret}function CfnFunctionConfigurationAppSyncRuntimePropertyValidator(properties){if(!cdk().canInspect(properties))return cdk().VALIDATION_SUCCESS;const errors=new(cdk()).ValidationResults;return properties&&typeof properties=="object"&&!Array.isArray(properties)||errors.collect(new(cdk()).ValidationResult("Expected an object, but received: "+JSON.stringify(properties))),errors.collect(cdk().propertyValidator("name",cdk().requiredValidator)(properties.name)),errors.collect(cdk().propertyValidator("name",cdk().validateString)(properties.name)),errors.collect(cdk().propertyValidator("runtimeVersion",cdk().requiredValidator)(properties.runtimeVersion)),errors.collect(cdk().propertyValidator("runtimeVersion",cdk().validateString)(properties.runtimeVersion)),errors.wrap('supplied properties not correct for "AppSyncRuntimeProperty"')}function convertCfnFunctionConfigurationAppSyncRuntimePropertyToCloudFormation(properties){return cdk().canInspect(properties)?(CfnFunctionConfigurationAppSyncRuntimePropertyValidator(properties).assertSuccess(),{Name:cdk().stringToCloudFormation(properties.name),RuntimeVersion:cdk().stringToCloudFormation(properties.runtimeVersion)}):properties}function CfnFunctionConfigurationAppSyncRuntimePropertyFromCloudFormation(properties){if(cdk().isResolvableObject(properties))return new(cfn_parse()).FromCloudFormationResult(properties);if(properties=properties??{},!(properties&&typeof properties=="object"&&!Array.isArray(properties)))return new(cfn_parse()).FromCloudFormationResult(properties);const ret=new(cfn_parse()).FromCloudFormationPropertyObject;return ret.addPropertyResult("name","Name",properties.Name!=null?cfn_parse().FromCloudFormation.getString(properties.Name):void 0),ret.addPropertyResult("runtimeVersion","RuntimeVersion",properties.RuntimeVersion!=null?cfn_parse().FromCloudFormation.getString(properties.RuntimeVersion):void 0),ret.addUnrecognizedPropertiesAsExtra(properties),ret}function CfnFunctionConfigurationPropsValidator(properties){if(!cdk().canInspect(properties))return cdk().VALIDATION_SUCCESS;const errors=new(cdk()).ValidationResults;return properties&&typeof properties=="object"&&!Array.isArray(properties)||errors.collect(new(cdk()).ValidationResult("Expected an object, but received: "+JSON.stringify(properties))),errors.collect(cdk().propertyValidator("apiId",cdk().requiredValidator)(properties.apiId)),errors.collect(cdk().propertyValidator("apiId",cdk().validateString)(properties.apiId)),errors.collect(cdk().propertyValidator("code",cdk().validateString)(properties.code)),errors.collect(cdk().propertyValidator("codeS3Location",cdk().validateString)(properties.codeS3Location)),errors.collect(cdk().propertyValidator("dataSourceName",cdk().requiredValidator)(properties.dataSourceName)),errors.collect(cdk().propertyValidator("dataSourceName",cdk().validateString)(properties.dataSourceName)),errors.collect(cdk().propertyValidator("description",cdk().validateString)(properties.description)),errors.collect(cdk().propertyValidator("functionVersion",cdk().validateString)(properties.functionVersion)),errors.collect(cdk().propertyValidator("maxBatchSize",cdk().validateNumber)(properties.maxBatchSize)),errors.collect(cdk().propertyValidator("name",cdk().requiredValidator)(properties.name)),errors.collect(cdk().propertyValidator("name",cdk().validateString)(properties.name)),errors.collect(cdk().propertyValidator("requestMappingTemplate",cdk().validateString)(properties.requestMappingTemplate)),errors.collect(cdk().propertyValidator("requestMappingTemplateS3Location",cdk().validateString)(properties.requestMappingTemplateS3Location)),errors.collect(cdk().propertyValidator("responseMappingTemplate",cdk().validateString)(properties.responseMappingTemplate)),errors.collect(cdk().propertyValidator("responseMappingTemplateS3Location",cdk().validateString)(properties.responseMappingTemplateS3Location)),errors.collect(cdk().propertyValidator("runtime",CfnFunctionConfigurationAppSyncRuntimePropertyValidator)(properties.runtime)),errors.collect(cdk().propertyValidator("syncConfig",CfnFunctionConfigurationSyncConfigPropertyValidator)(properties.syncConfig)),errors.wrap('supplied properties not correct for "CfnFunctionConfigurationProps"')}function convertCfnFunctionConfigurationPropsToCloudFormation(properties){return cdk().canInspect(properties)?(CfnFunctionConfigurationPropsValidator(properties).assertSuccess(),{ApiId:cdk().stringToCloudFormation(properties.apiId),Code:cdk().stringToCloudFormation(properties.code),CodeS3Location:cdk().stringToCloudFormation(properties.codeS3Location),DataSourceName:cdk().stringToCloudFormation(properties.dataSourceName),Description:cdk().stringToCloudFormation(properties.description),FunctionVersion:cdk().stringToCloudFormation(properties.functionVersion),MaxBatchSize:cdk().numberToCloudFormation(properties.maxBatchSize),Name:cdk().stringToCloudFormation(properties.name),RequestMappingTemplate:cdk().stringToCloudFormation(properties.requestMappingTemplate),RequestMappingTemplateS3Location:cdk().stringToCloudFormation(properties.requestMappingTemplateS3Location),ResponseMappingTemplate:cdk().stringToCloudFormation(properties.responseMappingTemplate),ResponseMappingTemplateS3Location:cdk().stringToCloudFormation(properties.responseMappingTemplateS3Location),Runtime:convertCfnFunctionConfigurationAppSyncRuntimePropertyToCloudFormation(properties.runtime),SyncConfig:convertCfnFunctionConfigurationSyncConfigPropertyToCloudFormation(properties.syncConfig)}):properties}function CfnFunctionConfigurationPropsFromCloudFormation(properties){if(cdk().isResolvableObject(properties))return new(cfn_parse()).FromCloudFormationResult(properties);if(properties=properties??{},!(properties&&typeof properties=="object"&&!Array.isArray(properties)))return new(cfn_parse()).FromCloudFormationResult(properties);const ret=new(cfn_parse()).FromCloudFormationPropertyObject;return ret.addPropertyResult("apiId","ApiId",properties.ApiId!=null?cfn_parse().FromCloudFormation.getString(properties.ApiId):void 0),ret.addPropertyResult("code","Code",properties.Code!=null?cfn_parse().FromCloudFormation.getString(properties.Code):void 0),ret.addPropertyResult("codeS3Location","CodeS3Location",properties.CodeS3Location!=null?cfn_parse().FromCloudFormation.getString(properties.CodeS3Location):void 0),ret.addPropertyResult("dataSourceName","DataSourceName",properties.DataSourceName!=null?cfn_parse().FromCloudFormation.getString(properties.DataSourceName):void 0),ret.addPropertyResult("description","Description",properties.Description!=null?cfn_parse().FromCloudFormation.getString(properties.Description):void 0),ret.addPropertyResult("functionVersion","FunctionVersion",properties.FunctionVersion!=null?cfn_parse().FromCloudFormation.getString(properties.FunctionVersion):void 0),ret.addPropertyResult("maxBatchSize","MaxBatchSize",properties.MaxBatchSize!=null?cfn_parse().FromCloudFormation.getNumber(properties.MaxBatchSize):void 0),ret.addPropertyResult("name","Name",properties.Name!=null?cfn_parse().FromCloudFormation.getString(properties.Name):void 0),ret.addPropertyResult("requestMappingTemplate","RequestMappingTemplate",properties.RequestMappingTemplate!=null?cfn_parse().FromCloudFormation.getString(properties.RequestMappingTemplate):void 0),ret.addPropertyResult("requestMappingTemplateS3Location","RequestMappingTemplateS3Location",properties.RequestMappingTemplateS3Location!=null?cfn_parse().FromCloudFormation.getString(properties.RequestMappingTemplateS3Location):void 0),ret.addPropertyResult("responseMappingTemplate","ResponseMappingTemplate",properties.ResponseMappingTemplate!=null?cfn_parse().FromCloudFormation.getString(properties.ResponseMappingTemplate):void 0),ret.addPropertyResult("responseMappingTemplateS3Location","ResponseMappingTemplateS3Location",properties.ResponseMappingTemplateS3Location!=null?cfn_parse().FromCloudFormation.getString(properties.ResponseMappingTemplateS3Location):void 0),ret.addPropertyResult("runtime","Runtime",properties.Runtime!=null?CfnFunctionConfigurationAppSyncRuntimePropertyFromCloudFormation(properties.Runtime):void 0),ret.addPropertyResult("syncConfig","SyncConfig",properties.SyncConfig!=null?CfnFunctionConfigurationSyncConfigPropertyFromCloudFormation(properties.SyncConfig):void 0),ret.addUnrecognizedPropertiesAsExtra(properties),ret}class CfnGraphQLApi extends cdk().CfnResource{static _fromCloudFormation(scope,id,resourceAttributes,options){resourceAttributes=resourceAttributes||{};const resourceProperties=options.parser.parseValue(resourceAttributes.Properties),propsResult=CfnGraphQLApiPropsFromCloudFormation(resourceProperties);if(cdk().isResolvableObject(propsResult.value))throw new(cdk_errors()).ValidationError("Unexpected IResolvable",scope);const ret=new CfnGraphQLApi(scope,id,propsResult.value);for(const[propKey,propVal]of Object.entries(propsResult.extraProperties))ret.addPropertyOverride(propKey,propVal);return options.parser.handleAttributes(ret,resourceAttributes,id),ret}constructor(scope,id,props){super(scope,id,{type:CfnGraphQLApi.CFN_RESOURCE_TYPE_NAME,properties:props});try{jsiiDeprecationWarnings().aws_cdk_lib_aws_appsync_CfnGraphQLApiProps(props)}catch(error){throw process.env.JSII_DEBUG!=="1"&&error.name==="DeprecationError"&&Error.captureStackTrace(error,CfnGraphQLApi),error}cdk().requireProperty(props,"authenticationType",this),cdk().requireProperty(props,"name",this),this.attrApiId=cdk().Token.asString(this.getAtt("ApiId",cdk().ResolutionTypeHint.STRING)),this.attrArn=cdk().Token.asString(this.getAtt("Arn",cdk().ResolutionTypeHint.STRING)),this.attrGraphQlDns=cdk().Token.asString(this.getAtt("GraphQLDns",cdk().ResolutionTypeHint.STRING)),this.attrGraphQlEndpointArn=cdk().Token.asString(this.getAtt("GraphQLEndpointArn",cdk().ResolutionTypeHint.STRING)),this.attrGraphQlUrl=cdk().Token.asString(this.getAtt("GraphQLUrl",cdk().ResolutionTypeHint.STRING)),this.attrRealtimeDns=cdk().Token.asString(this.getAtt("RealtimeDns",cdk().ResolutionTypeHint.STRING)),this.attrRealtimeUrl=cdk().Token.asString(this.getAtt("RealtimeUrl",cdk().ResolutionTypeHint.STRING)),this.additionalAuthenticationProviders=props.additionalAuthenticationProviders,this.apiType=props.apiType,this.authenticationType=props.authenticationType,this.enhancedMetricsConfig=props.enhancedMetricsConfig,this.environmentVariables=props.environmentVariables,this.introspectionConfig=props.introspectionConfig,this.lambdaAuthorizerConfig=props.lambdaAuthorizerConfig,this.logConfig=props.logConfig,this.mergedApiExecutionRoleArn=props.mergedApiExecutionRoleArn,this.name=props.name,this.openIdConnectConfig=props.openIdConnectConfig,this.ownerContact=props.ownerContact,this.queryDepthLimit=props.queryDepthLimit,this.resolverCountLimit=props.resolverCountLimit,this.tags=new(cdk()).TagManager(cdk().TagType.STANDARD,"AWS::AppSync::GraphQLApi",props.tags,{tagPropertyName:"tags"}),this.tagsRaw=props.tags,this.userPoolConfig=props.userPoolConfig,this.visibility=props.visibility,this.xrayEnabled=props.xrayEnabled}get cfnProperties(){return{additionalAuthenticationProviders:this.additionalAuthenticationProviders,apiType:this.apiType,authenticationType:this.authenticationType,enhancedMetricsConfig:this.enhancedMetricsConfig,environmentVariables:this.environmentVariables,introspectionConfig:this.introspectionConfig,lambdaAuthorizerConfig:this.lambdaAuthorizerConfig,logConfig:this.logConfig,mergedApiExecutionRoleArn:this.mergedApiExecutionRoleArn,name:this.name,openIdConnectConfig:this.openIdConnectConfig,ownerContact:this.ownerContact,queryDepthLimit:this.queryDepthLimit,resolverCountLimit:this.resolverCountLimit,tags:this.tags.renderTags(),userPoolConfig:this.userPoolConfig,visibility:this.visibility,xrayEnabled:this.xrayEnabled}}inspect(inspector){try{jsiiDeprecationWarnings().aws_cdk_lib_TreeInspector(inspector)}catch(error){throw process.env.JSII_DEBUG!=="1"&&error.name==="DeprecationError"&&Error.captureStackTrace(error,this.inspect),error}inspector.addAttribute("aws:cdk:cloudformation:type",CfnGraphQLApi.CFN_RESOURCE_TYPE_NAME),inspector.addAttribute("aws:cdk:cloudformation:props",this.cfnProperties)}renderProperties(props){return convertCfnGraphQLApiPropsToCloudFormation(props)}}exports.CfnGraphQLApi=CfnGraphQLApi,_g=JSII_RTTI_SYMBOL_1,CfnGraphQLApi[_g]={fqn:"aws-cdk-lib.aws_appsync.CfnGraphQLApi",version:"2.201.0"},CfnGraphQLApi.CFN_RESOURCE_TYPE_NAME="AWS::AppSync::GraphQLApi";function CfnGraphQLApiOpenIDConnectConfigPropertyValidator(properties){if(!cdk().canInspect(properties))return cdk().VALIDATION_SUCCESS;const errors=new(cdk()).ValidationResults;return properties&&typeof properties=="object"&&!Array.isArray(properties)||errors.collect(new(cdk()).ValidationResult("Expected an object, but received: "+JSON.stringify(properties))),errors.collect(cdk().propertyValidator("authTtl",cdk().validateNumber)(properties.authTtl)),errors.collect(cdk().propertyValidator("clientId",cdk().validateString)(properties.clientId)),errors.collect(cdk().propertyValidator("iatTtl",cdk().validateNumber)(properties.iatTtl)),errors.collect(cdk().propertyValidator("issuer",cdk().validateString)(properties.issuer)),errors.wrap('supplied properties not correct for "OpenIDConnectConfigProperty"')}function convertCfnGraphQLApiOpenIDConnectConfigPropertyToCloudFormation(properties){return cdk().canInspect(properties)?(CfnGraphQLApiOpenIDConnectConfigPropertyValidator(properties).assertSuccess(),{AuthTTL:cdk().numberToCloudFormation(properties.authTtl),ClientId:cdk().stringToCloudFormation(properties.clientId),IatTTL:cdk().numberToCloudFormation(properties.iatTtl),Issuer:cdk().stringToCloudFormation(properties.issuer)}):properties}function CfnGraphQLApiOpenIDConnectConfigPropertyFromCloudFormation(properties){if(cdk().isResolvableObject(properties))return new(cfn_parse()).FromCloudFormationResult(properties);if(properties=properties??{},!(properties&&typeof properties=="object"&&!Array.isArray(properties)))return new(cfn_parse()).FromCloudFormationResult(properties);const ret=new(cfn_parse()).FromCloudFormationPropertyObject;return ret.addPropertyResult("authTtl","AuthTTL",properties.AuthTTL!=null?cfn_parse().FromCloudFormation.getNumber(properties.AuthTTL):void 0),ret.addPropertyResult("clientId","ClientId",properties.ClientId!=null?cfn_parse().FromCloudFormation.getString(properties.ClientId):void 0),ret.addPropertyResult("iatTtl","IatTTL",properties.IatTTL!=null?cfn_parse().FromCloudFormation.getNumber(properties.IatTTL):void 0),ret.addPropertyResult("issuer","Issuer",properties.Issuer!=null?cfn_parse().FromCloudFormation.getString(properties.Issuer):void 0),ret.addUnrecognizedPropertiesAsExtra(properties),ret}function CfnGraphQLApiLambdaAuthorizerConfigPropertyValidator(properties){if(!cdk().canInspect(properties))return cdk().VALIDATION_SUCCESS;const errors=new(cdk()).ValidationResults;return properties&&typeof properties=="object"&&!Array.isArray(properties)||errors.collect(new(cdk()).ValidationResult("Expected an object, but received: "+JSON.stringify(properties))),errors.collect(cdk().propertyValidator("authorizerResultTtlInSeconds",cdk().validateNumber)(properties.authorizerResultTtlInSeconds)),errors.collect(cdk().propertyValidator("authorizerUri",cdk().validateString)(properties.authorizerUri)),errors.collect(cdk().propertyValidator("identityValidationExpression",cdk().validateString)(properties.identityValidationExpression)),errors.wrap('supplied properties not correct for "LambdaAuthorizerConfigProperty"')}function convertCfnGraphQLApiLambdaAuthorizerConfigPropertyToCloudFormation(properties){return cdk().canInspect(properties)?(CfnGraphQLApiLambdaAuthorizerConfigPropertyValidator(properties).assertSuccess(),{AuthorizerResultTtlInSeconds:cdk().numberToCloudFormation(properties.authorizerResultTtlInSeconds),AuthorizerUri:cdk().stringToCloudFormation(properties.authorizerUri),IdentityValidationExpression:cdk().stringToCloudFormation(properties.identityValidationExpression)}):properties}function CfnGraphQLApiLambdaAuthorizerConfigPropertyFromCloudFormation(properties){if(cdk().isResolvableObject(properties))return new(cfn_parse()).FromCloudFormationResult(properties);if(properties=properties??{},!(properties&&typeof properties=="object"&&!Array.isArray(properties)))return new(cfn_parse()).FromCloudFormationResult(properties);const ret=new(cfn_parse()).FromCloudFormationPropertyObject;return ret.addPropertyResult("authorizerResultTtlInSeconds","AuthorizerResultTtlInSeconds",properties.AuthorizerResultTtlInSeconds!=null?cfn_parse().FromCloudFormation.getNumber(properties.AuthorizerResultTtlInSeconds):void 0),ret.addPropertyResult("authorizerUri","AuthorizerUri",properties.AuthorizerUri!=null?cfn_parse().FromCloudFormation.getString(properties.AuthorizerUri):void 0),ret.addPropertyResult("identityValidationExpression","IdentityValidationExpression",properties.IdentityValidationExpression!=null?cfn_parse().FromCloudFormation.getString(properties.IdentityValidationExpression):void 0),ret.addUnrecognizedPropertiesAsExtra(properties),ret}function CfnGraphQLApiCognitoUserPoolConfigPropertyValidator(properties){if(!cdk().canInspect(properties))return cdk().VALIDATION_SUCCESS;const errors=new(cdk()).ValidationResults;return properties&&typeof properties=="object"&&!Array.isArray(properties)||errors.collect(new(cdk()).ValidationResult("Expected an object, but received: "+JSON.stringify(properties))),errors.collect(cdk().propertyValidator("appIdClientRegex",cdk().validateString)(properties.appIdClientRegex)),errors.collect(cdk().propertyValidator("awsRegion",cdk().validateString)(properties.awsRegion)),errors.collect(cdk().propertyValidator("userPoolId",cdk().validateString)(properties.userPoolId)),errors.wrap('supplied properties not correct for "CognitoUserPoolConfigProperty"')}function convertCfnGraphQLApiCognitoUserPoolConfigPropertyToCloudFormation(properties){return cdk().canInspect(properties)?(CfnGraphQLApiCognitoUserPoolConfigPropertyValidator(properties).assertSuccess(),{AppIdClientRegex:cdk().stringToCloudFormation(properties.appIdClientRegex),AwsRegion:cdk().stringToCloudFormation(properties.awsRegion),UserPoolId:cdk().stringToCloudFormation(properties.userPoolId)}):properties}function CfnGraphQLApiCognitoUserPoolConfigPropertyFromCloudFormation(properties){if(cdk().isResolvableObject(properties))return new(cfn_parse()).FromCloudFormationResult(properties);if(properties=properties??{},!(properties&&typeof properties=="object"&&!Array.isArray(properties)))return new(cfn_parse()).FromCloudFormationResult(properties);const ret=new(cfn_parse()).FromCloudFormationPropertyObject;return ret.addPropertyResult("appIdClientRegex","AppIdClientRegex",properties.AppIdClientRegex!=null?cfn_parse().FromCloudFormation.getString(properties.AppIdClientRegex):void 0),ret.addPropertyResult("awsRegion","AwsRegion",properties.AwsRegion!=null?cfn_parse().FromCloudFormation.getString(properties.AwsRegion):void 0),ret.addPropertyResult("userPoolId","UserPoolId",properties.UserPoolId!=null?cfn_parse().FromCloudFormation.getString(properties.UserPoolId):void 0),ret.addUnrecognizedPropertiesAsExtra(properties),ret}function CfnGraphQLApiAdditionalAuthenticationProviderPropertyValidator(properties){if(!cdk().canInspect(properties))return cdk().VALIDATION_SUCCESS;const errors=new(cdk()).ValidationResults;return properties&&typeof properties=="object"&&!Array.isArray(properties)||errors.collect(new(cdk()).ValidationResult("Expected an object, but received: "+JSON.stringify(properties))),errors.collect(cdk().propertyValidator("authenticationType",cdk().requiredValidator)(properties.authenticationType)),errors.collect(cdk().propertyValidator("authenticationType",cdk().validateString)(properties.authenticationType)),errors.collect(cdk().propertyValidator("lambdaAuthorizerConfig",CfnGraphQLApiLambdaAuthorizerConfigPropertyValidator)(properties.lambdaAuthorizerConfig)),errors.collect(cdk().propertyValidator("openIdConnectConfig",CfnGraphQLApiOpenIDConnectConfigPropertyValidator)(properties.openIdConnectConfig)),errors.collect(cdk().propertyValidator("userPoolConfig",CfnGraphQLApiCognitoUserPoolConfigPropertyValidator)(properties.userPoolConfig)),errors.wrap('supplied properties not correct for "AdditionalAuthenticationProviderProperty"')}function convertCfnGraphQLApiAdditionalAuthenticationProviderPropertyToCloudFormation(properties){return cdk().canInspect(properties)?(CfnGraphQLApiAdditionalAuthenticationProviderPropertyValidator(properties).assertSuccess(),{AuthenticationType:cdk().stringToCloudFormation(properties.authenticationType),LambdaAuthorizerConfig:convertCfnGraphQLApiLambdaAuthorizerConfigPropertyToCloudFormation(properties.lambdaAuthorizerConfig),OpenIDConnectConfig:convertCfnGraphQLApiOpenIDConnectConfigPropertyToCloudFormation(properties.openIdConnectConfig),UserPoolConfig:convertCfnGraphQLApiCognitoUserPoolConfigPropertyToCloudFormation(properties.userPoolConfig)}):properties}function CfnGraphQLApiAdditionalAuthenticationProviderPropertyFromCloudFormation(properties){if(cdk().isResolvableObject(properties))return new(cfn_parse()).FromCloudFormationResult(properties);if(properties=properties??{},!(properties&&typeof properties=="object"&&!Array.isArray(properties)))return new(cfn_parse()).FromCloudFormationResult(properties);const ret=new(cfn_parse()).FromCloudFormationPropertyObject;return ret.addPropertyResult("authenticationType","AuthenticationType",properties.AuthenticationType!=null?cfn_parse().FromCloudFormation.getString(properties.AuthenticationType):void 0),ret.addPropertyResult("lambdaAuthorizerConfig","LambdaAuthorizerConfig",properties.LambdaAuthorizerConfig!=null?CfnGraphQLApiLambdaAuthorizerConfigPropertyFromCloudFormation(properties.LambdaAuthorizerConfig):void 0),ret.addPropertyResult("openIdConnectConfig","OpenIDConnectConfig",properties.OpenIDConnectConfig!=null?CfnGraphQLApiOpenIDConnectConfigPropertyFromCloudFormation(properties.OpenIDConnectConfig):void 0),ret.addPropertyResult("userPoolConfig","UserPoolConfig",properties.UserPoolConfig!=null?CfnGraphQLApiCognitoUserPoolConfigPropertyFromCloudFormation(properties.UserPoolConfig):void 0),ret.addUnrecognizedPropertiesAsExtra(properties),ret}function CfnGraphQLApiUserPoolConfigPropertyValidator(properties){if(!cdk().canInspect(properties))return cdk().VALIDATION_SUCCESS;const errors=new(cdk()).ValidationResults;return properties&&typeof properties=="object"&&!Array.isArray(properties)||errors.collect(new(cdk()).ValidationResult("Expected an object, but received: "+JSON.stringify(properties))),errors.collect(cdk().propertyValidator("appIdClientRegex",cdk().validateString)(properties.appIdClientRegex)),errors.collect(cdk().propertyValidator("awsRegion",cdk().validateString)(properties.awsRegion)),errors.collect(cdk().propertyValidator("defaultAction",cdk().validateString)(properties.defaultAction)),errors.collect(cdk().propertyValidator("userPoolId",cdk().validateString)(properties.userPoolId)),errors.wrap('supplied properties not correct for "UserPoolConfigProperty"')}function convertCfnGraphQLApiUserPoolConfigPropertyToCloudFormation(properties){return cdk().canInspect(properties)?(CfnGraphQLApiUserPoolConfigPropertyValidator(properties).assertSuccess(),{AppIdClientRegex:cdk().stringToCloudFormation(properties.appIdClientRegex),AwsRegion:cdk().stringToCloudFormation(properties.awsRegion),DefaultAction:cdk().stringToCloudFormation(properties.defaultAction),UserPoolId:cdk().stringToCloudFormation(properties.userPoolId)}):properties}function CfnGraphQLApiUserPoolConfigPropertyFromCloudFormation(properties){if(cdk().isResolvableObject(properties))return new(cfn_parse()).FromCloudFormationResult(properties);if(properties=properties??{},!(properties&&typeof properties=="object"&&!Array.isArray(properties)))return new(cfn_parse()).FromCloudFormationResult(properties);const ret=new(cfn_parse()).FromCloudFormationPropertyObject;return ret.addPropertyResult("appIdClientRegex","AppIdClientRegex",properties.AppIdClientRegex!=null?cfn_parse().FromCloudFormation.getString(properties.AppIdClientRegex):void 0),ret.addPropertyResult("awsRegion","AwsRegion",properties.AwsRegion!=null?cfn_parse().FromCloudFormation.getString(properties.AwsRegion):void 0),ret.addPropertyResult("defaultAction","DefaultAction",properties.DefaultAction!=null?cfn_parse().FromCloudFormation.getString(properties.DefaultAction):void 0),ret.addPropertyResult("userPoolId","UserPoolId",properties.UserPoolId!=null?cfn_parse().FromCloudFormation.getString(properties.UserPoolId):void 0),ret.addUnrecognizedPropertiesAsExtra(properties),ret}function CfnGraphQLApiLogConfigPropertyValidator(properties){if(!cdk().canInspect(properties))return cdk().VALIDATION_SUCCESS;const errors=new(cdk()).ValidationResults;return properties&&typeof properties=="object"&&!Array.isArray(properties)||errors.collect(new(cdk()).ValidationResult("Expected an object, but received: "+JSON.stringify(properties))),errors.collect(cdk().propertyValidator("cloudWatchLogsRoleArn",cdk().validateString)(properties.cloudWatchLogsRoleArn)),errors.collect(cdk().propertyValidator("excludeVerboseContent",cdk().validateBoolean)(properties.excludeVerboseContent)),errors.collect(cdk().propertyValidator("fieldLogLevel",cdk().validateString)(properties.fieldLogLevel)),errors.wrap('supplied properties not correct for "LogConfigProperty"')}function convertCfnGraphQLApiLogConfigPropertyToCloudFormation(properties){return cdk().canInspect(properties)?(CfnGraphQLApiLogConfigPropertyValidator(properties).assertSuccess(),{CloudWatchLogsRoleArn:cdk().stringToCloudFormation(properties.cloudWatchLogsRoleArn),ExcludeVerboseContent:cdk().booleanToCloudFormation(properties.excludeVerboseContent),FieldLogLevel:cdk().stringToCloudFormation(properties.fieldLogLevel)}):properties}function CfnGraphQLApiLogConfigPropertyFromCloudFormation(properties){if(cdk().isResolvableObject(properties))return new(cfn_parse()).FromCloudFormationResult(properties);if(properties=properties??{},!(properties&&typeof properties=="object"&&!Array.isArray(properties)))return new(cfn_parse()).FromCloudFormationResult(properties);const ret=new(cfn_parse()).FromCloudFormationPropertyObject;return ret.addPropertyResult("cloudWatchLogsRoleArn","CloudWatchLogsRoleArn",properties.CloudWatchLogsRoleArn!=null?cfn_parse().FromCloudFormation.getString(properties.CloudWatchLogsRoleArn):void 0),ret.addPropertyResult("excludeVerboseContent","ExcludeVerboseContent",properties.ExcludeVerboseContent!=null?cfn_parse().FromCloudFormation.getBoolean(properties.ExcludeVerboseContent):void 0),ret.addPropertyResult("fieldLogLevel","FieldLogLevel",properties.FieldLogLevel!=null?cfn_parse().FromCloudFormation.getString(properties.FieldLogLevel):void 0),ret.addUnrecognizedPropertiesAsExtra(properties),ret}function CfnGraphQLApiEnhancedMetricsConfigPropertyValidator(properties){if(!cdk().canInspect(properties))return cdk().VALIDATION_SUCCESS;const errors=new(cdk()).ValidationResults;return properties&&typeof properties=="object"&&!Array.isArray(properties)||errors.collect(new(cdk()).ValidationResult("Expected an object, but received: "+JSON.stringify(properties))),errors.collect(cdk().propertyValidator("dataSourceLevelMetricsBehavior",cdk().requiredValidator)(properties.dataSourceLevelMetricsBehavior)),errors.collect(cdk().propertyValidator("dataSourceLevelMetricsBehavior",cdk().validateString)(properties.dataSourceLevelMetricsBehavior)),errors.collect(cdk().propertyValidator("operationLevelMetricsConfig",cdk().requiredValidator)(properties.operationLevelMetricsConfig)),errors.collect(cdk().propertyValidator("operationLevelMetricsConfig",cdk().validateString)(properties.operationLevelMetricsConfig)),errors.collect(cdk().propertyValidator("resolverLevelMetricsBehavior",cdk().requiredValidator)(properties.resolverLevelMetricsBehavior)),errors.collect(cdk().propertyValidator("resolverLevelMetricsBehavior",cdk().validateString)(properties.resolverLevelMetricsBehavior)),errors.wrap('supplied properties not correct for "EnhancedMetricsConfigProperty"')}function convertCfnGraphQLApiEnhancedMetricsConfigPropertyToCloudFormation(properties){return cdk().canInspect(properties)?(CfnGraphQLApiEnhancedMetricsConfigPropertyValidator(properties).assertSuccess(),{DataSourceLevelMetricsBehavior:cdk().stringToCloudFormation(properties.dataSourceLevelMetricsBehavior),OperationLevelMetricsConfig:cdk().stringToCloudFormation(properties.operationLevelMetricsConfig),ResolverLevelMetricsBehavior:cdk().stringToCloudFormation(properties.resolverLevelMetricsBehavior)}):properties}function CfnGraphQLApiEnhancedMetricsConfigPropertyFromCloudFormation(properties){if(cdk().isResolvableObject(properties))return new(cfn_parse()).FromCloudFormationResult(properties);if(properties=properties??{},!(properties&&typeof properties=="object"&&!Array.isArray(properties)))return new(cfn_parse()).FromCloudFormationResult(properties);const ret=new(cfn_parse()).FromCloudFormationPropertyObject;return ret.addPropertyResult("dataSourceLevelMetricsBehavior","DataSourceLevelMetricsBehavior",properties.DataSourceLevelMetricsBehavior!=null?cfn_parse().FromCloudFormation.getString(properties.DataSourceLevelMetricsBehavior):void 0),ret.addPropertyResult("operationLevelMetricsConfig","OperationLevelMetricsConfig",properties.OperationLevelMetricsConfig!=null?cfn_parse().FromCloudFormation.getString(properties.OperationLevelMetricsConfig):void 0),ret.addPropertyResult("resolverLevelMetricsBehavior","ResolverLevelMetricsBehavior",properties.ResolverLevelMetricsBehavior!=null?cfn_parse().FromCloudFormation.getString(properties.ResolverLevelMetricsBehavior):void 0),ret.addUnrecognizedPropertiesAsExtra(properties),ret}function CfnGraphQLApiPropsValidator(properties){if(!cdk().canInspect(properties))return cdk().VALIDATION_SUCCESS;const errors=new(cdk()).ValidationResults;return properties&&typeof properties=="object"&&!Array.isArray(properties)||errors.collect(new(cdk()).ValidationResult("Expected an object, but received: "+JSON.stringify(properties))),errors.collect(cdk().propertyValidator("additionalAuthenticationProviders",cdk().listValidator(CfnGraphQLApiAdditionalAuthenticationProviderPropertyValidator))(properties.additionalAuthenticationProviders)),errors.collect(cdk().propertyValidator("apiType",cdk().validateString)(properties.apiType)),errors.collect(cdk().propertyValidator("authenticationType",cdk().requiredValidator)(properties.authenticationType)),errors.collect(cdk().propertyValidator("authenticationType",cdk().validateString)(properties.authenticationType)),errors.collect(cdk().propertyValidator("enhancedMetricsConfig",CfnGraphQLApiEnhancedMetricsConfigPropertyValidator)(properties.enhancedMetricsConfig)),errors.collect(cdk().propertyValidator("environmentVariables",cdk().hashValidator(cdk().validateString))(properties.environmentVariables)),errors.collect(cdk().propertyValidator("introspectionConfig",cdk().validateString)(properties.introspectionConfig)),errors.collect(cdk().propertyValidator("lambdaAuthorizerConfig",CfnGraphQLApiLambdaAuthorizerConfigPropertyValidator)(properties.lambdaAuthorizerConfig)),errors.collect(cdk().propertyValidator("logConfig",CfnGraphQLApiLogConfigPropertyValidator)(properties.logConfig)),errors.collect(cdk().propertyValidator("mergedApiExecutionRoleArn",cdk().validateString)(properties.mergedApiExecutionRoleArn)),errors.collect(cdk().propertyValidator("name",cdk().requiredValidator)(properties.name)),errors.collect(cdk().propertyValidator("name",cdk().validateString)(properties.name)),errors.collect(cdk().propertyValidator("openIdConnectConfig",CfnGraphQLApiOpenIDConnectConfigPropertyValidator)(properties.openIdConnectConfig)),errors.collect(cdk().propertyValidator("ownerContact",cdk().validateString)(properties.ownerContact)),errors.collect(cdk().propertyValidator("queryDepthLimit",cdk().validateNumber)(properties.queryDepthLimit)),errors.collect(cdk().propertyValidator("resolverCountLimit",cdk().validateNumber)(properties.resolverCountLimit)),errors.collect(cdk().propertyValidator("tags",cdk().listValidator(cdk().validateCfnTag))(properties.tags)),errors.collect(cdk().propertyValidator("userPoolConfig",CfnGraphQLApiUserPoolConfigPropertyValidator)(properties.userPoolConfig)),errors.collect(cdk().propertyValidator("visibility",cdk().validateString)(properties.visibility)),errors.collect(cdk().propertyValidator("xrayEnabled",cdk().validateBoolean)(properties.xrayEnabled)),errors.wrap('supplied properties not correct for "CfnGraphQLApiProps"')}function convertCfnGraphQLApiPropsToCloudFormation(properties){return cdk().canInspect(properties)?(CfnGraphQLApiPropsValidator(properties).assertSuccess(),{AdditionalAuthenticationProviders:cdk().listMapper(convertCfnGraphQLApiAdditionalAuthenticationProviderPropertyToCloudFormation)(properties.additionalAuthenticationProviders),ApiType:cdk().stringToCloudFormation(properties.apiType),AuthenticationType:cdk().stringToCloudFormation(properties.authenticationType),EnhancedMetricsConfig:convertCfnGraphQLApiEnhancedMetricsConfigPropertyToCloudFormation(properties.enhancedMetricsConfig),EnvironmentVariables:cdk().hashMapper(cdk().stringToCloudFormation)(properties.environmentVariables),IntrospectionConfig:cdk().stringToCloudFormation(properties.introspectionConfig),LambdaAuthorizerConfig:convertCfnGraphQLApiLambdaAuthorizerConfigPropertyToCloudFormation(properties.lambdaAuthorizerConfig),LogConfig:convertCfnGraphQLApiLogConfigPropertyToCloudFormation(properties.logConfig),MergedApiExecutionRoleArn:cdk().stringToCloudFormation(properties.mergedApiExecutionRoleArn),Name:cdk().stringToCloudFormation(properties.name),OpenIDConnectConfig:convertCfnGraphQLApiOpenIDConnectConfigPropertyToCloudFormation(properties.openIdConnectConfig),OwnerContact:cdk().stringToCloudFormation(properties.ownerContact),QueryDepthLimit:cdk().numberToCloudFormation(properties.queryDepthLimit),ResolverCountLimit:cdk().numberToCloudFormation(properties.resolverCountLimit),Tags:cdk().listMapper(cdk().cfnTagToCloudFormation)(properties.tags),UserPoolConfig:convertCfnGraphQLApiUserPoolConfigPropertyToCloudFormation(properties.userPoolConfig),Visibility:cdk().stringToCloudFormation(properties.visibility),XrayEnabled:cdk().booleanToCloudFormation(properties.xrayEnabled)}):properties}function CfnGraphQLApiPropsFromCloudFormation(properties){if(cdk().isResolvableObject(properties))return new(cfn_parse()).FromCloudFormationResult(properties);if(properties=properties??{},!(properties&&typeof properties=="object"&&!Array.isArray(properties)))return new(cfn_parse()).FromCloudFormationResult(properties);const ret=new(cfn_parse()).FromCloudFormationPropertyObject;return ret.addPropertyResult("additionalAuthenticationProviders","AdditionalAuthenticationProviders",properties.AdditionalAuthenticationProviders!=null?cfn_parse().FromCloudFormation.getArray(CfnGraphQLApiAdditionalAuthenticationProviderPropertyFromCloudFormation)(properties.AdditionalAuthenticationProviders):void 0),ret.addPropertyResult("apiType","ApiType",properties.ApiType!=null?cfn_parse().FromCloudFormation.getString(properties.ApiType):void 0),ret.addPropertyResult("authenticationType","AuthenticationType",properties.AuthenticationType!=null?cfn_parse().FromCloudFormation.getString(properties.AuthenticationType):void 0),ret.addPropertyResult("enhancedMetricsConfig","EnhancedMetricsConfig",properties.EnhancedMetricsConfig!=null?CfnGraphQLApiEnhancedMetricsConfigPropertyFromCloudFormation(properties.EnhancedMetricsConfig):void 0),ret.addPropertyResult("environmentVariables","EnvironmentVariables",properties.EnvironmentVariables!=null?cfn_parse().FromCloudFormation.getMap(cfn_parse().FromCloudFormation.getString)(properties.EnvironmentVariables):void 0),ret.addPropertyResult("introspectionConfig","IntrospectionConfig",properties.IntrospectionConfig!=null?cfn_parse().FromCloudFormation.getString(properties.IntrospectionConfig):void 0),ret.addPropertyResult("lambdaAuthorizerConfig","LambdaAuthorizerConfig",properties.LambdaAuthorizerConfig!=null?CfnGraphQLApiLambdaAuthorizerConfigPropertyFromCloudFormation(properties.LambdaAuthorizerConfig):void 0),ret.addPropertyResult("logConfig","LogConfig",properties.LogConfig!=null?CfnGraphQLApiLogConfigPropertyFromCloudFormation(properties.LogConfig):void 0),ret.addPropertyResult("mergedApiExecutionRoleArn","MergedApiExecutionRoleArn",properties.MergedApiExecutionRoleArn!=null?cfn_parse().FromCloudFormation.getString(properties.MergedApiExecutionRoleArn):void 0),ret.addPropertyResult("name","Name",properties.Name!=null?cfn_parse().FromCloudFormation.getString(properties.Name):void 0),ret.addPropertyResult("openIdConnectConfig","OpenIDConnectConfig",properties.OpenIDConnectConfig!=null?CfnGraphQLApiOpenIDConnectConfigPropertyFromCloudFormation(properties.OpenIDConnectConfig):void 0),ret.addPropertyResult("ownerContact","OwnerContact",properties.OwnerContact!=null?cfn_parse().FromCloudFormation.getString(properties.OwnerContact):void 0),ret.addPropertyResult("queryDepthLimit","QueryDepthLimit",properties.QueryDepthLimit!=null?cfn_parse().FromCloudFormation.getNumber(properties.QueryDepthLimit):void 0),ret.addPropertyResult("resolverCountLimit","ResolverCountLimit",properties.ResolverCountLimit!=null?cfn_parse().FromCloudFormation.getNumber(properties.ResolverCountLimit):void 0),ret.addPropertyResult("tags","Tags",properties.Tags!=null?cfn_parse().FromCloudFormation.getArray(cfn_parse().FromCloudFormation.getCfnTag)(properties.Tags):void 0),ret.addPropertyResult("userPoolConfig","UserPoolConfig",properties.UserPoolConfig!=null?CfnGraphQLApiUserPoolConfigPropertyFromCloudFormation(properties.UserPoolConfig):void 0),ret.addPropertyResult("visibility","Visibility",properties.Visibility!=null?cfn_parse().FromCloudFormation.getString(properties.Visibility):void 0),ret.addPropertyResult("xrayEnabled","XrayEnabled",properties.XrayEnabled!=null?cfn_parse().FromCloudFormation.getBoolean(properties.XrayEnabled):void 0),ret.addUnrecognizedPropertiesAsExtra(properties),ret}class CfnGraphQLSchema extends cdk().CfnResource{static _fromCloudFormation(scope,id,resourceAttributes,options){resourceAttributes=resourceAttributes||{};const resourceProperties=options.parser.parseValue(resourceAttributes.Properties),propsResult=CfnGraphQLSchemaPropsFromCloudFormation(resourceProperties);if(cdk().isResolvableObject(propsResult.value))throw new(cdk_errors()).ValidationError("Unexpected IResolvable",scope);const ret=new CfnGraphQLSchema(scope,id,propsResult.value);for(const[propKey,propVal]of Object.entries(propsResult.extraProperties))ret.addPropertyOverride(propKey,propVal);return options.parser.handleAttributes(ret,resourceAttributes,id),ret}constructor(scope,id,props){super(scope,id,{type:CfnGraphQLSchema.CFN_RESOURCE_TYPE_NAME,properties:props});try{jsiiDeprecationWarnings().aws_cdk_lib_aws_appsync_CfnGraphQLSchemaProps(props)}catch(error){throw process.env.JSII_DEBUG!=="1"&&error.name==="DeprecationError"&&Error.captureStackTrace(error,CfnGraphQLSchema),error}cdk().requireProperty(props,"apiId",this),this.attrId=cdk().Token.asString(this.getAtt("Id",cdk().ResolutionTypeHint.STRING)),this.apiId=props.apiId,this.definition=props.definition,this.definitionS3Location=props.definitionS3Location}get cfnProperties(){return{apiId:this.apiId,definition:this.definition,definitionS3Location:this.definitionS3Location}}inspect(inspector){try{jsiiDeprecationWarnings().aws_cdk_lib_TreeInspector(inspector)}catch(error){throw process.env.JSII_DEBUG!=="1"&&error.name==="DeprecationError"&&Error.captureStackTrace(error,this.inspect),error}inspector.addAttribute("aws:cdk:cloudformation:type",CfnGraphQLSchema.CFN_RESOURCE_TYPE_NAME),inspector.addAttribute("aws:cdk:cloudformation:props",this.cfnProperties)}renderProperties(props){return convertCfnGraphQLSchemaPropsToCloudFormation(props)}}exports.CfnGraphQLSchema=CfnGraphQLSchema,_h=JSII_RTTI_SYMBOL_1,CfnGraphQLSchema[_h]={fqn:"aws-cdk-lib.aws_appsync.CfnGraphQLSchema",version:"2.201.0"},CfnGraphQLSchema.CFN_RESOURCE_TYPE_NAME="AWS::AppSync::GraphQLSchema";function CfnGraphQLSchemaPropsValidator(properties){if(!cdk().canInspect(properties))return cdk().VALIDATION_SUCCESS;const errors=new(cdk()).ValidationResults;return properties&&typeof properties=="object"&&!Array.isArray(properties)||errors.collect(new(cdk()).ValidationResult("Expected an object, but received: "+JSON.stringify(properties))),errors.collect(cdk().propertyValidator("apiId",cdk().requiredValidator)(properties.apiId)),errors.collect(cdk().propertyValidator("apiId",cdk().validateString)(properties.apiId)),errors.collect(cdk().propertyValidator("definition",cdk().validateString)(properties.definition)),errors.collect(cdk().propertyValidator("definitionS3Location",cdk().validateString)(properties.definitionS3Location)),errors.wrap('supplied properties not correct for "CfnGraphQLSchemaProps"')}function convertCfnGraphQLSchemaPropsToCloudFormation(properties){return cdk().canInspect(properties)?(CfnGraphQLSchemaPropsValidator(properties).assertSuccess(),{ApiId:cdk().stringToCloudFormation(properties.apiId),Definition:cdk().stringToCloudFormation(properties.definition),DefinitionS3Location:cdk().stringToCloudFormation(properties.definitionS3Location)}):properties}function CfnGraphQLSchemaPropsFromCloudFormation(properties){if(cdk().isResolvableObject(properties))return new(cfn_parse()).FromCloudFormationResult(properties);if(properties=properties??{},!(properties&&typeof properties=="object"&&!Array.isArray(properties)))return new(cfn_parse()).FromCloudFormationResult(properties);const ret=new(cfn_parse()).FromCloudFormationPropertyObject;return ret.addPropertyResult("apiId","ApiId",properties.ApiId!=null?cfn_parse().FromCloudFormation.getString(properties.ApiId):void 0),ret.addPropertyResult("definition","Definition",properties.Definition!=null?cfn_parse().FromCloudFormation.getString(properties.Definition):void 0),ret.addPropertyResult("definitionS3Location","DefinitionS3Location",properties.DefinitionS3Location!=null?cfn_parse().FromCloudFormation.getString(properties.DefinitionS3Location):void 0),ret.addUnrecognizedPropertiesAsExtra(properties),ret}class CfnResolver extends cdk().CfnResource{static _fromCloudFormation(scope,id,resourceAttributes,options){resourceAttributes=resourceAttributes||{};const resourceProperties=options.parser.parseValue(resourceAttributes.Properties),propsResult=CfnResolverPropsFromCloudFormation(resourceProperties);if(cdk().isResolvableObject(propsResult.value))throw new(cdk_errors()).ValidationError("Unexpected IResolvable",scope);const ret=new CfnResolver(scope,id,propsResult.value);for(const[propKey,propVal]of Object.entries(propsResult.extraProperties))ret.addPropertyOverride(propKey,propVal);return options.parser.handleAttributes(ret,resourceAttributes,id),ret}constructor(scope,id,props){super(scope,id,{type:CfnResolver.CFN_RESOURCE_TYPE_NAME,properties:props});try{jsiiDeprecationWarnings().aws_cdk_lib_aws_appsync_CfnResolverProps(props)}catch(error){throw process.env.JSII_DEBUG!=="1"&&error.name==="DeprecationError"&&Error.captureStackTrace(error,CfnResolver),error}cdk().requireProperty(props,"apiId",this),cdk().requireProperty(props,"fieldName",this),cdk().requireProperty(props,"typeName",this),this.attrFieldName=cdk().Token.asString(this.getAtt("FieldName",cdk().ResolutionTypeHint.STRING)),this.attrResolverArn=cdk().Token.asString(this.getAtt("ResolverArn",cdk().ResolutionTypeHint.STRING)),this.attrTypeName=cdk().Token.asString(this.getAtt("TypeName",cdk().ResolutionTypeHint.STRING)),this.apiId=props.apiId,this.cachingConfig=props.cachingConfig,this.code=props.code,this.codeS3Location=props.codeS3Location,this.dataSourceName=props.dataSourceName,this.fieldName=props.fieldName,this.kind=props.kind,this.maxBatchSize=props.maxBatchSize,this.metricsConfig=props.metricsConfig,this.pipelineConfig=props.pipelineConfig,this.requestMappingTemplate=props.requestMappingTemplate,this.requestMappingTemplateS3Location=props.requestMappingTemplateS3Location,this.responseMappingTemplate=props.responseMappingTemplate,this.responseMappingTemplateS3Location=props.responseMappingTemplateS3Location,this.runtime=props.runtime,this.syncConfig=props.syncConfig,this.typeName=props.typeName}get cfnProperties(){return{apiId:this.apiId,cachingConfig:this.cachingConfig,code:this.code,codeS3Location:this.codeS3Location,dataSourceName:this.dataSourceName,fieldName:this.fieldName,kind:this.kind,maxBatchSize:this.maxBatchSize,metricsConfig:this.metricsConfig,pipelineConfig:this.pipelineConfig,requestMappingTemplate:this.requestMappingTemplate,requestMappingTemplateS3Location:this.requestMappingTemplateS3Location,responseMappingTemplate:this.responseMappingTemplate,responseMappingTemplateS3Location:this.responseMappingTemplateS3Location,runtime:this.runtime,syncConfig:this.syncConfig,typeName:this.typeName}}inspect(inspector){try{jsiiDeprecationWarnings().aws_cdk_lib_TreeInspector(inspector)}catch(error){throw process.env.JSII_DEBUG!=="1"&&error.name==="DeprecationError"&&Error.captureStackTrace(error,this.inspect),error}inspector.addAttribute("aws:cdk:cloudformation:type",CfnResolver.CFN_RESOURCE_TYPE_NAME),inspector.addAttribute("aws:cdk:cloudformation:props",this.cfnProperties)}renderProperties(props){return convertCfnResolverPropsToCloudFormation(props)}}exports.CfnResolver=CfnResolver,_j=JSII_RTTI_SYMBOL_1,CfnResolver[_j]={fqn:"aws-cdk-lib.aws_appsync.CfnResolver",version:"2.201.0"},CfnResolver.CFN_RESOURCE_TYPE_NAME="AWS::AppSync::Resolver";function CfnResolverPipelineConfigPropertyValidator(properties){if(!cdk().canInspect(properties))return cdk().VALIDATION_SUCCESS;const errors=new(cdk()).ValidationResults;return properties&&typeof properties=="object"&&!Array.isArray(properties)||errors.collect(new(cdk()).ValidationResult("Expected an object, but received: "+JSON.stringify(properties))),errors.collect(cdk().propertyValidator("functions",cdk().listValidator(cdk().validateString))(properties.functions)),errors.wrap('supplied properties not correct for "PipelineConfigProperty"')}function convertCfnResolverPipelineConfigPropertyToCloudFormation(properties){return cdk().canInspect(properties)?(CfnResolverPipelineConfigPropertyValidator(properties).assertSuccess(),{Functions:cdk().listMapper(cdk().stringToCloudFormation)(properties.functions)}):properties}function CfnResolverPipelineConfigPropertyFromCloudFormation(properties){if(cdk().isResolvableObject(properties))return new(cfn_parse()).FromCloudFormationResult(properties);if(properties=properties??{},!(properties&&typeof properties=="object"&&!Array.isArray(properties)))return new(cfn_parse()).FromCloudFormationResult(properties);const ret=new(cfn_parse()).FromCloudFormationPropertyObject;return ret.addPropertyResult("functions","Functions",properties.Functions!=null?cfn_parse().FromCloudFormation.getArray(cfn_parse().FromCloudFormation.getString)(properties.Functions):void 0),ret.addUnrecognizedPropertiesAsExtra(properties),ret}function CfnResolverLambdaConflictHandlerConfigPropertyValidator(properties){if(!cdk().canInspect(properties))return cdk().VALIDATION_SUCCESS;const errors=new(cdk()).ValidationResults;return properties&&typeof properties=="object"&&!Array.isArray(properties)||errors.collect(new(cdk()).ValidationResult("Expected an object, but received: "+JSON.stringify(properties))),errors.collect(cdk().propertyValidator("lambdaConflictHandlerArn",cdk().validateString)(properties.lambdaConflictHandlerArn)),errors.wrap('supplied properties not correct for "LambdaConflictHandlerConfigProperty"')}function convertCfnResolverLambdaConflictHandlerConfigPropertyToCloudFormation(properties){return cdk().canInspect(properties)?(CfnResolverLambdaConflictHandlerConfigPropertyValidator(properties).assertSuccess(),{LambdaConflictHandlerArn:cdk().stringToCloudFormation(properties.lambdaConflictHandlerArn)}):properties}function CfnResolverLambdaConflictHandlerConfigPropertyFromCloudFormation(properties){if(cdk().isResolvableObject(properties))return new(cfn_parse()).FromCloudFormationResult(properties);if(properties=properties??{},!(properties&&typeof properties=="object"&&!Array.isArray(properties)))return new(cfn_parse()).FromCloudFormationResult(properties);const ret=new(cfn_parse()).FromCloudFormationPropertyObject;return ret.addPropertyResult("lambdaConflictHandlerArn","LambdaConflictHandlerArn",properties.LambdaConflictHandlerArn!=null?cfn_parse().FromCloudFormation.getString(properties.LambdaConflictHandlerArn):void 0),ret.addUnrecognizedPropertiesAsExtra(properties),ret}function CfnResolverSyncConfigPropertyValidator(properties){if(!cdk().canInspect(properties))return cdk().VALIDATION_SUCCESS;const errors=new(cdk()).ValidationResults;return properties&&typeof properties=="object"&&!Array.isArray(properties)||errors.collect(new(cdk()).ValidationResult("Expected an object, but received: "+JSON.stringify(properties))),errors.collect(cdk().propertyValidator("conflictDetection",cdk().requiredValidator)(properties.conflictDetection)),errors.collect(cdk().propertyValidator("conflictDetection",cdk().validateString)(properties.conflictDetection)),errors.collect(cdk().propertyValidator("conflictHandler",cdk().validateString)(properties.conflictHandler)),errors.collect(cdk().propertyValidator("lambdaConflictHandlerConfig",CfnResolverLambdaConflictHandlerConfigPropertyValidator)(properties.lambdaConflictHandlerConfig)),errors.wrap('supplied properties not correct for "SyncConfigProperty"')}function convertCfnResolverSyncConfigPropertyToCloudFormation(properties){return cdk().canInspect(properties)?(CfnResolverSyncConfigPropertyValidator(properties).assertSuccess(),{ConflictDetection:cdk().stringToCloudFormation(properties.conflictDetection),ConflictHandler:cdk().stringToCloudFormation(properties.conflictHandler),LambdaConflictHandlerConfig:convertCfnResolverLambdaConflictHandlerConfigPropertyToCloudFormation(properties.lambdaConflictHandlerConfig)}):properties}function CfnResolverSyncConfigPropertyFromCloudFormation(properties){if(cdk().isResolvableObject(properties))return new(cfn_parse()).FromCloudFormationResult(properties);if(properties=properties??{},!(properties&&typeof properties=="object"&&!Array.isArray(properties)))return new(cfn_parse()).FromCloudFormationResult(properties);const ret=new(cfn_parse()).FromCloudFormationPropertyObject;return ret.addPropertyResult("conflictDetection","ConflictDetection",properties.ConflictDetection!=null?cfn_parse().FromCloudFormation.getString(properties.ConflictDetection):void 0),ret.addPropertyResult("conflictHandler","ConflictHandler",properties.ConflictHandler!=null?cfn_parse().FromCloudFormation.getString(properties.ConflictHandler):void 0),ret.addPropertyResult("lambdaConflictHandlerConfig","LambdaConflictHandlerConfig",properties.LambdaConflictHandlerConfig!=null?CfnResolverLambdaConflictHandlerConfigPropertyFromCloudFormation(properties.LambdaConflictHandlerConfig):void 0),ret.addUnrecognizedPropertiesAsExtra(properties),ret}function CfnResolverAppSyncRuntimePropertyValidator(properties){if(!cdk().canInspect(properties))return cdk().VALIDATION_SUCCESS;const errors=new(cdk()).ValidationResults;return properties&&typeof properties=="object"&&!Array.isArray(properties)||errors.collect(new(cdk()).ValidationResult("Expected an object, but received: "+JSON.stringify(properties))),errors.collect(cdk().propertyValidator("name",cdk().requiredValidator)(properties.name)),errors.collect(cdk().propertyValidator("name",cdk().validateString)(properties.name)),errors.collect(cdk().propertyValidator("runtimeVersion",cdk().requiredValidator)(properties.runtimeVersion)),errors.collect(cdk().propertyValidator("runtimeVersion",cdk().validateString)(properties.runtimeVersion)),errors.wrap('supplied properties not correct for "AppSyncRuntimeProperty"')}function convertCfnResolverAppSyncRuntimePropertyToCloudFormation(properties){return cdk().canInspect(properties)?(CfnResolverAppSyncRuntimePropertyValidator(properties).assertSuccess(),{Name:cdk().stringToCloudFormation(properties.name),RuntimeVersion:cdk().stringToCloudFormation(properties.runtimeVersion)}):properties}function CfnResolverAppSyncRuntimePropertyFromCloudFormation(properties){if(cdk().isResolvableObject(properties))return new(cfn_parse()).FromCloudFormationResult(properties);if(properties=properties??{},!(properties&&typeof properties=="object"&&!Array.isArray(properties)))return new(cfn_parse()).FromCloudFormationResult(properties);const ret=new(cfn_parse()).FromCloudFormationPropertyObject;return ret.addPropertyResult("name","Name",properties.Name!=null?cfn_parse().FromCloudFormation.getString(properties.Name):void 0),ret.addPropertyResult("runtimeVersion","RuntimeVersion",properties.RuntimeVersion!=null?cfn_parse().FromCloudFormation.getString(properties.RuntimeVersion):void 0),ret.addUnrecognizedPropertiesAsExtra(properties),ret}function CfnResolverCachingConfigPropertyValidator(properties){if(!cdk().canInspect(properties))return cdk().VALIDATION_SUCCESS;const errors=new(cdk()).ValidationResults;return properties&&typeof properties=="object"&&!Array.isArray(properties)||errors.collect(new(cdk()).ValidationResult("Expected an object, but received: "+JSON.stringify(properties))),errors.collect(cdk().propertyValidator("cachingKeys",cdk().listValidator(cdk().validateString))(properties.cachingKeys)),errors.collect(cdk().propertyValidator("ttl",cdk().requiredValidator)(properties.ttl)),errors.collect(cdk().propertyValidator("ttl",cdk().validateNumber)(properties.ttl)),errors.wrap('supplied properties not correct for "CachingConfigProperty"')}function convertCfnResolverCachingConfigPropertyToCloudFormation(properties){return cdk().canInspect(properties)?(CfnResolverCachingConfigPropertyValidator(properties).assertSuccess(),{CachingKeys:cdk().listMapper(cdk().stringToCloudFormation)(properties.cachingKeys),Ttl:cdk().numberToCloudFormation(properties.ttl)}):properties}function CfnResolverCachingConfigPropertyFromCloudFormation(properties){if(cdk().isResolvableObject(properties))return new(cfn_parse()).FromCloudFormationResult(properties);if(properties=properties??{},!(properties&&typeof properties=="object"&&!Array.isArray(properties)))return new(cfn_parse()).FromCloudFormationResult(properties);const ret=new(cfn_parse()).FromCloudFormationPropertyObject;return ret.addPropertyResult("cachingKeys","CachingKeys",properties.CachingKeys!=null?cfn_parse().FromCloudFormation.getArray(cfn_parse().FromCloudFormation.getString)(properties.CachingKeys):void 0),ret.addPropertyResult("ttl","Ttl",properties.Ttl!=null?cfn_parse().FromCloudFormation.getNumber(properties.Ttl):void 0),ret.addUnrecognizedPropertiesAsExtra(properties),ret}function CfnResolverPropsValidator(properties){if(!cdk().canInspect(properties))return cdk().VALIDATION_SUCCESS;const errors=new(cdk()).ValidationResults;return properties&&typeof properties=="object"&&!Array.isArray(properties)||errors.collect(new(cdk()).ValidationResult("Expected an object, but received: "+JSON.stringify(properties))),errors.collect(cdk().propertyValidator("apiId",cdk().requiredValidator)(properties.apiId)),errors.collect(cdk().propertyValidator("apiId",cdk().validateString)(properties.apiId)),errors.collect(cdk().propertyValidator("cachingConfig",CfnResolverCachingConfigPropertyValidator)(properties.cachingConfig)),errors.collect(cdk().propertyValidator("code",cdk().validateString)(properties.code)),errors.collect(cdk().propertyValidator("codeS3Location",cdk().validateString)(properties.codeS3Location)),errors.collect(cdk().propertyValidator("dataSourceName",cdk().validateString)(properties.dataSourceName)),errors.collect(cdk().propertyValidator("fieldName",cdk().requiredValidator)(properties.fieldName)),errors.collect(cdk().propertyValidator("fieldName",cdk().validateString)(properties.fieldName)),errors.collect(cdk().propertyValidator("kind",cdk().validateString)(properties.kind)),errors.collect(cdk().propertyValidator("maxBatchSize",cdk().validateNumber)(properties.maxBatchSize)),errors.collect(cdk().propertyValidator("metricsConfig",cdk().validateString)(properties.metricsConfig)),errors.collect(cdk().propertyValidator("pipelineConfig",CfnResolverPipelineConfigPropertyValidator)(properties.pipelineConfig)),errors.collect(cdk().propertyValidator("requestMappingTemplate",cdk().validateString)(properties.requestMappingTemplate)),errors.collect(cdk().propertyValidator("requestMappingTemplateS3Location",cdk().validateString)(properties.requestMappingTemplateS3Location)),errors.collect(cdk().propertyValidator("responseMappingTemplate",cdk().validateString)(properties.responseMappingTemplate)),errors.collect(cdk().propertyValidator("responseMappingTemplateS3Location",cdk().validateString)(properties.responseMappingTemplateS3Location)),errors.collect(cdk().propertyValidator("runtime",CfnResolverAppSyncRuntimePropertyValidator)(properties.runtime)),errors.collect(cdk().propertyValidator("syncConfig",CfnResolverSyncConfigPropertyValidator)(properties.syncConfig)),errors.collect(cdk().propertyValidator("typeName",cdk().requiredValidator)(properties.typeName)),errors.collect(cdk().propertyValidator("typeName",cdk().validateString)(properties.typeName)),errors.wrap('supplied properties not correct for "CfnResolverProps"')}function convertCfnResolverPropsToCloudFormation(properties){return cdk().canInspect(properties)?(CfnResolverPropsValidator(properties).assertSuccess(),{ApiId:cdk().stringToCloudFormation(properties.apiId),CachingConfig:convertCfnResolverCachingConfigPropertyToCloudFormation(properties.cachingConfig),Code:cdk().stringToCloudFormation(properties.code),CodeS3Location:cdk().stringToCloudFormation(properties.codeS3Location),DataSourceName:cdk().stringToCloudFormation(properties.dataSourceName),FieldName:cdk().stringToCloudFormation(properties.fieldName),Kind:cdk().stringToCloudFormation(properties.kind),MaxBatchSize:cdk().numberToCloudFormation(properties.maxBatchSize),MetricsConfig:cdk().stringToCloudFormation(properties.metricsConfig),PipelineConfig:convertCfnResolverPipelineConfigPropertyToCloudFormation(properties.pipelineConfig),RequestMappingTemplate:cdk().stringToCloudFormation(properties.requestMappingTemplate),RequestMappingTemplateS3Location:cdk().stringToCloudFormation(properties.requestMappingTemplateS3Location),ResponseMappingTemplate:cdk().stringToCloudFormation(properties.responseMappingTemplate),ResponseMappingTemplateS3Location:cdk().stringToCloudFormation(properties.responseMappingTemplateS3Location),Runtime:convertCfnResolverAppSyncRuntimePropertyToCloudFormation(properties.runtime),SyncConfig:convertCfnResolverSyncConfigPropertyToCloudFormation(properties.syncConfig),TypeName:cdk().stringToCloudFormation(properties.typeName)}):properties}function CfnResolverPropsFromCloudFormation(properties){if(cdk().isResolvableObject(properties))return new(cfn_parse()).FromCloudFormationResult(properties);if(properties=properties??{},!(properties&&typeof properties=="object"&&!Array.isArray(properties)))return new(cfn_parse()).FromCloudFormationResult(properties);const ret=new(cfn_parse()).FromCloudFormationPropertyObject;return ret.addPropertyResult("apiId","ApiId",properties.ApiId!=null?cfn_parse().FromCloudFormation.getString(properties.ApiId):void 0),ret.addPropertyResult("cachingConfig","CachingConfig",properties.CachingConfig!=null?CfnResolverCachingConfigPropertyFromCloudFormation(properties.CachingConfig):void 0),ret.addPropertyResult("code","Code",properties.Code!=null?cfn_parse().FromCloudFormation.getString(properties.Code):void 0),ret.addPropertyResult("codeS3Location","CodeS3Location",properties.CodeS3Location!=null?cfn_parse().FromCloudFormation.getString(properties.CodeS3Location):void 0),ret.addPropertyResult("dataSourceName","DataSourceName",properties.DataSourceName!=null?cfn_parse().FromCloudFormation.getString(properties.DataSourceName):void 0),ret.addPropertyResult("fieldName","FieldName",properties.FieldName!=null?cfn_parse().FromCloudFormation.getString(properties.FieldName):void 0),ret.addPropertyResult("kind","Kind",properties.Kind!=null?cfn_parse().FromCloudFormation.getString(properties.Kind):void 0),ret.addPropertyResult("maxBatchSize","MaxBatchSize",properties.MaxBatchSize!=null?cfn_parse().FromCloudFormation.getNumber(properties.MaxBatchSize):void 0),ret.addPropertyResult("metricsConfig","MetricsConfig",properties.MetricsConfig!=null?cfn_parse().FromCloudFormation.getString(properties.MetricsConfig):void 0),ret.addPropertyResult("pipelineConfig","PipelineConfig",properties.PipelineConfig!=null?CfnResolverPipelineConfigPropertyFromCloudFormation(properties.PipelineConfig):void 0),ret.addPropertyResult("requestMappingTemplate","RequestMappingTemplate",properties.RequestMappingTemplate!=null?cfn_parse().FromCloudFormation.getString(properties.RequestMappingTemplate):void 0),ret.addPropertyResult("requestMappingTemplateS3Location","RequestMappingTemplateS3Location",properties.RequestMappingTemplateS3Location!=null?cfn_parse().FromCloudFormation.getString(properties.RequestMappingTemplateS3Location):void 0),ret.addPropertyResult("responseMappingTemplate","ResponseMappingTemplate",properties.ResponseMappingTemplate!=null?cfn_parse().FromCloudFormation.getString(properties.ResponseMappingTemplate):void 0),ret.addPropertyResult("responseMappingTemplateS3Location","ResponseMappingTemplateS3Location",properties.ResponseMappingTemplateS3Location!=null?cfn_parse().FromCloudFormation.getString(properties.ResponseMappingTemplateS3Location):void 0),ret.addPropertyResult("runtime","Runtime",properties.Runtime!=null?CfnResolverAppSyncRuntimePropertyFromCloudFormation(properties.Runtime):void 0),ret.addPropertyResult("syncConfig","SyncConfig",properties.SyncConfig!=null?CfnResolverSyncConfigPropertyFromCloudFormation(properties.SyncConfig):void 0),ret.addPropertyResult("typeName","TypeName",properties.TypeName!=null?cfn_parse().FromCloudFormation.getString(properties.TypeName):void 0),ret.addUnrecognizedPropertiesAsExtra(properties),ret}class CfnSourceApiAssociation extends cdk().CfnResource{static _fromCloudFormation(scope,id,resourceAttributes,options){resourceAttributes=resourceAttributes||{};const resourceProperties=options.parser.parseValue(resourceAttributes.Properties),propsResult=CfnSourceApiAssociationPropsFromCloudFormation(resourceProperties);if(cdk().isResolvableObject(propsResult.value))throw new(cdk_errors()).ValidationError("Unexpected IResolvable",scope);const ret=new CfnSourceApiAssociation(scope,id,propsResult.value);for(const[propKey,propVal]of Object.entries(propsResult.extraProperties))ret.addPropertyOverride(propKey,propVal);return options.parser.handleAttributes(ret,resourceAttributes,id),ret}constructor(scope,id,props={}){super(scope,id,{type:CfnSourceApiAssociation.CFN_RESOURCE_TYPE_NAME,properties:props});try{jsiiDeprecationWarnings().aws_cdk_lib_aws_appsync_CfnSourceApiAssociationProps(props)}catch(error){throw process.env.JSII_DEBUG!=="1"&&error.name==="DeprecationError"&&Error.captureStackTrace(error,CfnSourceApiAssociation),error}this.attrAssociationArn=cdk().Token.asString(this.getAtt("AssociationArn",cdk().ResolutionTypeHint.STRING)),this.attrAssociationId=cdk().Token.asString(this.getAtt("AssociationId",cdk().ResolutionTypeHint.STRING)),this.attrLastSuccessfulMergeDate=cdk().Token.asString(this.getAtt("LastSuccessfulMergeDate",cdk().ResolutionTypeHint.STRING)),this.attrMergedApiArn=cdk().Token.asString(this.getAtt("MergedApiArn",cdk().ResolutionTypeHint.STRING)),this.attrMergedApiId=cdk().Token.asString(this.getAtt("MergedApiId",cdk().ResolutionTypeHint.STRING)),this.attrSourceApiArn=cdk().Token.asString(this.getAtt("SourceApiArn",cdk().ResolutionTypeHint.STRING)),this.attrSourceApiAssociationStatus=cdk().Token.asString(this.getAtt("SourceApiAssociationStatus",cdk().ResolutionTypeHint.STRING)),this.attrSourceApiAssociationStatusDetail=cdk().Token.asString(this.getAtt("SourceApiAssociationStatusDetail",cdk().ResolutionTypeHint.STRING)),this.attrSourceApiId=cdk().Token.asString(this.getAtt("SourceApiId",cdk().ResolutionTypeHint.STRING)),this.description=props.description,this.mergedApiIdentifier=props.mergedApiIdentifier,this.sourceApiAssociationConfig=props.sourceApiAssociationConfig,this.sourceApiIdentifier=props.sourceApiIdentifier}get cfnProperties(){return{description:this.description,mergedApiIdentifier:this.mergedApiIdentifier,sourceApiAssociationConfig:this.sourceApiAssociationConfig,sourceApiIdentifier:this.sourceApiIdentifier}}inspect(inspector){try{jsiiDeprecationWarnings().aws_cdk_lib_TreeInspector(inspector)}catch(error){throw process.env.JSII_DEBUG!=="1"&&error.name==="DeprecationError"&&Error.captureStackTrace(error,this.inspect),error}inspector.addAttribute("aws:cdk:cloudformation:type",CfnSourceApiAssociation.CFN_RESOURCE_TYPE_NAME),inspector.addAttribute("aws:cdk:cloudformation:props",this.cfnProperties)}renderProperties(props){return convertCfnSourceApiAssociationPropsToCloudFormation(props)}}exports.CfnSourceApiAssociation=CfnSourceApiAssociation,_k=JSII_RTTI_SYMBOL_1,CfnSourceApiAssociation[_k]={fqn:"aws-cdk-lib.aws_appsync.CfnSourceApiAssociation",version:"2.201.0"},CfnSourceApiAssociation.CFN_RESOURCE_TYPE_NAME="AWS::AppSync::SourceApiAssociation";function CfnSourceApiAssociationSourceApiAssociationConfigPropertyValidator(properties){if(!cdk().canInspect(properties))return cdk().VALIDATION_SUCCESS;const errors=new(cdk()).ValidationResults;return properties&&typeof properties=="object"&&!Array.isArray(properties)||errors.collect(new(cdk()).ValidationResult("Expected an object, but received: "+JSON.stringify(properties))),errors.collect(cdk().propertyValidator("mergeType",cdk().validateString)(properties.mergeType)),errors.wrap('supplied properties not correct for "SourceApiAssociationConfigProperty"')}function convertCfnSourceApiAssociationSourceApiAssociationConfigPropertyToCloudFormation(properties){return cdk().canInspect(properties)?(CfnSourceApiAssociationSourceApiAssociationConfigPropertyValidator(properties).assertSuccess(),{MergeType:cdk().stringToCloudFormation(properties.mergeType)}):properties}function CfnSourceApiAssociationSourceApiAssociationConfigPropertyFromCloudFormation(properties){if(cdk().isResolvableObject(properties))return new(cfn_parse()).FromCloudFormationResult(properties);if(properties=properties??{},!(properties&&typeof properties=="object"&&!Array.isArray(properties)))return new(cfn_parse()).FromCloudFormationResult(properties);const ret=new(cfn_parse()).FromCloudFormationPropertyObject;return ret.addPropertyResult("mergeType","MergeType",properties.MergeType!=null?cfn_parse().FromCloudFormation.getString(properties.MergeType):void 0),ret.addUnrecognizedPropertiesAsExtra(properties),ret}function CfnSourceApiAssociationPropsValidator(properties){if(!cdk().canInspect(properties))return cdk().VALIDATION_SUCCESS;const errors=new(cdk()).ValidationResults;return properties&&typeof properties=="object"&&!Array.isArray(properties)||errors.collect(new(cdk()).ValidationResult("Expected an object, but received: "+JSON.stringify(properties))),errors.collect(cdk().propertyValidator("description",cdk().validateString)(properties.description)),errors.collect(cdk().propertyValidator("mergedApiIdentifier",cdk().validateString)(properties.mergedApiIdentifier)),errors.collect(cdk().propertyValidator("sourceApiAssociationConfig",CfnSourceApiAssociationSourceApiAssociationConfigPropertyValidator)(properties.sourceApiAssociationConfig)),errors.collect(cdk().propertyValidator("sourceApiIdentifier",cdk().validateString)(properties.sourceApiIdentifier)),errors.wrap('supplied properties not correct for "CfnSourceApiAssociationProps"')}function convertCfnSourceApiAssociationPropsToCloudFormation(properties){return cdk().canInspect(properties)?(CfnSourceApiAssociationPropsValidator(properties).assertSuccess(),{Description:cdk().stringToCloudFormation(properties.description),MergedApiIdentifier:cdk().stringToCloudFormation(properties.mergedApiIdentifier),SourceApiAssociationConfig:convertCfnSourceApiAssociationSourceApiAssociationConfigPropertyToCloudFormation(properties.sourceApiAssociationConfig),SourceApiIdentifier:cdk().stringToCloudFormation(properties.sourceApiIdentifier)}):properties}function CfnSourceApiAssociationPropsFromCloudFormation(properties){if(cdk().isResolvableObject(properties))return new(cfn_parse()).FromCloudFormationResult(properties);if(properties=properties??{},!(properties&&typeof properties=="object"&&!Array.isArray(properties)))return new(cfn_parse()).FromCloudFormationResult(properties);const ret=new(cfn_parse()).FromCloudFormationPropertyObject;return ret.addPropertyResult("description","Description",properties.Description!=null?cfn_parse().FromCloudFormation.getString(properties.Description):void 0),ret.addPropertyResult("mergedApiIdentifier","MergedApiIdentifier",properties.MergedApiIdentifier!=null?cfn_parse().FromCloudFormation.getString(properties.MergedApiIdentifier):void 0),ret.addPropertyResult("sourceApiAssociationConfig","SourceApiAssociationConfig",properties.SourceApiAssociationConfig!=null?CfnSourceApiAssociationSourceApiAssociationConfigPropertyFromCloudFormation(properties.SourceApiAssociationConfig):void 0),ret.addPropertyResult("sourceApiIdentifier","SourceApiIdentifier",properties.SourceApiIdentifier!=null?cfn_parse().FromCloudFormation.getString(properties.SourceApiIdentifier):void 0),ret.addUnrecognizedPropertiesAsExtra(properties),ret}class CfnApi extends cdk().CfnResource{static _fromCloudFormation(scope,id,resourceAttributes,options){resourceAttributes=resourceAttributes||{};const resourceProperties=options.parser.parseValue(resourceAttributes.Properties),propsResult=CfnApiPropsFromCloudFormation(resourceProperties);if(cdk().isResolvableObject(propsResult.value))throw new(cdk_errors()).ValidationError("Unexpected IResolvable",scope);const ret=new CfnApi(scope,id,propsResult.value);for(const[propKey,propVal]of Object.entries(propsResult.extraProperties))ret.addPropertyOverride(propKey,propVal);return options.parser.handleAttributes(ret,resourceAttributes,id),ret}constructor(scope,id,props){super(scope,id,{type:CfnApi.CFN_RESOURCE_TYPE_NAME,properties:props});try{jsiiDeprecationWarnings().aws_cdk_lib_aws_appsync_CfnApiProps(props)}catch(error){throw process.env.JSII_DEBUG!=="1"&&error.name==="DeprecationError"&&Error.captureStackTrace(error,CfnApi),error}cdk().requireProperty(props,"name",this),this.attrApiArn=cdk().Token.asString(this.getAtt("ApiArn",cdk().ResolutionTypeHint.STRING)),this.attrApiId=cdk().Token.asString(this.getAtt("ApiId",cdk().ResolutionTypeHint.STRING)),this.attrDns=this.getAtt("Dns"),this.attrDnsHttp=cdk().Token.asString(this.getAtt("Dns.Http",cdk().ResolutionTypeHint.STRING)),this.attrDnsRealtime=cdk().Token.asString(this.getAtt("Dns.Realtime",cdk().ResolutionTypeHint.STRING)),this.cdkTagManager=new(cdk()).TagManager(cdk().TagType.STANDARD,"AWS::AppSync::Api",void 0,{tagPropertyName:"tags"}),this.eventConfig=props.eventConfig,this.name=props.name,this.ownerContact=props.ownerContact,this.tags=props.tags}get cfnProperties(){return{tags:this.cdkTagManager.renderTags(this.tags),eventConfig:this.eventConfig,name:this.name,ownerContact:this.ownerContact}}inspect(inspector){try{jsiiDeprecationWarnings().aws_cdk_lib_TreeInspector(inspector)}catch(error){throw process.env.JSII_DEBUG!=="1"&&error.name==="DeprecationError"&&Error.captureStackTrace(error,this.inspect),error}inspector.addAttribute("aws:cdk:cloudformation:type",CfnApi.CFN_RESOURCE_TYPE_NAME),inspector.addAttribute("aws:cdk:cloudformation:props",this.cfnProperties)}renderProperties(props){return convertCfnApiPropsToCloudFormation(props)}}exports.CfnApi=CfnApi,_l=JSII_RTTI_SYMBOL_1,CfnApi[_l]={fqn:"aws-cdk-lib.aws_appsync.CfnApi",version:"2.201.0"},CfnApi.CFN_RESOURCE_TYPE_NAME="AWS::AppSync::Api";function CfnApiOpenIDConnectConfigPropertyValidator(properties){if(!cdk().canInspect(properties))return cdk().VALIDATION_SUCCESS;const errors=new(cdk()).ValidationResults;return properties&&typeof properties=="object"&&!Array.isArray(properties)||errors.collect(new(cdk()).ValidationResult("Expected an object, but received: "+JSON.stringify(properties))),errors.collect(cdk().propertyValidator("authTtl",cdk().validateNumber)(properties.authTtl)),errors.collect(cdk().propertyValidator("clientId",cdk().validateString)(properties.clientId)),errors.collect(cdk().propertyValidator("iatTtl",cdk().validateNumber)(properties.iatTtl)),errors.collect(cdk().propertyValidator("issuer",cdk().requiredValidator)(properties.issuer)),errors.collect(cdk().propertyValidator("issuer",cdk().validateString)(properties.issuer)),errors.wrap('supplied properties not correct for "OpenIDConnectConfigProperty"')}function convertCfnApiOpenIDConnectConfigPropertyToCloudFormation(properties){return cdk().canInspect(properties)?(CfnApiOpenIDConnectConfigPropertyValidator(properties).assertSuccess(),{AuthTTL:cdk().numberToCloudFormation(properties.authTtl),ClientId:cdk().stringToCloudFormation(properties.clientId),IatTTL:cdk().numberToCloudFormation(properties.iatTtl),Issuer:cdk().stringToCloudFormation(properties.issuer)}):properties}function CfnApiOpenIDConnectConfigPropertyFromCloudFormation(properties){if(cdk().isResolvableObject(properties))return new(cfn_parse()).FromCloudFormationResult(properties);if(properties=properties??{},!(properties&&typeof properties=="object"&&!Array.isArray(properties)))return new(cfn_parse()).FromCloudFormationResult(properties);const ret=new(cfn_parse()).FromCloudFormationPropertyObject;return ret.addPropertyResult("authTtl","AuthTTL",properties.AuthTTL!=null?cfn_parse().FromCloudFormation.getNumber(properties.AuthTTL):void 0),ret.addPropertyResult("clientId","ClientId",properties.ClientId!=null?cfn_parse().FromCloudFormation.getString(properties.ClientId):void 0),ret.addPropertyResult("iatTtl","IatTTL",properties.IatTTL!=null?cfn_parse().FromCloudFormation.getNumber(properties.IatTTL):void 0),ret.addPropertyResult("issuer","Issuer",properties.Issuer!=null?cfn_parse().FromCloudFormation.getString(properties.Issuer):void 0),ret.addUnrecognizedPropertiesAsExtra(properties),ret}function CfnApiCognitoConfigPropertyValidator(properties){if(!cdk().canInspect(properties))return cdk().VALIDATION_SUCCESS;const errors=new(cdk()).ValidationResults;return properties&&typeof properties=="object"&&!Array.isArray(properties)||errors.collect(new(cdk()).ValidationResult("Expected an object, but received: "+JSON.stringify(properties))),errors.collect(cdk().propertyValidator("appIdClientRegex",cdk().validateString)(properties.appIdClientRegex)),errors.collect(cdk().propertyValidator("awsRegion",cdk().requiredValidator)(properties.awsRegion)),errors.collect(cdk().propertyValidator("awsRegion",cdk().validateString)(properties.awsRegion)),errors.collect(cdk().propertyValidator("userPoolId",cdk().requiredValidator)(properties.userPoolId)),errors.collect(cdk().propertyValidator("userPoolId",cdk().validateString)(properties.userPoolId)),errors.wrap('supplied properties not correct for "CognitoConfigProperty"')}function convertCfnApiCognitoConfigPropertyToCloudFormation(properties){return cdk().canInspect(properties)?(CfnApiCognitoConfigPropertyValidator(properties).assertSuccess(),{AppIdClientRegex:cdk().stringToCloudFormation(properties.appIdClientRegex),AwsRegion:cdk().stringToCloudFormation(properties.awsRegion),UserPoolId:cdk().stringToCloudFormation(properties.userPoolId)}):properties}function CfnApiCognitoConfigPropertyFromCloudFormation(properties){if(cdk().isResolvableObject(properties))return new(cfn_parse()).FromCloudFormationResult(properties);if(properties=properties??{},!(properties&&typeof properties=="object"&&!Array.isArray(properties)))return new(cfn_parse()).FromCloudFormationResult(properties);const ret=new(cfn_parse()).FromCloudFormationPropertyObject;return ret.addPropertyResult("appIdClientRegex","AppIdClientRegex",properties.AppIdClientRegex!=null?cfn_parse().FromCloudFormation.getString(properties.AppIdClientRegex):void 0),ret.addPropertyResult("awsRegion","AwsRegion",properties.AwsRegion!=null?cfn_parse().FromCloudFormation.getString(properties.AwsRegion):void 0),ret.addPropertyResult("userPoolId","UserPoolId",properties.UserPoolId!=null?cfn_parse().FromCloudFormation.getString(properties.UserPoolId):void 0),ret.addUnrecognizedPropertiesAsExtra(properties),ret}function CfnApiLambdaAuthorizerConfigPropertyValidator(properties){if(!cdk().canInspect(properties))return cdk().VALIDATION_SUCCESS;const errors=new(cdk()).ValidationResults;return properties&&typeof properties=="object"&&!Array.isArray(properties)||errors.collect(new(cdk()).ValidationResult("Expected an object, but received: "+JSON.stringify(properties))),errors.collect(cdk().propertyValidator("authorizerResultTtlInSeconds",cdk().validateNumber)(properties.authorizerResultTtlInSeconds)),errors.collect(cdk().propertyValidator("authorizerUri",cdk().requiredValidator)(properties.authorizerUri)),errors.collect(cdk().propertyValidator("authorizerUri",cdk().validateString)(properties.authorizerUri)),errors.collect(cdk().propertyValidator("identityValidationExpression",cdk().validateString)(properties.identityValidationExpression)),errors.wrap('supplied properties not correct for "LambdaAuthorizerConfigProperty"')}function convertCfnApiLambdaAuthorizerConfigPropertyToCloudFormation(properties){return cdk().canInspect(properties)?(CfnApiLambdaAuthorizerConfigPropertyValidator(properties).assertSuccess(),{AuthorizerResultTtlInSeconds:cdk().numberToCloudFormation(properties.authorizerResultTtlInSeconds),AuthorizerUri:cdk().stringToCloudFormation(properties.authorizerUri),IdentityValidationExpression:cdk().stringToCloudFormation(properties.identityValidationExpression)}):properties}function CfnApiLambdaAuthorizerConfigPropertyFromCloudFormation(properties){if(cdk().isResolvableObject(properties))return new(cfn_parse()).FromCloudFormationResult(properties);if(properties=properties??{},!(properties&&typeof properties=="object"&&!Array.isArray(properties)))return new(cfn_parse()).FromCloudFormationResult(properties);const ret=new(cfn_parse()).FromCloudFormationPropertyObject;return ret.addPropertyResult("authorizerResultTtlInSeconds","AuthorizerResultTtlInSeconds",properties.AuthorizerResultTtlInSeconds!=null?cfn_parse().FromCloudFormation.getNumber(properties.AuthorizerResultTtlInSeconds):void 0),ret.addPropertyResult("authorizerUri","AuthorizerUri",properties.AuthorizerUri!=null?cfn_parse().FromCloudFormation.getString(properties.AuthorizerUri):void 0),ret.addPropertyResult("identityValidationExpression","IdentityValidationExpression",properties.IdentityValidationExpression!=null?cfn_parse().FromCloudFormation.getString(properties.IdentityValidationExpression):void 0),ret.addUnrecognizedPropertiesAsExtra(properties),ret}function CfnApiAuthProviderPropertyValidator(properties){if(!cdk().canInspect(properties))return cdk().VALIDATION_SUCCESS;const errors=new(cdk()).ValidationResults;return properties&&typeof properties=="object"&&!Array.isArray(properties)||errors.collect(new(cdk()).ValidationResult("Expected an object, but received: "+JSON.stringify(properties))),errors.collect(cdk().propertyValidator("authType",cdk().requiredValidator)(properties.authType)),errors.collect(cdk().propertyValidator("authType",cdk().validateString)(properties.authType)),errors.collect(cdk().propertyValidator("cognitoConfig",CfnApiCognitoConfigPropertyValidator)(properties.cognitoConfig)),errors.collect(cdk().propertyValidator("lambdaAuthorizerConfig",CfnApiLambdaAuthorizerConfigPropertyValidator)(properties.lambdaAuthorizerConfig)),errors.collect(cdk().propertyValidator("openIdConnectConfig",CfnApiOpenIDConnectConfigPropertyValidator)(properties.openIdConnectConfig)),errors.wrap('supplied properties not correct for "AuthProviderProperty"')}function convertCfnApiAuthProviderPropertyToCloudFormation(properties){return cdk().canInspect(properties)?(CfnApiAuthProviderPropertyValidator(properties).assertSuccess(),{AuthType:cdk().stringToCloudFormation(properties.authType),CognitoConfig:convertCfnApiCognitoConfigPropertyToCloudFormation(properties.cognitoConfig),LambdaAuthorizerConfig:convertCfnApiLambdaAuthorizerConfigPropertyToCloudFormation(properties.lambdaAuthorizerConfig),OpenIDConnectConfig:convertCfnApiOpenIDConnectConfigPropertyToCloudFormation(properties.openIdConnectConfig)}):properties}function CfnApiAuthProviderPropertyFromCloudFormation(properties){if(cdk().isResolvableObject(properties))return new(cfn_parse()).FromCloudFormationResult(properties);if(properties=properties??{},!(properties&&typeof properties=="object"&&!Array.isArray(properties)))return new(cfn_parse()).FromCloudFormationResult(properties);const ret=new(cfn_parse()).FromCloudFormationPropertyObject;return ret.addPropertyResult("authType","AuthType",properties.AuthType!=null?cfn_parse().FromCloudFormation.getString(properties.AuthType):void 0),ret.addPropertyResult("cognitoConfig","CognitoConfig",properties.CognitoConfig!=null?CfnApiCognitoConfigPropertyFromCloudFormation(properties.CognitoConfig):void 0),ret.addPropertyResult("lambdaAuthorizerConfig","LambdaAuthorizerConfig",properties.LambdaAuthorizerConfig!=null?CfnApiLambdaAuthorizerConfigPropertyFromCloudFormation(properties.LambdaAuthorizerConfig):void 0),ret.addPropertyResult("openIdConnectConfig","OpenIDConnectConfig",properties.OpenIDConnectConfig!=null?CfnApiOpenIDConnectConfigPropertyFromCloudFormation(properties.OpenIDConnectConfig):void 0),ret.addUnrecognizedPropertiesAsExtra(properties),ret}function CfnApiAuthModePropertyValidator(properties){if(!cdk().canInspect(properties))return cdk().VALIDATION_SUCCESS;const errors=new(cdk()).ValidationResults;return properties&&typeof properties=="object"&&!Array.isArray(properties)||errors.collect(new(cdk()).ValidationResult("Expected an object, but received: "+JSON.stringify(properties))),errors.collect(cdk().propertyValidator("authType",cdk().validateString)(properties.authType)),errors.wrap('supplied properties not correct for "AuthModeProperty"')}function convertCfnApiAuthModePropertyToCloudFormation(properties){return cdk().canInspect(properties)?(CfnApiAuthModePropertyValidator(properties).assertSuccess(),{AuthType:cdk().stringToCloudFormation(properties.authType)}):properties}function CfnApiAuthModePropertyFromCloudFormation(properties){if(cdk().isResolvableObject(properties))return new(cfn_parse()).FromCloudFormationResult(properties);if(properties=properties??{},!(properties&&typeof properties=="object"&&!Array.isArray(properties)))return new(cfn_parse()).FromCloudFormationResult(properties);const ret=new(cfn_parse()).FromCloudFormationPropertyObject;return ret.addPropertyResult("authType","AuthType",properties.AuthType!=null?cfn_parse().FromCloudFormation.getString(properties.AuthType):void 0),ret.addUnrecognizedPropertiesAsExtra(properties),ret}function CfnApiEventLogConfigPropertyValidator(properties){if(!cdk().canInspect(properties))return cdk().VALIDATION_SUCCESS;const errors=new(cdk()).ValidationResults;return properties&&typeof properties=="object"&&!Array.isArray(properties)||errors.collect(new(cdk()).ValidationResult("Expected an object, but received: "+JSON.stringify(properties))),errors.collect(cdk().propertyValidator("cloudWatchLogsRoleArn",cdk().requiredValidator)(properties.cloudWatchLogsRoleArn)),errors.collect(cdk().propertyValidator("cloudWatchLogsRoleArn",cdk().validateString)(properties.cloudWatchLogsRoleArn)),errors.collect(cdk().propertyValidator("logLevel",cdk().requiredValidator)(properties.logLevel)),errors.collect(cdk().propertyValidator("logLevel",cdk().validateString)(properties.logLevel)),errors.wrap('supplied properties not correct for "EventLogConfigProperty"')}function convertCfnApiEventLogConfigPropertyToCloudFormation(properties){return cdk().canInspect(properties)?(CfnApiEventLogConfigPropertyValidator(properties).assertSuccess(),{CloudWatchLogsRoleArn:cdk().stringToCloudFormation(properties.cloudWatchLogsRoleArn),LogLevel:cdk().stringToCloudFormation(properties.logLevel)}):properties}function CfnApiEventLogConfigPropertyFromCloudFormation(properties){if(cdk().isResolvableObject(properties))return new(cfn_parse()).FromCloudFormationResult(properties);if(properties=properties??{},!(properties&&typeof properties=="object"&&!Array.isArray(properties)))return new(cfn_parse()).FromCloudFormationResult(properties);const ret=new(cfn_parse()).FromCloudFormationPropertyObject;return ret.addPropertyResult("cloudWatchLogsRoleArn","CloudWatchLogsRoleArn",properties.CloudWatchLogsRoleArn!=null?cfn_parse().FromCloudFormation.getString(properties.CloudWatchLogsRoleArn):void 0),ret.addPropertyResult("logLevel","LogLevel",properties.LogLevel!=null?cfn_parse().FromCloudFormation.getString(properties.LogLevel):void 0),ret.addUnrecognizedPropertiesAsExtra(properties),ret}function CfnApiEventConfigPropertyValidator(properties){if(!cdk().canInspect(properties))return cdk().VALIDATION_SUCCESS;const errors=new(cdk()).ValidationResults;return properties&&typeof properties=="object"&&!Array.isArray(properties)||errors.collect(new(cdk()).ValidationResult("Expected an object, but received: "+JSON.stringify(properties))),errors.collect(cdk().propertyValidator("authProviders",cdk().requiredValidator)(properties.authProviders)),errors.collect(cdk().propertyValidator("authProviders",cdk().listValidator(CfnApiAuthProviderPropertyValidator))(properties.authProviders)),errors.collect(cdk().propertyValidator("connectionAuthModes",cdk().requiredValidator)(properties.connectionAuthModes)),errors.collect(cdk().propertyValidator("connectionAuthModes",cdk().listValidator(CfnApiAuthModePropertyValidator))(properties.connectionAuthModes)),errors.collect(cdk().propertyValidator("defaultPublishAuthModes",cdk().requiredValidator)(properties.defaultPublishAuthModes)),errors.collect(cdk().propertyValidator("defaultPublishAuthModes",cdk().listValidator(CfnApiAuthModePropertyValidator))(properties.defaultPublishAuthModes)),errors.collect(cdk().propertyValidator("defaultSubscribeAuthModes",cdk().requiredValidator)(properties.defaultSubscribeAuthModes)),errors.collect(cdk().propertyValidator("defaultSubscribeAuthModes",cdk().listValidator(CfnApiAuthModePropertyValidator))(properties.defaultSubscribeAuthModes)),errors.collect(cdk().propertyValidator("logConfig",CfnApiEventLogConfigPropertyValidator)(properties.logConfig)),errors.wrap('supplied properties not correct for "EventConfigProperty"')}function convertCfnApiEventConfigPropertyToCloudFormation(properties){return cdk().canInspect(properties)?(CfnApiEventConfigPropertyValidator(properties).assertSuccess(),{AuthProviders:cdk().listMapper(convertCfnApiAuthProviderPropertyToCloudFormation)(properties.authProviders),ConnectionAuthModes:cdk().listMapper(convertCfnApiAuthModePropertyToCloudFormation)(properties.connectionAuthModes),DefaultPublishAuthModes:cdk().listMapper(convertCfnApiAuthModePropertyToCloudFormation)(properties.defaultPublishAuthModes),DefaultSubscribeAuthModes:cdk().listMapper(convertCfnApiAuthModePropertyToCloudFormation)(properties.defaultSubscribeAuthModes),LogConfig:convertCfnApiEventLogConfigPropertyToCloudFormation(properties.logConfig)}):properties}function CfnApiEventConfigPropertyFromCloudFormation(properties){if(cdk().isResolvableObject(properties))return new(cfn_parse()).FromCloudFormationResult(properties);if(properties=properties??{},!(properties&&typeof properties=="object"&&!Array.isArray(properties)))return new(cfn_parse()).FromCloudFormationResult(properties);const ret=new(cfn_parse()).FromCloudFormationPropertyObject;return ret.addPropertyResult("authProviders","AuthProviders",properties.AuthProviders!=null?cfn_parse().FromCloudFormation.getArray(CfnApiAuthProviderPropertyFromCloudFormation)(properties.AuthProviders):void 0),ret.addPropertyResult("connectionAuthModes","ConnectionAuthModes",properties.ConnectionAuthModes!=null?cfn_parse().FromCloudFormation.getArray(CfnApiAuthModePropertyFromCloudFormation)(properties.ConnectionAuthModes):void 0),ret.addPropertyResult("defaultPublishAuthModes","DefaultPublishAuthModes",properties.DefaultPublishAuthModes!=null?cfn_parse().FromCloudFormation.getArray(CfnApiAuthModePropertyFromCloudFormation)(properties.DefaultPublishAuthModes):void 0),ret.addPropertyResult("defaultSubscribeAuthModes","DefaultSubscribeAuthModes",properties.DefaultSubscribeAuthModes!=null?cfn_parse().FromCloudFormation.getArray(CfnApiAuthModePropertyFromCloudFormation)(properties.DefaultSubscribeAuthModes):void 0),ret.addPropertyResult("logConfig","LogConfig",properties.LogConfig!=null?CfnApiEventLogConfigPropertyFromCloudFormation(properties.LogConfig):void 0),ret.addUnrecognizedPropertiesAsExtra(properties),ret}function CfnApiDnsMapPropertyValidator(properties){if(!cdk().canInspect(properties))return cdk().VALIDATION_SUCCESS;const errors=new(cdk()).ValidationResults;return properties&&typeof properties=="object"&&!Array.isArray(properties)||errors.collect(new(cdk()).ValidationResult("Expected an object, but received: "+JSON.stringify(properties))),errors.collect(cdk().propertyValidator("http",cdk().validateString)(properties.http)),errors.collect(cdk().propertyValidator("realtime",cdk().validateString)(properties.realtime)),errors.wrap('supplied properties not correct for "DnsMapProperty"')}function convertCfnApiDnsMapPropertyToCloudFormation(properties){return cdk().canInspect(properties)?(CfnApiDnsMapPropertyValidator(properties).assertSuccess(),{Http:cdk().stringToCloudFormation(properties.http),Realtime:cdk().stringToCloudFormation(properties.realtime)}):properties}function CfnApiDnsMapPropertyFromCloudFormation(properties){if(cdk().isResolvableObject(properties))return new(cfn_parse()).FromCloudFormationResult(properties);if(properties=properties??{},!(properties&&typeof properties=="object"&&!Array.isArray(properties)))return new(cfn_parse()).FromCloudFormationResult(properties);const ret=new(cfn_parse()).FromCloudFormationPropertyObject;return ret.addPropertyResult("http","Http",properties.Http!=null?cfn_parse().FromCloudFormation.getString(properties.Http):void 0),ret.addPropertyResult("realtime","Realtime",properties.Realtime!=null?cfn_parse().FromCloudFormation.getString(properties.Realtime):void 0),ret.addUnrecognizedPropertiesAsExtra(properties),ret}function CfnApiPropsValidator(properties){if(!cdk().canInspect(properties))return cdk().VALIDATION_SUCCESS;const errors=new(cdk()).ValidationResults;return properties&&typeof properties=="object"&&!Array.isArray(properties)||errors.collect(new(cdk()).ValidationResult("Expected an object, but received: "+JSON.stringify(properties))),errors.collect(cdk().propertyValidator("eventConfig",CfnApiEventConfigPropertyValidator)(properties.eventConfig)),errors.collect(cdk().propertyValidator("name",cdk().requiredValidator)(properties.name)),errors.collect(cdk().propertyValidator("name",cdk().validateString)(properties.name)),errors.collect(cdk().propertyValidator("ownerContact",cdk().validateString)(properties.ownerContact)),errors.collect(cdk().propertyValidator("tags",cdk().listValidator(cdk().validateCfnTag))(properties.tags)),errors.wrap('supplied properties not correct for "CfnApiProps"')}function convertCfnApiPropsToCloudFormation(properties){return cdk().canInspect(properties)?(CfnApiPropsValidator(properties).assertSuccess(),{EventConfig:convertCfnApiEventConfigPropertyToCloudFormation(properties.eventConfig),Name:cdk().stringToCloudFormation(properties.name),OwnerContact:cdk().stringToCloudFormation(properties.ownerContact),Tags:cdk().listMapper(cdk().cfnTagToCloudFormation)(properties.tags)}):properties}function CfnApiPropsFromCloudFormation(properties){if(cdk().isResolvableObject(properties))return new(cfn_parse()).FromCloudFormationResult(properties);if(properties=properties??{},!(properties&&typeof properties=="object"&&!Array.isArray(properties)))return new(cfn_parse()).FromCloudFormationResult(properties);const ret=new(cfn_parse()).FromCloudFormationPropertyObject;return ret.addPropertyResult("eventConfig","EventConfig",properties.EventConfig!=null?CfnApiEventConfigPropertyFromCloudFormation(properties.EventConfig):void 0),ret.addPropertyResult("name","Name",properties.Name!=null?cfn_parse().FromCloudFormation.getString(properties.Name):void 0),ret.addPropertyResult("ownerContact","OwnerContact",properties.OwnerContact!=null?cfn_parse().FromCloudFormation.getString(properties.OwnerContact):void 0),ret.addPropertyResult("tags","Tags",properties.Tags!=null?cfn_parse().FromCloudFormation.getArray(cfn_parse().FromCloudFormation.getCfnTag)(properties.Tags):void 0),ret.addUnrecognizedPropertiesAsExtra(properties),ret}class CfnChannelNamespace extends cdk().CfnResource{static _fromCloudFormation(scope,id,resourceAttributes,options){resourceAttributes=resourceAttributes||{};const resourceProperties=options.parser.parseValue(resourceAttributes.Properties),propsResult=CfnChannelNamespacePropsFromCloudFormation(resourceProperties);if(cdk().isResolvableObject(propsResult.value))throw new(cdk_errors()).ValidationError("Unexpected IResolvable",scope);const ret=new CfnChannelNamespace(scope,id,propsResult.value);for(const[propKey,propVal]of Object.entries(propsResult.extraProperties))ret.addPropertyOverride(propKey,propVal);return options.parser.handleAttributes(ret,resourceAttributes,id),ret}constructor(scope,id,props){super(scope,id,{type:CfnChannelNamespace.CFN_RESOURCE_TYPE_NAME,properties:props});try{jsiiDeprecationWarnings().aws_cdk_lib_aws_appsync_CfnChannelNamespaceProps(props)}catch(error){throw process.env.JSII_DEBUG!=="1"&&error.name==="DeprecationError"&&Error.captureStackTrace(error,CfnChannelNamespace),error}cdk().requireProperty(props,"apiId",this),cdk().requireProperty(props,"name",this),this.attrChannelNamespaceArn=cdk().Token.asString(this.getAtt("ChannelNamespaceArn",cdk().ResolutionTypeHint.STRING)),this.apiId=props.apiId,this.cdkTagManager=new(cdk()).TagManager(cdk().TagType.STANDARD,"AWS::AppSync::ChannelNamespace",void 0,{tagPropertyName:"tags"}),this.codeHandlers=props.codeHandlers,this.codeS3Location=props.codeS3Location,this.handlerConfigs=props.handlerConfigs,this.name=props.name,this.publishAuthModes=props.publishAuthModes,this.subscribeAuthModes=props.subscribeAuthModes,this.tags=props.tags}get cfnProperties(){return{apiId:this.apiId,tags:this.cdkTagManager.renderTags(this.tags),codeHandlers:this.codeHandlers,codeS3Location:this.codeS3Location,handlerConfigs:this.handlerConfigs,name:this.name,publishAuthModes:this.publishAuthModes,subscribeAuthModes:this.subscribeAuthModes}}inspect(inspector){try{jsiiDeprecationWarnings().aws_cdk_lib_TreeInspector(inspector)}catch(error){throw process.env.JSII_DEBUG!=="1"&&error.name==="DeprecationError"&&Error.captureStackTrace(error,this.inspect),error}inspector.addAttribute("aws:cdk:cloudformation:type",CfnChannelNamespace.CFN_RESOURCE_TYPE_NAME),inspector.addAttribute("aws:cdk:cloudformation:props",this.cfnProperties)}renderProperties(props){return convertCfnChannelNamespacePropsToCloudFormation(props)}}exports.CfnChannelNamespace=CfnChannelNamespace,_m=JSII_RTTI_SYMBOL_1,CfnChannelNamespace[_m]={fqn:"aws-cdk-lib.aws_appsync.CfnChannelNamespace",version:"2.201.0"},CfnChannelNamespace.CFN_RESOURCE_TYPE_NAME="AWS::AppSync::ChannelNamespace";function CfnChannelNamespaceAuthModePropertyValidator(properties){if(!cdk().canInspect(properties))return cdk().VALIDATION_SUCCESS;const errors=new(cdk()).ValidationResults;return properties&&typeof properties=="object"&&!Array.isArray(properties)||errors.collect(new(cdk()).ValidationResult("Expected an object, but received: "+JSON.stringify(properties))),errors.collect(cdk().propertyValidator("authType",cdk().validateString)(properties.authType)),errors.wrap('supplied properties not correct for "AuthModeProperty"')}function convertCfnChannelNamespaceAuthModePropertyToCloudFormation(properties){return cdk().canInspect(properties)?(CfnChannelNamespaceAuthModePropertyValidator(properties).assertSuccess(),{AuthType:cdk().stringToCloudFormation(properties.authType)}):properties}function CfnChannelNamespaceAuthModePropertyFromCloudFormation(properties){if(cdk().isResolvableObject(properties))return new(cfn_parse()).FromCloudFormationResult(properties);if(properties=properties??{},!(properties&&typeof properties=="object"&&!Array.isArray(properties)))return new(cfn_parse()).FromCloudFormationResult(properties);const ret=new(cfn_parse()).FromCloudFormationPropertyObject;return ret.addPropertyResult("authType","AuthType",properties.AuthType!=null?cfn_parse().FromCloudFormation.getString(properties.AuthType):void 0),ret.addUnrecognizedPropertiesAsExtra(properties),ret}function CfnChannelNamespaceLambdaConfigPropertyValidator(properties){if(!cdk().canInspect(properties))return cdk().VALIDATION_SUCCESS;const errors=new(cdk()).ValidationResults;return properties&&typeof properties=="object"&&!Array.isArray(properties)||errors.collect(new(cdk()).ValidationResult("Expected an object, but received: "+JSON.stringify(properties))),errors.collect(cdk().propertyValidator("invokeType",cdk().requiredValidator)(properties.invokeType)),errors.collect(cdk().propertyValidator("invokeType",cdk().validateString)(properties.invokeType)),errors.wrap('supplied properties not correct for "LambdaConfigProperty"')}function convertCfnChannelNamespaceLambdaConfigPropertyToCloudFormation(properties){return cdk().canInspect(properties)?(CfnChannelNamespaceLambdaConfigPropertyValidator(properties).assertSuccess(),{InvokeType:cdk().stringToCloudFormation(properties.invokeType)}):properties}function CfnChannelNamespaceLambdaConfigPropertyFromCloudFormation(properties){if(cdk().isResolvableObject(properties))return new(cfn_parse()).FromCloudFormationResult(properties);if(properties=properties??{},!(properties&&typeof properties=="object"&&!Array.isArray(properties)))return new(cfn_parse()).FromCloudFormationResult(properties);const ret=new(cfn_parse()).FromCloudFormationPropertyObject;return ret.addPropertyResult("invokeType","InvokeType",properties.InvokeType!=null?cfn_parse().FromCloudFormation.getString(properties.InvokeType):void 0),ret.addUnrecognizedPropertiesAsExtra(properties),ret}function CfnChannelNamespaceIntegrationPropertyValidator(properties){if(!cdk().canInspect(properties))return cdk().VALIDATION_SUCCESS;const errors=new(cdk()).ValidationResults;return properties&&typeof properties=="object"&&!Array.isArray(properties)||errors.collect(new(cdk()).ValidationResult("Expected an object, but received: "+JSON.stringify(properties))),errors.collect(cdk().propertyValidator("dataSourceName",cdk().requiredValidator)(properties.dataSourceName)),errors.collect(cdk().propertyValidator("dataSourceName",cdk().validateString)(properties.dataSourceName)),errors.collect(cdk().propertyValidator("lambdaConfig",CfnChannelNamespaceLambdaConfigPropertyValidator)(properties.lambdaConfig)),errors.wrap('supplied properties not correct for "IntegrationProperty"')}function convertCfnChannelNamespaceIntegrationPropertyToCloudFormation(properties){return cdk().canInspect(properties)?(CfnChannelNamespaceIntegrationPropertyValidator(properties).assertSuccess(),{DataSourceName:cdk().stringToCloudFormation(properties.dataSourceName),LambdaConfig:convertCfnChannelNamespaceLambdaConfigPropertyToCloudFormation(properties.lambdaConfig)}):properties}function CfnChannelNamespaceIntegrationPropertyFromCloudFormation(properties){if(cdk().isResolvableObject(properties))return new(cfn_parse()).FromCloudFormationResult(properties);if(properties=properties??{},!(properties&&typeof properties=="object"&&!Array.isArray(properties)))return new(cfn_parse()).FromCloudFormationResult(properties);const ret=new(cfn_parse()).FromCloudFormationPropertyObject;return ret.addPropertyResult("dataSourceName","DataSourceName",properties.DataSourceName!=null?cfn_parse().FromCloudFormation.getString(properties.DataSourceName):void 0),ret.addPropertyResult("lambdaConfig","LambdaConfig",properties.LambdaConfig!=null?CfnChannelNamespaceLambdaConfigPropertyFromCloudFormation(properties.LambdaConfig):void 0),ret.addUnrecognizedPropertiesAsExtra(properties),ret}function CfnChannelNamespaceHandlerConfigPropertyValidator(properties){if(!cdk().canInspect(properties))return cdk().VALIDATION_SUCCESS;const errors=new(cdk()).ValidationResults;return properties&&typeof properties=="object"&&!Array.isArray(properties)||errors.collect(new(cdk()).ValidationResult("Expected an object, but received: "+JSON.stringify(properties))),errors.collect(cdk().propertyValidator("behavior",cdk().requiredValidator)(properties.behavior)),errors.collect(cdk().propertyValidator("behavior",cdk().validateString)(properties.behavior)),errors.collect(cdk().propertyValidator("integration",cdk().requiredValidator)(properties.integration)),errors.collect(cdk().propertyValidator("integration",CfnChannelNamespaceIntegrationPropertyValidator)(properties.integration)),errors.wrap('supplied properties not correct for "HandlerConfigProperty"')}function convertCfnChannelNamespaceHandlerConfigPropertyToCloudFormation(properties){return cdk().canInspect(properties)?(CfnChannelNamespaceHandlerConfigPropertyValidator(properties).assertSuccess(),{Behavior:cdk().stringToCloudFormation(properties.behavior),Integration:convertCfnChannelNamespaceIntegrationPropertyToCloudFormation(properties.integration)}):properties}function CfnChannelNamespaceHandlerConfigPropertyFromCloudFormation(properties){if(cdk().isResolvableObject(properties))return new(cfn_parse()).FromCloudFormationResult(properties);if(properties=properties??{},!(properties&&typeof properties=="object"&&!Array.isArray(properties)))return new(cfn_parse()).FromCloudFormationResult(properties);const ret=new(cfn_parse()).FromCloudFormationPropertyObject;return ret.addPropertyResult("behavior","Behavior",properties.Behavior!=null?cfn_parse().FromCloudFormation.getString(properties.Behavior):void 0),ret.addPropertyResult("integration","Integration",properties.Integration!=null?CfnChannelNamespaceIntegrationPropertyFromCloudFormation(properties.Integration):void 0),ret.addUnrecognizedPropertiesAsExtra(properties),ret}function CfnChannelNamespaceHandlerConfigsPropertyValidator(properties){if(!cdk().canInspect(properties))return cdk().VALIDATION_SUCCESS;const errors=new(cdk()).ValidationResults;return properties&&typeof properties=="object"&&!Array.isArray(properties)||errors.collect(new(cdk()).ValidationResult("Expected an object, but received: "+JSON.stringify(properties))),errors.collect(cdk().propertyValidator("onPublish",CfnChannelNamespaceHandlerConfigPropertyValidator)(properties.onPublish)),errors.collect(cdk().propertyValidator("onSubscribe",CfnChannelNamespaceHandlerConfigPropertyValidator)(properties.onSubscribe)),errors.wrap('supplied properties not correct for "HandlerConfigsProperty"')}function convertCfnChannelNamespaceHandlerConfigsPropertyToCloudFormation(properties){return cdk().canInspect(properties)?(CfnChannelNamespaceHandlerConfigsPropertyValidator(properties).assertSuccess(),{OnPublish:convertCfnChannelNamespaceHandlerConfigPropertyToCloudFormation(properties.onPublish),OnSubscribe:convertCfnChannelNamespaceHandlerConfigPropertyToCloudFormation(properties.onSubscribe)}):properties}function CfnChannelNamespaceHandlerConfigsPropertyFromCloudFormation(properties){if(cdk().isResolvableObject(properties))return new(cfn_parse()).FromCloudFormationResult(properties);if(properties=properties??{},!(properties&&typeof properties=="object"&&!Array.isArray(properties)))return new(cfn_parse()).FromCloudFormationResult(properties);const ret=new(cfn_parse()).FromCloudFormationPropertyObject;return ret.addPropertyResult("onPublish","OnPublish",properties.OnPublish!=null?CfnChannelNamespaceHandlerConfigPropertyFromCloudFormation(properties.OnPublish):void 0),ret.addPropertyResult("onSubscribe","OnSubscribe",properties.OnSubscribe!=null?CfnChannelNamespaceHandlerConfigPropertyFromCloudFormation(properties.OnSubscribe):void 0),ret.addUnrecognizedPropertiesAsExtra(properties),ret}function CfnChannelNamespacePropsValidator(properties){if(!cdk().canInspect(properties))return cdk().VALIDATION_SUCCESS;const errors=new(cdk()).ValidationResults;return properties&&typeof properties=="object"&&!Array.isArray(properties)||errors.collect(new(cdk()).ValidationResult("Expected an object, but received: "+JSON.stringify(properties))),errors.collect(cdk().propertyValidator("apiId",cdk().requiredValidator)(properties.apiId)),errors.collect(cdk().propertyValidator("apiId",cdk().validateString)(properties.apiId)),errors.collect(cdk().propertyValidator("codeHandlers",cdk().validateString)(properties.codeHandlers)),errors.collect(cdk().propertyValidator("codeS3Location",cdk().validateString)(properties.codeS3Location)),errors.collect(cdk().propertyValidator("handlerConfigs",CfnChannelNamespaceHandlerConfigsPropertyValidator)(properties.handlerConfigs)),errors.collect(cdk().propertyValidator("name",cdk().requiredValidator)(properties.name)),errors.collect(cdk().propertyValidator("name",cdk().validateString)(properties.name)),errors.collect(cdk().propertyValidator("publishAuthModes",cdk().listValidator(CfnChannelNamespaceAuthModePropertyValidator))(properties.publishAuthModes)),errors.collect(cdk().propertyValidator("subscribeAuthModes",cdk().listValidator(CfnChannelNamespaceAuthModePropertyValidator))(properties.subscribeAuthModes)),errors.collect(cdk().propertyValidator("tags",cdk().listValidator(cdk().validateCfnTag))(properties.tags)),errors.wrap('supplied properties not correct for "CfnChannelNamespaceProps"')}function convertCfnChannelNamespacePropsToCloudFormation(properties){return cdk().canInspect(properties)?(CfnChannelNamespacePropsValidator(properties).assertSuccess(),{ApiId:cdk().stringToCloudFormation(properties.apiId),CodeHandlers:cdk().stringToCloudFormation(properties.codeHandlers),CodeS3Location:cdk().stringToCloudFormation(properties.codeS3Location),HandlerConfigs:convertCfnChannelNamespaceHandlerConfigsPropertyToCloudFormation(properties.handlerConfigs),Name:cdk().stringToCloudFormation(properties.name),PublishAuthModes:cdk().listMapper(convertCfnChannelNamespaceAuthModePropertyToCloudFormation)(properties.publishAuthModes),SubscribeAuthModes:cdk().listMapper(convertCfnChannelNamespaceAuthModePropertyToCloudFormation)(properties.subscribeAuthModes),Tags:cdk().listMapper(cdk().cfnTagToCloudFormation)(properties.tags)}):properties}function CfnChannelNamespacePropsFromCloudFormation(properties){if(cdk().isResolvableObject(properties))return new(cfn_parse()).FromCloudFormationResult(properties);if(properties=properties??{},!(properties&&typeof properties=="object"&&!Array.isArray(properties)))return new(cfn_parse()).FromCloudFormationResult(properties);const ret=new(cfn_parse()).FromCloudFormationPropertyObject;return ret.addPropertyResult("apiId","ApiId",properties.ApiId!=null?cfn_parse().FromCloudFormation.getString(properties.ApiId):void 0),ret.addPropertyResult("codeHandlers","CodeHandlers",properties.CodeHandlers!=null?cfn_parse().FromCloudFormation.getString(properties.CodeHandlers):void 0),ret.addPropertyResult("codeS3Location","CodeS3Location",properties.CodeS3Location!=null?cfn_parse().FromCloudFormation.getString(properties.CodeS3Location):void 0),ret.addPropertyResult("handlerConfigs","HandlerConfigs",properties.HandlerConfigs!=null?CfnChannelNamespaceHandlerConfigsPropertyFromCloudFormation(properties.HandlerConfigs):void 0),ret.addPropertyResult("name","Name",properties.Name!=null?cfn_parse().FromCloudFormation.getString(properties.Name):void 0),ret.addPropertyResult("publishAuthModes","PublishAuthModes",properties.PublishAuthModes!=null?cfn_parse().FromCloudFormation.getArray(CfnChannelNamespaceAuthModePropertyFromCloudFormation)(properties.PublishAuthModes):void 0),ret.addPropertyResult("subscribeAuthModes","SubscribeAuthModes",properties.SubscribeAuthModes!=null?cfn_parse().FromCloudFormation.getArray(CfnChannelNamespaceAuthModePropertyFromCloudFormation)(properties.SubscribeAuthModes):void 0),ret.addPropertyResult("tags","Tags",properties.Tags!=null?cfn_parse().FromCloudFormation.getArray(cfn_parse().FromCloudFormation.getCfnTag)(properties.Tags):void 0),ret.addUnrecognizedPropertiesAsExtra(properties),ret}
