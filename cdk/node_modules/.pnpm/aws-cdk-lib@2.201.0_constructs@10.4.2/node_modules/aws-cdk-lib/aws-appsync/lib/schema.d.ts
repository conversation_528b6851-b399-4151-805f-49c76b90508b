import { IGraphqlApi } from './graphqlapi-base';
/**
 * Configuration for bound graphql schema
 *
 * Returned from ISchema.bind allowing late binding of schemas to graphqlapi-base
 */
export interface ISchemaConfig {
    /**
     * The ID of the api the schema is bound to
     */
    apiId: string;
    /**
     * The schema definition string
     */
    definition: string;
}
/**
 * Used for configuring schema bind behavior.
 *
 * This is intended to prevent breaking changes to implementors of ISchema
 * if needing to add new behavior.
 */
export interface SchemaBindOptions {
}
/**
 * Interface for implementing your own schema
 *
 * Useful for providing schema's from sources other than assets
 */
export interface ISchema {
    /**
     * Binds a schema string to a GraphQlApi
     *
     * @returns ISchemaConfig with apiId and schema definition string
     * @param api the api to bind the schema to
     * @param options configuration for bind behavior
     */
    bind(api: IGraphqlApi, options?: SchemaBindOptions): ISchemaConfig;
}
/**
 * The options for configuring a schema from an existing file
 */
export interface SchemaProps {
    /**
     * The file path for the schema. When this option is
     * configured, then the schema will be generated from an
     * existing file from disk.
     */
    readonly filePath: string;
}
/**
 * The Schema for a GraphQL Api
 *
 * If no options are configured, schema will be generated
 * code-first.
 */
export declare class SchemaFile implements ISchema {
    /**
     * Generate a Schema from file
     *
     * @returns `SchemaAsset` with immutable schema defintion
     * @param filePath the file path of the schema file
     */
    static fromAsset(filePath: string): SchemaFile;
    /**
     * The definition for this schema
     */
    definition: string;
    constructor(options: SchemaProps);
    /**
     * Called when the GraphQL Api is initialized to allow this object to bind
     * to the stack.
     *
     * @param api The binding GraphQL Api
     */
    bind(api: IGraphqlApi, _options?: SchemaBindOptions): ISchemaConfig;
}
