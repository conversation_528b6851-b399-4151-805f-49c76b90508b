"use strict";var _a,_b,_c,_d;Object.defineProperty(exports,"__esModule",{value:!0}),exports.CfnSubscriberNotification=exports.CfnSubscriber=exports.CfnDataLake=exports.CfnAwsLogSource=void 0;var jsiiDeprecationWarnings=()=>{var tmp=require("../../.warnings.jsii.js");return jsiiDeprecationWarnings=()=>tmp,tmp};const JSII_RTTI_SYMBOL_1=Symbol.for("jsii.rtti");var cdk=()=>{var tmp=require("../../core");return cdk=()=>tmp,tmp},cfn_parse=()=>{var tmp=require("../../core/lib/helpers-internal");return cfn_parse=()=>tmp,tmp},cdk_errors=()=>{var tmp=require("../../core/lib/errors");return cdk_errors=()=>tmp,tmp};class CfnAwsLogSource extends cdk().CfnResource{static _fromCloudFormation(scope,id,resourceAttributes,options){resourceAttributes=resourceAttributes||{};const resourceProperties=options.parser.parseValue(resourceAttributes.Properties),propsResult=CfnAwsLogSourcePropsFromCloudFormation(resourceProperties);if(cdk().isResolvableObject(propsResult.value))throw new(cdk_errors()).ValidationError("Unexpected IResolvable",scope);const ret=new CfnAwsLogSource(scope,id,propsResult.value);for(const[propKey,propVal]of Object.entries(propsResult.extraProperties))ret.addPropertyOverride(propKey,propVal);return options.parser.handleAttributes(ret,resourceAttributes,id),ret}constructor(scope,id,props){super(scope,id,{type:CfnAwsLogSource.CFN_RESOURCE_TYPE_NAME,properties:props});try{jsiiDeprecationWarnings().aws_cdk_lib_aws_securitylake_CfnAwsLogSourceProps(props)}catch(error){throw process.env.JSII_DEBUG!=="1"&&error.name==="DeprecationError"&&Error.captureStackTrace(error,CfnAwsLogSource),error}cdk().requireProperty(props,"dataLakeArn",this),cdk().requireProperty(props,"sourceName",this),cdk().requireProperty(props,"sourceVersion",this),this.accounts=props.accounts,this.dataLakeArn=props.dataLakeArn,this.sourceName=props.sourceName,this.sourceVersion=props.sourceVersion}get cfnProperties(){return{accounts:this.accounts,dataLakeArn:this.dataLakeArn,sourceName:this.sourceName,sourceVersion:this.sourceVersion}}inspect(inspector){try{jsiiDeprecationWarnings().aws_cdk_lib_TreeInspector(inspector)}catch(error){throw process.env.JSII_DEBUG!=="1"&&error.name==="DeprecationError"&&Error.captureStackTrace(error,this.inspect),error}inspector.addAttribute("aws:cdk:cloudformation:type",CfnAwsLogSource.CFN_RESOURCE_TYPE_NAME),inspector.addAttribute("aws:cdk:cloudformation:props",this.cfnProperties)}renderProperties(props){return convertCfnAwsLogSourcePropsToCloudFormation(props)}}exports.CfnAwsLogSource=CfnAwsLogSource,_a=JSII_RTTI_SYMBOL_1,CfnAwsLogSource[_a]={fqn:"aws-cdk-lib.aws_securitylake.CfnAwsLogSource",version:"2.201.0"},CfnAwsLogSource.CFN_RESOURCE_TYPE_NAME="AWS::SecurityLake::AwsLogSource";function CfnAwsLogSourcePropsValidator(properties){if(!cdk().canInspect(properties))return cdk().VALIDATION_SUCCESS;const errors=new(cdk()).ValidationResults;return properties&&typeof properties=="object"&&!Array.isArray(properties)||errors.collect(new(cdk()).ValidationResult("Expected an object, but received: "+JSON.stringify(properties))),errors.collect(cdk().propertyValidator("accounts",cdk().listValidator(cdk().validateString))(properties.accounts)),errors.collect(cdk().propertyValidator("dataLakeArn",cdk().requiredValidator)(properties.dataLakeArn)),errors.collect(cdk().propertyValidator("dataLakeArn",cdk().validateString)(properties.dataLakeArn)),errors.collect(cdk().propertyValidator("sourceName",cdk().requiredValidator)(properties.sourceName)),errors.collect(cdk().propertyValidator("sourceName",cdk().validateString)(properties.sourceName)),errors.collect(cdk().propertyValidator("sourceVersion",cdk().requiredValidator)(properties.sourceVersion)),errors.collect(cdk().propertyValidator("sourceVersion",cdk().validateString)(properties.sourceVersion)),errors.wrap('supplied properties not correct for "CfnAwsLogSourceProps"')}function convertCfnAwsLogSourcePropsToCloudFormation(properties){return cdk().canInspect(properties)?(CfnAwsLogSourcePropsValidator(properties).assertSuccess(),{Accounts:cdk().listMapper(cdk().stringToCloudFormation)(properties.accounts),DataLakeArn:cdk().stringToCloudFormation(properties.dataLakeArn),SourceName:cdk().stringToCloudFormation(properties.sourceName),SourceVersion:cdk().stringToCloudFormation(properties.sourceVersion)}):properties}function CfnAwsLogSourcePropsFromCloudFormation(properties){if(cdk().isResolvableObject(properties))return new(cfn_parse()).FromCloudFormationResult(properties);if(properties=properties??{},!(properties&&typeof properties=="object"&&!Array.isArray(properties)))return new(cfn_parse()).FromCloudFormationResult(properties);const ret=new(cfn_parse()).FromCloudFormationPropertyObject;return ret.addPropertyResult("accounts","Accounts",properties.Accounts!=null?cfn_parse().FromCloudFormation.getArray(cfn_parse().FromCloudFormation.getString)(properties.Accounts):void 0),ret.addPropertyResult("dataLakeArn","DataLakeArn",properties.DataLakeArn!=null?cfn_parse().FromCloudFormation.getString(properties.DataLakeArn):void 0),ret.addPropertyResult("sourceName","SourceName",properties.SourceName!=null?cfn_parse().FromCloudFormation.getString(properties.SourceName):void 0),ret.addPropertyResult("sourceVersion","SourceVersion",properties.SourceVersion!=null?cfn_parse().FromCloudFormation.getString(properties.SourceVersion):void 0),ret.addUnrecognizedPropertiesAsExtra(properties),ret}class CfnDataLake extends cdk().CfnResource{static _fromCloudFormation(scope,id,resourceAttributes,options){resourceAttributes=resourceAttributes||{};const resourceProperties=options.parser.parseValue(resourceAttributes.Properties),propsResult=CfnDataLakePropsFromCloudFormation(resourceProperties);if(cdk().isResolvableObject(propsResult.value))throw new(cdk_errors()).ValidationError("Unexpected IResolvable",scope);const ret=new CfnDataLake(scope,id,propsResult.value);for(const[propKey,propVal]of Object.entries(propsResult.extraProperties))ret.addPropertyOverride(propKey,propVal);return options.parser.handleAttributes(ret,resourceAttributes,id),ret}constructor(scope,id,props={}){super(scope,id,{type:CfnDataLake.CFN_RESOURCE_TYPE_NAME,properties:props});try{jsiiDeprecationWarnings().aws_cdk_lib_aws_securitylake_CfnDataLakeProps(props)}catch(error){throw process.env.JSII_DEBUG!=="1"&&error.name==="DeprecationError"&&Error.captureStackTrace(error,CfnDataLake),error}this.attrArn=cdk().Token.asString(this.getAtt("Arn",cdk().ResolutionTypeHint.STRING)),this.attrS3BucketArn=cdk().Token.asString(this.getAtt("S3BucketArn",cdk().ResolutionTypeHint.STRING)),this.cdkTagManager=new(cdk()).TagManager(cdk().TagType.STANDARD,"AWS::SecurityLake::DataLake",void 0,{tagPropertyName:"tags"}),this.encryptionConfiguration=props.encryptionConfiguration,this.lifecycleConfiguration=props.lifecycleConfiguration,this.metaStoreManagerRoleArn=props.metaStoreManagerRoleArn,this.replicationConfiguration=props.replicationConfiguration,this.tags=props.tags}get cfnProperties(){return{tags:this.cdkTagManager.renderTags(this.tags),encryptionConfiguration:this.encryptionConfiguration,lifecycleConfiguration:this.lifecycleConfiguration,metaStoreManagerRoleArn:this.metaStoreManagerRoleArn,replicationConfiguration:this.replicationConfiguration}}inspect(inspector){try{jsiiDeprecationWarnings().aws_cdk_lib_TreeInspector(inspector)}catch(error){throw process.env.JSII_DEBUG!=="1"&&error.name==="DeprecationError"&&Error.captureStackTrace(error,this.inspect),error}inspector.addAttribute("aws:cdk:cloudformation:type",CfnDataLake.CFN_RESOURCE_TYPE_NAME),inspector.addAttribute("aws:cdk:cloudformation:props",this.cfnProperties)}renderProperties(props){return convertCfnDataLakePropsToCloudFormation(props)}}exports.CfnDataLake=CfnDataLake,_b=JSII_RTTI_SYMBOL_1,CfnDataLake[_b]={fqn:"aws-cdk-lib.aws_securitylake.CfnDataLake",version:"2.201.0"},CfnDataLake.CFN_RESOURCE_TYPE_NAME="AWS::SecurityLake::DataLake";function CfnDataLakeEncryptionConfigurationPropertyValidator(properties){if(!cdk().canInspect(properties))return cdk().VALIDATION_SUCCESS;const errors=new(cdk()).ValidationResults;return properties&&typeof properties=="object"&&!Array.isArray(properties)||errors.collect(new(cdk()).ValidationResult("Expected an object, but received: "+JSON.stringify(properties))),errors.collect(cdk().propertyValidator("kmsKeyId",cdk().validateString)(properties.kmsKeyId)),errors.wrap('supplied properties not correct for "EncryptionConfigurationProperty"')}function convertCfnDataLakeEncryptionConfigurationPropertyToCloudFormation(properties){return cdk().canInspect(properties)?(CfnDataLakeEncryptionConfigurationPropertyValidator(properties).assertSuccess(),{KmsKeyId:cdk().stringToCloudFormation(properties.kmsKeyId)}):properties}function CfnDataLakeEncryptionConfigurationPropertyFromCloudFormation(properties){if(cdk().isResolvableObject(properties))return new(cfn_parse()).FromCloudFormationResult(properties);if(properties=properties??{},!(properties&&typeof properties=="object"&&!Array.isArray(properties)))return new(cfn_parse()).FromCloudFormationResult(properties);const ret=new(cfn_parse()).FromCloudFormationPropertyObject;return ret.addPropertyResult("kmsKeyId","KmsKeyId",properties.KmsKeyId!=null?cfn_parse().FromCloudFormation.getString(properties.KmsKeyId):void 0),ret.addUnrecognizedPropertiesAsExtra(properties),ret}function CfnDataLakeExpirationPropertyValidator(properties){if(!cdk().canInspect(properties))return cdk().VALIDATION_SUCCESS;const errors=new(cdk()).ValidationResults;return properties&&typeof properties=="object"&&!Array.isArray(properties)||errors.collect(new(cdk()).ValidationResult("Expected an object, but received: "+JSON.stringify(properties))),errors.collect(cdk().propertyValidator("days",cdk().validateNumber)(properties.days)),errors.wrap('supplied properties not correct for "ExpirationProperty"')}function convertCfnDataLakeExpirationPropertyToCloudFormation(properties){return cdk().canInspect(properties)?(CfnDataLakeExpirationPropertyValidator(properties).assertSuccess(),{Days:cdk().numberToCloudFormation(properties.days)}):properties}function CfnDataLakeExpirationPropertyFromCloudFormation(properties){if(cdk().isResolvableObject(properties))return new(cfn_parse()).FromCloudFormationResult(properties);if(properties=properties??{},!(properties&&typeof properties=="object"&&!Array.isArray(properties)))return new(cfn_parse()).FromCloudFormationResult(properties);const ret=new(cfn_parse()).FromCloudFormationPropertyObject;return ret.addPropertyResult("days","Days",properties.Days!=null?cfn_parse().FromCloudFormation.getNumber(properties.Days):void 0),ret.addUnrecognizedPropertiesAsExtra(properties),ret}function CfnDataLakeTransitionsPropertyValidator(properties){if(!cdk().canInspect(properties))return cdk().VALIDATION_SUCCESS;const errors=new(cdk()).ValidationResults;return properties&&typeof properties=="object"&&!Array.isArray(properties)||errors.collect(new(cdk()).ValidationResult("Expected an object, but received: "+JSON.stringify(properties))),errors.collect(cdk().propertyValidator("days",cdk().validateNumber)(properties.days)),errors.collect(cdk().propertyValidator("storageClass",cdk().validateString)(properties.storageClass)),errors.wrap('supplied properties not correct for "TransitionsProperty"')}function convertCfnDataLakeTransitionsPropertyToCloudFormation(properties){return cdk().canInspect(properties)?(CfnDataLakeTransitionsPropertyValidator(properties).assertSuccess(),{Days:cdk().numberToCloudFormation(properties.days),StorageClass:cdk().stringToCloudFormation(properties.storageClass)}):properties}function CfnDataLakeTransitionsPropertyFromCloudFormation(properties){if(cdk().isResolvableObject(properties))return new(cfn_parse()).FromCloudFormationResult(properties);if(properties=properties??{},!(properties&&typeof properties=="object"&&!Array.isArray(properties)))return new(cfn_parse()).FromCloudFormationResult(properties);const ret=new(cfn_parse()).FromCloudFormationPropertyObject;return ret.addPropertyResult("days","Days",properties.Days!=null?cfn_parse().FromCloudFormation.getNumber(properties.Days):void 0),ret.addPropertyResult("storageClass","StorageClass",properties.StorageClass!=null?cfn_parse().FromCloudFormation.getString(properties.StorageClass):void 0),ret.addUnrecognizedPropertiesAsExtra(properties),ret}function CfnDataLakeLifecycleConfigurationPropertyValidator(properties){if(!cdk().canInspect(properties))return cdk().VALIDATION_SUCCESS;const errors=new(cdk()).ValidationResults;return properties&&typeof properties=="object"&&!Array.isArray(properties)||errors.collect(new(cdk()).ValidationResult("Expected an object, but received: "+JSON.stringify(properties))),errors.collect(cdk().propertyValidator("expiration",CfnDataLakeExpirationPropertyValidator)(properties.expiration)),errors.collect(cdk().propertyValidator("transitions",cdk().listValidator(CfnDataLakeTransitionsPropertyValidator))(properties.transitions)),errors.wrap('supplied properties not correct for "LifecycleConfigurationProperty"')}function convertCfnDataLakeLifecycleConfigurationPropertyToCloudFormation(properties){return cdk().canInspect(properties)?(CfnDataLakeLifecycleConfigurationPropertyValidator(properties).assertSuccess(),{Expiration:convertCfnDataLakeExpirationPropertyToCloudFormation(properties.expiration),Transitions:cdk().listMapper(convertCfnDataLakeTransitionsPropertyToCloudFormation)(properties.transitions)}):properties}function CfnDataLakeLifecycleConfigurationPropertyFromCloudFormation(properties){if(cdk().isResolvableObject(properties))return new(cfn_parse()).FromCloudFormationResult(properties);if(properties=properties??{},!(properties&&typeof properties=="object"&&!Array.isArray(properties)))return new(cfn_parse()).FromCloudFormationResult(properties);const ret=new(cfn_parse()).FromCloudFormationPropertyObject;return ret.addPropertyResult("expiration","Expiration",properties.Expiration!=null?CfnDataLakeExpirationPropertyFromCloudFormation(properties.Expiration):void 0),ret.addPropertyResult("transitions","Transitions",properties.Transitions!=null?cfn_parse().FromCloudFormation.getArray(CfnDataLakeTransitionsPropertyFromCloudFormation)(properties.Transitions):void 0),ret.addUnrecognizedPropertiesAsExtra(properties),ret}function CfnDataLakeReplicationConfigurationPropertyValidator(properties){if(!cdk().canInspect(properties))return cdk().VALIDATION_SUCCESS;const errors=new(cdk()).ValidationResults;return properties&&typeof properties=="object"&&!Array.isArray(properties)||errors.collect(new(cdk()).ValidationResult("Expected an object, but received: "+JSON.stringify(properties))),errors.collect(cdk().propertyValidator("regions",cdk().listValidator(cdk().validateString))(properties.regions)),errors.collect(cdk().propertyValidator("roleArn",cdk().validateString)(properties.roleArn)),errors.wrap('supplied properties not correct for "ReplicationConfigurationProperty"')}function convertCfnDataLakeReplicationConfigurationPropertyToCloudFormation(properties){return cdk().canInspect(properties)?(CfnDataLakeReplicationConfigurationPropertyValidator(properties).assertSuccess(),{Regions:cdk().listMapper(cdk().stringToCloudFormation)(properties.regions),RoleArn:cdk().stringToCloudFormation(properties.roleArn)}):properties}function CfnDataLakeReplicationConfigurationPropertyFromCloudFormation(properties){if(cdk().isResolvableObject(properties))return new(cfn_parse()).FromCloudFormationResult(properties);if(properties=properties??{},!(properties&&typeof properties=="object"&&!Array.isArray(properties)))return new(cfn_parse()).FromCloudFormationResult(properties);const ret=new(cfn_parse()).FromCloudFormationPropertyObject;return ret.addPropertyResult("regions","Regions",properties.Regions!=null?cfn_parse().FromCloudFormation.getArray(cfn_parse().FromCloudFormation.getString)(properties.Regions):void 0),ret.addPropertyResult("roleArn","RoleArn",properties.RoleArn!=null?cfn_parse().FromCloudFormation.getString(properties.RoleArn):void 0),ret.addUnrecognizedPropertiesAsExtra(properties),ret}function CfnDataLakePropsValidator(properties){if(!cdk().canInspect(properties))return cdk().VALIDATION_SUCCESS;const errors=new(cdk()).ValidationResults;return properties&&typeof properties=="object"&&!Array.isArray(properties)||errors.collect(new(cdk()).ValidationResult("Expected an object, but received: "+JSON.stringify(properties))),errors.collect(cdk().propertyValidator("encryptionConfiguration",CfnDataLakeEncryptionConfigurationPropertyValidator)(properties.encryptionConfiguration)),errors.collect(cdk().propertyValidator("lifecycleConfiguration",CfnDataLakeLifecycleConfigurationPropertyValidator)(properties.lifecycleConfiguration)),errors.collect(cdk().propertyValidator("metaStoreManagerRoleArn",cdk().validateString)(properties.metaStoreManagerRoleArn)),errors.collect(cdk().propertyValidator("replicationConfiguration",CfnDataLakeReplicationConfigurationPropertyValidator)(properties.replicationConfiguration)),errors.collect(cdk().propertyValidator("tags",cdk().listValidator(cdk().validateCfnTag))(properties.tags)),errors.wrap('supplied properties not correct for "CfnDataLakeProps"')}function convertCfnDataLakePropsToCloudFormation(properties){return cdk().canInspect(properties)?(CfnDataLakePropsValidator(properties).assertSuccess(),{EncryptionConfiguration:convertCfnDataLakeEncryptionConfigurationPropertyToCloudFormation(properties.encryptionConfiguration),LifecycleConfiguration:convertCfnDataLakeLifecycleConfigurationPropertyToCloudFormation(properties.lifecycleConfiguration),MetaStoreManagerRoleArn:cdk().stringToCloudFormation(properties.metaStoreManagerRoleArn),ReplicationConfiguration:convertCfnDataLakeReplicationConfigurationPropertyToCloudFormation(properties.replicationConfiguration),Tags:cdk().listMapper(cdk().cfnTagToCloudFormation)(properties.tags)}):properties}function CfnDataLakePropsFromCloudFormation(properties){if(cdk().isResolvableObject(properties))return new(cfn_parse()).FromCloudFormationResult(properties);if(properties=properties??{},!(properties&&typeof properties=="object"&&!Array.isArray(properties)))return new(cfn_parse()).FromCloudFormationResult(properties);const ret=new(cfn_parse()).FromCloudFormationPropertyObject;return ret.addPropertyResult("encryptionConfiguration","EncryptionConfiguration",properties.EncryptionConfiguration!=null?CfnDataLakeEncryptionConfigurationPropertyFromCloudFormation(properties.EncryptionConfiguration):void 0),ret.addPropertyResult("lifecycleConfiguration","LifecycleConfiguration",properties.LifecycleConfiguration!=null?CfnDataLakeLifecycleConfigurationPropertyFromCloudFormation(properties.LifecycleConfiguration):void 0),ret.addPropertyResult("metaStoreManagerRoleArn","MetaStoreManagerRoleArn",properties.MetaStoreManagerRoleArn!=null?cfn_parse().FromCloudFormation.getString(properties.MetaStoreManagerRoleArn):void 0),ret.addPropertyResult("replicationConfiguration","ReplicationConfiguration",properties.ReplicationConfiguration!=null?CfnDataLakeReplicationConfigurationPropertyFromCloudFormation(properties.ReplicationConfiguration):void 0),ret.addPropertyResult("tags","Tags",properties.Tags!=null?cfn_parse().FromCloudFormation.getArray(cfn_parse().FromCloudFormation.getCfnTag)(properties.Tags):void 0),ret.addUnrecognizedPropertiesAsExtra(properties),ret}class CfnSubscriber extends cdk().CfnResource{static _fromCloudFormation(scope,id,resourceAttributes,options){resourceAttributes=resourceAttributes||{};const resourceProperties=options.parser.parseValue(resourceAttributes.Properties),propsResult=CfnSubscriberPropsFromCloudFormation(resourceProperties);if(cdk().isResolvableObject(propsResult.value))throw new(cdk_errors()).ValidationError("Unexpected IResolvable",scope);const ret=new CfnSubscriber(scope,id,propsResult.value);for(const[propKey,propVal]of Object.entries(propsResult.extraProperties))ret.addPropertyOverride(propKey,propVal);return options.parser.handleAttributes(ret,resourceAttributes,id),ret}constructor(scope,id,props){super(scope,id,{type:CfnSubscriber.CFN_RESOURCE_TYPE_NAME,properties:props});try{jsiiDeprecationWarnings().aws_cdk_lib_aws_securitylake_CfnSubscriberProps(props)}catch(error){throw process.env.JSII_DEBUG!=="1"&&error.name==="DeprecationError"&&Error.captureStackTrace(error,CfnSubscriber),error}cdk().requireProperty(props,"accessTypes",this),cdk().requireProperty(props,"dataLakeArn",this),cdk().requireProperty(props,"sources",this),cdk().requireProperty(props,"subscriberIdentity",this),cdk().requireProperty(props,"subscriberName",this),this.attrResourceShareArn=cdk().Token.asString(this.getAtt("ResourceShareArn",cdk().ResolutionTypeHint.STRING)),this.attrResourceShareName=cdk().Token.asString(this.getAtt("ResourceShareName",cdk().ResolutionTypeHint.STRING)),this.attrS3BucketArn=cdk().Token.asString(this.getAtt("S3BucketArn",cdk().ResolutionTypeHint.STRING)),this.attrSubscriberArn=cdk().Token.asString(this.getAtt("SubscriberArn",cdk().ResolutionTypeHint.STRING)),this.attrSubscriberRoleArn=cdk().Token.asString(this.getAtt("SubscriberRoleArn",cdk().ResolutionTypeHint.STRING)),this.accessTypes=props.accessTypes,this.cdkTagManager=new(cdk()).TagManager(cdk().TagType.STANDARD,"AWS::SecurityLake::Subscriber",void 0,{tagPropertyName:"tags"}),this.dataLakeArn=props.dataLakeArn,this.sources=props.sources,this.subscriberDescription=props.subscriberDescription,this.subscriberIdentity=props.subscriberIdentity,this.subscriberName=props.subscriberName,this.tags=props.tags}get cfnProperties(){return{accessTypes:this.accessTypes,tags:this.cdkTagManager.renderTags(this.tags),dataLakeArn:this.dataLakeArn,sources:this.sources,subscriberDescription:this.subscriberDescription,subscriberIdentity:this.subscriberIdentity,subscriberName:this.subscriberName}}inspect(inspector){try{jsiiDeprecationWarnings().aws_cdk_lib_TreeInspector(inspector)}catch(error){throw process.env.JSII_DEBUG!=="1"&&error.name==="DeprecationError"&&Error.captureStackTrace(error,this.inspect),error}inspector.addAttribute("aws:cdk:cloudformation:type",CfnSubscriber.CFN_RESOURCE_TYPE_NAME),inspector.addAttribute("aws:cdk:cloudformation:props",this.cfnProperties)}renderProperties(props){return convertCfnSubscriberPropsToCloudFormation(props)}}exports.CfnSubscriber=CfnSubscriber,_c=JSII_RTTI_SYMBOL_1,CfnSubscriber[_c]={fqn:"aws-cdk-lib.aws_securitylake.CfnSubscriber",version:"2.201.0"},CfnSubscriber.CFN_RESOURCE_TYPE_NAME="AWS::SecurityLake::Subscriber";function CfnSubscriberSubscriberIdentityPropertyValidator(properties){if(!cdk().canInspect(properties))return cdk().VALIDATION_SUCCESS;const errors=new(cdk()).ValidationResults;return properties&&typeof properties=="object"&&!Array.isArray(properties)||errors.collect(new(cdk()).ValidationResult("Expected an object, but received: "+JSON.stringify(properties))),errors.collect(cdk().propertyValidator("externalId",cdk().requiredValidator)(properties.externalId)),errors.collect(cdk().propertyValidator("externalId",cdk().validateString)(properties.externalId)),errors.collect(cdk().propertyValidator("principal",cdk().requiredValidator)(properties.principal)),errors.collect(cdk().propertyValidator("principal",cdk().validateString)(properties.principal)),errors.wrap('supplied properties not correct for "SubscriberIdentityProperty"')}function convertCfnSubscriberSubscriberIdentityPropertyToCloudFormation(properties){return cdk().canInspect(properties)?(CfnSubscriberSubscriberIdentityPropertyValidator(properties).assertSuccess(),{ExternalId:cdk().stringToCloudFormation(properties.externalId),Principal:cdk().stringToCloudFormation(properties.principal)}):properties}function CfnSubscriberSubscriberIdentityPropertyFromCloudFormation(properties){if(cdk().isResolvableObject(properties))return new(cfn_parse()).FromCloudFormationResult(properties);if(properties=properties??{},!(properties&&typeof properties=="object"&&!Array.isArray(properties)))return new(cfn_parse()).FromCloudFormationResult(properties);const ret=new(cfn_parse()).FromCloudFormationPropertyObject;return ret.addPropertyResult("externalId","ExternalId",properties.ExternalId!=null?cfn_parse().FromCloudFormation.getString(properties.ExternalId):void 0),ret.addPropertyResult("principal","Principal",properties.Principal!=null?cfn_parse().FromCloudFormation.getString(properties.Principal):void 0),ret.addUnrecognizedPropertiesAsExtra(properties),ret}function CfnSubscriberAwsLogSourcePropertyValidator(properties){if(!cdk().canInspect(properties))return cdk().VALIDATION_SUCCESS;const errors=new(cdk()).ValidationResults;return properties&&typeof properties=="object"&&!Array.isArray(properties)||errors.collect(new(cdk()).ValidationResult("Expected an object, but received: "+JSON.stringify(properties))),errors.collect(cdk().propertyValidator("sourceName",cdk().validateString)(properties.sourceName)),errors.collect(cdk().propertyValidator("sourceVersion",cdk().validateString)(properties.sourceVersion)),errors.wrap('supplied properties not correct for "AwsLogSourceProperty"')}function convertCfnSubscriberAwsLogSourcePropertyToCloudFormation(properties){return cdk().canInspect(properties)?(CfnSubscriberAwsLogSourcePropertyValidator(properties).assertSuccess(),{SourceName:cdk().stringToCloudFormation(properties.sourceName),SourceVersion:cdk().stringToCloudFormation(properties.sourceVersion)}):properties}function CfnSubscriberAwsLogSourcePropertyFromCloudFormation(properties){if(cdk().isResolvableObject(properties))return new(cfn_parse()).FromCloudFormationResult(properties);if(properties=properties??{},!(properties&&typeof properties=="object"&&!Array.isArray(properties)))return new(cfn_parse()).FromCloudFormationResult(properties);const ret=new(cfn_parse()).FromCloudFormationPropertyObject;return ret.addPropertyResult("sourceName","SourceName",properties.SourceName!=null?cfn_parse().FromCloudFormation.getString(properties.SourceName):void 0),ret.addPropertyResult("sourceVersion","SourceVersion",properties.SourceVersion!=null?cfn_parse().FromCloudFormation.getString(properties.SourceVersion):void 0),ret.addUnrecognizedPropertiesAsExtra(properties),ret}function CfnSubscriberCustomLogSourcePropertyValidator(properties){if(!cdk().canInspect(properties))return cdk().VALIDATION_SUCCESS;const errors=new(cdk()).ValidationResults;return properties&&typeof properties=="object"&&!Array.isArray(properties)||errors.collect(new(cdk()).ValidationResult("Expected an object, but received: "+JSON.stringify(properties))),errors.collect(cdk().propertyValidator("sourceName",cdk().validateString)(properties.sourceName)),errors.collect(cdk().propertyValidator("sourceVersion",cdk().validateString)(properties.sourceVersion)),errors.wrap('supplied properties not correct for "CustomLogSourceProperty"')}function convertCfnSubscriberCustomLogSourcePropertyToCloudFormation(properties){return cdk().canInspect(properties)?(CfnSubscriberCustomLogSourcePropertyValidator(properties).assertSuccess(),{SourceName:cdk().stringToCloudFormation(properties.sourceName),SourceVersion:cdk().stringToCloudFormation(properties.sourceVersion)}):properties}function CfnSubscriberCustomLogSourcePropertyFromCloudFormation(properties){if(cdk().isResolvableObject(properties))return new(cfn_parse()).FromCloudFormationResult(properties);if(properties=properties??{},!(properties&&typeof properties=="object"&&!Array.isArray(properties)))return new(cfn_parse()).FromCloudFormationResult(properties);const ret=new(cfn_parse()).FromCloudFormationPropertyObject;return ret.addPropertyResult("sourceName","SourceName",properties.SourceName!=null?cfn_parse().FromCloudFormation.getString(properties.SourceName):void 0),ret.addPropertyResult("sourceVersion","SourceVersion",properties.SourceVersion!=null?cfn_parse().FromCloudFormation.getString(properties.SourceVersion):void 0),ret.addUnrecognizedPropertiesAsExtra(properties),ret}function CfnSubscriberSourcePropertyValidator(properties){if(!cdk().canInspect(properties))return cdk().VALIDATION_SUCCESS;const errors=new(cdk()).ValidationResults;return properties&&typeof properties=="object"&&!Array.isArray(properties)||errors.collect(new(cdk()).ValidationResult("Expected an object, but received: "+JSON.stringify(properties))),errors.collect(cdk().propertyValidator("awsLogSource",CfnSubscriberAwsLogSourcePropertyValidator)(properties.awsLogSource)),errors.collect(cdk().propertyValidator("customLogSource",CfnSubscriberCustomLogSourcePropertyValidator)(properties.customLogSource)),errors.wrap('supplied properties not correct for "SourceProperty"')}function convertCfnSubscriberSourcePropertyToCloudFormation(properties){return cdk().canInspect(properties)?(CfnSubscriberSourcePropertyValidator(properties).assertSuccess(),{AwsLogSource:convertCfnSubscriberAwsLogSourcePropertyToCloudFormation(properties.awsLogSource),CustomLogSource:convertCfnSubscriberCustomLogSourcePropertyToCloudFormation(properties.customLogSource)}):properties}function CfnSubscriberSourcePropertyFromCloudFormation(properties){if(cdk().isResolvableObject(properties))return new(cfn_parse()).FromCloudFormationResult(properties);if(properties=properties??{},!(properties&&typeof properties=="object"&&!Array.isArray(properties)))return new(cfn_parse()).FromCloudFormationResult(properties);const ret=new(cfn_parse()).FromCloudFormationPropertyObject;return ret.addPropertyResult("awsLogSource","AwsLogSource",properties.AwsLogSource!=null?CfnSubscriberAwsLogSourcePropertyFromCloudFormation(properties.AwsLogSource):void 0),ret.addPropertyResult("customLogSource","CustomLogSource",properties.CustomLogSource!=null?CfnSubscriberCustomLogSourcePropertyFromCloudFormation(properties.CustomLogSource):void 0),ret.addUnrecognizedPropertiesAsExtra(properties),ret}function CfnSubscriberPropsValidator(properties){if(!cdk().canInspect(properties))return cdk().VALIDATION_SUCCESS;const errors=new(cdk()).ValidationResults;return properties&&typeof properties=="object"&&!Array.isArray(properties)||errors.collect(new(cdk()).ValidationResult("Expected an object, but received: "+JSON.stringify(properties))),errors.collect(cdk().propertyValidator("accessTypes",cdk().requiredValidator)(properties.accessTypes)),errors.collect(cdk().propertyValidator("accessTypes",cdk().listValidator(cdk().validateString))(properties.accessTypes)),errors.collect(cdk().propertyValidator("dataLakeArn",cdk().requiredValidator)(properties.dataLakeArn)),errors.collect(cdk().propertyValidator("dataLakeArn",cdk().validateString)(properties.dataLakeArn)),errors.collect(cdk().propertyValidator("sources",cdk().requiredValidator)(properties.sources)),errors.collect(cdk().propertyValidator("sources",cdk().listValidator(CfnSubscriberSourcePropertyValidator))(properties.sources)),errors.collect(cdk().propertyValidator("subscriberDescription",cdk().validateString)(properties.subscriberDescription)),errors.collect(cdk().propertyValidator("subscriberIdentity",cdk().requiredValidator)(properties.subscriberIdentity)),errors.collect(cdk().propertyValidator("subscriberIdentity",CfnSubscriberSubscriberIdentityPropertyValidator)(properties.subscriberIdentity)),errors.collect(cdk().propertyValidator("subscriberName",cdk().requiredValidator)(properties.subscriberName)),errors.collect(cdk().propertyValidator("subscriberName",cdk().validateString)(properties.subscriberName)),errors.collect(cdk().propertyValidator("tags",cdk().listValidator(cdk().validateCfnTag))(properties.tags)),errors.wrap('supplied properties not correct for "CfnSubscriberProps"')}function convertCfnSubscriberPropsToCloudFormation(properties){return cdk().canInspect(properties)?(CfnSubscriberPropsValidator(properties).assertSuccess(),{AccessTypes:cdk().listMapper(cdk().stringToCloudFormation)(properties.accessTypes),DataLakeArn:cdk().stringToCloudFormation(properties.dataLakeArn),Sources:cdk().listMapper(convertCfnSubscriberSourcePropertyToCloudFormation)(properties.sources),SubscriberDescription:cdk().stringToCloudFormation(properties.subscriberDescription),SubscriberIdentity:convertCfnSubscriberSubscriberIdentityPropertyToCloudFormation(properties.subscriberIdentity),SubscriberName:cdk().stringToCloudFormation(properties.subscriberName),Tags:cdk().listMapper(cdk().cfnTagToCloudFormation)(properties.tags)}):properties}function CfnSubscriberPropsFromCloudFormation(properties){if(cdk().isResolvableObject(properties))return new(cfn_parse()).FromCloudFormationResult(properties);if(properties=properties??{},!(properties&&typeof properties=="object"&&!Array.isArray(properties)))return new(cfn_parse()).FromCloudFormationResult(properties);const ret=new(cfn_parse()).FromCloudFormationPropertyObject;return ret.addPropertyResult("accessTypes","AccessTypes",properties.AccessTypes!=null?cfn_parse().FromCloudFormation.getArray(cfn_parse().FromCloudFormation.getString)(properties.AccessTypes):void 0),ret.addPropertyResult("dataLakeArn","DataLakeArn",properties.DataLakeArn!=null?cfn_parse().FromCloudFormation.getString(properties.DataLakeArn):void 0),ret.addPropertyResult("sources","Sources",properties.Sources!=null?cfn_parse().FromCloudFormation.getArray(CfnSubscriberSourcePropertyFromCloudFormation)(properties.Sources):void 0),ret.addPropertyResult("subscriberDescription","SubscriberDescription",properties.SubscriberDescription!=null?cfn_parse().FromCloudFormation.getString(properties.SubscriberDescription):void 0),ret.addPropertyResult("subscriberIdentity","SubscriberIdentity",properties.SubscriberIdentity!=null?CfnSubscriberSubscriberIdentityPropertyFromCloudFormation(properties.SubscriberIdentity):void 0),ret.addPropertyResult("subscriberName","SubscriberName",properties.SubscriberName!=null?cfn_parse().FromCloudFormation.getString(properties.SubscriberName):void 0),ret.addPropertyResult("tags","Tags",properties.Tags!=null?cfn_parse().FromCloudFormation.getArray(cfn_parse().FromCloudFormation.getCfnTag)(properties.Tags):void 0),ret.addUnrecognizedPropertiesAsExtra(properties),ret}class CfnSubscriberNotification extends cdk().CfnResource{static _fromCloudFormation(scope,id,resourceAttributes,options){resourceAttributes=resourceAttributes||{};const resourceProperties=options.parser.parseValue(resourceAttributes.Properties),propsResult=CfnSubscriberNotificationPropsFromCloudFormation(resourceProperties);if(cdk().isResolvableObject(propsResult.value))throw new(cdk_errors()).ValidationError("Unexpected IResolvable",scope);const ret=new CfnSubscriberNotification(scope,id,propsResult.value);for(const[propKey,propVal]of Object.entries(propsResult.extraProperties))ret.addPropertyOverride(propKey,propVal);return options.parser.handleAttributes(ret,resourceAttributes,id),ret}constructor(scope,id,props){super(scope,id,{type:CfnSubscriberNotification.CFN_RESOURCE_TYPE_NAME,properties:props});try{jsiiDeprecationWarnings().aws_cdk_lib_aws_securitylake_CfnSubscriberNotificationProps(props)}catch(error){throw process.env.JSII_DEBUG!=="1"&&error.name==="DeprecationError"&&Error.captureStackTrace(error,CfnSubscriberNotification),error}cdk().requireProperty(props,"notificationConfiguration",this),cdk().requireProperty(props,"subscriberArn",this),this.attrSubscriberEndpoint=cdk().Token.asString(this.getAtt("SubscriberEndpoint",cdk().ResolutionTypeHint.STRING)),this.notificationConfiguration=props.notificationConfiguration,this.subscriberArn=props.subscriberArn}get cfnProperties(){return{notificationConfiguration:this.notificationConfiguration,subscriberArn:this.subscriberArn}}inspect(inspector){try{jsiiDeprecationWarnings().aws_cdk_lib_TreeInspector(inspector)}catch(error){throw process.env.JSII_DEBUG!=="1"&&error.name==="DeprecationError"&&Error.captureStackTrace(error,this.inspect),error}inspector.addAttribute("aws:cdk:cloudformation:type",CfnSubscriberNotification.CFN_RESOURCE_TYPE_NAME),inspector.addAttribute("aws:cdk:cloudformation:props",this.cfnProperties)}renderProperties(props){return convertCfnSubscriberNotificationPropsToCloudFormation(props)}}exports.CfnSubscriberNotification=CfnSubscriberNotification,_d=JSII_RTTI_SYMBOL_1,CfnSubscriberNotification[_d]={fqn:"aws-cdk-lib.aws_securitylake.CfnSubscriberNotification",version:"2.201.0"},CfnSubscriberNotification.CFN_RESOURCE_TYPE_NAME="AWS::SecurityLake::SubscriberNotification";function CfnSubscriberNotificationHttpsNotificationConfigurationPropertyValidator(properties){if(!cdk().canInspect(properties))return cdk().VALIDATION_SUCCESS;const errors=new(cdk()).ValidationResults;return properties&&typeof properties=="object"&&!Array.isArray(properties)||errors.collect(new(cdk()).ValidationResult("Expected an object, but received: "+JSON.stringify(properties))),errors.collect(cdk().propertyValidator("authorizationApiKeyName",cdk().validateString)(properties.authorizationApiKeyName)),errors.collect(cdk().propertyValidator("authorizationApiKeyValue",cdk().validateString)(properties.authorizationApiKeyValue)),errors.collect(cdk().propertyValidator("endpoint",cdk().requiredValidator)(properties.endpoint)),errors.collect(cdk().propertyValidator("endpoint",cdk().validateString)(properties.endpoint)),errors.collect(cdk().propertyValidator("httpMethod",cdk().validateString)(properties.httpMethod)),errors.collect(cdk().propertyValidator("targetRoleArn",cdk().requiredValidator)(properties.targetRoleArn)),errors.collect(cdk().propertyValidator("targetRoleArn",cdk().validateString)(properties.targetRoleArn)),errors.wrap('supplied properties not correct for "HttpsNotificationConfigurationProperty"')}function convertCfnSubscriberNotificationHttpsNotificationConfigurationPropertyToCloudFormation(properties){return cdk().canInspect(properties)?(CfnSubscriberNotificationHttpsNotificationConfigurationPropertyValidator(properties).assertSuccess(),{AuthorizationApiKeyName:cdk().stringToCloudFormation(properties.authorizationApiKeyName),AuthorizationApiKeyValue:cdk().stringToCloudFormation(properties.authorizationApiKeyValue),Endpoint:cdk().stringToCloudFormation(properties.endpoint),HttpMethod:cdk().stringToCloudFormation(properties.httpMethod),TargetRoleArn:cdk().stringToCloudFormation(properties.targetRoleArn)}):properties}function CfnSubscriberNotificationHttpsNotificationConfigurationPropertyFromCloudFormation(properties){if(cdk().isResolvableObject(properties))return new(cfn_parse()).FromCloudFormationResult(properties);if(properties=properties??{},!(properties&&typeof properties=="object"&&!Array.isArray(properties)))return new(cfn_parse()).FromCloudFormationResult(properties);const ret=new(cfn_parse()).FromCloudFormationPropertyObject;return ret.addPropertyResult("authorizationApiKeyName","AuthorizationApiKeyName",properties.AuthorizationApiKeyName!=null?cfn_parse().FromCloudFormation.getString(properties.AuthorizationApiKeyName):void 0),ret.addPropertyResult("authorizationApiKeyValue","AuthorizationApiKeyValue",properties.AuthorizationApiKeyValue!=null?cfn_parse().FromCloudFormation.getString(properties.AuthorizationApiKeyValue):void 0),ret.addPropertyResult("endpoint","Endpoint",properties.Endpoint!=null?cfn_parse().FromCloudFormation.getString(properties.Endpoint):void 0),ret.addPropertyResult("httpMethod","HttpMethod",properties.HttpMethod!=null?cfn_parse().FromCloudFormation.getString(properties.HttpMethod):void 0),ret.addPropertyResult("targetRoleArn","TargetRoleArn",properties.TargetRoleArn!=null?cfn_parse().FromCloudFormation.getString(properties.TargetRoleArn):void 0),ret.addUnrecognizedPropertiesAsExtra(properties),ret}function CfnSubscriberNotificationNotificationConfigurationPropertyValidator(properties){if(!cdk().canInspect(properties))return cdk().VALIDATION_SUCCESS;const errors=new(cdk()).ValidationResults;return properties&&typeof properties=="object"&&!Array.isArray(properties)||errors.collect(new(cdk()).ValidationResult("Expected an object, but received: "+JSON.stringify(properties))),errors.collect(cdk().propertyValidator("httpsNotificationConfiguration",CfnSubscriberNotificationHttpsNotificationConfigurationPropertyValidator)(properties.httpsNotificationConfiguration)),errors.collect(cdk().propertyValidator("sqsNotificationConfiguration",cdk().validateObject)(properties.sqsNotificationConfiguration)),errors.wrap('supplied properties not correct for "NotificationConfigurationProperty"')}function convertCfnSubscriberNotificationNotificationConfigurationPropertyToCloudFormation(properties){return cdk().canInspect(properties)?(CfnSubscriberNotificationNotificationConfigurationPropertyValidator(properties).assertSuccess(),{HttpsNotificationConfiguration:convertCfnSubscriberNotificationHttpsNotificationConfigurationPropertyToCloudFormation(properties.httpsNotificationConfiguration),SqsNotificationConfiguration:cdk().objectToCloudFormation(properties.sqsNotificationConfiguration)}):properties}function CfnSubscriberNotificationNotificationConfigurationPropertyFromCloudFormation(properties){if(cdk().isResolvableObject(properties))return new(cfn_parse()).FromCloudFormationResult(properties);if(properties=properties??{},!(properties&&typeof properties=="object"&&!Array.isArray(properties)))return new(cfn_parse()).FromCloudFormationResult(properties);const ret=new(cfn_parse()).FromCloudFormationPropertyObject;return ret.addPropertyResult("httpsNotificationConfiguration","HttpsNotificationConfiguration",properties.HttpsNotificationConfiguration!=null?CfnSubscriberNotificationHttpsNotificationConfigurationPropertyFromCloudFormation(properties.HttpsNotificationConfiguration):void 0),ret.addPropertyResult("sqsNotificationConfiguration","SqsNotificationConfiguration",properties.SqsNotificationConfiguration!=null?cfn_parse().FromCloudFormation.getAny(properties.SqsNotificationConfiguration):void 0),ret.addUnrecognizedPropertiesAsExtra(properties),ret}function CfnSubscriberNotificationPropsValidator(properties){if(!cdk().canInspect(properties))return cdk().VALIDATION_SUCCESS;const errors=new(cdk()).ValidationResults;return properties&&typeof properties=="object"&&!Array.isArray(properties)||errors.collect(new(cdk()).ValidationResult("Expected an object, but received: "+JSON.stringify(properties))),errors.collect(cdk().propertyValidator("notificationConfiguration",cdk().requiredValidator)(properties.notificationConfiguration)),errors.collect(cdk().propertyValidator("notificationConfiguration",CfnSubscriberNotificationNotificationConfigurationPropertyValidator)(properties.notificationConfiguration)),errors.collect(cdk().propertyValidator("subscriberArn",cdk().requiredValidator)(properties.subscriberArn)),errors.collect(cdk().propertyValidator("subscriberArn",cdk().validateString)(properties.subscriberArn)),errors.wrap('supplied properties not correct for "CfnSubscriberNotificationProps"')}function convertCfnSubscriberNotificationPropsToCloudFormation(properties){return cdk().canInspect(properties)?(CfnSubscriberNotificationPropsValidator(properties).assertSuccess(),{NotificationConfiguration:convertCfnSubscriberNotificationNotificationConfigurationPropertyToCloudFormation(properties.notificationConfiguration),SubscriberArn:cdk().stringToCloudFormation(properties.subscriberArn)}):properties}function CfnSubscriberNotificationPropsFromCloudFormation(properties){if(cdk().isResolvableObject(properties))return new(cfn_parse()).FromCloudFormationResult(properties);if(properties=properties??{},!(properties&&typeof properties=="object"&&!Array.isArray(properties)))return new(cfn_parse()).FromCloudFormationResult(properties);const ret=new(cfn_parse()).FromCloudFormationPropertyObject;return ret.addPropertyResult("notificationConfiguration","NotificationConfiguration",properties.NotificationConfiguration!=null?CfnSubscriberNotificationNotificationConfigurationPropertyFromCloudFormation(properties.NotificationConfiguration):void 0),ret.addPropertyResult("subscriberArn","SubscriberArn",properties.SubscriberArn!=null?cfn_parse().FromCloudFormation.getString(properties.SubscriberArn):void 0),ret.addUnrecognizedPropertiesAsExtra(properties),ret}
