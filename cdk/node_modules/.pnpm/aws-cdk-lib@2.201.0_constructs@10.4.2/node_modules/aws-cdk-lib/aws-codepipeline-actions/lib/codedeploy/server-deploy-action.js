"use strict";var _a;Object.defineProperty(exports,"__esModule",{value:!0}),exports.CodeDeployServerDeployAction=void 0;var jsiiDeprecationWarnings=()=>{var tmp=require("../../../.warnings.jsii.js");return jsiiDeprecationWarnings=()=>tmp,tmp};const JSII_RTTI_SYMBOL_1=Symbol.for("jsii.rtti");var codepipeline=()=>{var tmp=require("../../../aws-codepipeline");return codepipeline=()=>tmp,tmp},iam=()=>{var tmp=require("../../../aws-iam");return iam=()=>tmp,tmp},action_1=()=>{var tmp=require("../action");return action_1=()=>tmp,tmp},common_1=()=>{var tmp=require("../common");return common_1=()=>tmp,tmp};class CodeDeployServerDeployAction extends action_1().Action{constructor(props){super({...props,resource:props.deploymentGroup,category:codepipeline().ActionCategory.DEPLOY,provider:"CodeDeploy",artifactBounds:(0,common_1().deployArtifactBounds)(),inputs:[props.input]});try{jsiiDeprecationWarnings().aws_cdk_lib_aws_codepipeline_actions_CodeDeployServerDeployActionProps(props)}catch(error){throw process.env.JSII_DEBUG!=="1"&&error.name==="DeprecationError"&&Error.captureStackTrace(error,CodeDeployServerDeployAction),error}this.deploymentGroup=props.deploymentGroup}bound(_scope,_stage,options){try{jsiiDeprecationWarnings().aws_cdk_lib_aws_codepipeline_IStage(_stage),jsiiDeprecationWarnings().aws_cdk_lib_aws_codepipeline_ActionBindOptions(options)}catch(error){throw process.env.JSII_DEBUG!=="1"&&error.name==="DeprecationError"&&Error.captureStackTrace(error,this.bound),error}options.role.addToPolicy(new(iam()).PolicyStatement({resources:[this.deploymentGroup.application.applicationArn],actions:["codedeploy:GetApplicationRevision","codedeploy:RegisterApplicationRevision"]})),options.role.addToPolicy(new(iam()).PolicyStatement({resources:[this.deploymentGroup.deploymentGroupArn],actions:["codedeploy:CreateDeployment","codedeploy:GetDeployment"]})),options.role.addToPolicy(new(iam()).PolicyStatement({resources:[this.deploymentGroup.deploymentConfig.deploymentConfigArn],actions:["codedeploy:GetDeploymentConfig"]}));for(const asg of this.deploymentGroup.autoScalingGroups||[])options.bucket.grantRead(asg);return options.bucket.grantRead(options.role),{configuration:{ApplicationName:this.deploymentGroup.application.applicationName,DeploymentGroupName:this.deploymentGroup.deploymentGroupName}}}}exports.CodeDeployServerDeployAction=CodeDeployServerDeployAction,_a=JSII_RTTI_SYMBOL_1,CodeDeployServerDeployAction[_a]={fqn:"aws-cdk-lib.aws_codepipeline_actions.CodeDeployServerDeployAction",version:"2.201.0"};
