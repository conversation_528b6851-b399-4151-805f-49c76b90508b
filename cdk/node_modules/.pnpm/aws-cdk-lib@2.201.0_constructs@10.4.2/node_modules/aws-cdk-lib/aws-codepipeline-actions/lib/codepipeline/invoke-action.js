"use strict";var _a;Object.defineProperty(exports,"__esModule",{value:!0}),exports.PipelineInvokeAction=exports.RevisionType=void 0;var jsiiDeprecationWarnings=()=>{var tmp=require("../../../.warnings.jsii.js");return jsiiDeprecationWarnings=()=>tmp,tmp};const JSII_RTTI_SYMBOL_1=Symbol.for("jsii.rtti");var codepipeline=()=>{var tmp=require("../../../aws-codepipeline");return codepipeline=()=>tmp,tmp},iam=()=>{var tmp=require("../../../aws-iam");return iam=()=>tmp,tmp},core_1=()=>{var tmp=require("../../../core");return core_1=()=>tmp,tmp},action_1=()=>{var tmp=require("../action");return action_1=()=>tmp,tmp},RevisionType;(function(RevisionType2){RevisionType2.COMMIT_ID="COMMIT_ID",RevisionType2.IMAGE_DIGEST="IMAGE_DIGEST",RevisionType2.S3_OBJECT_VERSION_ID="S3_OBJECT_VERSION_ID",RevisionType2.S3_OBJECT_KEY="S3_OBJECT_KEY"})(RevisionType||(exports.RevisionType=RevisionType={}));class PipelineInvokeAction extends action_1().Action{constructor(props){super({...props,category:codepipeline().ActionCategory.INVOKE,provider:"CodePipeline",artifactBounds:{minInputs:0,maxInputs:0,minOutputs:0,maxOutputs:0},inputs:[],outputs:[]});try{jsiiDeprecationWarnings().aws_cdk_lib_aws_codepipeline_actions_PipelineInvokeActionProps(props)}catch(error){throw process.env.JSII_DEBUG!=="1"&&error.name==="DeprecationError"&&Error.captureStackTrace(error,PipelineInvokeAction),error}this.props=props}bound(scope,_stage,options){try{jsiiDeprecationWarnings().aws_cdk_lib_aws_codepipeline_IStage(_stage),jsiiDeprecationWarnings().aws_cdk_lib_aws_codepipeline_ActionBindOptions(options)}catch(error){throw process.env.JSII_DEBUG!=="1"&&error.name==="DeprecationError"&&Error.captureStackTrace(error,this.bound),error}return options.role.addToPolicy(new(iam()).PolicyStatement({actions:["codepipeline:StartPipelineExecution"],resources:[this.props.targetPipeline.pipelineArn]})),{configuration:{PipelineName:this.props.targetPipeline.pipelineName,SourceRevisions:core_1().Stack.of(scope).toJsonString(this.props.sourceRevisions),Variables:core_1().Stack.of(scope).toJsonString(this.props.variables)}}}}exports.PipelineInvokeAction=PipelineInvokeAction,_a=JSII_RTTI_SYMBOL_1,PipelineInvokeAction[_a]={fqn:"aws-cdk-lib.aws_codepipeline_actions.PipelineInvokeAction",version:"2.201.0"};
