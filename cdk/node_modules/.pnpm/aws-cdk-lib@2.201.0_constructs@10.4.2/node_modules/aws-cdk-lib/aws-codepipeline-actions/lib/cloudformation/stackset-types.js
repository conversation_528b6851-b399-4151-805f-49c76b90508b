"use strict";var _a,_b,_c,_d;Object.defineProperty(exports,"__esModule",{value:!0}),exports.StackSetOrganizationsAutoDeployment=exports.StackSetDeploymentModel=exports.StackSetParameters=exports.StackInstances=exports.StackSetTemplate=void 0;var jsiiDeprecationWarnings=()=>{var tmp=require("../../../.warnings.jsii.js");return jsiiDeprecationWarnings=()=>tmp,tmp};const JSII_RTTI_SYMBOL_1=Symbol.for("jsii.rtti");var iam=()=>{var tmp=require("../../../aws-iam");return iam=()=>tmp,tmp},cdk=()=>{var tmp=require("../../../core");return cdk=()=>tmp,tmp};class StackSetTemplate{static fromArtifactPath(artifactPath){try{jsiiDeprecationWarnings().aws_cdk_lib_aws_codepipeline_ArtifactPath(artifactPath)}catch(error){throw process.env.JSII_DEBUG!=="1"&&error.name==="DeprecationError"&&Error.captureStackTrace(error,this.fromArtifactPath),error}return new class extends StackSetTemplate{constructor(){super(...arguments),this._artifactsReferenced=[artifactPath.artifact]}_render(){return artifactPath.location}}}}exports.StackSetTemplate=StackSetTemplate,_a=JSII_RTTI_SYMBOL_1,StackSetTemplate[_a]={fqn:"aws-cdk-lib.aws_codepipeline_actions.StackSetTemplate",version:"2.201.0"};class StackInstances{static inAccounts(accounts,regions){return StackInstances.fromList(accounts,regions)}static inOrganizationalUnits(ous,regions){return StackInstances.fromList(ous,regions)}static fromArtifactPath(artifactPath,regions){try{jsiiDeprecationWarnings().aws_cdk_lib_aws_codepipeline_ArtifactPath(artifactPath)}catch(error){throw process.env.JSII_DEBUG!=="1"&&error.name==="DeprecationError"&&Error.captureStackTrace(error,this.fromArtifactPath),error}if(regions.length===0)throw new(cdk()).UnscopedValidationError("'regions' may not be an empty list");return new class extends StackInstances{constructor(){super(...arguments),this._artifactsReferenced=[artifactPath.artifact]}_bind(_scope){return{stackSetConfiguration:{DeploymentTargets:artifactPath.location,Regions:regions.join(",")}}}}}static fromList(targets,regions){if(targets.length===0)throw new(cdk()).UnscopedValidationError("'targets' may not be an empty list");if(regions.length===0)throw new(cdk()).UnscopedValidationError("'regions' may not be an empty list");return new class extends StackInstances{_bind(_scope){return{stackSetConfiguration:{DeploymentTargets:targets.join(","),Regions:regions.join(",")}}}}}}exports.StackInstances=StackInstances,_b=JSII_RTTI_SYMBOL_1,StackInstances[_b]={fqn:"aws-cdk-lib.aws_codepipeline_actions.StackInstances",version:"2.201.0"};class StackSetParameters{static fromLiteral(parameters,usePreviousValues){return new class extends StackSetParameters{constructor(){super(...arguments),this._artifactsReferenced=[]}_render(){return[...Object.entries(parameters).map(([key,value])=>`ParameterKey=${key},ParameterValue=${value}`),...(usePreviousValues??[]).map(key=>`ParameterKey=${key},UsePreviousValue=true`)].join(" ")}}}static fromArtifactPath(artifactPath){try{jsiiDeprecationWarnings().aws_cdk_lib_aws_codepipeline_ArtifactPath(artifactPath)}catch(error){throw process.env.JSII_DEBUG!=="1"&&error.name==="DeprecationError"&&Error.captureStackTrace(error,this.fromArtifactPath),error}return new class extends StackSetParameters{constructor(){super(...arguments),this._artifactsReferenced=[artifactPath.artifact]}_render(){return artifactPath.location}}}}exports.StackSetParameters=StackSetParameters,_c=JSII_RTTI_SYMBOL_1,StackSetParameters[_c]={fqn:"aws-cdk-lib.aws_codepipeline_actions.StackSetParameters",version:"2.201.0"};class StackSetDeploymentModel{static organizations(props={}){try{jsiiDeprecationWarnings().aws_cdk_lib_aws_codepipeline_actions_OrganizationsDeploymentProps(props)}catch(error){throw process.env.JSII_DEBUG!=="1"&&error.name==="DeprecationError"&&Error.captureStackTrace(error,this.organizations),error}return new class extends StackSetDeploymentModel{_bind(){return{stackSetConfiguration:{PermissionModel:"SERVICE_MANAGED",OrganizationsAutoDeployment:props.autoDeployment}}}}}static selfManaged(props={}){try{jsiiDeprecationWarnings().aws_cdk_lib_aws_codepipeline_actions_SelfManagedDeploymentProps(props)}catch(error){throw process.env.JSII_DEBUG!=="1"&&error.name==="DeprecationError"&&Error.captureStackTrace(error,this.selfManaged),error}return new class extends StackSetDeploymentModel{_bind(scope){let administrationRole=props.administrationRole;return administrationRole||(administrationRole=new(iam()).Role(scope,"StackSetAdministrationRole",{assumedBy:new(iam()).ServicePrincipal("cloudformation.amazonaws.com",{conditions:{StringLike:{"aws:SourceArn":`arn:${cdk().Aws.PARTITION}:cloudformation:*:${cdk().Aws.ACCOUNT_ID}:stackset/*`}}})}),administrationRole.addToPrincipalPolicy(new(iam()).PolicyStatement({actions:["sts:AssumeRole"],resources:[`arn:${cdk().Aws.PARTITION}:iam::*:role/${props.executionRoleName??"AWSCloudFormationStackSetExecutionRole"}`]}))),{stackSetConfiguration:{PermissionModel:"SELF_MANAGED",AdministrationRoleArn:administrationRole.roleArn,ExecutionRoleName:props.executionRoleName},passedRoles:[administrationRole]}}}}}exports.StackSetDeploymentModel=StackSetDeploymentModel,_d=JSII_RTTI_SYMBOL_1,StackSetDeploymentModel[_d]={fqn:"aws-cdk-lib.aws_codepipeline_actions.StackSetDeploymentModel",version:"2.201.0"};var StackSetOrganizationsAutoDeployment;(function(StackSetOrganizationsAutoDeployment2){StackSetOrganizationsAutoDeployment2.ENABLED="Enabled",StackSetOrganizationsAutoDeployment2.DISABLED="Disabled",StackSetOrganizationsAutoDeployment2.ENABLED_WITH_STACK_RETENTION="EnabledWithStackRetention"})(StackSetOrganizationsAutoDeployment||(exports.StackSetOrganizationsAutoDeployment=StackSetOrganizationsAutoDeployment={}));
