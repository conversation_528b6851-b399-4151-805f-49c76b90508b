"use strict";var _a,_b,_c,_d;Object.defineProperty(exports,"__esModule",{value:!0}),exports.CloudFormationDeleteStackAction=exports.CloudFormationCreateUpdateStackAction=exports.CloudFormationCreateReplaceChangeSetAction=exports.CloudFormationExecuteChangeSetAction=void 0;var jsiiDeprecationWarnings=()=>{var tmp=require("../../../.warnings.jsii.js");return jsiiDeprecationWarnings=()=>tmp,tmp};const JSII_RTTI_SYMBOL_1=Symbol.for("jsii.rtti");var singleton_policy_1=()=>{var tmp=require("./private/singleton-policy");return singleton_policy_1=()=>tmp,tmp},cloudformation=()=>{var tmp=require("../../../aws-cloudformation");return cloudformation=()=>tmp,tmp},codepipeline=()=>{var tmp=require("../../../aws-codepipeline");return codepipeline=()=>tmp,tmp},iam=()=>{var tmp=require("../../../aws-iam");return iam=()=>tmp,tmp},cdk=()=>{var tmp=require("../../../core");return cdk=()=>tmp,tmp},action_1=()=>{var tmp=require("../action");return action_1=()=>tmp,tmp};class CloudFormationAction extends action_1().Action{constructor(props,inputs){super({...props,provider:"CloudFormation",category:codepipeline().ActionCategory.DEPLOY,artifactBounds:{minInputs:0,maxInputs:10,minOutputs:0,maxOutputs:1},inputs,outputs:props.outputFileName?[props.output||new(codepipeline()).Artifact(`${props.actionName}_${props.stackName}_Artifact`)]:void 0}),this.props=props}bound(_scope,_stage,options){const singletonPolicy=singleton_policy_1().SingletonPolicy.forRole(options.role);return(this.actionProperties.outputs||[]).length>0?options.bucket.grantReadWrite(singletonPolicy):(this.actionProperties.inputs||[]).length>0&&options.bucket.grantRead(singletonPolicy),{configuration:{StackName:this.props.stackName,OutputFileName:this.props.outputFileName}}}}class CloudFormationExecuteChangeSetAction extends CloudFormationAction{constructor(props){super(props,void 0);try{jsiiDeprecationWarnings().aws_cdk_lib_aws_codepipeline_actions_CloudFormationExecuteChangeSetActionProps(props)}catch(error){throw process.env.JSII_DEBUG!=="1"&&error.name==="DeprecationError"&&Error.captureStackTrace(error,CloudFormationExecuteChangeSetAction),error}this.props2=props}bound(scope,stage,options){try{jsiiDeprecationWarnings().aws_cdk_lib_aws_codepipeline_IStage(stage),jsiiDeprecationWarnings().aws_cdk_lib_aws_codepipeline_ActionBindOptions(options)}catch(error){throw process.env.JSII_DEBUG!=="1"&&error.name==="DeprecationError"&&Error.captureStackTrace(error,this.bound),error}singleton_policy_1().SingletonPolicy.forRole(options.role).grantExecuteChangeSet(this.props2);const actionConfig=super.bound(scope,stage,options);return{...actionConfig,configuration:{...actionConfig.configuration,ActionMode:"CHANGE_SET_EXECUTE",ChangeSetName:this.props2.changeSetName}}}}exports.CloudFormationExecuteChangeSetAction=CloudFormationExecuteChangeSetAction,_a=JSII_RTTI_SYMBOL_1,CloudFormationExecuteChangeSetAction[_a]={fqn:"aws-cdk-lib.aws_codepipeline_actions.CloudFormationExecuteChangeSetAction",version:"2.201.0"};class CloudFormationDeployAction extends CloudFormationAction{constructor(props,inputs){super(props,(props.extraInputs||[]).concat(inputs||[])),this.props2=props}addToDeploymentRolePolicy(statement){return this.getDeploymentRole("method addToRolePolicy()").addToPolicy(statement)}get deploymentRole(){return this.getDeploymentRole("property role()")}bound(scope,stage,options){if(this.props2.deploymentRole)this._deploymentRole=this.props2.deploymentRole;else{const roleStack=cdk().Stack.of(options.role),pipelineStack=cdk().Stack.of(scope);roleStack.account!==pipelineStack.account?this._deploymentRole=new(iam()).Role(roleStack,`${cdk().Names.nodeUniqueId(stage.pipeline.node)}-${stage.stageName}-${this.actionProperties.actionName}-DeploymentRole`,{assumedBy:new(iam()).ServicePrincipal("cloudformation.amazonaws.com"),roleName:cdk().PhysicalName.GENERATE_IF_NEEDED}):this._deploymentRole=new(iam()).Role(scope,"Role",{assumedBy:new(iam()).ServicePrincipal("cloudformation.amazonaws.com")}),options.bucket.grantRead(this._deploymentRole),this.props2.adminPermissions&&this._deploymentRole.addToPolicy(new(iam()).PolicyStatement({actions:["*"],resources:["*"]}))}singleton_policy_1().SingletonPolicy.forRole(options.role).grantPassRole(this._deploymentRole);const providedCapabilities=this.props2.cfnCapabilities??this.props2.capabilities?.map(c=>{switch(c){case cloudformation().CloudFormationCapabilities.NONE:return cdk().CfnCapabilities.NONE;case cloudformation().CloudFormationCapabilities.ANONYMOUS_IAM:return cdk().CfnCapabilities.ANONYMOUS_IAM;case cloudformation().CloudFormationCapabilities.NAMED_IAM:return cdk().CfnCapabilities.NAMED_IAM;case cloudformation().CloudFormationCapabilities.AUTO_EXPAND:return cdk().CfnCapabilities.AUTO_EXPAND}}),capabilities=this.props2.adminPermissions&&providedCapabilities===void 0?[cdk().CfnCapabilities.NAMED_IAM]:providedCapabilities,actionConfig=super.bound(scope,stage,options);return{...actionConfig,configuration:{...actionConfig.configuration,Capabilities:(0,singleton_policy_1().parseCapabilities)(capabilities),RoleArn:this.deploymentRole.roleArn,ParameterOverrides:cdk().Stack.of(scope).toJsonString(this.props2.parameterOverrides),TemplateConfiguration:this.props2.templateConfiguration?this.props2.templateConfiguration.location:void 0,StackName:this.props2.stackName}}}getDeploymentRole(member){if(this._deploymentRole)return this._deploymentRole;throw new(cdk()).UnscopedValidationError(`Cannot use the ${member} before the Action has been added to a Pipeline`)}}class CloudFormationCreateReplaceChangeSetAction extends CloudFormationDeployAction{constructor(props){super(props,props.templateConfiguration?[props.templatePath.artifact,props.templateConfiguration.artifact]:[props.templatePath.artifact]);try{jsiiDeprecationWarnings().aws_cdk_lib_aws_codepipeline_actions_CloudFormationCreateReplaceChangeSetActionProps(props)}catch(error){throw process.env.JSII_DEBUG!=="1"&&error.name==="DeprecationError"&&Error.captureStackTrace(error,CloudFormationCreateReplaceChangeSetAction),error}this.props3=props}bound(scope,stage,options){try{jsiiDeprecationWarnings().aws_cdk_lib_aws_codepipeline_IStage(stage),jsiiDeprecationWarnings().aws_cdk_lib_aws_codepipeline_ActionBindOptions(options)}catch(error){throw process.env.JSII_DEBUG!=="1"&&error.name==="DeprecationError"&&Error.captureStackTrace(error,this.bound),error}const actionConfig=super.bound(scope,stage,options);return singleton_policy_1().SingletonPolicy.forRole(options.role).grantCreateReplaceChangeSet(this.props3),{...actionConfig,configuration:{...actionConfig.configuration,ActionMode:"CHANGE_SET_REPLACE",ChangeSetName:this.props3.changeSetName,TemplatePath:this.props3.templatePath.location}}}}exports.CloudFormationCreateReplaceChangeSetAction=CloudFormationCreateReplaceChangeSetAction,_b=JSII_RTTI_SYMBOL_1,CloudFormationCreateReplaceChangeSetAction[_b]={fqn:"aws-cdk-lib.aws_codepipeline_actions.CloudFormationCreateReplaceChangeSetAction",version:"2.201.0"};class CloudFormationCreateUpdateStackAction extends CloudFormationDeployAction{constructor(props){super(props,props.templateConfiguration?[props.templatePath.artifact,props.templateConfiguration.artifact]:[props.templatePath.artifact]);try{jsiiDeprecationWarnings().aws_cdk_lib_aws_codepipeline_actions_CloudFormationCreateUpdateStackActionProps(props)}catch(error){throw process.env.JSII_DEBUG!=="1"&&error.name==="DeprecationError"&&Error.captureStackTrace(error,CloudFormationCreateUpdateStackAction),error}this.props3=props}bound(scope,stage,options){try{jsiiDeprecationWarnings().aws_cdk_lib_aws_codepipeline_IStage(stage),jsiiDeprecationWarnings().aws_cdk_lib_aws_codepipeline_ActionBindOptions(options)}catch(error){throw process.env.JSII_DEBUG!=="1"&&error.name==="DeprecationError"&&Error.captureStackTrace(error,this.bound),error}const actionConfig=super.bound(scope,stage,options);return singleton_policy_1().SingletonPolicy.forRole(options.role).grantCreateUpdateStack(this.props3),{...actionConfig,configuration:{...actionConfig.configuration,ActionMode:this.props3.replaceOnFailure?"REPLACE_ON_FAILURE":"CREATE_UPDATE",TemplatePath:this.props3.templatePath.location}}}}exports.CloudFormationCreateUpdateStackAction=CloudFormationCreateUpdateStackAction,_c=JSII_RTTI_SYMBOL_1,CloudFormationCreateUpdateStackAction[_c]={fqn:"aws-cdk-lib.aws_codepipeline_actions.CloudFormationCreateUpdateStackAction",version:"2.201.0"};class CloudFormationDeleteStackAction extends CloudFormationDeployAction{constructor(props){super(props,void 0);try{jsiiDeprecationWarnings().aws_cdk_lib_aws_codepipeline_actions_CloudFormationDeleteStackActionProps(props)}catch(error){throw process.env.JSII_DEBUG!=="1"&&error.name==="DeprecationError"&&Error.captureStackTrace(error,CloudFormationDeleteStackAction),error}this.props3=props}bound(scope,stage,options){try{jsiiDeprecationWarnings().aws_cdk_lib_aws_codepipeline_IStage(stage),jsiiDeprecationWarnings().aws_cdk_lib_aws_codepipeline_ActionBindOptions(options)}catch(error){throw process.env.JSII_DEBUG!=="1"&&error.name==="DeprecationError"&&Error.captureStackTrace(error,this.bound),error}const actionConfig=super.bound(scope,stage,options);return singleton_policy_1().SingletonPolicy.forRole(options.role).grantDeleteStack(this.props3),{...actionConfig,configuration:{...actionConfig.configuration,ActionMode:"DELETE_ONLY"}}}}exports.CloudFormationDeleteStackAction=CloudFormationDeleteStackAction,_d=JSII_RTTI_SYMBOL_1,CloudFormationDeleteStackAction[_d]={fqn:"aws-cdk-lib.aws_codepipeline_actions.CloudFormationDeleteStackAction",version:"2.201.0"};
