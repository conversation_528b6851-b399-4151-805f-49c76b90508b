"use strict";var _a;Object.defineProperty(exports,"__esModule",{value:!0}),exports.CodeDeployEcsDeployAction=void 0;var jsiiDeprecationWarnings=()=>{var tmp=require("../../../.warnings.jsii.js");return jsiiDeprecationWarnings=()=>tmp,tmp};const JSII_RTTI_SYMBOL_1=Symbol.for("jsii.rtti");var codepipeline=()=>{var tmp=require("../../../aws-codepipeline");return codepipeline=()=>tmp,tmp},iam=()=>{var tmp=require("../../../aws-iam");return iam=()=>tmp,tmp},core_1=()=>{var tmp=require("../../../core");return core_1=()=>tmp,tmp},action_1=()=>{var tmp=require("../action");return action_1=()=>tmp,tmp},stack_dependency_1=()=>{var tmp=require("../private/stack-dependency");return stack_dependency_1=()=>tmp,tmp};class CodeDeployEcsDeployAction extends action_1().Action{constructor(props){try{jsiiDeprecationWarnings().aws_cdk_lib_aws_codepipeline_actions_CodeDeployEcsDeployActionProps(props)}catch(error){throw process.env.JSII_DEBUG!=="1"&&error.name==="DeprecationError"&&Error.captureStackTrace(error,CodeDeployEcsDeployAction),error}const inputs=[];if(inputs.push(determineTaskDefinitionArtifact(props)),inputs.push(determineAppSpecArtifact(props)),props.containerImageInputs){if(props.containerImageInputs.length>4)throw new(core_1()).UnscopedValidationError(`Action cannot have more than 4 container image inputs, got: ${props.containerImageInputs.length}`);for(const imageInput of props.containerImageInputs)inputs.push(imageInput.input)}super({...props,resource:props.deploymentGroup,category:codepipeline().ActionCategory.DEPLOY,provider:"CodeDeployToECS",artifactBounds:{minInputs:1,maxInputs:5,minOutputs:0,maxOutputs:0},inputs}),this.actionProps=props}bound(_scope,_stage,options){try{jsiiDeprecationWarnings().aws_cdk_lib_aws_codepipeline_IStage(_stage),jsiiDeprecationWarnings().aws_cdk_lib_aws_codepipeline_ActionBindOptions(options)}catch(error){throw process.env.JSII_DEBUG!=="1"&&error.name==="DeprecationError"&&Error.captureStackTrace(error,this.bound),error}options.role.addToPolicy(new(iam()).PolicyStatement({resources:[this.actionProps.deploymentGroup.application.applicationArn],actions:["codedeploy:GetApplication","codedeploy:GetApplicationRevision","codedeploy:RegisterApplicationRevision"]})),options.role.addToPolicy(new(iam()).PolicyStatement({resources:[this.actionProps.deploymentGroup.deploymentGroupArn],actions:["codedeploy:CreateDeployment","codedeploy:GetDeployment"]})),options.role.addToPolicy(new(iam()).PolicyStatement({resources:[this.actionProps.deploymentGroup.deploymentConfig.deploymentConfigArn],actions:["codedeploy:GetDeploymentConfig"]})),options.role.addToPolicy(new(iam()).PolicyStatement({resources:["*"],actions:["ecs:RegisterTaskDefinition"]})),options.role.addToPolicy(new(iam()).PolicyStatement({actions:["iam:PassRole"],resources:["*"],conditions:{StringEqualsIfExists:{"iam:PassedToService":["ecs-tasks.amazonaws.com"]}}})),options.bucket.grantRead(options.role),(0,stack_dependency_1().forceSupportStackDependency)(options.bucket,options.role);const taskDefinitionTemplateArtifact=determineTaskDefinitionArtifact(this.actionProps),appSpecTemplateArtifact=determineAppSpecArtifact(this.actionProps),actionConfig={configuration:{ApplicationName:this.actionProps.deploymentGroup.application.applicationName,DeploymentGroupName:this.actionProps.deploymentGroup.deploymentGroupName,TaskDefinitionTemplateArtifact:core_1().Lazy.string({produce:()=>taskDefinitionTemplateArtifact.artifactName}),TaskDefinitionTemplatePath:this.actionProps.taskDefinitionTemplateFile?this.actionProps.taskDefinitionTemplateFile.fileName:"taskdef.json",AppSpecTemplateArtifact:core_1().Lazy.string({produce:()=>appSpecTemplateArtifact.artifactName}),AppSpecTemplatePath:this.actionProps.appSpecTemplateFile?this.actionProps.appSpecTemplateFile.fileName:"appspec.yaml"}};if(this.actionProps.containerImageInputs)for(let i=1;i<=this.actionProps.containerImageInputs.length;i++){const imageInput=this.actionProps.containerImageInputs[i-1];actionConfig.configuration[`Image${i}ArtifactName`]=core_1().Lazy.string({produce:()=>imageInput.input.artifactName}),actionConfig.configuration[`Image${i}ContainerName`]=imageInput.taskDefinitionPlaceholder?imageInput.taskDefinitionPlaceholder:"IMAGE"}return actionConfig}}exports.CodeDeployEcsDeployAction=CodeDeployEcsDeployAction,_a=JSII_RTTI_SYMBOL_1,CodeDeployEcsDeployAction[_a]={fqn:"aws-cdk-lib.aws_codepipeline_actions.CodeDeployEcsDeployAction",version:"2.201.0"};function determineTaskDefinitionArtifact(props){if(props.taskDefinitionTemplateFile&&props.taskDefinitionTemplateInput)throw new(core_1()).UnscopedValidationError("Exactly one of 'taskDefinitionTemplateInput' or 'taskDefinitionTemplateFile' can be provided in the ECS CodeDeploy Action");if(props.taskDefinitionTemplateFile)return props.taskDefinitionTemplateFile.artifact;if(props.taskDefinitionTemplateInput)return props.taskDefinitionTemplateInput;throw new(core_1()).UnscopedValidationError("Specifying one of 'taskDefinitionTemplateInput' or 'taskDefinitionTemplateFile' is required for the ECS CodeDeploy Action")}function determineAppSpecArtifact(props){if(props.appSpecTemplateFile&&props.appSpecTemplateInput)throw new(core_1()).UnscopedValidationError("Exactly one of 'appSpecTemplateInput' or 'appSpecTemplateFile' can be provided in the ECS CodeDeploy Action");if(props.appSpecTemplateFile)return props.appSpecTemplateFile.artifact;if(props.appSpecTemplateInput)return props.appSpecTemplateInput;throw new(core_1()).UnscopedValidationError("Specifying one of 'appSpecTemplateInput' or 'appSpecTemplateFile' is required for the ECS CodeDeploy Action")}
