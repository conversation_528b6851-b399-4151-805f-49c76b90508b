"use strict";var _a;Object.defineProperty(exports,"__esModule",{value:!0}),exports.BitBucketSourceAction=void 0;const JSII_RTTI_SYMBOL_1=Symbol.for("jsii.rtti");var source_action_1=()=>{var tmp=require("../codestar-connections/source-action");return source_action_1=()=>tmp,tmp};class BitBucketSourceAction{constructor(props){this.codeStarConnectionsSourceAction=new(source_action_1()).CodeStarConnectionsSourceAction(props)}get actionProperties(){return this.codeStarConnectionsSourceAction.actionProperties}bind(scope,stage,options){return this.codeStarConnectionsSourceAction.bind(scope,stage,options)}onStateChange(name,target,options){return this.codeStarConnectionsSourceAction.onStateChange(name,target,options)}}exports.BitBucketSourceAction=BitBucketSourceAction,_a=JSII_RTTI_SYMBOL_1,BitBucketSourceAction[_a]={fqn:"aws-cdk-lib.aws_codepipeline_actions.BitBucketSourceAction",version:"2.201.0"};
