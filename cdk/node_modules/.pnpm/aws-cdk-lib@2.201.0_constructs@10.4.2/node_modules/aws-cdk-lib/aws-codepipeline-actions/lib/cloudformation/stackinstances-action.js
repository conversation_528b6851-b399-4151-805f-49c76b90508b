"use strict";var _a;Object.defineProperty(exports,"__esModule",{value:!0}),exports.CloudFormationDeployStackInstancesAction=void 0;var jsiiDeprecationWarnings=()=>{var tmp=require("../../../.warnings.jsii.js");return jsiiDeprecationWarnings=()=>tmp,tmp};const JSII_RTTI_SYMBOL_1=Symbol.for("jsii.rtti");var singleton_policy_1=()=>{var tmp=require("./private/singleton-policy");return singleton_policy_1=()=>tmp,tmp},codepipeline=()=>{var tmp=require("../../../aws-codepipeline");return codepipeline=()=>tmp,tmp},action_1=()=>{var tmp=require("../action");return action_1=()=>tmp,tmp},common_1=()=>{var tmp=require("../common");return common_1=()=>tmp,tmp};class CloudFormationDeployStackInstancesAction extends action_1().Action{constructor(props){super({...props,region:props.stackSetRegion,provider:"CloudFormationStackInstances",category:codepipeline().ActionCategory.DEPLOY,artifactBounds:{minInputs:0,maxInputs:3,minOutputs:0,maxOutputs:0},inputs:[...props.parameterOverrides?._artifactsReferenced??[],...props.stackInstances?._artifactsReferenced??[]]});try{jsiiDeprecationWarnings().aws_cdk_lib_aws_codepipeline_actions_CloudFormationDeployStackInstancesActionProps(props)}catch(error){throw process.env.JSII_DEBUG!=="1"&&error.name==="DeprecationError"&&Error.captureStackTrace(error,CloudFormationDeployStackInstancesAction),error}this.props=props,(0,common_1().validatePercentage)("failureTolerancePercentage",props.failureTolerancePercentage),(0,common_1().validatePercentage)("maxAccountConcurrencyPercentage",props.maxAccountConcurrencyPercentage)}bound(scope,_stage,options){try{jsiiDeprecationWarnings().aws_cdk_lib_aws_codepipeline_IStage(_stage),jsiiDeprecationWarnings().aws_cdk_lib_aws_codepipeline_ActionBindOptions(options)}catch(error){throw process.env.JSII_DEBUG!=="1"&&error.name==="DeprecationError"&&Error.captureStackTrace(error,this.bound),error}const singletonPolicy=singleton_policy_1().SingletonPolicy.forRole(options.role);singletonPolicy.grantCreateUpdateStackSet(this.props);const instancesResult=this.props.stackInstances?._bind(scope);return(this.actionProperties.inputs||[]).length>0&&options.bucket.grantRead(singletonPolicy),{configuration:{StackSetName:this.props.stackSetName,ParameterOverrides:this.props.parameterOverrides?._render(),FailureTolerancePercentage:this.props.failureTolerancePercentage,MaxConcurrentPercentage:this.props.maxAccountConcurrencyPercentage,...instancesResult?.stackSetConfiguration}}}}exports.CloudFormationDeployStackInstancesAction=CloudFormationDeployStackInstancesAction,_a=JSII_RTTI_SYMBOL_1,CloudFormationDeployStackInstancesAction[_a]={fqn:"aws-cdk-lib.aws_codepipeline_actions.CloudFormationDeployStackInstancesAction",version:"2.201.0"};
