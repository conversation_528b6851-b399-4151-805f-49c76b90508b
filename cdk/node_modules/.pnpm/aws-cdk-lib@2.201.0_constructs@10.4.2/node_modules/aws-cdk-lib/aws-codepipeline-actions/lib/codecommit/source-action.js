"use strict";var _a;Object.defineProperty(exports,"__esModule",{value:!0}),exports.CodeCommitSourceAction=exports.CodeCommitTrigger=void 0;var jsiiDeprecationWarnings=()=>{var tmp=require("../../../.warnings.jsii.js");return jsiiDeprecationWarnings=()=>tmp,tmp};const JSII_RTTI_SYMBOL_1=Symbol.for("jsii.rtti");var codepipeline=()=>{var tmp=require("../../../aws-codepipeline");return codepipeline=()=>tmp,tmp},targets=()=>{var tmp=require("../../../aws-events-targets");return targets=()=>tmp,tmp},iam=()=>{var tmp=require("../../../aws-iam");return iam=()=>tmp,tmp},core_1=()=>{var tmp=require("../../../core");return core_1=()=>tmp,tmp},cx_api_1=()=>{var tmp=require("../../../cx-api");return cx_api_1=()=>tmp,tmp},action_1=()=>{var tmp=require("../action");return action_1=()=>tmp,tmp},common_1=()=>{var tmp=require("../common");return common_1=()=>tmp,tmp},CodeCommitTrigger;(function(CodeCommitTrigger2){CodeCommitTrigger2.NONE="None",CodeCommitTrigger2.POLL="Poll",CodeCommitTrigger2.EVENTS="Events"})(CodeCommitTrigger||(exports.CodeCommitTrigger=CodeCommitTrigger={}));class CodeCommitSourceAction extends action_1().Action{constructor(props){try{jsiiDeprecationWarnings().aws_cdk_lib_aws_codepipeline_actions_CodeCommitSourceActionProps(props)}catch(error){throw process.env.JSII_DEBUG!=="1"&&error.name==="DeprecationError"&&Error.captureStackTrace(error,CodeCommitSourceAction),error}const branch=props.branch??CodeCommitSourceAction.OLD_DEFAULT_BRANCH_NAME;if(!branch)throw new(core_1()).UnscopedValidationError("'branch' parameter cannot be an empty string");props.codeBuildCloneOutput===!0&&props.output.setMetadata(CodeCommitSourceAction._FULL_CLONE_ARN_PROPERTY,props.repository.repositoryArn),super({...props,resource:props.repository,category:codepipeline().ActionCategory.SOURCE,provider:"CodeCommit",artifactBounds:(0,common_1().sourceArtifactBounds)(),outputs:[props.output]}),this.branch=branch,this.props=props}get variables(){return{repositoryName:this.variableExpression("RepositoryName"),branchName:this.variableExpression("BranchName"),authorDate:this.variableExpression("AuthorDate"),committerDate:this.variableExpression("CommitterDate"),commitId:this.variableExpression("CommitId"),commitMessage:this.variableExpression("CommitMessage")}}bound(_scope,stage,options){try{jsiiDeprecationWarnings().aws_cdk_lib_aws_codepipeline_IStage(stage),jsiiDeprecationWarnings().aws_cdk_lib_aws_codepipeline_ActionBindOptions(options)}catch(error){throw process.env.JSII_DEBUG!=="1"&&error.name==="DeprecationError"&&Error.captureStackTrace(error,this.bound),error}const branchOrDefault=this.getBranchOrDefault(_scope),createEvent=this.props.trigger===void 0||this.props.trigger===CodeCommitTrigger.EVENTS,eventId=this.generateEventId(stage);return createEvent&&this.props.customEventRule===void 0?this.props.repository.onCommit(eventId,{target:new(targets()).CodePipeline(stage.pipeline,{eventRole:this.props.eventRole}),branches:[branchOrDefault],crossStackScope:stage.pipeline}):this.props.customEventRule!==void 0&&this.props.repository.onEvent(eventId,{...this.props.customEventRule,crossStackScope:stage.pipeline}),options.bucket.grantReadWrite(options.role),core_1().Token.compareStrings(this.props.repository.env.account,core_1().Stack.of(stage.pipeline).account)===core_1().TokenComparison.DIFFERENT&&options.bucket.grantPutAcl(options.role),options.role.addToPrincipalPolicy(new(iam()).PolicyStatement({resources:[this.props.repository.repositoryArn],actions:["codecommit:GetBranch","codecommit:GetCommit","codecommit:UploadArchive","codecommit:GetUploadArchiveStatus","codecommit:CancelUploadArchive",...this.props.codeBuildCloneOutput===!0?["codecommit:GetRepository"]:[]]})),{configuration:{RepositoryName:this.props.repository.repositoryName,BranchName:branchOrDefault,PollForSourceChanges:this.props.trigger===CodeCommitTrigger.POLL,OutputArtifactFormat:this.props.codeBuildCloneOutput===!0?"CODEBUILD_CLONE_REF":void 0}}}getBranchOrDefault(scope){const defaultBranch=core_1().FeatureFlags.of(scope).isEnabled(cx_api_1().CODECOMMIT_SOURCE_ACTION_DEFAULT_BRANCH_NAME)?CodeCommitSourceAction.NEW_DEFAULT_BRANCH_NAME:CodeCommitSourceAction.OLD_DEFAULT_BRANCH_NAME;return this.props.branch===void 0?defaultBranch:this.branch}generateEventId(stage){const baseId=core_1().Names.nodeUniqueId(stage.pipeline.node);if(core_1().Token.isUnresolved(this.branch)){let candidate="",counter=0;do candidate=this.eventIdFromPrefix(`${baseId}${counter}`),counter+=1;while(this.props.repository.node.tryFindChild(candidate)!==void 0);return candidate}else{const branchIdDisambiguator=this.props.branch===void 0||this.branch===CodeCommitSourceAction.OLD_DEFAULT_BRANCH_NAME?"":`-${this.branch}-`;return this.eventIdFromPrefix(`${baseId}${branchIdDisambiguator}`)}}eventIdFromPrefix(eventIdPrefix){return`${eventIdPrefix}EventRule`}}exports.CodeCommitSourceAction=CodeCommitSourceAction,_a=JSII_RTTI_SYMBOL_1,CodeCommitSourceAction[_a]={fqn:"aws-cdk-lib.aws_codepipeline_actions.CodeCommitSourceAction",version:"2.201.0"},CodeCommitSourceAction._FULL_CLONE_ARN_PROPERTY="CodeCommitCloneRepositoryArn",CodeCommitSourceAction.NEW_DEFAULT_BRANCH_NAME="main",CodeCommitSourceAction.OLD_DEFAULT_BRANCH_NAME="master";
