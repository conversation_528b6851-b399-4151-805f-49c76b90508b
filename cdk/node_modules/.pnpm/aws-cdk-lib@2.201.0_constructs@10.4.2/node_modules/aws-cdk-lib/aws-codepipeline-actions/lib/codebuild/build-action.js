"use strict";var _a;Object.defineProperty(exports,"__esModule",{value:!0}),exports.CodeBuildAction=exports.CodeBuildActionType=void 0;var jsiiDeprecationWarnings=()=>{var tmp=require("../../../.warnings.jsii.js");return jsiiDeprecationWarnings=()=>tmp,tmp};const JSII_RTTI_SYMBOL_1=Symbol.for("jsii.rtti");var __1=()=>{var tmp=require("..");return __1=()=>tmp,tmp},codebuild=()=>{var tmp=require("../../../aws-codebuild");return codebuild=()=>tmp,tmp},codepipeline=()=>{var tmp=require("../../../aws-codepipeline");return codepipeline=()=>tmp,tmp},iam=()=>{var tmp=require("../../../aws-iam");return iam=()=>tmp,tmp},cdk=()=>{var tmp=require("../../../core");return cdk=()=>tmp,tmp},action_1=()=>{var tmp=require("../action");return action_1=()=>tmp,tmp},source_action_1=()=>{var tmp=require("../codecommit/source-action");return source_action_1=()=>tmp,tmp},CodeBuildActionType;(function(CodeBuildActionType2){CodeBuildActionType2[CodeBuildActionType2.BUILD=0]="BUILD",CodeBuildActionType2[CodeBuildActionType2.TEST=1]="TEST"})(CodeBuildActionType||(exports.CodeBuildActionType=CodeBuildActionType={}));class CodeBuildAction extends action_1().Action{constructor(props){super({...props,category:props.type===CodeBuildActionType.TEST?codepipeline().ActionCategory.TEST:codepipeline().ActionCategory.BUILD,provider:"CodeBuild",artifactBounds:{minInputs:1,maxInputs:5,minOutputs:0,maxOutputs:5},inputs:[props.input,...props.extraInputs||[]],resource:props.project});try{jsiiDeprecationWarnings().aws_cdk_lib_aws_codepipeline_actions_CodeBuildActionProps(props)}catch(error){throw process.env.JSII_DEBUG!=="1"&&error.name==="DeprecationError"&&Error.captureStackTrace(error,CodeBuildAction),error}this.props=props}variable(variableName){return this.variableExpression(variableName)}bound(scope,_stage,options){try{jsiiDeprecationWarnings().aws_cdk_lib_aws_codepipeline_IStage(_stage),jsiiDeprecationWarnings().aws_cdk_lib_aws_codepipeline_ActionBindOptions(options)}catch(error){throw process.env.JSII_DEBUG!=="1"&&error.name==="DeprecationError"&&Error.captureStackTrace(error,this.bound),error}if((this.actionProperties.outputs||[]).length>0){const pipelineStack=cdk().Stack.of(scope),projectStack=cdk().Stack.of(this.props.project);if(pipelineStack.account!==projectStack.account)throw new(cdk()).ValidationError("A cross-account CodeBuild action cannot have outputs. This is a known CodeBuild limitation. See https://github.com/aws/aws-cdk/issues/4169 for details",scope)}options.role.addToPolicy(new(iam()).PolicyStatement({resources:[this.props.project.projectArn],actions:[`codebuild:${this.props.executeBatchBuild?"BatchGetBuildBatches":"BatchGetBuilds"}`,`codebuild:${this.props.executeBatchBuild?"StartBuildBatch":"StartBuild"}`,`codebuild:${this.props.executeBatchBuild?"StopBuildBatch":"StopBuild"}`]})),this.props.project.role&&((this.actionProperties.outputs||[]).length>0?options.bucket.grantReadWrite(this.props.project):options.bucket.grantRead(this.props.project)),this.props.project instanceof codebuild().Project&&this.props.project.bindToCodePipeline(scope,{artifactBucket:options.bucket});for(const inputArtifact of this.actionProperties.inputs||[]){const connectionArn=inputArtifact.getMetadata(__1().CodeStarConnectionsSourceAction._CONNECTION_ARN_PROPERTY);connectionArn&&this.props.project.addToRolePolicy(new(iam()).PolicyStatement({actions:["codestar-connections:UseConnection"],resources:[connectionArn]}));const codecommitRepositoryArn=inputArtifact.getMetadata(source_action_1().CodeCommitSourceAction._FULL_CLONE_ARN_PROPERTY);codecommitRepositoryArn&&this.props.project.addToRolePolicy(new(iam()).PolicyStatement({actions:["codecommit:GitPull"],resources:[codecommitRepositoryArn]}))}const configuration={ProjectName:this.props.project.projectName,EnvironmentVariables:this.props.environmentVariables&&cdk().Stack.of(scope).toJsonString(codebuild().Project.serializeEnvVariables(this.props.environmentVariables,this.props.checkSecretsInPlainTextEnvVariables??!0,this.props.project))};return(this.actionProperties.inputs||[]).length>1&&(configuration.PrimarySource=cdk().Lazy.string({produce:()=>this.props.input.artifactName})),this.props.executeBatchBuild&&(configuration.BatchEnabled="true",this.props.project.enableBatchBuilds(),this.props.combineBatchBuildArtifacts&&(configuration.CombineArtifacts="true")),{configuration}}}exports.CodeBuildAction=CodeBuildAction,_a=JSII_RTTI_SYMBOL_1,CodeBuildAction[_a]={fqn:"aws-cdk-lib.aws_codepipeline_actions.CodeBuildAction",version:"2.201.0"};
