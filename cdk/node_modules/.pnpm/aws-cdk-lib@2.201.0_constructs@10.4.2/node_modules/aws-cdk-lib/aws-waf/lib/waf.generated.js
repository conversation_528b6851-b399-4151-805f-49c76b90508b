"use strict";var _a,_b,_c,_d,_e,_f,_g;Object.defineProperty(exports,"__esModule",{value:!0}),exports.CfnXssMatchSet=exports.CfnWebACL=exports.CfnSqlInjectionMatchSet=exports.CfnSizeConstraintSet=exports.CfnRule=exports.CfnIPSet=exports.CfnByteMatchSet=void 0;var jsiiDeprecationWarnings=()=>{var tmp=require("../../.warnings.jsii.js");return jsiiDeprecationWarnings=()=>tmp,tmp};const JSII_RTTI_SYMBOL_1=Symbol.for("jsii.rtti");var cdk=()=>{var tmp=require("../../core");return cdk=()=>tmp,tmp},cfn_parse=()=>{var tmp=require("../../core/lib/helpers-internal");return cfn_parse=()=>tmp,tmp},cdk_errors=()=>{var tmp=require("../../core/lib/errors");return cdk_errors=()=>tmp,tmp};class CfnByteMatchSet extends cdk().CfnResource{static _fromCloudFormation(scope,id,resourceAttributes,options){resourceAttributes=resourceAttributes||{};const resourceProperties=options.parser.parseValue(resourceAttributes.Properties),propsResult=CfnByteMatchSetPropsFromCloudFormation(resourceProperties);if(cdk().isResolvableObject(propsResult.value))throw new(cdk_errors()).ValidationError("Unexpected IResolvable",scope);const ret=new CfnByteMatchSet(scope,id,propsResult.value);for(const[propKey,propVal]of Object.entries(propsResult.extraProperties))ret.addPropertyOverride(propKey,propVal);return options.parser.handleAttributes(ret,resourceAttributes,id),ret}constructor(scope,id,props){super(scope,id,{type:CfnByteMatchSet.CFN_RESOURCE_TYPE_NAME,properties:props});try{jsiiDeprecationWarnings().aws_cdk_lib_aws_waf_CfnByteMatchSetProps(props)}catch(error){throw process.env.JSII_DEBUG!=="1"&&error.name==="DeprecationError"&&Error.captureStackTrace(error,CfnByteMatchSet),error}cdk().requireProperty(props,"name",this),this.attrId=cdk().Token.asString(this.getAtt("Id",cdk().ResolutionTypeHint.STRING)),this.byteMatchTuples=props.byteMatchTuples,this.name=props.name}get cfnProperties(){return{byteMatchTuples:this.byteMatchTuples,name:this.name}}inspect(inspector){try{jsiiDeprecationWarnings().aws_cdk_lib_TreeInspector(inspector)}catch(error){throw process.env.JSII_DEBUG!=="1"&&error.name==="DeprecationError"&&Error.captureStackTrace(error,this.inspect),error}inspector.addAttribute("aws:cdk:cloudformation:type",CfnByteMatchSet.CFN_RESOURCE_TYPE_NAME),inspector.addAttribute("aws:cdk:cloudformation:props",this.cfnProperties)}renderProperties(props){return convertCfnByteMatchSetPropsToCloudFormation(props)}}exports.CfnByteMatchSet=CfnByteMatchSet,_a=JSII_RTTI_SYMBOL_1,CfnByteMatchSet[_a]={fqn:"aws-cdk-lib.aws_waf.CfnByteMatchSet",version:"2.201.0"},CfnByteMatchSet.CFN_RESOURCE_TYPE_NAME="AWS::WAF::ByteMatchSet";function CfnByteMatchSetFieldToMatchPropertyValidator(properties){if(!cdk().canInspect(properties))return cdk().VALIDATION_SUCCESS;const errors=new(cdk()).ValidationResults;return properties&&typeof properties=="object"&&!Array.isArray(properties)||errors.collect(new(cdk()).ValidationResult("Expected an object, but received: "+JSON.stringify(properties))),errors.collect(cdk().propertyValidator("data",cdk().validateString)(properties.data)),errors.collect(cdk().propertyValidator("type",cdk().requiredValidator)(properties.type)),errors.collect(cdk().propertyValidator("type",cdk().validateString)(properties.type)),errors.wrap('supplied properties not correct for "FieldToMatchProperty"')}function convertCfnByteMatchSetFieldToMatchPropertyToCloudFormation(properties){return cdk().canInspect(properties)?(CfnByteMatchSetFieldToMatchPropertyValidator(properties).assertSuccess(),{Data:cdk().stringToCloudFormation(properties.data),Type:cdk().stringToCloudFormation(properties.type)}):properties}function CfnByteMatchSetFieldToMatchPropertyFromCloudFormation(properties){if(cdk().isResolvableObject(properties))return new(cfn_parse()).FromCloudFormationResult(properties);if(properties=properties??{},!(properties&&typeof properties=="object"&&!Array.isArray(properties)))return new(cfn_parse()).FromCloudFormationResult(properties);const ret=new(cfn_parse()).FromCloudFormationPropertyObject;return ret.addPropertyResult("data","Data",properties.Data!=null?cfn_parse().FromCloudFormation.getString(properties.Data):void 0),ret.addPropertyResult("type","Type",properties.Type!=null?cfn_parse().FromCloudFormation.getString(properties.Type):void 0),ret.addUnrecognizedPropertiesAsExtra(properties),ret}function CfnByteMatchSetByteMatchTuplePropertyValidator(properties){if(!cdk().canInspect(properties))return cdk().VALIDATION_SUCCESS;const errors=new(cdk()).ValidationResults;return properties&&typeof properties=="object"&&!Array.isArray(properties)||errors.collect(new(cdk()).ValidationResult("Expected an object, but received: "+JSON.stringify(properties))),errors.collect(cdk().propertyValidator("fieldToMatch",cdk().requiredValidator)(properties.fieldToMatch)),errors.collect(cdk().propertyValidator("fieldToMatch",CfnByteMatchSetFieldToMatchPropertyValidator)(properties.fieldToMatch)),errors.collect(cdk().propertyValidator("positionalConstraint",cdk().requiredValidator)(properties.positionalConstraint)),errors.collect(cdk().propertyValidator("positionalConstraint",cdk().validateString)(properties.positionalConstraint)),errors.collect(cdk().propertyValidator("targetString",cdk().validateString)(properties.targetString)),errors.collect(cdk().propertyValidator("targetStringBase64",cdk().validateString)(properties.targetStringBase64)),errors.collect(cdk().propertyValidator("textTransformation",cdk().requiredValidator)(properties.textTransformation)),errors.collect(cdk().propertyValidator("textTransformation",cdk().validateString)(properties.textTransformation)),errors.wrap('supplied properties not correct for "ByteMatchTupleProperty"')}function convertCfnByteMatchSetByteMatchTuplePropertyToCloudFormation(properties){return cdk().canInspect(properties)?(CfnByteMatchSetByteMatchTuplePropertyValidator(properties).assertSuccess(),{FieldToMatch:convertCfnByteMatchSetFieldToMatchPropertyToCloudFormation(properties.fieldToMatch),PositionalConstraint:cdk().stringToCloudFormation(properties.positionalConstraint),TargetString:cdk().stringToCloudFormation(properties.targetString),TargetStringBase64:cdk().stringToCloudFormation(properties.targetStringBase64),TextTransformation:cdk().stringToCloudFormation(properties.textTransformation)}):properties}function CfnByteMatchSetByteMatchTuplePropertyFromCloudFormation(properties){if(cdk().isResolvableObject(properties))return new(cfn_parse()).FromCloudFormationResult(properties);if(properties=properties??{},!(properties&&typeof properties=="object"&&!Array.isArray(properties)))return new(cfn_parse()).FromCloudFormationResult(properties);const ret=new(cfn_parse()).FromCloudFormationPropertyObject;return ret.addPropertyResult("fieldToMatch","FieldToMatch",properties.FieldToMatch!=null?CfnByteMatchSetFieldToMatchPropertyFromCloudFormation(properties.FieldToMatch):void 0),ret.addPropertyResult("positionalConstraint","PositionalConstraint",properties.PositionalConstraint!=null?cfn_parse().FromCloudFormation.getString(properties.PositionalConstraint):void 0),ret.addPropertyResult("targetString","TargetString",properties.TargetString!=null?cfn_parse().FromCloudFormation.getString(properties.TargetString):void 0),ret.addPropertyResult("targetStringBase64","TargetStringBase64",properties.TargetStringBase64!=null?cfn_parse().FromCloudFormation.getString(properties.TargetStringBase64):void 0),ret.addPropertyResult("textTransformation","TextTransformation",properties.TextTransformation!=null?cfn_parse().FromCloudFormation.getString(properties.TextTransformation):void 0),ret.addUnrecognizedPropertiesAsExtra(properties),ret}function CfnByteMatchSetPropsValidator(properties){if(!cdk().canInspect(properties))return cdk().VALIDATION_SUCCESS;const errors=new(cdk()).ValidationResults;return properties&&typeof properties=="object"&&!Array.isArray(properties)||errors.collect(new(cdk()).ValidationResult("Expected an object, but received: "+JSON.stringify(properties))),errors.collect(cdk().propertyValidator("byteMatchTuples",cdk().listValidator(CfnByteMatchSetByteMatchTuplePropertyValidator))(properties.byteMatchTuples)),errors.collect(cdk().propertyValidator("name",cdk().requiredValidator)(properties.name)),errors.collect(cdk().propertyValidator("name",cdk().validateString)(properties.name)),errors.wrap('supplied properties not correct for "CfnByteMatchSetProps"')}function convertCfnByteMatchSetPropsToCloudFormation(properties){return cdk().canInspect(properties)?(CfnByteMatchSetPropsValidator(properties).assertSuccess(),{ByteMatchTuples:cdk().listMapper(convertCfnByteMatchSetByteMatchTuplePropertyToCloudFormation)(properties.byteMatchTuples),Name:cdk().stringToCloudFormation(properties.name)}):properties}function CfnByteMatchSetPropsFromCloudFormation(properties){if(cdk().isResolvableObject(properties))return new(cfn_parse()).FromCloudFormationResult(properties);if(properties=properties??{},!(properties&&typeof properties=="object"&&!Array.isArray(properties)))return new(cfn_parse()).FromCloudFormationResult(properties);const ret=new(cfn_parse()).FromCloudFormationPropertyObject;return ret.addPropertyResult("byteMatchTuples","ByteMatchTuples",properties.ByteMatchTuples!=null?cfn_parse().FromCloudFormation.getArray(CfnByteMatchSetByteMatchTuplePropertyFromCloudFormation)(properties.ByteMatchTuples):void 0),ret.addPropertyResult("name","Name",properties.Name!=null?cfn_parse().FromCloudFormation.getString(properties.Name):void 0),ret.addUnrecognizedPropertiesAsExtra(properties),ret}class CfnIPSet extends cdk().CfnResource{static _fromCloudFormation(scope,id,resourceAttributes,options){resourceAttributes=resourceAttributes||{};const resourceProperties=options.parser.parseValue(resourceAttributes.Properties),propsResult=CfnIPSetPropsFromCloudFormation(resourceProperties);if(cdk().isResolvableObject(propsResult.value))throw new(cdk_errors()).ValidationError("Unexpected IResolvable",scope);const ret=new CfnIPSet(scope,id,propsResult.value);for(const[propKey,propVal]of Object.entries(propsResult.extraProperties))ret.addPropertyOverride(propKey,propVal);return options.parser.handleAttributes(ret,resourceAttributes,id),ret}constructor(scope,id,props){super(scope,id,{type:CfnIPSet.CFN_RESOURCE_TYPE_NAME,properties:props});try{jsiiDeprecationWarnings().aws_cdk_lib_aws_waf_CfnIPSetProps(props)}catch(error){throw process.env.JSII_DEBUG!=="1"&&error.name==="DeprecationError"&&Error.captureStackTrace(error,CfnIPSet),error}cdk().requireProperty(props,"name",this),this.attrId=cdk().Token.asString(this.getAtt("Id",cdk().ResolutionTypeHint.STRING)),this.ipSetDescriptors=props.ipSetDescriptors,this.name=props.name}get cfnProperties(){return{ipSetDescriptors:this.ipSetDescriptors,name:this.name}}inspect(inspector){try{jsiiDeprecationWarnings().aws_cdk_lib_TreeInspector(inspector)}catch(error){throw process.env.JSII_DEBUG!=="1"&&error.name==="DeprecationError"&&Error.captureStackTrace(error,this.inspect),error}inspector.addAttribute("aws:cdk:cloudformation:type",CfnIPSet.CFN_RESOURCE_TYPE_NAME),inspector.addAttribute("aws:cdk:cloudformation:props",this.cfnProperties)}renderProperties(props){return convertCfnIPSetPropsToCloudFormation(props)}}exports.CfnIPSet=CfnIPSet,_b=JSII_RTTI_SYMBOL_1,CfnIPSet[_b]={fqn:"aws-cdk-lib.aws_waf.CfnIPSet",version:"2.201.0"},CfnIPSet.CFN_RESOURCE_TYPE_NAME="AWS::WAF::IPSet";function CfnIPSetIPSetDescriptorPropertyValidator(properties){if(!cdk().canInspect(properties))return cdk().VALIDATION_SUCCESS;const errors=new(cdk()).ValidationResults;return properties&&typeof properties=="object"&&!Array.isArray(properties)||errors.collect(new(cdk()).ValidationResult("Expected an object, but received: "+JSON.stringify(properties))),errors.collect(cdk().propertyValidator("type",cdk().requiredValidator)(properties.type)),errors.collect(cdk().propertyValidator("type",cdk().validateString)(properties.type)),errors.collect(cdk().propertyValidator("value",cdk().requiredValidator)(properties.value)),errors.collect(cdk().propertyValidator("value",cdk().validateString)(properties.value)),errors.wrap('supplied properties not correct for "IPSetDescriptorProperty"')}function convertCfnIPSetIPSetDescriptorPropertyToCloudFormation(properties){return cdk().canInspect(properties)?(CfnIPSetIPSetDescriptorPropertyValidator(properties).assertSuccess(),{Type:cdk().stringToCloudFormation(properties.type),Value:cdk().stringToCloudFormation(properties.value)}):properties}function CfnIPSetIPSetDescriptorPropertyFromCloudFormation(properties){if(cdk().isResolvableObject(properties))return new(cfn_parse()).FromCloudFormationResult(properties);if(properties=properties??{},!(properties&&typeof properties=="object"&&!Array.isArray(properties)))return new(cfn_parse()).FromCloudFormationResult(properties);const ret=new(cfn_parse()).FromCloudFormationPropertyObject;return ret.addPropertyResult("type","Type",properties.Type!=null?cfn_parse().FromCloudFormation.getString(properties.Type):void 0),ret.addPropertyResult("value","Value",properties.Value!=null?cfn_parse().FromCloudFormation.getString(properties.Value):void 0),ret.addUnrecognizedPropertiesAsExtra(properties),ret}function CfnIPSetPropsValidator(properties){if(!cdk().canInspect(properties))return cdk().VALIDATION_SUCCESS;const errors=new(cdk()).ValidationResults;return properties&&typeof properties=="object"&&!Array.isArray(properties)||errors.collect(new(cdk()).ValidationResult("Expected an object, but received: "+JSON.stringify(properties))),errors.collect(cdk().propertyValidator("ipSetDescriptors",cdk().listValidator(CfnIPSetIPSetDescriptorPropertyValidator))(properties.ipSetDescriptors)),errors.collect(cdk().propertyValidator("name",cdk().requiredValidator)(properties.name)),errors.collect(cdk().propertyValidator("name",cdk().validateString)(properties.name)),errors.wrap('supplied properties not correct for "CfnIPSetProps"')}function convertCfnIPSetPropsToCloudFormation(properties){return cdk().canInspect(properties)?(CfnIPSetPropsValidator(properties).assertSuccess(),{IPSetDescriptors:cdk().listMapper(convertCfnIPSetIPSetDescriptorPropertyToCloudFormation)(properties.ipSetDescriptors),Name:cdk().stringToCloudFormation(properties.name)}):properties}function CfnIPSetPropsFromCloudFormation(properties){if(cdk().isResolvableObject(properties))return new(cfn_parse()).FromCloudFormationResult(properties);if(properties=properties??{},!(properties&&typeof properties=="object"&&!Array.isArray(properties)))return new(cfn_parse()).FromCloudFormationResult(properties);const ret=new(cfn_parse()).FromCloudFormationPropertyObject;return ret.addPropertyResult("ipSetDescriptors","IPSetDescriptors",properties.IPSetDescriptors!=null?cfn_parse().FromCloudFormation.getArray(CfnIPSetIPSetDescriptorPropertyFromCloudFormation)(properties.IPSetDescriptors):void 0),ret.addPropertyResult("name","Name",properties.Name!=null?cfn_parse().FromCloudFormation.getString(properties.Name):void 0),ret.addUnrecognizedPropertiesAsExtra(properties),ret}class CfnRule extends cdk().CfnResource{static _fromCloudFormation(scope,id,resourceAttributes,options){resourceAttributes=resourceAttributes||{};const resourceProperties=options.parser.parseValue(resourceAttributes.Properties),propsResult=CfnRulePropsFromCloudFormation(resourceProperties);if(cdk().isResolvableObject(propsResult.value))throw new(cdk_errors()).ValidationError("Unexpected IResolvable",scope);const ret=new CfnRule(scope,id,propsResult.value);for(const[propKey,propVal]of Object.entries(propsResult.extraProperties))ret.addPropertyOverride(propKey,propVal);return options.parser.handleAttributes(ret,resourceAttributes,id),ret}constructor(scope,id,props){super(scope,id,{type:CfnRule.CFN_RESOURCE_TYPE_NAME,properties:props});try{jsiiDeprecationWarnings().aws_cdk_lib_aws_waf_CfnRuleProps(props)}catch(error){throw process.env.JSII_DEBUG!=="1"&&error.name==="DeprecationError"&&Error.captureStackTrace(error,CfnRule),error}cdk().requireProperty(props,"metricName",this),cdk().requireProperty(props,"name",this),this.attrId=cdk().Token.asString(this.getAtt("Id",cdk().ResolutionTypeHint.STRING)),this.metricName=props.metricName,this.name=props.name,this.predicates=props.predicates}get cfnProperties(){return{metricName:this.metricName,name:this.name,predicates:this.predicates}}inspect(inspector){try{jsiiDeprecationWarnings().aws_cdk_lib_TreeInspector(inspector)}catch(error){throw process.env.JSII_DEBUG!=="1"&&error.name==="DeprecationError"&&Error.captureStackTrace(error,this.inspect),error}inspector.addAttribute("aws:cdk:cloudformation:type",CfnRule.CFN_RESOURCE_TYPE_NAME),inspector.addAttribute("aws:cdk:cloudformation:props",this.cfnProperties)}renderProperties(props){return convertCfnRulePropsToCloudFormation(props)}}exports.CfnRule=CfnRule,_c=JSII_RTTI_SYMBOL_1,CfnRule[_c]={fqn:"aws-cdk-lib.aws_waf.CfnRule",version:"2.201.0"},CfnRule.CFN_RESOURCE_TYPE_NAME="AWS::WAF::Rule";function CfnRulePredicatePropertyValidator(properties){if(!cdk().canInspect(properties))return cdk().VALIDATION_SUCCESS;const errors=new(cdk()).ValidationResults;return properties&&typeof properties=="object"&&!Array.isArray(properties)||errors.collect(new(cdk()).ValidationResult("Expected an object, but received: "+JSON.stringify(properties))),errors.collect(cdk().propertyValidator("dataId",cdk().requiredValidator)(properties.dataId)),errors.collect(cdk().propertyValidator("dataId",cdk().validateString)(properties.dataId)),errors.collect(cdk().propertyValidator("negated",cdk().requiredValidator)(properties.negated)),errors.collect(cdk().propertyValidator("negated",cdk().validateBoolean)(properties.negated)),errors.collect(cdk().propertyValidator("type",cdk().requiredValidator)(properties.type)),errors.collect(cdk().propertyValidator("type",cdk().validateString)(properties.type)),errors.wrap('supplied properties not correct for "PredicateProperty"')}function convertCfnRulePredicatePropertyToCloudFormation(properties){return cdk().canInspect(properties)?(CfnRulePredicatePropertyValidator(properties).assertSuccess(),{DataId:cdk().stringToCloudFormation(properties.dataId),Negated:cdk().booleanToCloudFormation(properties.negated),Type:cdk().stringToCloudFormation(properties.type)}):properties}function CfnRulePredicatePropertyFromCloudFormation(properties){if(cdk().isResolvableObject(properties))return new(cfn_parse()).FromCloudFormationResult(properties);if(properties=properties??{},!(properties&&typeof properties=="object"&&!Array.isArray(properties)))return new(cfn_parse()).FromCloudFormationResult(properties);const ret=new(cfn_parse()).FromCloudFormationPropertyObject;return ret.addPropertyResult("dataId","DataId",properties.DataId!=null?cfn_parse().FromCloudFormation.getString(properties.DataId):void 0),ret.addPropertyResult("negated","Negated",properties.Negated!=null?cfn_parse().FromCloudFormation.getBoolean(properties.Negated):void 0),ret.addPropertyResult("type","Type",properties.Type!=null?cfn_parse().FromCloudFormation.getString(properties.Type):void 0),ret.addUnrecognizedPropertiesAsExtra(properties),ret}function CfnRulePropsValidator(properties){if(!cdk().canInspect(properties))return cdk().VALIDATION_SUCCESS;const errors=new(cdk()).ValidationResults;return properties&&typeof properties=="object"&&!Array.isArray(properties)||errors.collect(new(cdk()).ValidationResult("Expected an object, but received: "+JSON.stringify(properties))),errors.collect(cdk().propertyValidator("metricName",cdk().requiredValidator)(properties.metricName)),errors.collect(cdk().propertyValidator("metricName",cdk().validateString)(properties.metricName)),errors.collect(cdk().propertyValidator("name",cdk().requiredValidator)(properties.name)),errors.collect(cdk().propertyValidator("name",cdk().validateString)(properties.name)),errors.collect(cdk().propertyValidator("predicates",cdk().listValidator(CfnRulePredicatePropertyValidator))(properties.predicates)),errors.wrap('supplied properties not correct for "CfnRuleProps"')}function convertCfnRulePropsToCloudFormation(properties){return cdk().canInspect(properties)?(CfnRulePropsValidator(properties).assertSuccess(),{MetricName:cdk().stringToCloudFormation(properties.metricName),Name:cdk().stringToCloudFormation(properties.name),Predicates:cdk().listMapper(convertCfnRulePredicatePropertyToCloudFormation)(properties.predicates)}):properties}function CfnRulePropsFromCloudFormation(properties){if(cdk().isResolvableObject(properties))return new(cfn_parse()).FromCloudFormationResult(properties);if(properties=properties??{},!(properties&&typeof properties=="object"&&!Array.isArray(properties)))return new(cfn_parse()).FromCloudFormationResult(properties);const ret=new(cfn_parse()).FromCloudFormationPropertyObject;return ret.addPropertyResult("metricName","MetricName",properties.MetricName!=null?cfn_parse().FromCloudFormation.getString(properties.MetricName):void 0),ret.addPropertyResult("name","Name",properties.Name!=null?cfn_parse().FromCloudFormation.getString(properties.Name):void 0),ret.addPropertyResult("predicates","Predicates",properties.Predicates!=null?cfn_parse().FromCloudFormation.getArray(CfnRulePredicatePropertyFromCloudFormation)(properties.Predicates):void 0),ret.addUnrecognizedPropertiesAsExtra(properties),ret}class CfnSizeConstraintSet extends cdk().CfnResource{static _fromCloudFormation(scope,id,resourceAttributes,options){resourceAttributes=resourceAttributes||{};const resourceProperties=options.parser.parseValue(resourceAttributes.Properties),propsResult=CfnSizeConstraintSetPropsFromCloudFormation(resourceProperties);if(cdk().isResolvableObject(propsResult.value))throw new(cdk_errors()).ValidationError("Unexpected IResolvable",scope);const ret=new CfnSizeConstraintSet(scope,id,propsResult.value);for(const[propKey,propVal]of Object.entries(propsResult.extraProperties))ret.addPropertyOverride(propKey,propVal);return options.parser.handleAttributes(ret,resourceAttributes,id),ret}constructor(scope,id,props){super(scope,id,{type:CfnSizeConstraintSet.CFN_RESOURCE_TYPE_NAME,properties:props});try{jsiiDeprecationWarnings().aws_cdk_lib_aws_waf_CfnSizeConstraintSetProps(props)}catch(error){throw process.env.JSII_DEBUG!=="1"&&error.name==="DeprecationError"&&Error.captureStackTrace(error,CfnSizeConstraintSet),error}cdk().requireProperty(props,"name",this),cdk().requireProperty(props,"sizeConstraints",this),this.attrId=cdk().Token.asString(this.getAtt("Id",cdk().ResolutionTypeHint.STRING)),this.name=props.name,this.sizeConstraints=props.sizeConstraints}get cfnProperties(){return{name:this.name,sizeConstraints:this.sizeConstraints}}inspect(inspector){try{jsiiDeprecationWarnings().aws_cdk_lib_TreeInspector(inspector)}catch(error){throw process.env.JSII_DEBUG!=="1"&&error.name==="DeprecationError"&&Error.captureStackTrace(error,this.inspect),error}inspector.addAttribute("aws:cdk:cloudformation:type",CfnSizeConstraintSet.CFN_RESOURCE_TYPE_NAME),inspector.addAttribute("aws:cdk:cloudformation:props",this.cfnProperties)}renderProperties(props){return convertCfnSizeConstraintSetPropsToCloudFormation(props)}}exports.CfnSizeConstraintSet=CfnSizeConstraintSet,_d=JSII_RTTI_SYMBOL_1,CfnSizeConstraintSet[_d]={fqn:"aws-cdk-lib.aws_waf.CfnSizeConstraintSet",version:"2.201.0"},CfnSizeConstraintSet.CFN_RESOURCE_TYPE_NAME="AWS::WAF::SizeConstraintSet";function CfnSizeConstraintSetFieldToMatchPropertyValidator(properties){if(!cdk().canInspect(properties))return cdk().VALIDATION_SUCCESS;const errors=new(cdk()).ValidationResults;return properties&&typeof properties=="object"&&!Array.isArray(properties)||errors.collect(new(cdk()).ValidationResult("Expected an object, but received: "+JSON.stringify(properties))),errors.collect(cdk().propertyValidator("data",cdk().validateString)(properties.data)),errors.collect(cdk().propertyValidator("type",cdk().requiredValidator)(properties.type)),errors.collect(cdk().propertyValidator("type",cdk().validateString)(properties.type)),errors.wrap('supplied properties not correct for "FieldToMatchProperty"')}function convertCfnSizeConstraintSetFieldToMatchPropertyToCloudFormation(properties){return cdk().canInspect(properties)?(CfnSizeConstraintSetFieldToMatchPropertyValidator(properties).assertSuccess(),{Data:cdk().stringToCloudFormation(properties.data),Type:cdk().stringToCloudFormation(properties.type)}):properties}function CfnSizeConstraintSetFieldToMatchPropertyFromCloudFormation(properties){if(cdk().isResolvableObject(properties))return new(cfn_parse()).FromCloudFormationResult(properties);if(properties=properties??{},!(properties&&typeof properties=="object"&&!Array.isArray(properties)))return new(cfn_parse()).FromCloudFormationResult(properties);const ret=new(cfn_parse()).FromCloudFormationPropertyObject;return ret.addPropertyResult("data","Data",properties.Data!=null?cfn_parse().FromCloudFormation.getString(properties.Data):void 0),ret.addPropertyResult("type","Type",properties.Type!=null?cfn_parse().FromCloudFormation.getString(properties.Type):void 0),ret.addUnrecognizedPropertiesAsExtra(properties),ret}function CfnSizeConstraintSetSizeConstraintPropertyValidator(properties){if(!cdk().canInspect(properties))return cdk().VALIDATION_SUCCESS;const errors=new(cdk()).ValidationResults;return properties&&typeof properties=="object"&&!Array.isArray(properties)||errors.collect(new(cdk()).ValidationResult("Expected an object, but received: "+JSON.stringify(properties))),errors.collect(cdk().propertyValidator("comparisonOperator",cdk().requiredValidator)(properties.comparisonOperator)),errors.collect(cdk().propertyValidator("comparisonOperator",cdk().validateString)(properties.comparisonOperator)),errors.collect(cdk().propertyValidator("fieldToMatch",cdk().requiredValidator)(properties.fieldToMatch)),errors.collect(cdk().propertyValidator("fieldToMatch",CfnSizeConstraintSetFieldToMatchPropertyValidator)(properties.fieldToMatch)),errors.collect(cdk().propertyValidator("size",cdk().requiredValidator)(properties.size)),errors.collect(cdk().propertyValidator("size",cdk().validateNumber)(properties.size)),errors.collect(cdk().propertyValidator("textTransformation",cdk().requiredValidator)(properties.textTransformation)),errors.collect(cdk().propertyValidator("textTransformation",cdk().validateString)(properties.textTransformation)),errors.wrap('supplied properties not correct for "SizeConstraintProperty"')}function convertCfnSizeConstraintSetSizeConstraintPropertyToCloudFormation(properties){return cdk().canInspect(properties)?(CfnSizeConstraintSetSizeConstraintPropertyValidator(properties).assertSuccess(),{ComparisonOperator:cdk().stringToCloudFormation(properties.comparisonOperator),FieldToMatch:convertCfnSizeConstraintSetFieldToMatchPropertyToCloudFormation(properties.fieldToMatch),Size:cdk().numberToCloudFormation(properties.size),TextTransformation:cdk().stringToCloudFormation(properties.textTransformation)}):properties}function CfnSizeConstraintSetSizeConstraintPropertyFromCloudFormation(properties){if(cdk().isResolvableObject(properties))return new(cfn_parse()).FromCloudFormationResult(properties);if(properties=properties??{},!(properties&&typeof properties=="object"&&!Array.isArray(properties)))return new(cfn_parse()).FromCloudFormationResult(properties);const ret=new(cfn_parse()).FromCloudFormationPropertyObject;return ret.addPropertyResult("comparisonOperator","ComparisonOperator",properties.ComparisonOperator!=null?cfn_parse().FromCloudFormation.getString(properties.ComparisonOperator):void 0),ret.addPropertyResult("fieldToMatch","FieldToMatch",properties.FieldToMatch!=null?CfnSizeConstraintSetFieldToMatchPropertyFromCloudFormation(properties.FieldToMatch):void 0),ret.addPropertyResult("size","Size",properties.Size!=null?cfn_parse().FromCloudFormation.getNumber(properties.Size):void 0),ret.addPropertyResult("textTransformation","TextTransformation",properties.TextTransformation!=null?cfn_parse().FromCloudFormation.getString(properties.TextTransformation):void 0),ret.addUnrecognizedPropertiesAsExtra(properties),ret}function CfnSizeConstraintSetPropsValidator(properties){if(!cdk().canInspect(properties))return cdk().VALIDATION_SUCCESS;const errors=new(cdk()).ValidationResults;return properties&&typeof properties=="object"&&!Array.isArray(properties)||errors.collect(new(cdk()).ValidationResult("Expected an object, but received: "+JSON.stringify(properties))),errors.collect(cdk().propertyValidator("name",cdk().requiredValidator)(properties.name)),errors.collect(cdk().propertyValidator("name",cdk().validateString)(properties.name)),errors.collect(cdk().propertyValidator("sizeConstraints",cdk().requiredValidator)(properties.sizeConstraints)),errors.collect(cdk().propertyValidator("sizeConstraints",cdk().listValidator(CfnSizeConstraintSetSizeConstraintPropertyValidator))(properties.sizeConstraints)),errors.wrap('supplied properties not correct for "CfnSizeConstraintSetProps"')}function convertCfnSizeConstraintSetPropsToCloudFormation(properties){return cdk().canInspect(properties)?(CfnSizeConstraintSetPropsValidator(properties).assertSuccess(),{Name:cdk().stringToCloudFormation(properties.name),SizeConstraints:cdk().listMapper(convertCfnSizeConstraintSetSizeConstraintPropertyToCloudFormation)(properties.sizeConstraints)}):properties}function CfnSizeConstraintSetPropsFromCloudFormation(properties){if(cdk().isResolvableObject(properties))return new(cfn_parse()).FromCloudFormationResult(properties);if(properties=properties??{},!(properties&&typeof properties=="object"&&!Array.isArray(properties)))return new(cfn_parse()).FromCloudFormationResult(properties);const ret=new(cfn_parse()).FromCloudFormationPropertyObject;return ret.addPropertyResult("name","Name",properties.Name!=null?cfn_parse().FromCloudFormation.getString(properties.Name):void 0),ret.addPropertyResult("sizeConstraints","SizeConstraints",properties.SizeConstraints!=null?cfn_parse().FromCloudFormation.getArray(CfnSizeConstraintSetSizeConstraintPropertyFromCloudFormation)(properties.SizeConstraints):void 0),ret.addUnrecognizedPropertiesAsExtra(properties),ret}class CfnSqlInjectionMatchSet extends cdk().CfnResource{static _fromCloudFormation(scope,id,resourceAttributes,options){resourceAttributes=resourceAttributes||{};const resourceProperties=options.parser.parseValue(resourceAttributes.Properties),propsResult=CfnSqlInjectionMatchSetPropsFromCloudFormation(resourceProperties);if(cdk().isResolvableObject(propsResult.value))throw new(cdk_errors()).ValidationError("Unexpected IResolvable",scope);const ret=new CfnSqlInjectionMatchSet(scope,id,propsResult.value);for(const[propKey,propVal]of Object.entries(propsResult.extraProperties))ret.addPropertyOverride(propKey,propVal);return options.parser.handleAttributes(ret,resourceAttributes,id),ret}constructor(scope,id,props){super(scope,id,{type:CfnSqlInjectionMatchSet.CFN_RESOURCE_TYPE_NAME,properties:props});try{jsiiDeprecationWarnings().aws_cdk_lib_aws_waf_CfnSqlInjectionMatchSetProps(props)}catch(error){throw process.env.JSII_DEBUG!=="1"&&error.name==="DeprecationError"&&Error.captureStackTrace(error,CfnSqlInjectionMatchSet),error}cdk().requireProperty(props,"name",this),this.attrId=cdk().Token.asString(this.getAtt("Id",cdk().ResolutionTypeHint.STRING)),this.name=props.name,this.sqlInjectionMatchTuples=props.sqlInjectionMatchTuples}get cfnProperties(){return{name:this.name,sqlInjectionMatchTuples:this.sqlInjectionMatchTuples}}inspect(inspector){try{jsiiDeprecationWarnings().aws_cdk_lib_TreeInspector(inspector)}catch(error){throw process.env.JSII_DEBUG!=="1"&&error.name==="DeprecationError"&&Error.captureStackTrace(error,this.inspect),error}inspector.addAttribute("aws:cdk:cloudformation:type",CfnSqlInjectionMatchSet.CFN_RESOURCE_TYPE_NAME),inspector.addAttribute("aws:cdk:cloudformation:props",this.cfnProperties)}renderProperties(props){return convertCfnSqlInjectionMatchSetPropsToCloudFormation(props)}}exports.CfnSqlInjectionMatchSet=CfnSqlInjectionMatchSet,_e=JSII_RTTI_SYMBOL_1,CfnSqlInjectionMatchSet[_e]={fqn:"aws-cdk-lib.aws_waf.CfnSqlInjectionMatchSet",version:"2.201.0"},CfnSqlInjectionMatchSet.CFN_RESOURCE_TYPE_NAME="AWS::WAF::SqlInjectionMatchSet";function CfnSqlInjectionMatchSetFieldToMatchPropertyValidator(properties){if(!cdk().canInspect(properties))return cdk().VALIDATION_SUCCESS;const errors=new(cdk()).ValidationResults;return properties&&typeof properties=="object"&&!Array.isArray(properties)||errors.collect(new(cdk()).ValidationResult("Expected an object, but received: "+JSON.stringify(properties))),errors.collect(cdk().propertyValidator("data",cdk().validateString)(properties.data)),errors.collect(cdk().propertyValidator("type",cdk().requiredValidator)(properties.type)),errors.collect(cdk().propertyValidator("type",cdk().validateString)(properties.type)),errors.wrap('supplied properties not correct for "FieldToMatchProperty"')}function convertCfnSqlInjectionMatchSetFieldToMatchPropertyToCloudFormation(properties){return cdk().canInspect(properties)?(CfnSqlInjectionMatchSetFieldToMatchPropertyValidator(properties).assertSuccess(),{Data:cdk().stringToCloudFormation(properties.data),Type:cdk().stringToCloudFormation(properties.type)}):properties}function CfnSqlInjectionMatchSetFieldToMatchPropertyFromCloudFormation(properties){if(cdk().isResolvableObject(properties))return new(cfn_parse()).FromCloudFormationResult(properties);if(properties=properties??{},!(properties&&typeof properties=="object"&&!Array.isArray(properties)))return new(cfn_parse()).FromCloudFormationResult(properties);const ret=new(cfn_parse()).FromCloudFormationPropertyObject;return ret.addPropertyResult("data","Data",properties.Data!=null?cfn_parse().FromCloudFormation.getString(properties.Data):void 0),ret.addPropertyResult("type","Type",properties.Type!=null?cfn_parse().FromCloudFormation.getString(properties.Type):void 0),ret.addUnrecognizedPropertiesAsExtra(properties),ret}function CfnSqlInjectionMatchSetSqlInjectionMatchTuplePropertyValidator(properties){if(!cdk().canInspect(properties))return cdk().VALIDATION_SUCCESS;const errors=new(cdk()).ValidationResults;return properties&&typeof properties=="object"&&!Array.isArray(properties)||errors.collect(new(cdk()).ValidationResult("Expected an object, but received: "+JSON.stringify(properties))),errors.collect(cdk().propertyValidator("fieldToMatch",cdk().requiredValidator)(properties.fieldToMatch)),errors.collect(cdk().propertyValidator("fieldToMatch",CfnSqlInjectionMatchSetFieldToMatchPropertyValidator)(properties.fieldToMatch)),errors.collect(cdk().propertyValidator("textTransformation",cdk().requiredValidator)(properties.textTransformation)),errors.collect(cdk().propertyValidator("textTransformation",cdk().validateString)(properties.textTransformation)),errors.wrap('supplied properties not correct for "SqlInjectionMatchTupleProperty"')}function convertCfnSqlInjectionMatchSetSqlInjectionMatchTuplePropertyToCloudFormation(properties){return cdk().canInspect(properties)?(CfnSqlInjectionMatchSetSqlInjectionMatchTuplePropertyValidator(properties).assertSuccess(),{FieldToMatch:convertCfnSqlInjectionMatchSetFieldToMatchPropertyToCloudFormation(properties.fieldToMatch),TextTransformation:cdk().stringToCloudFormation(properties.textTransformation)}):properties}function CfnSqlInjectionMatchSetSqlInjectionMatchTuplePropertyFromCloudFormation(properties){if(cdk().isResolvableObject(properties))return new(cfn_parse()).FromCloudFormationResult(properties);if(properties=properties??{},!(properties&&typeof properties=="object"&&!Array.isArray(properties)))return new(cfn_parse()).FromCloudFormationResult(properties);const ret=new(cfn_parse()).FromCloudFormationPropertyObject;return ret.addPropertyResult("fieldToMatch","FieldToMatch",properties.FieldToMatch!=null?CfnSqlInjectionMatchSetFieldToMatchPropertyFromCloudFormation(properties.FieldToMatch):void 0),ret.addPropertyResult("textTransformation","TextTransformation",properties.TextTransformation!=null?cfn_parse().FromCloudFormation.getString(properties.TextTransformation):void 0),ret.addUnrecognizedPropertiesAsExtra(properties),ret}function CfnSqlInjectionMatchSetPropsValidator(properties){if(!cdk().canInspect(properties))return cdk().VALIDATION_SUCCESS;const errors=new(cdk()).ValidationResults;return properties&&typeof properties=="object"&&!Array.isArray(properties)||errors.collect(new(cdk()).ValidationResult("Expected an object, but received: "+JSON.stringify(properties))),errors.collect(cdk().propertyValidator("name",cdk().requiredValidator)(properties.name)),errors.collect(cdk().propertyValidator("name",cdk().validateString)(properties.name)),errors.collect(cdk().propertyValidator("sqlInjectionMatchTuples",cdk().listValidator(CfnSqlInjectionMatchSetSqlInjectionMatchTuplePropertyValidator))(properties.sqlInjectionMatchTuples)),errors.wrap('supplied properties not correct for "CfnSqlInjectionMatchSetProps"')}function convertCfnSqlInjectionMatchSetPropsToCloudFormation(properties){return cdk().canInspect(properties)?(CfnSqlInjectionMatchSetPropsValidator(properties).assertSuccess(),{Name:cdk().stringToCloudFormation(properties.name),SqlInjectionMatchTuples:cdk().listMapper(convertCfnSqlInjectionMatchSetSqlInjectionMatchTuplePropertyToCloudFormation)(properties.sqlInjectionMatchTuples)}):properties}function CfnSqlInjectionMatchSetPropsFromCloudFormation(properties){if(cdk().isResolvableObject(properties))return new(cfn_parse()).FromCloudFormationResult(properties);if(properties=properties??{},!(properties&&typeof properties=="object"&&!Array.isArray(properties)))return new(cfn_parse()).FromCloudFormationResult(properties);const ret=new(cfn_parse()).FromCloudFormationPropertyObject;return ret.addPropertyResult("name","Name",properties.Name!=null?cfn_parse().FromCloudFormation.getString(properties.Name):void 0),ret.addPropertyResult("sqlInjectionMatchTuples","SqlInjectionMatchTuples",properties.SqlInjectionMatchTuples!=null?cfn_parse().FromCloudFormation.getArray(CfnSqlInjectionMatchSetSqlInjectionMatchTuplePropertyFromCloudFormation)(properties.SqlInjectionMatchTuples):void 0),ret.addUnrecognizedPropertiesAsExtra(properties),ret}class CfnWebACL extends cdk().CfnResource{static _fromCloudFormation(scope,id,resourceAttributes,options){resourceAttributes=resourceAttributes||{};const resourceProperties=options.parser.parseValue(resourceAttributes.Properties),propsResult=CfnWebACLPropsFromCloudFormation(resourceProperties);if(cdk().isResolvableObject(propsResult.value))throw new(cdk_errors()).ValidationError("Unexpected IResolvable",scope);const ret=new CfnWebACL(scope,id,propsResult.value);for(const[propKey,propVal]of Object.entries(propsResult.extraProperties))ret.addPropertyOverride(propKey,propVal);return options.parser.handleAttributes(ret,resourceAttributes,id),ret}constructor(scope,id,props){super(scope,id,{type:CfnWebACL.CFN_RESOURCE_TYPE_NAME,properties:props});try{jsiiDeprecationWarnings().aws_cdk_lib_aws_waf_CfnWebACLProps(props)}catch(error){throw process.env.JSII_DEBUG!=="1"&&error.name==="DeprecationError"&&Error.captureStackTrace(error,CfnWebACL),error}cdk().requireProperty(props,"defaultAction",this),cdk().requireProperty(props,"metricName",this),cdk().requireProperty(props,"name",this),this.attrId=cdk().Token.asString(this.getAtt("Id",cdk().ResolutionTypeHint.STRING)),this.defaultAction=props.defaultAction,this.metricName=props.metricName,this.name=props.name,this.rules=props.rules}get cfnProperties(){return{defaultAction:this.defaultAction,metricName:this.metricName,name:this.name,rules:this.rules}}inspect(inspector){try{jsiiDeprecationWarnings().aws_cdk_lib_TreeInspector(inspector)}catch(error){throw process.env.JSII_DEBUG!=="1"&&error.name==="DeprecationError"&&Error.captureStackTrace(error,this.inspect),error}inspector.addAttribute("aws:cdk:cloudformation:type",CfnWebACL.CFN_RESOURCE_TYPE_NAME),inspector.addAttribute("aws:cdk:cloudformation:props",this.cfnProperties)}renderProperties(props){return convertCfnWebACLPropsToCloudFormation(props)}}exports.CfnWebACL=CfnWebACL,_f=JSII_RTTI_SYMBOL_1,CfnWebACL[_f]={fqn:"aws-cdk-lib.aws_waf.CfnWebACL",version:"2.201.0"},CfnWebACL.CFN_RESOURCE_TYPE_NAME="AWS::WAF::WebACL";function CfnWebACLWafActionPropertyValidator(properties){if(!cdk().canInspect(properties))return cdk().VALIDATION_SUCCESS;const errors=new(cdk()).ValidationResults;return properties&&typeof properties=="object"&&!Array.isArray(properties)||errors.collect(new(cdk()).ValidationResult("Expected an object, but received: "+JSON.stringify(properties))),errors.collect(cdk().propertyValidator("type",cdk().requiredValidator)(properties.type)),errors.collect(cdk().propertyValidator("type",cdk().validateString)(properties.type)),errors.wrap('supplied properties not correct for "WafActionProperty"')}function convertCfnWebACLWafActionPropertyToCloudFormation(properties){return cdk().canInspect(properties)?(CfnWebACLWafActionPropertyValidator(properties).assertSuccess(),{Type:cdk().stringToCloudFormation(properties.type)}):properties}function CfnWebACLWafActionPropertyFromCloudFormation(properties){if(cdk().isResolvableObject(properties))return new(cfn_parse()).FromCloudFormationResult(properties);if(properties=properties??{},!(properties&&typeof properties=="object"&&!Array.isArray(properties)))return new(cfn_parse()).FromCloudFormationResult(properties);const ret=new(cfn_parse()).FromCloudFormationPropertyObject;return ret.addPropertyResult("type","Type",properties.Type!=null?cfn_parse().FromCloudFormation.getString(properties.Type):void 0),ret.addUnrecognizedPropertiesAsExtra(properties),ret}function CfnWebACLActivatedRulePropertyValidator(properties){if(!cdk().canInspect(properties))return cdk().VALIDATION_SUCCESS;const errors=new(cdk()).ValidationResults;return properties&&typeof properties=="object"&&!Array.isArray(properties)||errors.collect(new(cdk()).ValidationResult("Expected an object, but received: "+JSON.stringify(properties))),errors.collect(cdk().propertyValidator("action",CfnWebACLWafActionPropertyValidator)(properties.action)),errors.collect(cdk().propertyValidator("priority",cdk().requiredValidator)(properties.priority)),errors.collect(cdk().propertyValidator("priority",cdk().validateNumber)(properties.priority)),errors.collect(cdk().propertyValidator("ruleId",cdk().requiredValidator)(properties.ruleId)),errors.collect(cdk().propertyValidator("ruleId",cdk().validateString)(properties.ruleId)),errors.wrap('supplied properties not correct for "ActivatedRuleProperty"')}function convertCfnWebACLActivatedRulePropertyToCloudFormation(properties){return cdk().canInspect(properties)?(CfnWebACLActivatedRulePropertyValidator(properties).assertSuccess(),{Action:convertCfnWebACLWafActionPropertyToCloudFormation(properties.action),Priority:cdk().numberToCloudFormation(properties.priority),RuleId:cdk().stringToCloudFormation(properties.ruleId)}):properties}function CfnWebACLActivatedRulePropertyFromCloudFormation(properties){if(cdk().isResolvableObject(properties))return new(cfn_parse()).FromCloudFormationResult(properties);if(properties=properties??{},!(properties&&typeof properties=="object"&&!Array.isArray(properties)))return new(cfn_parse()).FromCloudFormationResult(properties);const ret=new(cfn_parse()).FromCloudFormationPropertyObject;return ret.addPropertyResult("action","Action",properties.Action!=null?CfnWebACLWafActionPropertyFromCloudFormation(properties.Action):void 0),ret.addPropertyResult("priority","Priority",properties.Priority!=null?cfn_parse().FromCloudFormation.getNumber(properties.Priority):void 0),ret.addPropertyResult("ruleId","RuleId",properties.RuleId!=null?cfn_parse().FromCloudFormation.getString(properties.RuleId):void 0),ret.addUnrecognizedPropertiesAsExtra(properties),ret}function CfnWebACLPropsValidator(properties){if(!cdk().canInspect(properties))return cdk().VALIDATION_SUCCESS;const errors=new(cdk()).ValidationResults;return properties&&typeof properties=="object"&&!Array.isArray(properties)||errors.collect(new(cdk()).ValidationResult("Expected an object, but received: "+JSON.stringify(properties))),errors.collect(cdk().propertyValidator("defaultAction",cdk().requiredValidator)(properties.defaultAction)),errors.collect(cdk().propertyValidator("defaultAction",CfnWebACLWafActionPropertyValidator)(properties.defaultAction)),errors.collect(cdk().propertyValidator("metricName",cdk().requiredValidator)(properties.metricName)),errors.collect(cdk().propertyValidator("metricName",cdk().validateString)(properties.metricName)),errors.collect(cdk().propertyValidator("name",cdk().requiredValidator)(properties.name)),errors.collect(cdk().propertyValidator("name",cdk().validateString)(properties.name)),errors.collect(cdk().propertyValidator("rules",cdk().listValidator(CfnWebACLActivatedRulePropertyValidator))(properties.rules)),errors.wrap('supplied properties not correct for "CfnWebACLProps"')}function convertCfnWebACLPropsToCloudFormation(properties){return cdk().canInspect(properties)?(CfnWebACLPropsValidator(properties).assertSuccess(),{DefaultAction:convertCfnWebACLWafActionPropertyToCloudFormation(properties.defaultAction),MetricName:cdk().stringToCloudFormation(properties.metricName),Name:cdk().stringToCloudFormation(properties.name),Rules:cdk().listMapper(convertCfnWebACLActivatedRulePropertyToCloudFormation)(properties.rules)}):properties}function CfnWebACLPropsFromCloudFormation(properties){if(cdk().isResolvableObject(properties))return new(cfn_parse()).FromCloudFormationResult(properties);if(properties=properties??{},!(properties&&typeof properties=="object"&&!Array.isArray(properties)))return new(cfn_parse()).FromCloudFormationResult(properties);const ret=new(cfn_parse()).FromCloudFormationPropertyObject;return ret.addPropertyResult("defaultAction","DefaultAction",properties.DefaultAction!=null?CfnWebACLWafActionPropertyFromCloudFormation(properties.DefaultAction):void 0),ret.addPropertyResult("metricName","MetricName",properties.MetricName!=null?cfn_parse().FromCloudFormation.getString(properties.MetricName):void 0),ret.addPropertyResult("name","Name",properties.Name!=null?cfn_parse().FromCloudFormation.getString(properties.Name):void 0),ret.addPropertyResult("rules","Rules",properties.Rules!=null?cfn_parse().FromCloudFormation.getArray(CfnWebACLActivatedRulePropertyFromCloudFormation)(properties.Rules):void 0),ret.addUnrecognizedPropertiesAsExtra(properties),ret}class CfnXssMatchSet extends cdk().CfnResource{static _fromCloudFormation(scope,id,resourceAttributes,options){resourceAttributes=resourceAttributes||{};const resourceProperties=options.parser.parseValue(resourceAttributes.Properties),propsResult=CfnXssMatchSetPropsFromCloudFormation(resourceProperties);if(cdk().isResolvableObject(propsResult.value))throw new(cdk_errors()).ValidationError("Unexpected IResolvable",scope);const ret=new CfnXssMatchSet(scope,id,propsResult.value);for(const[propKey,propVal]of Object.entries(propsResult.extraProperties))ret.addPropertyOverride(propKey,propVal);return options.parser.handleAttributes(ret,resourceAttributes,id),ret}constructor(scope,id,props){super(scope,id,{type:CfnXssMatchSet.CFN_RESOURCE_TYPE_NAME,properties:props});try{jsiiDeprecationWarnings().aws_cdk_lib_aws_waf_CfnXssMatchSetProps(props)}catch(error){throw process.env.JSII_DEBUG!=="1"&&error.name==="DeprecationError"&&Error.captureStackTrace(error,CfnXssMatchSet),error}cdk().requireProperty(props,"name",this),cdk().requireProperty(props,"xssMatchTuples",this),this.attrId=cdk().Token.asString(this.getAtt("Id",cdk().ResolutionTypeHint.STRING)),this.name=props.name,this.xssMatchTuples=props.xssMatchTuples}get cfnProperties(){return{name:this.name,xssMatchTuples:this.xssMatchTuples}}inspect(inspector){try{jsiiDeprecationWarnings().aws_cdk_lib_TreeInspector(inspector)}catch(error){throw process.env.JSII_DEBUG!=="1"&&error.name==="DeprecationError"&&Error.captureStackTrace(error,this.inspect),error}inspector.addAttribute("aws:cdk:cloudformation:type",CfnXssMatchSet.CFN_RESOURCE_TYPE_NAME),inspector.addAttribute("aws:cdk:cloudformation:props",this.cfnProperties)}renderProperties(props){return convertCfnXssMatchSetPropsToCloudFormation(props)}}exports.CfnXssMatchSet=CfnXssMatchSet,_g=JSII_RTTI_SYMBOL_1,CfnXssMatchSet[_g]={fqn:"aws-cdk-lib.aws_waf.CfnXssMatchSet",version:"2.201.0"},CfnXssMatchSet.CFN_RESOURCE_TYPE_NAME="AWS::WAF::XssMatchSet";function CfnXssMatchSetFieldToMatchPropertyValidator(properties){if(!cdk().canInspect(properties))return cdk().VALIDATION_SUCCESS;const errors=new(cdk()).ValidationResults;return properties&&typeof properties=="object"&&!Array.isArray(properties)||errors.collect(new(cdk()).ValidationResult("Expected an object, but received: "+JSON.stringify(properties))),errors.collect(cdk().propertyValidator("data",cdk().validateString)(properties.data)),errors.collect(cdk().propertyValidator("type",cdk().requiredValidator)(properties.type)),errors.collect(cdk().propertyValidator("type",cdk().validateString)(properties.type)),errors.wrap('supplied properties not correct for "FieldToMatchProperty"')}function convertCfnXssMatchSetFieldToMatchPropertyToCloudFormation(properties){return cdk().canInspect(properties)?(CfnXssMatchSetFieldToMatchPropertyValidator(properties).assertSuccess(),{Data:cdk().stringToCloudFormation(properties.data),Type:cdk().stringToCloudFormation(properties.type)}):properties}function CfnXssMatchSetFieldToMatchPropertyFromCloudFormation(properties){if(cdk().isResolvableObject(properties))return new(cfn_parse()).FromCloudFormationResult(properties);if(properties=properties??{},!(properties&&typeof properties=="object"&&!Array.isArray(properties)))return new(cfn_parse()).FromCloudFormationResult(properties);const ret=new(cfn_parse()).FromCloudFormationPropertyObject;return ret.addPropertyResult("data","Data",properties.Data!=null?cfn_parse().FromCloudFormation.getString(properties.Data):void 0),ret.addPropertyResult("type","Type",properties.Type!=null?cfn_parse().FromCloudFormation.getString(properties.Type):void 0),ret.addUnrecognizedPropertiesAsExtra(properties),ret}function CfnXssMatchSetXssMatchTuplePropertyValidator(properties){if(!cdk().canInspect(properties))return cdk().VALIDATION_SUCCESS;const errors=new(cdk()).ValidationResults;return properties&&typeof properties=="object"&&!Array.isArray(properties)||errors.collect(new(cdk()).ValidationResult("Expected an object, but received: "+JSON.stringify(properties))),errors.collect(cdk().propertyValidator("fieldToMatch",cdk().requiredValidator)(properties.fieldToMatch)),errors.collect(cdk().propertyValidator("fieldToMatch",CfnXssMatchSetFieldToMatchPropertyValidator)(properties.fieldToMatch)),errors.collect(cdk().propertyValidator("textTransformation",cdk().requiredValidator)(properties.textTransformation)),errors.collect(cdk().propertyValidator("textTransformation",cdk().validateString)(properties.textTransformation)),errors.wrap('supplied properties not correct for "XssMatchTupleProperty"')}function convertCfnXssMatchSetXssMatchTuplePropertyToCloudFormation(properties){return cdk().canInspect(properties)?(CfnXssMatchSetXssMatchTuplePropertyValidator(properties).assertSuccess(),{FieldToMatch:convertCfnXssMatchSetFieldToMatchPropertyToCloudFormation(properties.fieldToMatch),TextTransformation:cdk().stringToCloudFormation(properties.textTransformation)}):properties}function CfnXssMatchSetXssMatchTuplePropertyFromCloudFormation(properties){if(cdk().isResolvableObject(properties))return new(cfn_parse()).FromCloudFormationResult(properties);if(properties=properties??{},!(properties&&typeof properties=="object"&&!Array.isArray(properties)))return new(cfn_parse()).FromCloudFormationResult(properties);const ret=new(cfn_parse()).FromCloudFormationPropertyObject;return ret.addPropertyResult("fieldToMatch","FieldToMatch",properties.FieldToMatch!=null?CfnXssMatchSetFieldToMatchPropertyFromCloudFormation(properties.FieldToMatch):void 0),ret.addPropertyResult("textTransformation","TextTransformation",properties.TextTransformation!=null?cfn_parse().FromCloudFormation.getString(properties.TextTransformation):void 0),ret.addUnrecognizedPropertiesAsExtra(properties),ret}function CfnXssMatchSetPropsValidator(properties){if(!cdk().canInspect(properties))return cdk().VALIDATION_SUCCESS;const errors=new(cdk()).ValidationResults;return properties&&typeof properties=="object"&&!Array.isArray(properties)||errors.collect(new(cdk()).ValidationResult("Expected an object, but received: "+JSON.stringify(properties))),errors.collect(cdk().propertyValidator("name",cdk().requiredValidator)(properties.name)),errors.collect(cdk().propertyValidator("name",cdk().validateString)(properties.name)),errors.collect(cdk().propertyValidator("xssMatchTuples",cdk().requiredValidator)(properties.xssMatchTuples)),errors.collect(cdk().propertyValidator("xssMatchTuples",cdk().listValidator(CfnXssMatchSetXssMatchTuplePropertyValidator))(properties.xssMatchTuples)),errors.wrap('supplied properties not correct for "CfnXssMatchSetProps"')}function convertCfnXssMatchSetPropsToCloudFormation(properties){return cdk().canInspect(properties)?(CfnXssMatchSetPropsValidator(properties).assertSuccess(),{Name:cdk().stringToCloudFormation(properties.name),XssMatchTuples:cdk().listMapper(convertCfnXssMatchSetXssMatchTuplePropertyToCloudFormation)(properties.xssMatchTuples)}):properties}function CfnXssMatchSetPropsFromCloudFormation(properties){if(cdk().isResolvableObject(properties))return new(cfn_parse()).FromCloudFormationResult(properties);if(properties=properties??{},!(properties&&typeof properties=="object"&&!Array.isArray(properties)))return new(cfn_parse()).FromCloudFormationResult(properties);const ret=new(cfn_parse()).FromCloudFormationPropertyObject;return ret.addPropertyResult("name","Name",properties.Name!=null?cfn_parse().FromCloudFormation.getString(properties.Name):void 0),ret.addPropertyResult("xssMatchTuples","XssMatchTuples",properties.XssMatchTuples!=null?cfn_parse().FromCloudFormation.getArray(CfnXssMatchSetXssMatchTuplePropertyFromCloudFormation)(properties.XssMatchTuples):void 0),ret.addUnrecognizedPropertiesAsExtra(properties),ret}
