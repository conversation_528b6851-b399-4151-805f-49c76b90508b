"use strict";var _a;Object.defineProperty(exports,"__esModule",{value:!0}),exports.Runtime=exports.RuntimeFamily=void 0;var jsiiDeprecationWarnings=()=>{var tmp=require("../../.warnings.jsii.js");return jsiiDeprecationWarnings=()=>tmp,tmp};const JSII_RTTI_SYMBOL_1=Symbol.for("jsii.rtti");var RuntimeFamily;(function(RuntimeFamily2){RuntimeFamily2[RuntimeFamily2.NODEJS=0]="NODEJS",RuntimeFamily2[RuntimeFamily2.PYTHON=1]="PYTHON",RuntimeFamily2[RuntimeFamily2.OTHER=2]="OTHER"})(RuntimeFamily||(exports.RuntimeFamily=RuntimeFamily={}));class Runtime{constructor(name,family){this.name=name,this.family=family;try{jsiiDeprecationWarnings().aws_cdk_lib_aws_synthetics_RuntimeFamily(family)}catch(error){throw process.env.JSII_DEBUG!=="1"&&error.name==="DeprecationError"&&Error.captureStackTrace(error,Runtime),error}}}exports.Runtime=Runtime,_a=JSII_RTTI_SYMBOL_1,Runtime[_a]={fqn:"aws-cdk-lib.aws_synthetics.Runtime",version:"2.201.0"},Runtime.SYNTHETICS_NODEJS_PUPPETEER_3_5=new Runtime("syn-nodejs-puppeteer-3.5",RuntimeFamily.NODEJS),Runtime.SYNTHETICS_NODEJS_PUPPETEER_3_6=new Runtime("syn-nodejs-puppeteer-3.6",RuntimeFamily.NODEJS),Runtime.SYNTHETICS_NODEJS_PUPPETEER_3_7=new Runtime("syn-nodejs-puppeteer-3.7",RuntimeFamily.NODEJS),Runtime.SYNTHETICS_NODEJS_PUPPETEER_3_8=new Runtime("syn-nodejs-puppeteer-3.8",RuntimeFamily.NODEJS),Runtime.SYNTHETICS_NODEJS_PUPPETEER_3_9=new Runtime("syn-nodejs-puppeteer-3.9",RuntimeFamily.NODEJS),Runtime.SYNTHETICS_NODEJS_PUPPETEER_4_0=new Runtime("syn-nodejs-puppeteer-4.0",RuntimeFamily.NODEJS),Runtime.SYNTHETICS_NODEJS_PUPPETEER_5_0=new Runtime("syn-nodejs-puppeteer-5.0",RuntimeFamily.NODEJS),Runtime.SYNTHETICS_NODEJS_PUPPETEER_5_1=new Runtime("syn-nodejs-puppeteer-5.1",RuntimeFamily.NODEJS),Runtime.SYNTHETICS_NODEJS_PUPPETEER_5_2=new Runtime("syn-nodejs-puppeteer-5.2",RuntimeFamily.NODEJS),Runtime.SYNTHETICS_NODEJS_PUPPETEER_6_0=new Runtime("syn-nodejs-puppeteer-6.0",RuntimeFamily.NODEJS),Runtime.SYNTHETICS_NODEJS_PUPPETEER_6_1=new Runtime("syn-nodejs-puppeteer-6.1",RuntimeFamily.NODEJS),Runtime.SYNTHETICS_NODEJS_PUPPETEER_6_2=new Runtime("syn-nodejs-puppeteer-6.2",RuntimeFamily.NODEJS),Runtime.SYNTHETICS_NODEJS_PUPPETEER_7_0=new Runtime("syn-nodejs-puppeteer-7.0",RuntimeFamily.NODEJS),Runtime.SYNTHETICS_NODEJS_PUPPETEER_8_0=new Runtime("syn-nodejs-puppeteer-8.0",RuntimeFamily.NODEJS),Runtime.SYNTHETICS_NODEJS_PUPPETEER_9_0=new Runtime("syn-nodejs-puppeteer-9.0",RuntimeFamily.NODEJS),Runtime.SYNTHETICS_NODEJS_PUPPETEER_9_1=new Runtime("syn-nodejs-puppeteer-9.1",RuntimeFamily.NODEJS),Runtime.SYNTHETICS_NODEJS_PLAYWRIGHT_1_0=new Runtime("syn-nodejs-playwright-1.0",RuntimeFamily.NODEJS),Runtime.SYNTHETICS_NODEJS_PLAYWRIGHT_2_0=new Runtime("syn-nodejs-playwright-2.0",RuntimeFamily.NODEJS),Runtime.SYNTHETICS_PYTHON_SELENIUM_1_0=new Runtime("syn-python-selenium-1.0",RuntimeFamily.PYTHON),Runtime.SYNTHETICS_PYTHON_SELENIUM_1_1=new Runtime("syn-python-selenium-1.1",RuntimeFamily.PYTHON),Runtime.SYNTHETICS_PYTHON_SELENIUM_1_2=new Runtime("syn-python-selenium-1.2",RuntimeFamily.PYTHON),Runtime.SYNTHETICS_PYTHON_SELENIUM_1_3=new Runtime("syn-python-selenium-1.3",RuntimeFamily.PYTHON),Runtime.SYNTHETICS_PYTHON_SELENIUM_2_0=new Runtime("syn-python-selenium-2.0",RuntimeFamily.PYTHON),Runtime.SYNTHETICS_PYTHON_SELENIUM_2_1=new Runtime("syn-python-selenium-2.1",RuntimeFamily.PYTHON),Runtime.SYNTHETICS_PYTHON_SELENIUM_3_0=new Runtime("syn-python-selenium-3.0",RuntimeFamily.PYTHON),Runtime.SYNTHETICS_PYTHON_SELENIUM_4_0=new Runtime("syn-python-selenium-4.0",RuntimeFamily.PYTHON),Runtime.SYNTHETICS_PYTHON_SELENIUM_4_1=new Runtime("syn-python-selenium-4.1",RuntimeFamily.PYTHON),Runtime.SYNTHETICS_PYTHON_SELENIUM_5_0=new Runtime("syn-python-selenium-5.0",RuntimeFamily.PYTHON),Runtime.SYNTHETICS_PYTHON_SELENIUM_5_1=new Runtime("syn-python-selenium-5.1",RuntimeFamily.PYTHON),Runtime.SYNTHETICS_PYTHON_SELENIUM_6_0=new Runtime("syn-python-selenium-6.0",RuntimeFamily.PYTHON);
