"use strict";var _a,_b;Object.defineProperty(exports,"__esModule",{value:!0}),exports.CfnTableBucketPolicy=exports.CfnTableBucket=void 0;var jsiiDeprecationWarnings=()=>{var tmp=require("../../.warnings.jsii.js");return jsiiDeprecationWarnings=()=>tmp,tmp};const JSII_RTTI_SYMBOL_1=Symbol.for("jsii.rtti");var cdk=()=>{var tmp=require("../../core");return cdk=()=>tmp,tmp},cfn_parse=()=>{var tmp=require("../../core/lib/helpers-internal");return cfn_parse=()=>tmp,tmp},cdk_errors=()=>{var tmp=require("../../core/lib/errors");return cdk_errors=()=>tmp,tmp};class CfnTableBucket extends cdk().CfnResource{static _fromCloudFormation(scope,id,resourceAttributes,options){resourceAttributes=resourceAttributes||{};const resourceProperties=options.parser.parseValue(resourceAttributes.Properties),propsResult=CfnTableBucketPropsFromCloudFormation(resourceProperties);if(cdk().isResolvableObject(propsResult.value))throw new(cdk_errors()).ValidationError("Unexpected IResolvable",scope);const ret=new CfnTableBucket(scope,id,propsResult.value);for(const[propKey,propVal]of Object.entries(propsResult.extraProperties))ret.addPropertyOverride(propKey,propVal);return options.parser.handleAttributes(ret,resourceAttributes,id),ret}constructor(scope,id,props){super(scope,id,{type:CfnTableBucket.CFN_RESOURCE_TYPE_NAME,properties:props});try{jsiiDeprecationWarnings().aws_cdk_lib_aws_s3tables_CfnTableBucketProps(props)}catch(error){throw process.env.JSII_DEBUG!=="1"&&error.name==="DeprecationError"&&Error.captureStackTrace(error,CfnTableBucket),error}cdk().requireProperty(props,"tableBucketName",this),this.attrTableBucketArn=cdk().Token.asString(this.getAtt("TableBucketARN",cdk().ResolutionTypeHint.STRING)),this.encryptionConfiguration=props.encryptionConfiguration,this.tableBucketName=props.tableBucketName,this.unreferencedFileRemoval=props.unreferencedFileRemoval}get cfnProperties(){return{encryptionConfiguration:this.encryptionConfiguration,tableBucketName:this.tableBucketName,unreferencedFileRemoval:this.unreferencedFileRemoval}}inspect(inspector){try{jsiiDeprecationWarnings().aws_cdk_lib_TreeInspector(inspector)}catch(error){throw process.env.JSII_DEBUG!=="1"&&error.name==="DeprecationError"&&Error.captureStackTrace(error,this.inspect),error}inspector.addAttribute("aws:cdk:cloudformation:type",CfnTableBucket.CFN_RESOURCE_TYPE_NAME),inspector.addAttribute("aws:cdk:cloudformation:props",this.cfnProperties)}renderProperties(props){return convertCfnTableBucketPropsToCloudFormation(props)}}exports.CfnTableBucket=CfnTableBucket,_a=JSII_RTTI_SYMBOL_1,CfnTableBucket[_a]={fqn:"aws-cdk-lib.aws_s3tables.CfnTableBucket",version:"2.201.0"},CfnTableBucket.CFN_RESOURCE_TYPE_NAME="AWS::S3Tables::TableBucket";function CfnTableBucketUnreferencedFileRemovalPropertyValidator(properties){if(!cdk().canInspect(properties))return cdk().VALIDATION_SUCCESS;const errors=new(cdk()).ValidationResults;return properties&&typeof properties=="object"&&!Array.isArray(properties)||errors.collect(new(cdk()).ValidationResult("Expected an object, but received: "+JSON.stringify(properties))),errors.collect(cdk().propertyValidator("noncurrentDays",cdk().validateNumber)(properties.noncurrentDays)),errors.collect(cdk().propertyValidator("status",cdk().validateString)(properties.status)),errors.collect(cdk().propertyValidator("unreferencedDays",cdk().validateNumber)(properties.unreferencedDays)),errors.wrap('supplied properties not correct for "UnreferencedFileRemovalProperty"')}function convertCfnTableBucketUnreferencedFileRemovalPropertyToCloudFormation(properties){return cdk().canInspect(properties)?(CfnTableBucketUnreferencedFileRemovalPropertyValidator(properties).assertSuccess(),{NoncurrentDays:cdk().numberToCloudFormation(properties.noncurrentDays),Status:cdk().stringToCloudFormation(properties.status),UnreferencedDays:cdk().numberToCloudFormation(properties.unreferencedDays)}):properties}function CfnTableBucketUnreferencedFileRemovalPropertyFromCloudFormation(properties){if(cdk().isResolvableObject(properties))return new(cfn_parse()).FromCloudFormationResult(properties);if(properties=properties??{},!(properties&&typeof properties=="object"&&!Array.isArray(properties)))return new(cfn_parse()).FromCloudFormationResult(properties);const ret=new(cfn_parse()).FromCloudFormationPropertyObject;return ret.addPropertyResult("noncurrentDays","NoncurrentDays",properties.NoncurrentDays!=null?cfn_parse().FromCloudFormation.getNumber(properties.NoncurrentDays):void 0),ret.addPropertyResult("status","Status",properties.Status!=null?cfn_parse().FromCloudFormation.getString(properties.Status):void 0),ret.addPropertyResult("unreferencedDays","UnreferencedDays",properties.UnreferencedDays!=null?cfn_parse().FromCloudFormation.getNumber(properties.UnreferencedDays):void 0),ret.addUnrecognizedPropertiesAsExtra(properties),ret}function CfnTableBucketEncryptionConfigurationPropertyValidator(properties){if(!cdk().canInspect(properties))return cdk().VALIDATION_SUCCESS;const errors=new(cdk()).ValidationResults;return properties&&typeof properties=="object"&&!Array.isArray(properties)||errors.collect(new(cdk()).ValidationResult("Expected an object, but received: "+JSON.stringify(properties))),errors.collect(cdk().propertyValidator("kmsKeyArn",cdk().validateString)(properties.kmsKeyArn)),errors.collect(cdk().propertyValidator("sseAlgorithm",cdk().validateString)(properties.sseAlgorithm)),errors.wrap('supplied properties not correct for "EncryptionConfigurationProperty"')}function convertCfnTableBucketEncryptionConfigurationPropertyToCloudFormation(properties){return cdk().canInspect(properties)?(CfnTableBucketEncryptionConfigurationPropertyValidator(properties).assertSuccess(),{KMSKeyArn:cdk().stringToCloudFormation(properties.kmsKeyArn),SSEAlgorithm:cdk().stringToCloudFormation(properties.sseAlgorithm)}):properties}function CfnTableBucketEncryptionConfigurationPropertyFromCloudFormation(properties){if(cdk().isResolvableObject(properties))return new(cfn_parse()).FromCloudFormationResult(properties);if(properties=properties??{},!(properties&&typeof properties=="object"&&!Array.isArray(properties)))return new(cfn_parse()).FromCloudFormationResult(properties);const ret=new(cfn_parse()).FromCloudFormationPropertyObject;return ret.addPropertyResult("kmsKeyArn","KMSKeyArn",properties.KMSKeyArn!=null?cfn_parse().FromCloudFormation.getString(properties.KMSKeyArn):void 0),ret.addPropertyResult("sseAlgorithm","SSEAlgorithm",properties.SSEAlgorithm!=null?cfn_parse().FromCloudFormation.getString(properties.SSEAlgorithm):void 0),ret.addUnrecognizedPropertiesAsExtra(properties),ret}function CfnTableBucketPropsValidator(properties){if(!cdk().canInspect(properties))return cdk().VALIDATION_SUCCESS;const errors=new(cdk()).ValidationResults;return properties&&typeof properties=="object"&&!Array.isArray(properties)||errors.collect(new(cdk()).ValidationResult("Expected an object, but received: "+JSON.stringify(properties))),errors.collect(cdk().propertyValidator("encryptionConfiguration",CfnTableBucketEncryptionConfigurationPropertyValidator)(properties.encryptionConfiguration)),errors.collect(cdk().propertyValidator("tableBucketName",cdk().requiredValidator)(properties.tableBucketName)),errors.collect(cdk().propertyValidator("tableBucketName",cdk().validateString)(properties.tableBucketName)),errors.collect(cdk().propertyValidator("unreferencedFileRemoval",CfnTableBucketUnreferencedFileRemovalPropertyValidator)(properties.unreferencedFileRemoval)),errors.wrap('supplied properties not correct for "CfnTableBucketProps"')}function convertCfnTableBucketPropsToCloudFormation(properties){return cdk().canInspect(properties)?(CfnTableBucketPropsValidator(properties).assertSuccess(),{EncryptionConfiguration:convertCfnTableBucketEncryptionConfigurationPropertyToCloudFormation(properties.encryptionConfiguration),TableBucketName:cdk().stringToCloudFormation(properties.tableBucketName),UnreferencedFileRemoval:convertCfnTableBucketUnreferencedFileRemovalPropertyToCloudFormation(properties.unreferencedFileRemoval)}):properties}function CfnTableBucketPropsFromCloudFormation(properties){if(cdk().isResolvableObject(properties))return new(cfn_parse()).FromCloudFormationResult(properties);if(properties=properties??{},!(properties&&typeof properties=="object"&&!Array.isArray(properties)))return new(cfn_parse()).FromCloudFormationResult(properties);const ret=new(cfn_parse()).FromCloudFormationPropertyObject;return ret.addPropertyResult("encryptionConfiguration","EncryptionConfiguration",properties.EncryptionConfiguration!=null?CfnTableBucketEncryptionConfigurationPropertyFromCloudFormation(properties.EncryptionConfiguration):void 0),ret.addPropertyResult("tableBucketName","TableBucketName",properties.TableBucketName!=null?cfn_parse().FromCloudFormation.getString(properties.TableBucketName):void 0),ret.addPropertyResult("unreferencedFileRemoval","UnreferencedFileRemoval",properties.UnreferencedFileRemoval!=null?CfnTableBucketUnreferencedFileRemovalPropertyFromCloudFormation(properties.UnreferencedFileRemoval):void 0),ret.addUnrecognizedPropertiesAsExtra(properties),ret}class CfnTableBucketPolicy extends cdk().CfnResource{static _fromCloudFormation(scope,id,resourceAttributes,options){resourceAttributes=resourceAttributes||{};const resourceProperties=options.parser.parseValue(resourceAttributes.Properties),propsResult=CfnTableBucketPolicyPropsFromCloudFormation(resourceProperties);if(cdk().isResolvableObject(propsResult.value))throw new(cdk_errors()).ValidationError("Unexpected IResolvable",scope);const ret=new CfnTableBucketPolicy(scope,id,propsResult.value);for(const[propKey,propVal]of Object.entries(propsResult.extraProperties))ret.addPropertyOverride(propKey,propVal);return options.parser.handleAttributes(ret,resourceAttributes,id),ret}constructor(scope,id,props){super(scope,id,{type:CfnTableBucketPolicy.CFN_RESOURCE_TYPE_NAME,properties:props});try{jsiiDeprecationWarnings().aws_cdk_lib_aws_s3tables_CfnTableBucketPolicyProps(props)}catch(error){throw process.env.JSII_DEBUG!=="1"&&error.name==="DeprecationError"&&Error.captureStackTrace(error,CfnTableBucketPolicy),error}cdk().requireProperty(props,"resourcePolicy",this),cdk().requireProperty(props,"tableBucketArn",this),this.resourcePolicy=props.resourcePolicy,this.tableBucketArn=props.tableBucketArn}get cfnProperties(){return{resourcePolicy:this.resourcePolicy,tableBucketArn:this.tableBucketArn}}inspect(inspector){try{jsiiDeprecationWarnings().aws_cdk_lib_TreeInspector(inspector)}catch(error){throw process.env.JSII_DEBUG!=="1"&&error.name==="DeprecationError"&&Error.captureStackTrace(error,this.inspect),error}inspector.addAttribute("aws:cdk:cloudformation:type",CfnTableBucketPolicy.CFN_RESOURCE_TYPE_NAME),inspector.addAttribute("aws:cdk:cloudformation:props",this.cfnProperties)}renderProperties(props){return convertCfnTableBucketPolicyPropsToCloudFormation(props)}}exports.CfnTableBucketPolicy=CfnTableBucketPolicy,_b=JSII_RTTI_SYMBOL_1,CfnTableBucketPolicy[_b]={fqn:"aws-cdk-lib.aws_s3tables.CfnTableBucketPolicy",version:"2.201.0"},CfnTableBucketPolicy.CFN_RESOURCE_TYPE_NAME="AWS::S3Tables::TableBucketPolicy";function CfnTableBucketPolicyPropsValidator(properties){if(!cdk().canInspect(properties))return cdk().VALIDATION_SUCCESS;const errors=new(cdk()).ValidationResults;return properties&&typeof properties=="object"&&!Array.isArray(properties)||errors.collect(new(cdk()).ValidationResult("Expected an object, but received: "+JSON.stringify(properties))),errors.collect(cdk().propertyValidator("resourcePolicy",cdk().requiredValidator)(properties.resourcePolicy)),errors.collect(cdk().propertyValidator("resourcePolicy",cdk().unionValidator(cdk().validateString,cdk().validateObject))(properties.resourcePolicy)),errors.collect(cdk().propertyValidator("tableBucketArn",cdk().requiredValidator)(properties.tableBucketArn)),errors.collect(cdk().propertyValidator("tableBucketArn",cdk().validateString)(properties.tableBucketArn)),errors.wrap('supplied properties not correct for "CfnTableBucketPolicyProps"')}function convertCfnTableBucketPolicyPropsToCloudFormation(properties){return cdk().canInspect(properties)?(CfnTableBucketPolicyPropsValidator(properties).assertSuccess(),{ResourcePolicy:cdk().unionMapper([cdk().validateString,cdk().validateObject],[cdk().stringToCloudFormation,cdk().objectToCloudFormation])(properties.resourcePolicy),TableBucketARN:cdk().stringToCloudFormation(properties.tableBucketArn)}):properties}function CfnTableBucketPolicyPropsFromCloudFormation(properties){if(cdk().isResolvableObject(properties))return new(cfn_parse()).FromCloudFormationResult(properties);if(properties=properties??{},!(properties&&typeof properties=="object"&&!Array.isArray(properties)))return new(cfn_parse()).FromCloudFormationResult(properties);const ret=new(cfn_parse()).FromCloudFormationPropertyObject;return ret.addPropertyResult("resourcePolicy","ResourcePolicy",properties.ResourcePolicy!=null?cfn_parse().FromCloudFormation.getTypeUnion([cdk().validateString,cdk().validateObject],[cfn_parse().FromCloudFormation.getString,cfn_parse().FromCloudFormation.getAny])(properties.ResourcePolicy):void 0),ret.addPropertyResult("tableBucketArn","TableBucketARN",properties.TableBucketARN!=null?cfn_parse().FromCloudFormation.getString(properties.TableBucketARN):void 0),ret.addUnrecognizedPropertiesAsExtra(properties),ret}
