"use strict";Object.defineProperty(exports,"__esModule",{value:!0}),exports.ApiGatewayMetrics=void 0;class ApiGatewayMetrics{static _4XxErrorSum(dimensions){return{namespace:"AWS/ApiGateway",metricName:"4XXError",dimensionsMap:dimensions,statistic:"Sum"}}static _5XxErrorSum(dimensions){return{namespace:"AWS/ApiGateway",metricName:"5XXError",dimensionsMap:dimensions,statistic:"Sum"}}static cacheHitCountSum(dimensions){return{namespace:"AWS/ApiGateway",metricName:"CacheHitCount",dimensionsMap:dimensions,statistic:"Sum"}}static cacheMissCountSum(dimensions){return{namespace:"AWS/ApiGateway",metricName:"CacheMissCount",dimensionsMap:dimensions,statistic:"Sum"}}static countSum(dimensions){return{namespace:"AWS/ApiGateway",metricName:"Count",dimensionsMap:dimensions,statistic:"Sum"}}static integrationLatencyAverage(dimensions){return{namespace:"AWS/ApiGateway",metricName:"IntegrationLatency",dimensionsMap:dimensions,statistic:"Average"}}static latencyAverage(dimensions){return{namespace:"AWS/ApiGateway",metricName:"Latency",dimensionsMap:dimensions,statistic:"Average"}}}exports.ApiGatewayMetrics=ApiGatewayMetrics;
