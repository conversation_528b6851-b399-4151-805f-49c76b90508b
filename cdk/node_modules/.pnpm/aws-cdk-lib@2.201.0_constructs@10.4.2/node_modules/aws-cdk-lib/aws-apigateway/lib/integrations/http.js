"use strict";var _a;Object.defineProperty(exports,"__esModule",{value:!0}),exports.HttpIntegration=void 0;var jsiiDeprecationWarnings=()=>{var tmp=require("../../../.warnings.jsii.js");return jsiiDeprecationWarnings=()=>tmp,tmp};const JSII_RTTI_SYMBOL_1=Symbol.for("jsii.rtti");var integration_1=()=>{var tmp=require("../integration");return integration_1=()=>tmp,tmp};class HttpIntegration extends integration_1().Integration{constructor(url,props={}){try{jsiiDeprecationWarnings().aws_cdk_lib_aws_apigateway_HttpIntegrationProps(props)}catch(error){throw process.env.JSII_DEBUG!=="1"&&error.name==="DeprecationError"&&Error.captureStackTrace(error,HttpIntegration),error}const proxy=props.proxy??!0,method=props.httpMethod||"GET";super({type:proxy?integration_1().IntegrationType.HTTP_PROXY:integration_1().IntegrationType.HTTP,integrationHttpMethod:method,uri:url,options:props.options})}}exports.HttpIntegration=HttpIntegration,_a=JSII_RTTI_SYMBOL_1,HttpIntegration[_a]={fqn:"aws-cdk-lib.aws_apigateway.HttpIntegration",version:"2.201.0"};
