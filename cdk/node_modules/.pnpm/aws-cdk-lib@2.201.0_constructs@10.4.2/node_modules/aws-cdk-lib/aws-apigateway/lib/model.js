"use strict";var __decorate=exports&&exports.__decorate||function(decorators,target,key,desc){var c=arguments.length,r=c<3?target:desc===null?desc=Object.getOwnPropertyDescriptor(target,key):desc,d;if(typeof Reflect=="object"&&typeof Reflect.decorate=="function")r=Reflect.decorate(decorators,target,key,desc);else for(var i=decorators.length-1;i>=0;i--)(d=decorators[i])&&(r=(c<3?d(r):c>3?d(target,key,r):d(target,key))||r);return c>3&&r&&Object.defineProperty(target,key,r),r},_a,_b,_c;Object.defineProperty(exports,"__esModule",{value:!0}),exports.Model=exports.ErrorModel=exports.EmptyModel=void 0;var jsiiDeprecationWarnings=()=>{var tmp=require("../../.warnings.jsii.js");return jsiiDeprecationWarnings=()=>tmp,tmp};const JSII_RTTI_SYMBOL_1=Symbol.for("jsii.rtti");var apigateway_generated_1=()=>{var tmp=require("./apigateway.generated");return apigateway_generated_1=()=>tmp,tmp},restapi_1=()=>{var tmp=require("./restapi");return restapi_1=()=>tmp,tmp},util=()=>{var tmp=require("./util");return util=()=>tmp,tmp},core_1=()=>{var tmp=require("../../core");return core_1=()=>tmp,tmp},metadata_resource_1=()=>{var tmp=require("../../core/lib/metadata-resource");return metadata_resource_1=()=>tmp,tmp},prop_injectable_1=()=>{var tmp=require("../../core/lib/prop-injectable");return prop_injectable_1=()=>tmp,tmp};class EmptyModel{constructor(){this.modelId="Empty"}}exports.EmptyModel=EmptyModel,_a=JSII_RTTI_SYMBOL_1,EmptyModel[_a]={fqn:"aws-cdk-lib.aws_apigateway.EmptyModel",version:"2.201.0"};class ErrorModel{constructor(){this.modelId="Error"}}exports.ErrorModel=ErrorModel,_b=JSII_RTTI_SYMBOL_1,ErrorModel[_b]={fqn:"aws-cdk-lib.aws_apigateway.ErrorModel",version:"2.201.0"};let Model=class Model2 extends core_1().Resource{static fromModelName(scope,id,modelName){class Import extends core_1().Resource{constructor(){super(...arguments),this.modelId=modelName}}return new Import(scope,id)}constructor(scope,id,props){super(scope,id,{physicalName:props.modelName});try{jsiiDeprecationWarnings().aws_cdk_lib_aws_apigateway_ModelProps(props)}catch(error){throw process.env.JSII_DEBUG!=="1"&&error.name==="DeprecationError"&&Error.captureStackTrace(error,Model2),error}(0,metadata_resource_1().addConstructMetadata)(this,props);const modelProps={name:this.physicalName,restApiId:props.restApi.restApiId,contentType:props.contentType??"application/json",description:props.description,schema:util().JsonSchemaMapper.toCfnJsonSchema(props.schema)},resource=new(apigateway_generated_1()).CfnModel(this,"Resource",modelProps);this.modelId=this.getResourceNameAttribute(resource.ref);const deployment=props.restApi instanceof restapi_1().RestApi?props.restApi.latestDeployment:void 0;deployment&&(deployment.node.addDependency(resource),deployment.addToLogicalId({model:modelProps}))}};exports.Model=Model,_c=JSII_RTTI_SYMBOL_1,Model[_c]={fqn:"aws-cdk-lib.aws_apigateway.Model",version:"2.201.0"},Model.PROPERTY_INJECTION_ID="aws-cdk-lib.aws-apigateway.Model",Model.ERROR_MODEL=new ErrorModel,Model.EMPTY_MODEL=new EmptyModel,exports.Model=Model=__decorate([prop_injectable_1().propertyInjectable],Model);
