## Velocity Template used for API Gateway request mapping template
##
## This template forwards the request body, header, path, and querystring
## to the execution input of the state machine.
##
## "@@" is used here as a placeholder for '"' to avoid using escape characters.

#set($inputString = '')
#set($includeHeaders = %INCLUDE_HEADERS%)
#set($includeQueryString = %INCLUDE_QUERYSTRING%)
#set($includePath = %INCLUDE_PATH%)
#set($includeAuthorizer = %INCLUDE_AUTHORIZER%)
#set($allParams = $input.params())
{
    "stateMachineArn": "%STATEMACHINE%",

    #set($inputString = "$inputString,@@body@@: $input.body")

    #if ($includeHeaders)
        #set($inputString = "$inputString, @@header@@:{")
        #foreach($paramName in $allParams.header.keySet())
            #set($inputString = "$inputString @@$paramName@@: @@$util.escapeJavaScript($allParams.header.get($paramName))@@")
            #if($foreach.hasNext)
                #set($inputString = "$inputString,")
            #end
        #end
        #set($inputString = "$inputString }")
        
    #end

    #if ($includeQueryString)
        #set($inputString = "$inputString, @@querystring@@:{")
        #foreach($paramName in $allParams.querystring.keySet())
            #set($inputString = "$inputString @@$paramName@@: @@$util.escapeJavaScript($allParams.querystring.get($paramName))@@")
            #if($foreach.hasNext)
                #set($inputString = "$inputString,")
            #end
        #end
        #set($inputString = "$inputString }")
    #end

    #if ($includePath)
        #set($inputString = "$inputString, @@path@@:{")
        #foreach($paramName in $allParams.path.keySet())
            #set($inputString = "$inputString @@$paramName@@: @@$util.escapeJavaScript($allParams.path.get($paramName))@@")
            #if($foreach.hasNext)
                #set($inputString = "$inputString,")
            #end
        #end
        #set($inputString = "$inputString }")
    #end
    
    #if ($includeAuthorizer)
        #set($inputString = "$inputString, @@authorizer@@:{")
        #foreach($paramName in $context.authorizer.keySet())
            #set($inputString = "$inputString @@$paramName@@: @@$util.escapeJavaScript($context.authorizer.get($paramName))@@")
            #if($foreach.hasNext)
                #set($inputString = "$inputString,")
            #end
        #end
        #set($inputString = "$inputString }")
    #end

    #set($requestContext = "%REQUESTCONTEXT%")
    ## Check if the request context should be included as part of the execution input
    #if($requestContext && !$requestContext.empty)
        #set($inputString = "$inputString,")
        #set($inputString = "$inputString @@requestContext@@: $requestContext")
    #end

    #set($inputString = "$inputString}")
    #set($inputString = $inputString.replaceAll("@@",'"'))
    #set($len = $inputString.length() - 1)
    "input": "{$util.escapeJavaScript($inputString.substring(1,$len)).replaceAll("\\'","'")}"
}
