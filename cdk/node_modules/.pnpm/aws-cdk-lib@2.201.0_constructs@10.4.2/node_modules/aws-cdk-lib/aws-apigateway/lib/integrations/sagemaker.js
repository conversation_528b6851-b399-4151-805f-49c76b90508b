"use strict";var _a;Object.defineProperty(exports,"__esModule",{value:!0}),exports.SagemakerIntegration=void 0;var jsiiDeprecationWarnings=()=>{var tmp=require("../../../.warnings.jsii.js");return jsiiDeprecationWarnings=()=>tmp,tmp};const JSII_RTTI_SYMBOL_1=Symbol.for("jsii.rtti");var aws_1=()=>{var tmp=require("./aws");return aws_1=()=>tmp,tmp},iam=()=>{var tmp=require("../../../aws-iam");return iam=()=>tmp,tmp};class SagemakerIntegration extends aws_1().AwsIntegration{constructor(endpoint,options={}){super({service:"runtime.sagemaker",path:`endpoints/${endpoint.endpointName}/invocations`,options:{credentialsRole:options.credentialsRole,integrationResponses:[{statusCode:"200"}],...options}});try{jsiiDeprecationWarnings().aws_cdk_lib_aws_sagemaker_IEndpoint(endpoint),jsiiDeprecationWarnings().aws_cdk_lib_aws_apigateway_SagemakerIntegrationOptions(options)}catch(error){throw process.env.JSII_DEBUG!=="1"&&error.name==="DeprecationError"&&Error.captureStackTrace(error,SagemakerIntegration),error}this.endpoint=endpoint}bind(method){try{jsiiDeprecationWarnings().aws_cdk_lib_aws_apigateway_Method(method)}catch(error){throw process.env.JSII_DEBUG!=="1"&&error.name==="DeprecationError"&&Error.captureStackTrace(error,this.bind),error}const bindResult=super.bind(method),credentialsRole=bindResult.options?.credentialsRole??new(iam()).Role(method,"SagemakerRole",{assumedBy:new(iam()).ServicePrincipal("apigateway.amazonaws.com"),description:"Generated by CDK::ApiGateway::SagemakerIntegration"});return this.endpoint.grantInvoke(credentialsRole),method.addMethodResponse({statusCode:"200"}),{...bindResult,options:{...bindResult.options,credentialsRole}}}}exports.SagemakerIntegration=SagemakerIntegration,_a=JSII_RTTI_SYMBOL_1,SagemakerIntegration[_a]={fqn:"aws-cdk-lib.aws_apigateway.SagemakerIntegration",version:"2.201.0"};
