"use strict";var __decorate=exports&&exports.__decorate||function(decorators,target,key,desc){var c=arguments.length,r=c<3?target:desc===null?desc=Object.getOwnPropertyDescriptor(target,key):desc,d;if(typeof Reflect=="object"&&typeof Reflect.decorate=="function")r=Reflect.decorate(decorators,target,key,desc);else for(var i=decorators.length-1;i>=0;i--)(d=decorators[i])&&(r=(c<3?d(r):c>3?d(target,key,r):d(target,key))||r);return c>3&&r&&Object.defineProperty(target,key,r),r},_a,_b;Object.defineProperty(exports,"__esModule",{value:!0}),exports.Stage=exports.StageBase=exports.MethodLoggingLevel=void 0;var jsiiDeprecationWarnings=()=>{var tmp=require("../../.warnings.jsii.js");return jsiiDeprecationWarnings=()=>tmp,tmp};const JSII_RTTI_SYMBOL_1=Symbol.for("jsii.rtti");var access_log_1=()=>{var tmp=require("./access-log");return access_log_1=()=>tmp,tmp},api_key_1=()=>{var tmp=require("./api-key");return api_key_1=()=>tmp,tmp},apigateway_canned_metrics_generated_1=()=>{var tmp=require("./apigateway-canned-metrics.generated");return apigateway_canned_metrics_generated_1=()=>tmp,tmp},apigateway_generated_1=()=>{var tmp=require("./apigateway.generated");return apigateway_generated_1=()=>tmp,tmp},restapi_1=()=>{var tmp=require("./restapi");return restapi_1=()=>tmp,tmp},util_1=()=>{var tmp=require("./util");return util_1=()=>tmp,tmp},cloudwatch=()=>{var tmp=require("../../aws-cloudwatch");return cloudwatch=()=>tmp,tmp},core_1=()=>{var tmp=require("../../core");return core_1=()=>tmp,tmp},errors_1=()=>{var tmp=require("../../core/lib/errors");return errors_1=()=>tmp,tmp},metadata_resource_1=()=>{var tmp=require("../../core/lib/metadata-resource");return metadata_resource_1=()=>tmp,tmp},prop_injectable_1=()=>{var tmp=require("../../core/lib/prop-injectable");return prop_injectable_1=()=>tmp,tmp},MethodLoggingLevel;(function(MethodLoggingLevel2){MethodLoggingLevel2.OFF="OFF",MethodLoggingLevel2.ERROR="ERROR",MethodLoggingLevel2.INFO="INFO"})(MethodLoggingLevel||(exports.MethodLoggingLevel=MethodLoggingLevel={}));class StageBase extends core_1().Resource{addApiKey(id,options){try{jsiiDeprecationWarnings().aws_cdk_lib_aws_apigateway_ApiKeyOptions(options)}catch(error){throw process.env.JSII_DEBUG!=="1"&&error.name==="DeprecationError"&&Error.captureStackTrace(error,this.addApiKey),error}return new(api_key_1()).ApiKey(this,id,{stages:[this],...options})}urlForPath(path="/"){if(!path.startsWith("/"))throw new(errors_1()).ValidationError(`Path must begin with "/": ${path}`,this);return`https://${this.restApi.restApiId}.execute-api.${core_1().Stack.of(this).region}.${core_1().Stack.of(this).urlSuffix}/${this.stageName}${path}`}get stageArn(){return core_1().Stack.of(this).formatArn({arnFormat:core_1().ArnFormat.SLASH_RESOURCE_SLASH_RESOURCE_NAME,service:"apigateway",account:"",resource:"restapis",resourceName:`${this.restApi.restApiId}/stages/${this.stageName}`})}metric(metricName,props){try{jsiiDeprecationWarnings().aws_cdk_lib_aws_cloudwatch_MetricOptions(props)}catch(error){throw process.env.JSII_DEBUG!=="1"&&error.name==="DeprecationError"&&Error.captureStackTrace(error,this.metric),error}return new(cloudwatch()).Metric({namespace:"AWS/ApiGateway",metricName,dimensionsMap:{ApiName:this.restApi.restApiName,Stage:this.stageName},...props}).attachTo(this)}metricClientError(props){try{jsiiDeprecationWarnings().aws_cdk_lib_aws_cloudwatch_MetricOptions(props)}catch(error){throw process.env.JSII_DEBUG!=="1"&&error.name==="DeprecationError"&&Error.captureStackTrace(error,this.metricClientError),error}return this.cannedMetric(apigateway_canned_metrics_generated_1().ApiGatewayMetrics._4XxErrorSum,props)}metricServerError(props){try{jsiiDeprecationWarnings().aws_cdk_lib_aws_cloudwatch_MetricOptions(props)}catch(error){throw process.env.JSII_DEBUG!=="1"&&error.name==="DeprecationError"&&Error.captureStackTrace(error,this.metricServerError),error}return this.cannedMetric(apigateway_canned_metrics_generated_1().ApiGatewayMetrics._5XxErrorSum,props)}metricCacheHitCount(props){try{jsiiDeprecationWarnings().aws_cdk_lib_aws_cloudwatch_MetricOptions(props)}catch(error){throw process.env.JSII_DEBUG!=="1"&&error.name==="DeprecationError"&&Error.captureStackTrace(error,this.metricCacheHitCount),error}return this.cannedMetric(apigateway_canned_metrics_generated_1().ApiGatewayMetrics.cacheHitCountSum,props)}metricCacheMissCount(props){try{jsiiDeprecationWarnings().aws_cdk_lib_aws_cloudwatch_MetricOptions(props)}catch(error){throw process.env.JSII_DEBUG!=="1"&&error.name==="DeprecationError"&&Error.captureStackTrace(error,this.metricCacheMissCount),error}return this.cannedMetric(apigateway_canned_metrics_generated_1().ApiGatewayMetrics.cacheMissCountSum,props)}metricCount(props){try{jsiiDeprecationWarnings().aws_cdk_lib_aws_cloudwatch_MetricOptions(props)}catch(error){throw process.env.JSII_DEBUG!=="1"&&error.name==="DeprecationError"&&Error.captureStackTrace(error,this.metricCount),error}return this.cannedMetric(apigateway_canned_metrics_generated_1().ApiGatewayMetrics.countSum,{statistic:"SampleCount",...props})}metricIntegrationLatency(props){try{jsiiDeprecationWarnings().aws_cdk_lib_aws_cloudwatch_MetricOptions(props)}catch(error){throw process.env.JSII_DEBUG!=="1"&&error.name==="DeprecationError"&&Error.captureStackTrace(error,this.metricIntegrationLatency),error}return this.cannedMetric(apigateway_canned_metrics_generated_1().ApiGatewayMetrics.integrationLatencyAverage,props)}metricLatency(props){try{jsiiDeprecationWarnings().aws_cdk_lib_aws_cloudwatch_MetricOptions(props)}catch(error){throw process.env.JSII_DEBUG!=="1"&&error.name==="DeprecationError"&&Error.captureStackTrace(error,this.metricLatency),error}return this.cannedMetric(apigateway_canned_metrics_generated_1().ApiGatewayMetrics.latencyAverage,props)}cannedMetric(fn,props){return new(cloudwatch()).Metric({...fn({ApiName:this.restApi.restApiName,Stage:this.stageName}),...props}).attachTo(this)}}exports.StageBase=StageBase,_a=JSII_RTTI_SYMBOL_1,StageBase[_a]={fqn:"aws-cdk-lib.aws_apigateway.StageBase",version:"2.201.0"};let Stage=class Stage2 extends StageBase{static fromStageAttributes(scope,id,attrs){try{jsiiDeprecationWarnings().aws_cdk_lib_aws_apigateway_StageAttributes(attrs)}catch(error){throw process.env.JSII_DEBUG!=="1"&&error.name==="DeprecationError"&&Error.captureStackTrace(error,this.fromStageAttributes),error}class Import extends StageBase{constructor(){super(...arguments),this.stageName=attrs.stageName,this.restApi=attrs.restApi}}return new Import(scope,id)}constructor(scope,id,props){super(scope,id);try{jsiiDeprecationWarnings().aws_cdk_lib_aws_apigateway_StageProps(props)}catch(error){throw process.env.JSII_DEBUG!=="1"&&error.name==="DeprecationError"&&Error.captureStackTrace(error,Stage2),error}(0,metadata_resource_1().addConstructMetadata)(this,props),this.enableCacheCluster=props.cacheClusterEnabled;const methodSettings=this.renderMethodSettings(props);let accessLogSetting;const accessLogDestination=props.accessLogDestination,accessLogFormat=props.accessLogFormat;if(!accessLogDestination&&!accessLogFormat)accessLogSetting=void 0;else{if(accessLogFormat!==void 0&&!core_1().Token.isUnresolved(accessLogFormat.toString())&&!/.*\$context.(requestId|extendedRequestId)\b.*/.test(accessLogFormat.toString()))throw new(errors_1()).ValidationError("Access log must include either `AccessLogFormat.contextRequestId()` or `AccessLogFormat.contextExtendedRequestId()`",this);if(accessLogFormat!==void 0&&accessLogDestination===void 0)throw new(errors_1()).ValidationError("Access log format is specified without a destination",this);accessLogSetting={destinationArn:accessLogDestination?.bind(this).destinationArn,format:accessLogFormat?.toString()?accessLogFormat?.toString():access_log_1().AccessLogFormat.clf().toString()}}if(props.cacheClusterSize!==void 0){if(this.enableCacheCluster===void 0)this.enableCacheCluster=!0;else if(this.enableCacheCluster===!1)throw new(errors_1()).ValidationError(`Cannot set "cacheClusterSize" to ${props.cacheClusterSize} and "cacheClusterEnabled" to "false"`,this)}const cacheClusterSize=this.enableCacheCluster?props.cacheClusterSize||"0.5":void 0,resource=new(apigateway_generated_1()).CfnStage(this,"Resource",{stageName:props.stageName||"prod",accessLogSetting,cacheClusterEnabled:this.enableCacheCluster,cacheClusterSize,clientCertificateId:props.clientCertificateId,deploymentId:props.deployment.deploymentId,restApiId:props.deployment.api.restApiId,description:props.description,documentationVersion:props.documentationVersion,variables:props.variables,tracingEnabled:props.tracingEnabled,methodSettings});this.stageName=resource.ref,this.restApi=props.deployment.api,restapi_1().RestApiBase._isRestApiBase(this.restApi)&&this.restApi._attachStage(this)}renderMethodSettings(props){const settings=new Array,self=this,commonMethodOptions={metricsEnabled:props.metricsEnabled,loggingLevel:props.loggingLevel,dataTraceEnabled:props.dataTraceEnabled,throttlingBurstLimit:props.throttlingBurstLimit,throttlingRateLimit:props.throttlingRateLimit,cachingEnabled:props.cachingEnabled,cacheTtl:props.cacheTtl,cacheDataEncrypted:props.cacheDataEncrypted};if(Object.keys(commonMethodOptions).map(v=>commonMethodOptions[v]).filter(x=>x!==void 0).length>0&&settings.push(renderEntry("/*/*",commonMethodOptions)),props.methodOptions)for(const path of Object.keys(props.methodOptions))settings.push(renderEntry(path,props.methodOptions[path]));return settings.length===0?void 0:settings;function renderEntry(path,options){if(options.cachingEnabled){if(self.enableCacheCluster===void 0)self.enableCacheCluster=!0;else if(self.enableCacheCluster===!1)throw new(errors_1()).ValidationError(`Cannot enable caching for method ${path} since cache cluster is disabled on stage`,self)}const{httpMethod,resourcePath}=(0,util_1().parseMethodOptionsPath)(path);return{httpMethod,resourcePath,cacheDataEncrypted:options.cacheDataEncrypted,cacheTtlInSeconds:options.cacheTtl&&options.cacheTtl.toSeconds(),cachingEnabled:options.cachingEnabled,dataTraceEnabled:options.dataTraceEnabled??!1,loggingLevel:options.loggingLevel,metricsEnabled:options.metricsEnabled,throttlingBurstLimit:options.throttlingBurstLimit,throttlingRateLimit:options.throttlingRateLimit}}}};exports.Stage=Stage,_b=JSII_RTTI_SYMBOL_1,Stage[_b]={fqn:"aws-cdk-lib.aws_apigateway.Stage",version:"2.201.0"},Stage.PROPERTY_INJECTION_ID="aws-cdk-lib.aws-apigateway.Stage",exports.Stage=Stage=__decorate([prop_injectable_1().propertyInjectable],Stage);
