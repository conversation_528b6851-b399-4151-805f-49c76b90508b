"use strict";var __decorate=exports&&exports.__decorate||function(decorators,target,key,desc){var c=arguments.length,r=c<3?target:desc===null?desc=Object.getOwnPropertyDescriptor(target,key):desc,d;if(typeof Reflect=="object"&&typeof Reflect.decorate=="function")r=Reflect.decorate(decorators,target,key,desc);else for(var i=decorators.length-1;i>=0;i--)(d=decorators[i])&&(r=(c<3?d(r):c>3?d(target,key,r):d(target,key))||r);return c>3&&r&&Object.defineProperty(target,key,r),r},_a;Object.defineProperty(exports,"__esModule",{value:!0}),exports.DomainName=exports.SecurityPolicy=void 0;var jsiiDeprecationWarnings=()=>{var tmp=require("../../.warnings.jsii.js");return jsiiDeprecationWarnings=()=>tmp,tmp};const JSII_RTTI_SYMBOL_1=Symbol.for("jsii.rtti");var apigateway_generated_1=()=>{var tmp=require("./apigateway.generated");return apigateway_generated_1=()=>tmp,tmp},base_path_mapping_1=()=>{var tmp=require("./base-path-mapping");return base_path_mapping_1=()=>tmp,tmp},restapi_1=()=>{var tmp=require("./restapi");return restapi_1=()=>tmp,tmp},apigwv2=()=>{var tmp=require("../../aws-apigatewayv2");return apigwv2=()=>tmp,tmp},core_1=()=>{var tmp=require("../../core");return core_1=()=>tmp,tmp},errors_1=()=>{var tmp=require("../../core/lib/errors");return errors_1=()=>tmp,tmp},metadata_resource_1=()=>{var tmp=require("../../core/lib/metadata-resource");return metadata_resource_1=()=>tmp,tmp},prop_injectable_1=()=>{var tmp=require("../../core/lib/prop-injectable");return prop_injectable_1=()=>tmp,tmp},SecurityPolicy;(function(SecurityPolicy2){SecurityPolicy2.TLS_1_0="TLS_1_0",SecurityPolicy2.TLS_1_2="TLS_1_2"})(SecurityPolicy||(exports.SecurityPolicy=SecurityPolicy={}));let DomainName=class DomainName2 extends core_1().Resource{static fromDomainNameAttributes(scope,id,attrs){try{jsiiDeprecationWarnings().aws_cdk_lib_aws_apigateway_DomainNameAttributes(attrs)}catch(error){throw process.env.JSII_DEBUG!=="1"&&error.name==="DeprecationError"&&Error.captureStackTrace(error,this.fromDomainNameAttributes),error}class Import extends core_1().Resource{constructor(){super(...arguments),this.domainName=attrs.domainName,this.domainNameAliasDomainName=attrs.domainNameAliasTarget,this.domainNameAliasHostedZoneId=attrs.domainNameAliasHostedZoneId}}return new Import(scope,id)}constructor(scope,id,props){super(scope,id),this.basePaths=new Set;try{jsiiDeprecationWarnings().aws_cdk_lib_aws_apigateway_DomainNameProps(props)}catch(error){throw process.env.JSII_DEBUG!=="1"&&error.name==="DeprecationError"&&Error.captureStackTrace(error,DomainName2),error}(0,metadata_resource_1().addConstructMetadata)(this,props),this.endpointType=props.endpointType||restapi_1().EndpointType.REGIONAL;const edge=this.endpointType===restapi_1().EndpointType.EDGE;if(this.securityPolicy=props.securityPolicy,!core_1().Token.isUnresolved(props.domainName)&&/[A-Z]/.test(props.domainName))throw new(errors_1()).ValidationError(`Domain name does not support uppercase letters. Got: ${props.domainName}`,scope);const mtlsConfig=this.configureMTLS(props.mtls),resource=new(apigateway_generated_1()).CfnDomainName(this,"Resource",{domainName:props.domainName,certificateArn:edge?props.certificate.certificateArn:void 0,regionalCertificateArn:edge?void 0:props.certificate.certificateArn,endpointConfiguration:{types:[this.endpointType]},mutualTlsAuthentication:mtlsConfig,securityPolicy:props.securityPolicy});this.domainName=resource.ref,this.domainNameAliasDomainName=edge?resource.attrDistributionDomainName:resource.attrRegionalDomainName,this.domainNameAliasHostedZoneId=edge?resource.attrDistributionHostedZoneId:resource.attrRegionalHostedZoneId;const multiLevel=this.validateBasePath(props.basePath);props.mapping&&!multiLevel?this.addBasePathMapping(props.mapping,{basePath:props.basePath}):props.mapping&&multiLevel&&this.addApiMapping(props.mapping.deploymentStage,{basePath:props.basePath})}validateBasePath(path){if(this.isMultiLevel(path)){if(this.endpointType===restapi_1().EndpointType.EDGE)throw new(errors_1()).ValidationError("multi-level basePath is only supported when endpointType is EndpointType.REGIONAL",this);if(this.securityPolicy&&this.securityPolicy!==SecurityPolicy.TLS_1_2)throw new(errors_1()).ValidationError("securityPolicy must be set to TLS_1_2 if multi-level basePath is provided",this);return!0}return!1}isMultiLevel(path){return(path?.split("/").filter(x=>!!x)??[]).length>=2}addBasePathMapping(targetApi,options={}){try{jsiiDeprecationWarnings().aws_cdk_lib_aws_apigateway_IRestApi(targetApi),jsiiDeprecationWarnings().aws_cdk_lib_aws_apigateway_BasePathMappingOptions(options)}catch(error){throw process.env.JSII_DEBUG!=="1"&&error.name==="DeprecationError"&&Error.captureStackTrace(error,this.addBasePathMapping),error}if(this.basePaths.has(options.basePath))throw new(errors_1()).ValidationError(`DomainName ${this.node.id} already has a mapping for path ${options.basePath}`,this);if(this.isMultiLevel(options.basePath))throw new(errors_1()).ValidationError('BasePathMapping does not support multi-level paths. Use "addApiMapping instead.',this);this.basePaths.add(options.basePath);const id=`Map:${options.basePath||"/"}=>${core_1().Names.nodeUniqueId(targetApi.node)}`;return new(base_path_mapping_1()).BasePathMapping(this,id,{domainName:this,restApi:targetApi,...options})}addApiMapping(targetStage,options={}){try{jsiiDeprecationWarnings().aws_cdk_lib_aws_apigateway_IStage(targetStage),jsiiDeprecationWarnings().aws_cdk_lib_aws_apigateway_ApiMappingOptions(options)}catch(error){throw process.env.JSII_DEBUG!=="1"&&error.name==="DeprecationError"&&Error.captureStackTrace(error,this.addApiMapping),error}if(this.basePaths.has(options.basePath))throw new(errors_1()).ValidationError(`DomainName ${this.node.id} already has a mapping for path ${options.basePath}`,this);this.validateBasePath(options.basePath),this.basePaths.add(options.basePath);const id=`Map:${options.basePath??"none"}=>${core_1().Names.nodeUniqueId(targetStage.node)}`;new(apigwv2()).CfnApiMapping(this,id,{apiId:targetStage.restApi.restApiId,stage:targetStage.stageName,domainName:this.domainName,apiMappingKey:options.basePath})}configureMTLS(mtlsConfig){if(mtlsConfig)return{truststoreUri:mtlsConfig.bucket.s3UrlForObject(mtlsConfig.key),truststoreVersion:mtlsConfig.version}}};exports.DomainName=DomainName,_a=JSII_RTTI_SYMBOL_1,DomainName[_a]={fqn:"aws-cdk-lib.aws_apigateway.DomainName",version:"2.201.0"},DomainName.PROPERTY_INJECTION_ID="aws-cdk-lib.aws-apigateway.DomainName",__decorate([(0,metadata_resource_1().MethodMetadata)()],DomainName.prototype,"addBasePathMapping",null),__decorate([(0,metadata_resource_1().MethodMetadata)()],DomainName.prototype,"addApiMapping",null),exports.DomainName=DomainName=__decorate([prop_injectable_1().propertyInjectable],DomainName);
