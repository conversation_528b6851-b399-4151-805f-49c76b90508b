"use strict";var _a,_b,_c,_d,_e,_f,_g,_h,_j,_k,_l,_m,_o,_p,_q,_r,_s,_t,_u,_v,_w,_x;Object.defineProperty(exports,"__esModule",{value:!0}),exports.CfnDomainNameV2=exports.CfnDomainNameAccessAssociation=exports.CfnBasePathMappingV2=exports.CfnVpcLink=exports.CfnUsagePlanKey=exports.CfnUsagePlan=exports.CfnStage=exports.CfnRestApi=exports.CfnResource=exports.CfnRequestValidator=exports.CfnModel=exports.CfnMethod=exports.CfnGatewayResponse=exports.CfnDomainName=exports.CfnDocumentationVersion=exports.CfnDocumentationPart=exports.CfnDeployment=exports.CfnClientCertificate=exports.CfnBasePathMapping=exports.CfnAuthorizer=exports.CfnApiKey=exports.CfnAccount=void 0;var jsiiDeprecationWarnings=()=>{var tmp=require("../../.warnings.jsii.js");return jsiiDeprecationWarnings=()=>tmp,tmp};const JSII_RTTI_SYMBOL_1=Symbol.for("jsii.rtti");var cdk=()=>{var tmp=require("../../core");return cdk=()=>tmp,tmp},cfn_parse=()=>{var tmp=require("../../core/lib/helpers-internal");return cfn_parse=()=>tmp,tmp},cdk_errors=()=>{var tmp=require("../../core/lib/errors");return cdk_errors=()=>tmp,tmp};class CfnAccount extends cdk().CfnResource{static _fromCloudFormation(scope,id,resourceAttributes,options){resourceAttributes=resourceAttributes||{};const resourceProperties=options.parser.parseValue(resourceAttributes.Properties),propsResult=CfnAccountPropsFromCloudFormation(resourceProperties);if(cdk().isResolvableObject(propsResult.value))throw new(cdk_errors()).ValidationError("Unexpected IResolvable",scope);const ret=new CfnAccount(scope,id,propsResult.value);for(const[propKey,propVal]of Object.entries(propsResult.extraProperties))ret.addPropertyOverride(propKey,propVal);return options.parser.handleAttributes(ret,resourceAttributes,id),ret}constructor(scope,id,props={}){super(scope,id,{type:CfnAccount.CFN_RESOURCE_TYPE_NAME,properties:props});try{jsiiDeprecationWarnings().aws_cdk_lib_aws_apigateway_CfnAccountProps(props)}catch(error){throw process.env.JSII_DEBUG!=="1"&&error.name==="DeprecationError"&&Error.captureStackTrace(error,CfnAccount),error}this.attrId=cdk().Token.asString(this.getAtt("Id",cdk().ResolutionTypeHint.STRING)),this.cloudWatchRoleArn=props.cloudWatchRoleArn}get cfnProperties(){return{cloudWatchRoleArn:this.cloudWatchRoleArn}}inspect(inspector){try{jsiiDeprecationWarnings().aws_cdk_lib_TreeInspector(inspector)}catch(error){throw process.env.JSII_DEBUG!=="1"&&error.name==="DeprecationError"&&Error.captureStackTrace(error,this.inspect),error}inspector.addAttribute("aws:cdk:cloudformation:type",CfnAccount.CFN_RESOURCE_TYPE_NAME),inspector.addAttribute("aws:cdk:cloudformation:props",this.cfnProperties)}renderProperties(props){return convertCfnAccountPropsToCloudFormation(props)}}exports.CfnAccount=CfnAccount,_a=JSII_RTTI_SYMBOL_1,CfnAccount[_a]={fqn:"aws-cdk-lib.aws_apigateway.CfnAccount",version:"2.201.0"},CfnAccount.CFN_RESOURCE_TYPE_NAME="AWS::ApiGateway::Account";function CfnAccountPropsValidator(properties){if(!cdk().canInspect(properties))return cdk().VALIDATION_SUCCESS;const errors=new(cdk()).ValidationResults;return properties&&typeof properties=="object"&&!Array.isArray(properties)||errors.collect(new(cdk()).ValidationResult("Expected an object, but received: "+JSON.stringify(properties))),errors.collect(cdk().propertyValidator("cloudWatchRoleArn",cdk().validateString)(properties.cloudWatchRoleArn)),errors.wrap('supplied properties not correct for "CfnAccountProps"')}function convertCfnAccountPropsToCloudFormation(properties){return cdk().canInspect(properties)?(CfnAccountPropsValidator(properties).assertSuccess(),{CloudWatchRoleArn:cdk().stringToCloudFormation(properties.cloudWatchRoleArn)}):properties}function CfnAccountPropsFromCloudFormation(properties){if(cdk().isResolvableObject(properties))return new(cfn_parse()).FromCloudFormationResult(properties);if(properties=properties??{},!(properties&&typeof properties=="object"&&!Array.isArray(properties)))return new(cfn_parse()).FromCloudFormationResult(properties);const ret=new(cfn_parse()).FromCloudFormationPropertyObject;return ret.addPropertyResult("cloudWatchRoleArn","CloudWatchRoleArn",properties.CloudWatchRoleArn!=null?cfn_parse().FromCloudFormation.getString(properties.CloudWatchRoleArn):void 0),ret.addUnrecognizedPropertiesAsExtra(properties),ret}class CfnApiKey extends cdk().CfnResource{static _fromCloudFormation(scope,id,resourceAttributes,options){resourceAttributes=resourceAttributes||{};const resourceProperties=options.parser.parseValue(resourceAttributes.Properties),propsResult=CfnApiKeyPropsFromCloudFormation(resourceProperties);if(cdk().isResolvableObject(propsResult.value))throw new(cdk_errors()).ValidationError("Unexpected IResolvable",scope);const ret=new CfnApiKey(scope,id,propsResult.value);for(const[propKey,propVal]of Object.entries(propsResult.extraProperties))ret.addPropertyOverride(propKey,propVal);return options.parser.handleAttributes(ret,resourceAttributes,id),ret}constructor(scope,id,props={}){super(scope,id,{type:CfnApiKey.CFN_RESOURCE_TYPE_NAME,properties:props});try{jsiiDeprecationWarnings().aws_cdk_lib_aws_apigateway_CfnApiKeyProps(props)}catch(error){throw process.env.JSII_DEBUG!=="1"&&error.name==="DeprecationError"&&Error.captureStackTrace(error,CfnApiKey),error}this.attrApiKeyId=cdk().Token.asString(this.getAtt("APIKeyId",cdk().ResolutionTypeHint.STRING)),this.customerId=props.customerId,this.description=props.description,this.enabled=props.enabled,this.generateDistinctId=props.generateDistinctId,this.name=props.name,this.stageKeys=props.stageKeys,this.tags=new(cdk()).TagManager(cdk().TagType.STANDARD,"AWS::ApiGateway::ApiKey",props.tags,{tagPropertyName:"tags"}),this.tagsRaw=props.tags,this.value=props.value}get cfnProperties(){return{customerId:this.customerId,description:this.description,enabled:this.enabled,generateDistinctId:this.generateDistinctId,name:this.name,stageKeys:this.stageKeys,tags:this.tags.renderTags(),value:this.value}}inspect(inspector){try{jsiiDeprecationWarnings().aws_cdk_lib_TreeInspector(inspector)}catch(error){throw process.env.JSII_DEBUG!=="1"&&error.name==="DeprecationError"&&Error.captureStackTrace(error,this.inspect),error}inspector.addAttribute("aws:cdk:cloudformation:type",CfnApiKey.CFN_RESOURCE_TYPE_NAME),inspector.addAttribute("aws:cdk:cloudformation:props",this.cfnProperties)}renderProperties(props){return convertCfnApiKeyPropsToCloudFormation(props)}}exports.CfnApiKey=CfnApiKey,_b=JSII_RTTI_SYMBOL_1,CfnApiKey[_b]={fqn:"aws-cdk-lib.aws_apigateway.CfnApiKey",version:"2.201.0"},CfnApiKey.CFN_RESOURCE_TYPE_NAME="AWS::ApiGateway::ApiKey";function CfnApiKeyStageKeyPropertyValidator(properties){if(!cdk().canInspect(properties))return cdk().VALIDATION_SUCCESS;const errors=new(cdk()).ValidationResults;return properties&&typeof properties=="object"&&!Array.isArray(properties)||errors.collect(new(cdk()).ValidationResult("Expected an object, but received: "+JSON.stringify(properties))),errors.collect(cdk().propertyValidator("restApiId",cdk().validateString)(properties.restApiId)),errors.collect(cdk().propertyValidator("stageName",cdk().validateString)(properties.stageName)),errors.wrap('supplied properties not correct for "StageKeyProperty"')}function convertCfnApiKeyStageKeyPropertyToCloudFormation(properties){return cdk().canInspect(properties)?(CfnApiKeyStageKeyPropertyValidator(properties).assertSuccess(),{RestApiId:cdk().stringToCloudFormation(properties.restApiId),StageName:cdk().stringToCloudFormation(properties.stageName)}):properties}function CfnApiKeyStageKeyPropertyFromCloudFormation(properties){if(cdk().isResolvableObject(properties))return new(cfn_parse()).FromCloudFormationResult(properties);if(properties=properties??{},!(properties&&typeof properties=="object"&&!Array.isArray(properties)))return new(cfn_parse()).FromCloudFormationResult(properties);const ret=new(cfn_parse()).FromCloudFormationPropertyObject;return ret.addPropertyResult("restApiId","RestApiId",properties.RestApiId!=null?cfn_parse().FromCloudFormation.getString(properties.RestApiId):void 0),ret.addPropertyResult("stageName","StageName",properties.StageName!=null?cfn_parse().FromCloudFormation.getString(properties.StageName):void 0),ret.addUnrecognizedPropertiesAsExtra(properties),ret}function CfnApiKeyPropsValidator(properties){if(!cdk().canInspect(properties))return cdk().VALIDATION_SUCCESS;const errors=new(cdk()).ValidationResults;return properties&&typeof properties=="object"&&!Array.isArray(properties)||errors.collect(new(cdk()).ValidationResult("Expected an object, but received: "+JSON.stringify(properties))),errors.collect(cdk().propertyValidator("customerId",cdk().validateString)(properties.customerId)),errors.collect(cdk().propertyValidator("description",cdk().validateString)(properties.description)),errors.collect(cdk().propertyValidator("enabled",cdk().validateBoolean)(properties.enabled)),errors.collect(cdk().propertyValidator("generateDistinctId",cdk().validateBoolean)(properties.generateDistinctId)),errors.collect(cdk().propertyValidator("name",cdk().validateString)(properties.name)),errors.collect(cdk().propertyValidator("stageKeys",cdk().listValidator(CfnApiKeyStageKeyPropertyValidator))(properties.stageKeys)),errors.collect(cdk().propertyValidator("tags",cdk().listValidator(cdk().validateCfnTag))(properties.tags)),errors.collect(cdk().propertyValidator("value",cdk().validateString)(properties.value)),errors.wrap('supplied properties not correct for "CfnApiKeyProps"')}function convertCfnApiKeyPropsToCloudFormation(properties){return cdk().canInspect(properties)?(CfnApiKeyPropsValidator(properties).assertSuccess(),{CustomerId:cdk().stringToCloudFormation(properties.customerId),Description:cdk().stringToCloudFormation(properties.description),Enabled:cdk().booleanToCloudFormation(properties.enabled),GenerateDistinctId:cdk().booleanToCloudFormation(properties.generateDistinctId),Name:cdk().stringToCloudFormation(properties.name),StageKeys:cdk().listMapper(convertCfnApiKeyStageKeyPropertyToCloudFormation)(properties.stageKeys),Tags:cdk().listMapper(cdk().cfnTagToCloudFormation)(properties.tags),Value:cdk().stringToCloudFormation(properties.value)}):properties}function CfnApiKeyPropsFromCloudFormation(properties){if(cdk().isResolvableObject(properties))return new(cfn_parse()).FromCloudFormationResult(properties);if(properties=properties??{},!(properties&&typeof properties=="object"&&!Array.isArray(properties)))return new(cfn_parse()).FromCloudFormationResult(properties);const ret=new(cfn_parse()).FromCloudFormationPropertyObject;return ret.addPropertyResult("customerId","CustomerId",properties.CustomerId!=null?cfn_parse().FromCloudFormation.getString(properties.CustomerId):void 0),ret.addPropertyResult("description","Description",properties.Description!=null?cfn_parse().FromCloudFormation.getString(properties.Description):void 0),ret.addPropertyResult("enabled","Enabled",properties.Enabled!=null?cfn_parse().FromCloudFormation.getBoolean(properties.Enabled):void 0),ret.addPropertyResult("generateDistinctId","GenerateDistinctId",properties.GenerateDistinctId!=null?cfn_parse().FromCloudFormation.getBoolean(properties.GenerateDistinctId):void 0),ret.addPropertyResult("name","Name",properties.Name!=null?cfn_parse().FromCloudFormation.getString(properties.Name):void 0),ret.addPropertyResult("stageKeys","StageKeys",properties.StageKeys!=null?cfn_parse().FromCloudFormation.getArray(CfnApiKeyStageKeyPropertyFromCloudFormation)(properties.StageKeys):void 0),ret.addPropertyResult("tags","Tags",properties.Tags!=null?cfn_parse().FromCloudFormation.getArray(cfn_parse().FromCloudFormation.getCfnTag)(properties.Tags):void 0),ret.addPropertyResult("value","Value",properties.Value!=null?cfn_parse().FromCloudFormation.getString(properties.Value):void 0),ret.addUnrecognizedPropertiesAsExtra(properties),ret}class CfnAuthorizer extends cdk().CfnResource{static _fromCloudFormation(scope,id,resourceAttributes,options){resourceAttributes=resourceAttributes||{};const resourceProperties=options.parser.parseValue(resourceAttributes.Properties),propsResult=CfnAuthorizerPropsFromCloudFormation(resourceProperties);if(cdk().isResolvableObject(propsResult.value))throw new(cdk_errors()).ValidationError("Unexpected IResolvable",scope);const ret=new CfnAuthorizer(scope,id,propsResult.value);for(const[propKey,propVal]of Object.entries(propsResult.extraProperties))ret.addPropertyOverride(propKey,propVal);return options.parser.handleAttributes(ret,resourceAttributes,id),ret}constructor(scope,id,props){super(scope,id,{type:CfnAuthorizer.CFN_RESOURCE_TYPE_NAME,properties:props});try{jsiiDeprecationWarnings().aws_cdk_lib_aws_apigateway_CfnAuthorizerProps(props)}catch(error){throw process.env.JSII_DEBUG!=="1"&&error.name==="DeprecationError"&&Error.captureStackTrace(error,CfnAuthorizer),error}cdk().requireProperty(props,"name",this),cdk().requireProperty(props,"restApiId",this),cdk().requireProperty(props,"type",this),this.attrAuthorizerId=cdk().Token.asString(this.getAtt("AuthorizerId",cdk().ResolutionTypeHint.STRING)),this.authorizerCredentials=props.authorizerCredentials,this.authorizerResultTtlInSeconds=props.authorizerResultTtlInSeconds,this.authorizerUri=props.authorizerUri,this.authType=props.authType,this.identitySource=props.identitySource,this.identityValidationExpression=props.identityValidationExpression,this.name=props.name,this.providerArns=props.providerArns,this.restApiId=props.restApiId,this.type=props.type}get cfnProperties(){return{authorizerCredentials:this.authorizerCredentials,authorizerResultTtlInSeconds:this.authorizerResultTtlInSeconds,authorizerUri:this.authorizerUri,authType:this.authType,identitySource:this.identitySource,identityValidationExpression:this.identityValidationExpression,name:this.name,providerArns:this.providerArns,restApiId:this.restApiId,type:this.type}}inspect(inspector){try{jsiiDeprecationWarnings().aws_cdk_lib_TreeInspector(inspector)}catch(error){throw process.env.JSII_DEBUG!=="1"&&error.name==="DeprecationError"&&Error.captureStackTrace(error,this.inspect),error}inspector.addAttribute("aws:cdk:cloudformation:type",CfnAuthorizer.CFN_RESOURCE_TYPE_NAME),inspector.addAttribute("aws:cdk:cloudformation:props",this.cfnProperties)}renderProperties(props){return convertCfnAuthorizerPropsToCloudFormation(props)}}exports.CfnAuthorizer=CfnAuthorizer,_c=JSII_RTTI_SYMBOL_1,CfnAuthorizer[_c]={fqn:"aws-cdk-lib.aws_apigateway.CfnAuthorizer",version:"2.201.0"},CfnAuthorizer.CFN_RESOURCE_TYPE_NAME="AWS::ApiGateway::Authorizer";function CfnAuthorizerPropsValidator(properties){if(!cdk().canInspect(properties))return cdk().VALIDATION_SUCCESS;const errors=new(cdk()).ValidationResults;return properties&&typeof properties=="object"&&!Array.isArray(properties)||errors.collect(new(cdk()).ValidationResult("Expected an object, but received: "+JSON.stringify(properties))),errors.collect(cdk().propertyValidator("authType",cdk().validateString)(properties.authType)),errors.collect(cdk().propertyValidator("authorizerCredentials",cdk().validateString)(properties.authorizerCredentials)),errors.collect(cdk().propertyValidator("authorizerResultTtlInSeconds",cdk().validateNumber)(properties.authorizerResultTtlInSeconds)),errors.collect(cdk().propertyValidator("authorizerUri",cdk().validateString)(properties.authorizerUri)),errors.collect(cdk().propertyValidator("identitySource",cdk().validateString)(properties.identitySource)),errors.collect(cdk().propertyValidator("identityValidationExpression",cdk().validateString)(properties.identityValidationExpression)),errors.collect(cdk().propertyValidator("name",cdk().requiredValidator)(properties.name)),errors.collect(cdk().propertyValidator("name",cdk().validateString)(properties.name)),errors.collect(cdk().propertyValidator("providerArns",cdk().listValidator(cdk().validateString))(properties.providerArns)),errors.collect(cdk().propertyValidator("restApiId",cdk().requiredValidator)(properties.restApiId)),errors.collect(cdk().propertyValidator("restApiId",cdk().validateString)(properties.restApiId)),errors.collect(cdk().propertyValidator("type",cdk().requiredValidator)(properties.type)),errors.collect(cdk().propertyValidator("type",cdk().validateString)(properties.type)),errors.wrap('supplied properties not correct for "CfnAuthorizerProps"')}function convertCfnAuthorizerPropsToCloudFormation(properties){return cdk().canInspect(properties)?(CfnAuthorizerPropsValidator(properties).assertSuccess(),{AuthType:cdk().stringToCloudFormation(properties.authType),AuthorizerCredentials:cdk().stringToCloudFormation(properties.authorizerCredentials),AuthorizerResultTtlInSeconds:cdk().numberToCloudFormation(properties.authorizerResultTtlInSeconds),AuthorizerUri:cdk().stringToCloudFormation(properties.authorizerUri),IdentitySource:cdk().stringToCloudFormation(properties.identitySource),IdentityValidationExpression:cdk().stringToCloudFormation(properties.identityValidationExpression),Name:cdk().stringToCloudFormation(properties.name),ProviderARNs:cdk().listMapper(cdk().stringToCloudFormation)(properties.providerArns),RestApiId:cdk().stringToCloudFormation(properties.restApiId),Type:cdk().stringToCloudFormation(properties.type)}):properties}function CfnAuthorizerPropsFromCloudFormation(properties){if(cdk().isResolvableObject(properties))return new(cfn_parse()).FromCloudFormationResult(properties);if(properties=properties??{},!(properties&&typeof properties=="object"&&!Array.isArray(properties)))return new(cfn_parse()).FromCloudFormationResult(properties);const ret=new(cfn_parse()).FromCloudFormationPropertyObject;return ret.addPropertyResult("authorizerCredentials","AuthorizerCredentials",properties.AuthorizerCredentials!=null?cfn_parse().FromCloudFormation.getString(properties.AuthorizerCredentials):void 0),ret.addPropertyResult("authorizerResultTtlInSeconds","AuthorizerResultTtlInSeconds",properties.AuthorizerResultTtlInSeconds!=null?cfn_parse().FromCloudFormation.getNumber(properties.AuthorizerResultTtlInSeconds):void 0),ret.addPropertyResult("authorizerUri","AuthorizerUri",properties.AuthorizerUri!=null?cfn_parse().FromCloudFormation.getString(properties.AuthorizerUri):void 0),ret.addPropertyResult("authType","AuthType",properties.AuthType!=null?cfn_parse().FromCloudFormation.getString(properties.AuthType):void 0),ret.addPropertyResult("identitySource","IdentitySource",properties.IdentitySource!=null?cfn_parse().FromCloudFormation.getString(properties.IdentitySource):void 0),ret.addPropertyResult("identityValidationExpression","IdentityValidationExpression",properties.IdentityValidationExpression!=null?cfn_parse().FromCloudFormation.getString(properties.IdentityValidationExpression):void 0),ret.addPropertyResult("name","Name",properties.Name!=null?cfn_parse().FromCloudFormation.getString(properties.Name):void 0),ret.addPropertyResult("providerArns","ProviderARNs",properties.ProviderARNs!=null?cfn_parse().FromCloudFormation.getArray(cfn_parse().FromCloudFormation.getString)(properties.ProviderARNs):void 0),ret.addPropertyResult("restApiId","RestApiId",properties.RestApiId!=null?cfn_parse().FromCloudFormation.getString(properties.RestApiId):void 0),ret.addPropertyResult("type","Type",properties.Type!=null?cfn_parse().FromCloudFormation.getString(properties.Type):void 0),ret.addUnrecognizedPropertiesAsExtra(properties),ret}class CfnBasePathMapping extends cdk().CfnResource{static _fromCloudFormation(scope,id,resourceAttributes,options){resourceAttributes=resourceAttributes||{};const resourceProperties=options.parser.parseValue(resourceAttributes.Properties),propsResult=CfnBasePathMappingPropsFromCloudFormation(resourceProperties);if(cdk().isResolvableObject(propsResult.value))throw new(cdk_errors()).ValidationError("Unexpected IResolvable",scope);const ret=new CfnBasePathMapping(scope,id,propsResult.value);for(const[propKey,propVal]of Object.entries(propsResult.extraProperties))ret.addPropertyOverride(propKey,propVal);return options.parser.handleAttributes(ret,resourceAttributes,id),ret}constructor(scope,id,props){super(scope,id,{type:CfnBasePathMapping.CFN_RESOURCE_TYPE_NAME,properties:props});try{jsiiDeprecationWarnings().aws_cdk_lib_aws_apigateway_CfnBasePathMappingProps(props)}catch(error){throw process.env.JSII_DEBUG!=="1"&&error.name==="DeprecationError"&&Error.captureStackTrace(error,CfnBasePathMapping),error}cdk().requireProperty(props,"domainName",this),this.basePath=props.basePath,this.domainName=props.domainName,this.id=props.id,this.restApiId=props.restApiId,this.stage=props.stage}get cfnProperties(){return{basePath:this.basePath,domainName:this.domainName,id:this.id,restApiId:this.restApiId,stage:this.stage}}inspect(inspector){try{jsiiDeprecationWarnings().aws_cdk_lib_TreeInspector(inspector)}catch(error){throw process.env.JSII_DEBUG!=="1"&&error.name==="DeprecationError"&&Error.captureStackTrace(error,this.inspect),error}inspector.addAttribute("aws:cdk:cloudformation:type",CfnBasePathMapping.CFN_RESOURCE_TYPE_NAME),inspector.addAttribute("aws:cdk:cloudformation:props",this.cfnProperties)}renderProperties(props){return convertCfnBasePathMappingPropsToCloudFormation(props)}}exports.CfnBasePathMapping=CfnBasePathMapping,_d=JSII_RTTI_SYMBOL_1,CfnBasePathMapping[_d]={fqn:"aws-cdk-lib.aws_apigateway.CfnBasePathMapping",version:"2.201.0"},CfnBasePathMapping.CFN_RESOURCE_TYPE_NAME="AWS::ApiGateway::BasePathMapping";function CfnBasePathMappingPropsValidator(properties){if(!cdk().canInspect(properties))return cdk().VALIDATION_SUCCESS;const errors=new(cdk()).ValidationResults;return properties&&typeof properties=="object"&&!Array.isArray(properties)||errors.collect(new(cdk()).ValidationResult("Expected an object, but received: "+JSON.stringify(properties))),errors.collect(cdk().propertyValidator("basePath",cdk().validateString)(properties.basePath)),errors.collect(cdk().propertyValidator("domainName",cdk().requiredValidator)(properties.domainName)),errors.collect(cdk().propertyValidator("domainName",cdk().validateString)(properties.domainName)),errors.collect(cdk().propertyValidator("id",cdk().validateString)(properties.id)),errors.collect(cdk().propertyValidator("restApiId",cdk().validateString)(properties.restApiId)),errors.collect(cdk().propertyValidator("stage",cdk().validateString)(properties.stage)),errors.wrap('supplied properties not correct for "CfnBasePathMappingProps"')}function convertCfnBasePathMappingPropsToCloudFormation(properties){return cdk().canInspect(properties)?(CfnBasePathMappingPropsValidator(properties).assertSuccess(),{BasePath:cdk().stringToCloudFormation(properties.basePath),DomainName:cdk().stringToCloudFormation(properties.domainName),Id:cdk().stringToCloudFormation(properties.id),RestApiId:cdk().stringToCloudFormation(properties.restApiId),Stage:cdk().stringToCloudFormation(properties.stage)}):properties}function CfnBasePathMappingPropsFromCloudFormation(properties){if(cdk().isResolvableObject(properties))return new(cfn_parse()).FromCloudFormationResult(properties);if(properties=properties??{},!(properties&&typeof properties=="object"&&!Array.isArray(properties)))return new(cfn_parse()).FromCloudFormationResult(properties);const ret=new(cfn_parse()).FromCloudFormationPropertyObject;return ret.addPropertyResult("basePath","BasePath",properties.BasePath!=null?cfn_parse().FromCloudFormation.getString(properties.BasePath):void 0),ret.addPropertyResult("domainName","DomainName",properties.DomainName!=null?cfn_parse().FromCloudFormation.getString(properties.DomainName):void 0),ret.addPropertyResult("id","Id",properties.Id!=null?cfn_parse().FromCloudFormation.getString(properties.Id):void 0),ret.addPropertyResult("restApiId","RestApiId",properties.RestApiId!=null?cfn_parse().FromCloudFormation.getString(properties.RestApiId):void 0),ret.addPropertyResult("stage","Stage",properties.Stage!=null?cfn_parse().FromCloudFormation.getString(properties.Stage):void 0),ret.addUnrecognizedPropertiesAsExtra(properties),ret}class CfnClientCertificate extends cdk().CfnResource{static _fromCloudFormation(scope,id,resourceAttributes,options){resourceAttributes=resourceAttributes||{};const resourceProperties=options.parser.parseValue(resourceAttributes.Properties),propsResult=CfnClientCertificatePropsFromCloudFormation(resourceProperties);if(cdk().isResolvableObject(propsResult.value))throw new(cdk_errors()).ValidationError("Unexpected IResolvable",scope);const ret=new CfnClientCertificate(scope,id,propsResult.value);for(const[propKey,propVal]of Object.entries(propsResult.extraProperties))ret.addPropertyOverride(propKey,propVal);return options.parser.handleAttributes(ret,resourceAttributes,id),ret}constructor(scope,id,props={}){super(scope,id,{type:CfnClientCertificate.CFN_RESOURCE_TYPE_NAME,properties:props});try{jsiiDeprecationWarnings().aws_cdk_lib_aws_apigateway_CfnClientCertificateProps(props)}catch(error){throw process.env.JSII_DEBUG!=="1"&&error.name==="DeprecationError"&&Error.captureStackTrace(error,CfnClientCertificate),error}this.attrClientCertificateId=cdk().Token.asString(this.getAtt("ClientCertificateId",cdk().ResolutionTypeHint.STRING)),this.description=props.description,this.tags=new(cdk()).TagManager(cdk().TagType.STANDARD,"AWS::ApiGateway::ClientCertificate",props.tags,{tagPropertyName:"tags"}),this.tagsRaw=props.tags}get cfnProperties(){return{description:this.description,tags:this.tags.renderTags()}}inspect(inspector){try{jsiiDeprecationWarnings().aws_cdk_lib_TreeInspector(inspector)}catch(error){throw process.env.JSII_DEBUG!=="1"&&error.name==="DeprecationError"&&Error.captureStackTrace(error,this.inspect),error}inspector.addAttribute("aws:cdk:cloudformation:type",CfnClientCertificate.CFN_RESOURCE_TYPE_NAME),inspector.addAttribute("aws:cdk:cloudformation:props",this.cfnProperties)}renderProperties(props){return convertCfnClientCertificatePropsToCloudFormation(props)}}exports.CfnClientCertificate=CfnClientCertificate,_e=JSII_RTTI_SYMBOL_1,CfnClientCertificate[_e]={fqn:"aws-cdk-lib.aws_apigateway.CfnClientCertificate",version:"2.201.0"},CfnClientCertificate.CFN_RESOURCE_TYPE_NAME="AWS::ApiGateway::ClientCertificate";function CfnClientCertificatePropsValidator(properties){if(!cdk().canInspect(properties))return cdk().VALIDATION_SUCCESS;const errors=new(cdk()).ValidationResults;return properties&&typeof properties=="object"&&!Array.isArray(properties)||errors.collect(new(cdk()).ValidationResult("Expected an object, but received: "+JSON.stringify(properties))),errors.collect(cdk().propertyValidator("description",cdk().validateString)(properties.description)),errors.collect(cdk().propertyValidator("tags",cdk().listValidator(cdk().validateCfnTag))(properties.tags)),errors.wrap('supplied properties not correct for "CfnClientCertificateProps"')}function convertCfnClientCertificatePropsToCloudFormation(properties){return cdk().canInspect(properties)?(CfnClientCertificatePropsValidator(properties).assertSuccess(),{Description:cdk().stringToCloudFormation(properties.description),Tags:cdk().listMapper(cdk().cfnTagToCloudFormation)(properties.tags)}):properties}function CfnClientCertificatePropsFromCloudFormation(properties){if(cdk().isResolvableObject(properties))return new(cfn_parse()).FromCloudFormationResult(properties);if(properties=properties??{},!(properties&&typeof properties=="object"&&!Array.isArray(properties)))return new(cfn_parse()).FromCloudFormationResult(properties);const ret=new(cfn_parse()).FromCloudFormationPropertyObject;return ret.addPropertyResult("description","Description",properties.Description!=null?cfn_parse().FromCloudFormation.getString(properties.Description):void 0),ret.addPropertyResult("tags","Tags",properties.Tags!=null?cfn_parse().FromCloudFormation.getArray(cfn_parse().FromCloudFormation.getCfnTag)(properties.Tags):void 0),ret.addUnrecognizedPropertiesAsExtra(properties),ret}class CfnDeployment extends cdk().CfnResource{static _fromCloudFormation(scope,id,resourceAttributes,options){resourceAttributes=resourceAttributes||{};const resourceProperties=options.parser.parseValue(resourceAttributes.Properties),propsResult=CfnDeploymentPropsFromCloudFormation(resourceProperties);if(cdk().isResolvableObject(propsResult.value))throw new(cdk_errors()).ValidationError("Unexpected IResolvable",scope);const ret=new CfnDeployment(scope,id,propsResult.value);for(const[propKey,propVal]of Object.entries(propsResult.extraProperties))ret.addPropertyOverride(propKey,propVal);return options.parser.handleAttributes(ret,resourceAttributes,id),ret}constructor(scope,id,props){super(scope,id,{type:CfnDeployment.CFN_RESOURCE_TYPE_NAME,properties:props});try{jsiiDeprecationWarnings().aws_cdk_lib_aws_apigateway_CfnDeploymentProps(props)}catch(error){throw process.env.JSII_DEBUG!=="1"&&error.name==="DeprecationError"&&Error.captureStackTrace(error,CfnDeployment),error}cdk().requireProperty(props,"restApiId",this),this.attrDeploymentId=cdk().Token.asString(this.getAtt("DeploymentId",cdk().ResolutionTypeHint.STRING)),this.deploymentCanarySettings=props.deploymentCanarySettings,this.description=props.description,this.restApiId=props.restApiId,this.stageDescription=props.stageDescription,this.stageName=props.stageName}get cfnProperties(){return{deploymentCanarySettings:this.deploymentCanarySettings,description:this.description,restApiId:this.restApiId,stageDescription:this.stageDescription,stageName:this.stageName}}inspect(inspector){try{jsiiDeprecationWarnings().aws_cdk_lib_TreeInspector(inspector)}catch(error){throw process.env.JSII_DEBUG!=="1"&&error.name==="DeprecationError"&&Error.captureStackTrace(error,this.inspect),error}inspector.addAttribute("aws:cdk:cloudformation:type",CfnDeployment.CFN_RESOURCE_TYPE_NAME),inspector.addAttribute("aws:cdk:cloudformation:props",this.cfnProperties)}renderProperties(props){return convertCfnDeploymentPropsToCloudFormation(props)}}exports.CfnDeployment=CfnDeployment,_f=JSII_RTTI_SYMBOL_1,CfnDeployment[_f]={fqn:"aws-cdk-lib.aws_apigateway.CfnDeployment",version:"2.201.0"},CfnDeployment.CFN_RESOURCE_TYPE_NAME="AWS::ApiGateway::Deployment";function CfnDeploymentCanarySettingPropertyValidator(properties){if(!cdk().canInspect(properties))return cdk().VALIDATION_SUCCESS;const errors=new(cdk()).ValidationResults;return properties&&typeof properties=="object"&&!Array.isArray(properties)||errors.collect(new(cdk()).ValidationResult("Expected an object, but received: "+JSON.stringify(properties))),errors.collect(cdk().propertyValidator("percentTraffic",cdk().validateNumber)(properties.percentTraffic)),errors.collect(cdk().propertyValidator("stageVariableOverrides",cdk().hashValidator(cdk().validateString))(properties.stageVariableOverrides)),errors.collect(cdk().propertyValidator("useStageCache",cdk().validateBoolean)(properties.useStageCache)),errors.wrap('supplied properties not correct for "CanarySettingProperty"')}function convertCfnDeploymentCanarySettingPropertyToCloudFormation(properties){return cdk().canInspect(properties)?(CfnDeploymentCanarySettingPropertyValidator(properties).assertSuccess(),{PercentTraffic:cdk().numberToCloudFormation(properties.percentTraffic),StageVariableOverrides:cdk().hashMapper(cdk().stringToCloudFormation)(properties.stageVariableOverrides),UseStageCache:cdk().booleanToCloudFormation(properties.useStageCache)}):properties}function CfnDeploymentCanarySettingPropertyFromCloudFormation(properties){if(cdk().isResolvableObject(properties))return new(cfn_parse()).FromCloudFormationResult(properties);if(properties=properties??{},!(properties&&typeof properties=="object"&&!Array.isArray(properties)))return new(cfn_parse()).FromCloudFormationResult(properties);const ret=new(cfn_parse()).FromCloudFormationPropertyObject;return ret.addPropertyResult("percentTraffic","PercentTraffic",properties.PercentTraffic!=null?cfn_parse().FromCloudFormation.getNumber(properties.PercentTraffic):void 0),ret.addPropertyResult("stageVariableOverrides","StageVariableOverrides",properties.StageVariableOverrides!=null?cfn_parse().FromCloudFormation.getMap(cfn_parse().FromCloudFormation.getString)(properties.StageVariableOverrides):void 0),ret.addPropertyResult("useStageCache","UseStageCache",properties.UseStageCache!=null?cfn_parse().FromCloudFormation.getBoolean(properties.UseStageCache):void 0),ret.addUnrecognizedPropertiesAsExtra(properties),ret}function CfnDeploymentMethodSettingPropertyValidator(properties){if(!cdk().canInspect(properties))return cdk().VALIDATION_SUCCESS;const errors=new(cdk()).ValidationResults;return properties&&typeof properties=="object"&&!Array.isArray(properties)||errors.collect(new(cdk()).ValidationResult("Expected an object, but received: "+JSON.stringify(properties))),errors.collect(cdk().propertyValidator("cacheDataEncrypted",cdk().validateBoolean)(properties.cacheDataEncrypted)),errors.collect(cdk().propertyValidator("cacheTtlInSeconds",cdk().validateNumber)(properties.cacheTtlInSeconds)),errors.collect(cdk().propertyValidator("cachingEnabled",cdk().validateBoolean)(properties.cachingEnabled)),errors.collect(cdk().propertyValidator("dataTraceEnabled",cdk().validateBoolean)(properties.dataTraceEnabled)),errors.collect(cdk().propertyValidator("httpMethod",cdk().validateString)(properties.httpMethod)),errors.collect(cdk().propertyValidator("loggingLevel",cdk().validateString)(properties.loggingLevel)),errors.collect(cdk().propertyValidator("metricsEnabled",cdk().validateBoolean)(properties.metricsEnabled)),errors.collect(cdk().propertyValidator("resourcePath",cdk().validateString)(properties.resourcePath)),errors.collect(cdk().propertyValidator("throttlingBurstLimit",cdk().validateNumber)(properties.throttlingBurstLimit)),errors.collect(cdk().propertyValidator("throttlingRateLimit",cdk().validateNumber)(properties.throttlingRateLimit)),errors.wrap('supplied properties not correct for "MethodSettingProperty"')}function convertCfnDeploymentMethodSettingPropertyToCloudFormation(properties){return cdk().canInspect(properties)?(CfnDeploymentMethodSettingPropertyValidator(properties).assertSuccess(),{CacheDataEncrypted:cdk().booleanToCloudFormation(properties.cacheDataEncrypted),CacheTtlInSeconds:cdk().numberToCloudFormation(properties.cacheTtlInSeconds),CachingEnabled:cdk().booleanToCloudFormation(properties.cachingEnabled),DataTraceEnabled:cdk().booleanToCloudFormation(properties.dataTraceEnabled),HttpMethod:cdk().stringToCloudFormation(properties.httpMethod),LoggingLevel:cdk().stringToCloudFormation(properties.loggingLevel),MetricsEnabled:cdk().booleanToCloudFormation(properties.metricsEnabled),ResourcePath:cdk().stringToCloudFormation(properties.resourcePath),ThrottlingBurstLimit:cdk().numberToCloudFormation(properties.throttlingBurstLimit),ThrottlingRateLimit:cdk().numberToCloudFormation(properties.throttlingRateLimit)}):properties}function CfnDeploymentMethodSettingPropertyFromCloudFormation(properties){if(cdk().isResolvableObject(properties))return new(cfn_parse()).FromCloudFormationResult(properties);if(properties=properties??{},!(properties&&typeof properties=="object"&&!Array.isArray(properties)))return new(cfn_parse()).FromCloudFormationResult(properties);const ret=new(cfn_parse()).FromCloudFormationPropertyObject;return ret.addPropertyResult("cacheDataEncrypted","CacheDataEncrypted",properties.CacheDataEncrypted!=null?cfn_parse().FromCloudFormation.getBoolean(properties.CacheDataEncrypted):void 0),ret.addPropertyResult("cacheTtlInSeconds","CacheTtlInSeconds",properties.CacheTtlInSeconds!=null?cfn_parse().FromCloudFormation.getNumber(properties.CacheTtlInSeconds):void 0),ret.addPropertyResult("cachingEnabled","CachingEnabled",properties.CachingEnabled!=null?cfn_parse().FromCloudFormation.getBoolean(properties.CachingEnabled):void 0),ret.addPropertyResult("dataTraceEnabled","DataTraceEnabled",properties.DataTraceEnabled!=null?cfn_parse().FromCloudFormation.getBoolean(properties.DataTraceEnabled):void 0),ret.addPropertyResult("httpMethod","HttpMethod",properties.HttpMethod!=null?cfn_parse().FromCloudFormation.getString(properties.HttpMethod):void 0),ret.addPropertyResult("loggingLevel","LoggingLevel",properties.LoggingLevel!=null?cfn_parse().FromCloudFormation.getString(properties.LoggingLevel):void 0),ret.addPropertyResult("metricsEnabled","MetricsEnabled",properties.MetricsEnabled!=null?cfn_parse().FromCloudFormation.getBoolean(properties.MetricsEnabled):void 0),ret.addPropertyResult("resourcePath","ResourcePath",properties.ResourcePath!=null?cfn_parse().FromCloudFormation.getString(properties.ResourcePath):void 0),ret.addPropertyResult("throttlingBurstLimit","ThrottlingBurstLimit",properties.ThrottlingBurstLimit!=null?cfn_parse().FromCloudFormation.getNumber(properties.ThrottlingBurstLimit):void 0),ret.addPropertyResult("throttlingRateLimit","ThrottlingRateLimit",properties.ThrottlingRateLimit!=null?cfn_parse().FromCloudFormation.getNumber(properties.ThrottlingRateLimit):void 0),ret.addUnrecognizedPropertiesAsExtra(properties),ret}function CfnDeploymentAccessLogSettingPropertyValidator(properties){if(!cdk().canInspect(properties))return cdk().VALIDATION_SUCCESS;const errors=new(cdk()).ValidationResults;return properties&&typeof properties=="object"&&!Array.isArray(properties)||errors.collect(new(cdk()).ValidationResult("Expected an object, but received: "+JSON.stringify(properties))),errors.collect(cdk().propertyValidator("destinationArn",cdk().validateString)(properties.destinationArn)),errors.collect(cdk().propertyValidator("format",cdk().validateString)(properties.format)),errors.wrap('supplied properties not correct for "AccessLogSettingProperty"')}function convertCfnDeploymentAccessLogSettingPropertyToCloudFormation(properties){return cdk().canInspect(properties)?(CfnDeploymentAccessLogSettingPropertyValidator(properties).assertSuccess(),{DestinationArn:cdk().stringToCloudFormation(properties.destinationArn),Format:cdk().stringToCloudFormation(properties.format)}):properties}function CfnDeploymentAccessLogSettingPropertyFromCloudFormation(properties){if(cdk().isResolvableObject(properties))return new(cfn_parse()).FromCloudFormationResult(properties);if(properties=properties??{},!(properties&&typeof properties=="object"&&!Array.isArray(properties)))return new(cfn_parse()).FromCloudFormationResult(properties);const ret=new(cfn_parse()).FromCloudFormationPropertyObject;return ret.addPropertyResult("destinationArn","DestinationArn",properties.DestinationArn!=null?cfn_parse().FromCloudFormation.getString(properties.DestinationArn):void 0),ret.addPropertyResult("format","Format",properties.Format!=null?cfn_parse().FromCloudFormation.getString(properties.Format):void 0),ret.addUnrecognizedPropertiesAsExtra(properties),ret}function CfnDeploymentStageDescriptionPropertyValidator(properties){if(!cdk().canInspect(properties))return cdk().VALIDATION_SUCCESS;const errors=new(cdk()).ValidationResults;return properties&&typeof properties=="object"&&!Array.isArray(properties)||errors.collect(new(cdk()).ValidationResult("Expected an object, but received: "+JSON.stringify(properties))),errors.collect(cdk().propertyValidator("accessLogSetting",CfnDeploymentAccessLogSettingPropertyValidator)(properties.accessLogSetting)),errors.collect(cdk().propertyValidator("cacheClusterEnabled",cdk().validateBoolean)(properties.cacheClusterEnabled)),errors.collect(cdk().propertyValidator("cacheClusterSize",cdk().validateString)(properties.cacheClusterSize)),errors.collect(cdk().propertyValidator("cacheDataEncrypted",cdk().validateBoolean)(properties.cacheDataEncrypted)),errors.collect(cdk().propertyValidator("cacheTtlInSeconds",cdk().validateNumber)(properties.cacheTtlInSeconds)),errors.collect(cdk().propertyValidator("cachingEnabled",cdk().validateBoolean)(properties.cachingEnabled)),errors.collect(cdk().propertyValidator("canarySetting",CfnDeploymentCanarySettingPropertyValidator)(properties.canarySetting)),errors.collect(cdk().propertyValidator("clientCertificateId",cdk().validateString)(properties.clientCertificateId)),errors.collect(cdk().propertyValidator("dataTraceEnabled",cdk().validateBoolean)(properties.dataTraceEnabled)),errors.collect(cdk().propertyValidator("description",cdk().validateString)(properties.description)),errors.collect(cdk().propertyValidator("documentationVersion",cdk().validateString)(properties.documentationVersion)),errors.collect(cdk().propertyValidator("loggingLevel",cdk().validateString)(properties.loggingLevel)),errors.collect(cdk().propertyValidator("methodSettings",cdk().listValidator(CfnDeploymentMethodSettingPropertyValidator))(properties.methodSettings)),errors.collect(cdk().propertyValidator("metricsEnabled",cdk().validateBoolean)(properties.metricsEnabled)),errors.collect(cdk().propertyValidator("tags",cdk().listValidator(cdk().validateCfnTag))(properties.tags)),errors.collect(cdk().propertyValidator("throttlingBurstLimit",cdk().validateNumber)(properties.throttlingBurstLimit)),errors.collect(cdk().propertyValidator("throttlingRateLimit",cdk().validateNumber)(properties.throttlingRateLimit)),errors.collect(cdk().propertyValidator("tracingEnabled",cdk().validateBoolean)(properties.tracingEnabled)),errors.collect(cdk().propertyValidator("variables",cdk().hashValidator(cdk().validateString))(properties.variables)),errors.wrap('supplied properties not correct for "StageDescriptionProperty"')}function convertCfnDeploymentStageDescriptionPropertyToCloudFormation(properties){return cdk().canInspect(properties)?(CfnDeploymentStageDescriptionPropertyValidator(properties).assertSuccess(),{AccessLogSetting:convertCfnDeploymentAccessLogSettingPropertyToCloudFormation(properties.accessLogSetting),CacheClusterEnabled:cdk().booleanToCloudFormation(properties.cacheClusterEnabled),CacheClusterSize:cdk().stringToCloudFormation(properties.cacheClusterSize),CacheDataEncrypted:cdk().booleanToCloudFormation(properties.cacheDataEncrypted),CacheTtlInSeconds:cdk().numberToCloudFormation(properties.cacheTtlInSeconds),CachingEnabled:cdk().booleanToCloudFormation(properties.cachingEnabled),CanarySetting:convertCfnDeploymentCanarySettingPropertyToCloudFormation(properties.canarySetting),ClientCertificateId:cdk().stringToCloudFormation(properties.clientCertificateId),DataTraceEnabled:cdk().booleanToCloudFormation(properties.dataTraceEnabled),Description:cdk().stringToCloudFormation(properties.description),DocumentationVersion:cdk().stringToCloudFormation(properties.documentationVersion),LoggingLevel:cdk().stringToCloudFormation(properties.loggingLevel),MethodSettings:cdk().listMapper(convertCfnDeploymentMethodSettingPropertyToCloudFormation)(properties.methodSettings),MetricsEnabled:cdk().booleanToCloudFormation(properties.metricsEnabled),Tags:cdk().listMapper(cdk().cfnTagToCloudFormation)(properties.tags),ThrottlingBurstLimit:cdk().numberToCloudFormation(properties.throttlingBurstLimit),ThrottlingRateLimit:cdk().numberToCloudFormation(properties.throttlingRateLimit),TracingEnabled:cdk().booleanToCloudFormation(properties.tracingEnabled),Variables:cdk().hashMapper(cdk().stringToCloudFormation)(properties.variables)}):properties}function CfnDeploymentStageDescriptionPropertyFromCloudFormation(properties){if(cdk().isResolvableObject(properties))return new(cfn_parse()).FromCloudFormationResult(properties);if(properties=properties??{},!(properties&&typeof properties=="object"&&!Array.isArray(properties)))return new(cfn_parse()).FromCloudFormationResult(properties);const ret=new(cfn_parse()).FromCloudFormationPropertyObject;return ret.addPropertyResult("accessLogSetting","AccessLogSetting",properties.AccessLogSetting!=null?CfnDeploymentAccessLogSettingPropertyFromCloudFormation(properties.AccessLogSetting):void 0),ret.addPropertyResult("cacheClusterEnabled","CacheClusterEnabled",properties.CacheClusterEnabled!=null?cfn_parse().FromCloudFormation.getBoolean(properties.CacheClusterEnabled):void 0),ret.addPropertyResult("cacheClusterSize","CacheClusterSize",properties.CacheClusterSize!=null?cfn_parse().FromCloudFormation.getString(properties.CacheClusterSize):void 0),ret.addPropertyResult("cacheDataEncrypted","CacheDataEncrypted",properties.CacheDataEncrypted!=null?cfn_parse().FromCloudFormation.getBoolean(properties.CacheDataEncrypted):void 0),ret.addPropertyResult("cacheTtlInSeconds","CacheTtlInSeconds",properties.CacheTtlInSeconds!=null?cfn_parse().FromCloudFormation.getNumber(properties.CacheTtlInSeconds):void 0),ret.addPropertyResult("cachingEnabled","CachingEnabled",properties.CachingEnabled!=null?cfn_parse().FromCloudFormation.getBoolean(properties.CachingEnabled):void 0),ret.addPropertyResult("canarySetting","CanarySetting",properties.CanarySetting!=null?CfnDeploymentCanarySettingPropertyFromCloudFormation(properties.CanarySetting):void 0),ret.addPropertyResult("clientCertificateId","ClientCertificateId",properties.ClientCertificateId!=null?cfn_parse().FromCloudFormation.getString(properties.ClientCertificateId):void 0),ret.addPropertyResult("dataTraceEnabled","DataTraceEnabled",properties.DataTraceEnabled!=null?cfn_parse().FromCloudFormation.getBoolean(properties.DataTraceEnabled):void 0),ret.addPropertyResult("description","Description",properties.Description!=null?cfn_parse().FromCloudFormation.getString(properties.Description):void 0),ret.addPropertyResult("documentationVersion","DocumentationVersion",properties.DocumentationVersion!=null?cfn_parse().FromCloudFormation.getString(properties.DocumentationVersion):void 0),ret.addPropertyResult("loggingLevel","LoggingLevel",properties.LoggingLevel!=null?cfn_parse().FromCloudFormation.getString(properties.LoggingLevel):void 0),ret.addPropertyResult("methodSettings","MethodSettings",properties.MethodSettings!=null?cfn_parse().FromCloudFormation.getArray(CfnDeploymentMethodSettingPropertyFromCloudFormation)(properties.MethodSettings):void 0),ret.addPropertyResult("metricsEnabled","MetricsEnabled",properties.MetricsEnabled!=null?cfn_parse().FromCloudFormation.getBoolean(properties.MetricsEnabled):void 0),ret.addPropertyResult("tags","Tags",properties.Tags!=null?cfn_parse().FromCloudFormation.getArray(cfn_parse().FromCloudFormation.getCfnTag)(properties.Tags):void 0),ret.addPropertyResult("throttlingBurstLimit","ThrottlingBurstLimit",properties.ThrottlingBurstLimit!=null?cfn_parse().FromCloudFormation.getNumber(properties.ThrottlingBurstLimit):void 0),ret.addPropertyResult("throttlingRateLimit","ThrottlingRateLimit",properties.ThrottlingRateLimit!=null?cfn_parse().FromCloudFormation.getNumber(properties.ThrottlingRateLimit):void 0),ret.addPropertyResult("tracingEnabled","TracingEnabled",properties.TracingEnabled!=null?cfn_parse().FromCloudFormation.getBoolean(properties.TracingEnabled):void 0),ret.addPropertyResult("variables","Variables",properties.Variables!=null?cfn_parse().FromCloudFormation.getMap(cfn_parse().FromCloudFormation.getString)(properties.Variables):void 0),ret.addUnrecognizedPropertiesAsExtra(properties),ret}function CfnDeploymentDeploymentCanarySettingsPropertyValidator(properties){if(!cdk().canInspect(properties))return cdk().VALIDATION_SUCCESS;const errors=new(cdk()).ValidationResults;return properties&&typeof properties=="object"&&!Array.isArray(properties)||errors.collect(new(cdk()).ValidationResult("Expected an object, but received: "+JSON.stringify(properties))),errors.collect(cdk().propertyValidator("percentTraffic",cdk().validateNumber)(properties.percentTraffic)),errors.collect(cdk().propertyValidator("stageVariableOverrides",cdk().hashValidator(cdk().validateString))(properties.stageVariableOverrides)),errors.collect(cdk().propertyValidator("useStageCache",cdk().validateBoolean)(properties.useStageCache)),errors.wrap('supplied properties not correct for "DeploymentCanarySettingsProperty"')}function convertCfnDeploymentDeploymentCanarySettingsPropertyToCloudFormation(properties){return cdk().canInspect(properties)?(CfnDeploymentDeploymentCanarySettingsPropertyValidator(properties).assertSuccess(),{PercentTraffic:cdk().numberToCloudFormation(properties.percentTraffic),StageVariableOverrides:cdk().hashMapper(cdk().stringToCloudFormation)(properties.stageVariableOverrides),UseStageCache:cdk().booleanToCloudFormation(properties.useStageCache)}):properties}function CfnDeploymentDeploymentCanarySettingsPropertyFromCloudFormation(properties){if(cdk().isResolvableObject(properties))return new(cfn_parse()).FromCloudFormationResult(properties);if(properties=properties??{},!(properties&&typeof properties=="object"&&!Array.isArray(properties)))return new(cfn_parse()).FromCloudFormationResult(properties);const ret=new(cfn_parse()).FromCloudFormationPropertyObject;return ret.addPropertyResult("percentTraffic","PercentTraffic",properties.PercentTraffic!=null?cfn_parse().FromCloudFormation.getNumber(properties.PercentTraffic):void 0),ret.addPropertyResult("stageVariableOverrides","StageVariableOverrides",properties.StageVariableOverrides!=null?cfn_parse().FromCloudFormation.getMap(cfn_parse().FromCloudFormation.getString)(properties.StageVariableOverrides):void 0),ret.addPropertyResult("useStageCache","UseStageCache",properties.UseStageCache!=null?cfn_parse().FromCloudFormation.getBoolean(properties.UseStageCache):void 0),ret.addUnrecognizedPropertiesAsExtra(properties),ret}function CfnDeploymentPropsValidator(properties){if(!cdk().canInspect(properties))return cdk().VALIDATION_SUCCESS;const errors=new(cdk()).ValidationResults;return properties&&typeof properties=="object"&&!Array.isArray(properties)||errors.collect(new(cdk()).ValidationResult("Expected an object, but received: "+JSON.stringify(properties))),errors.collect(cdk().propertyValidator("deploymentCanarySettings",CfnDeploymentDeploymentCanarySettingsPropertyValidator)(properties.deploymentCanarySettings)),errors.collect(cdk().propertyValidator("description",cdk().validateString)(properties.description)),errors.collect(cdk().propertyValidator("restApiId",cdk().requiredValidator)(properties.restApiId)),errors.collect(cdk().propertyValidator("restApiId",cdk().validateString)(properties.restApiId)),errors.collect(cdk().propertyValidator("stageDescription",CfnDeploymentStageDescriptionPropertyValidator)(properties.stageDescription)),errors.collect(cdk().propertyValidator("stageName",cdk().validateString)(properties.stageName)),errors.wrap('supplied properties not correct for "CfnDeploymentProps"')}function convertCfnDeploymentPropsToCloudFormation(properties){return cdk().canInspect(properties)?(CfnDeploymentPropsValidator(properties).assertSuccess(),{DeploymentCanarySettings:convertCfnDeploymentDeploymentCanarySettingsPropertyToCloudFormation(properties.deploymentCanarySettings),Description:cdk().stringToCloudFormation(properties.description),RestApiId:cdk().stringToCloudFormation(properties.restApiId),StageDescription:convertCfnDeploymentStageDescriptionPropertyToCloudFormation(properties.stageDescription),StageName:cdk().stringToCloudFormation(properties.stageName)}):properties}function CfnDeploymentPropsFromCloudFormation(properties){if(cdk().isResolvableObject(properties))return new(cfn_parse()).FromCloudFormationResult(properties);if(properties=properties??{},!(properties&&typeof properties=="object"&&!Array.isArray(properties)))return new(cfn_parse()).FromCloudFormationResult(properties);const ret=new(cfn_parse()).FromCloudFormationPropertyObject;return ret.addPropertyResult("deploymentCanarySettings","DeploymentCanarySettings",properties.DeploymentCanarySettings!=null?CfnDeploymentDeploymentCanarySettingsPropertyFromCloudFormation(properties.DeploymentCanarySettings):void 0),ret.addPropertyResult("description","Description",properties.Description!=null?cfn_parse().FromCloudFormation.getString(properties.Description):void 0),ret.addPropertyResult("restApiId","RestApiId",properties.RestApiId!=null?cfn_parse().FromCloudFormation.getString(properties.RestApiId):void 0),ret.addPropertyResult("stageDescription","StageDescription",properties.StageDescription!=null?CfnDeploymentStageDescriptionPropertyFromCloudFormation(properties.StageDescription):void 0),ret.addPropertyResult("stageName","StageName",properties.StageName!=null?cfn_parse().FromCloudFormation.getString(properties.StageName):void 0),ret.addUnrecognizedPropertiesAsExtra(properties),ret}class CfnDocumentationPart extends cdk().CfnResource{static _fromCloudFormation(scope,id,resourceAttributes,options){resourceAttributes=resourceAttributes||{};const resourceProperties=options.parser.parseValue(resourceAttributes.Properties),propsResult=CfnDocumentationPartPropsFromCloudFormation(resourceProperties);if(cdk().isResolvableObject(propsResult.value))throw new(cdk_errors()).ValidationError("Unexpected IResolvable",scope);const ret=new CfnDocumentationPart(scope,id,propsResult.value);for(const[propKey,propVal]of Object.entries(propsResult.extraProperties))ret.addPropertyOverride(propKey,propVal);return options.parser.handleAttributes(ret,resourceAttributes,id),ret}constructor(scope,id,props){super(scope,id,{type:CfnDocumentationPart.CFN_RESOURCE_TYPE_NAME,properties:props});try{jsiiDeprecationWarnings().aws_cdk_lib_aws_apigateway_CfnDocumentationPartProps(props)}catch(error){throw process.env.JSII_DEBUG!=="1"&&error.name==="DeprecationError"&&Error.captureStackTrace(error,CfnDocumentationPart),error}cdk().requireProperty(props,"location",this),cdk().requireProperty(props,"properties",this),cdk().requireProperty(props,"restApiId",this),this.attrDocumentationPartId=cdk().Token.asString(this.getAtt("DocumentationPartId",cdk().ResolutionTypeHint.STRING)),this.location=props.location,this.properties=props.properties,this.restApiId=props.restApiId}get cfnProperties(){return{location:this.location,properties:this.properties,restApiId:this.restApiId}}inspect(inspector){try{jsiiDeprecationWarnings().aws_cdk_lib_TreeInspector(inspector)}catch(error){throw process.env.JSII_DEBUG!=="1"&&error.name==="DeprecationError"&&Error.captureStackTrace(error,this.inspect),error}inspector.addAttribute("aws:cdk:cloudformation:type",CfnDocumentationPart.CFN_RESOURCE_TYPE_NAME),inspector.addAttribute("aws:cdk:cloudformation:props",this.cfnProperties)}renderProperties(props){return convertCfnDocumentationPartPropsToCloudFormation(props)}}exports.CfnDocumentationPart=CfnDocumentationPart,_g=JSII_RTTI_SYMBOL_1,CfnDocumentationPart[_g]={fqn:"aws-cdk-lib.aws_apigateway.CfnDocumentationPart",version:"2.201.0"},CfnDocumentationPart.CFN_RESOURCE_TYPE_NAME="AWS::ApiGateway::DocumentationPart";function CfnDocumentationPartLocationPropertyValidator(properties){if(!cdk().canInspect(properties))return cdk().VALIDATION_SUCCESS;const errors=new(cdk()).ValidationResults;return properties&&typeof properties=="object"&&!Array.isArray(properties)||errors.collect(new(cdk()).ValidationResult("Expected an object, but received: "+JSON.stringify(properties))),errors.collect(cdk().propertyValidator("method",cdk().validateString)(properties.method)),errors.collect(cdk().propertyValidator("name",cdk().validateString)(properties.name)),errors.collect(cdk().propertyValidator("path",cdk().validateString)(properties.path)),errors.collect(cdk().propertyValidator("statusCode",cdk().validateString)(properties.statusCode)),errors.collect(cdk().propertyValidator("type",cdk().validateString)(properties.type)),errors.wrap('supplied properties not correct for "LocationProperty"')}function convertCfnDocumentationPartLocationPropertyToCloudFormation(properties){return cdk().canInspect(properties)?(CfnDocumentationPartLocationPropertyValidator(properties).assertSuccess(),{Method:cdk().stringToCloudFormation(properties.method),Name:cdk().stringToCloudFormation(properties.name),Path:cdk().stringToCloudFormation(properties.path),StatusCode:cdk().stringToCloudFormation(properties.statusCode),Type:cdk().stringToCloudFormation(properties.type)}):properties}function CfnDocumentationPartLocationPropertyFromCloudFormation(properties){if(cdk().isResolvableObject(properties))return new(cfn_parse()).FromCloudFormationResult(properties);if(properties=properties??{},!(properties&&typeof properties=="object"&&!Array.isArray(properties)))return new(cfn_parse()).FromCloudFormationResult(properties);const ret=new(cfn_parse()).FromCloudFormationPropertyObject;return ret.addPropertyResult("method","Method",properties.Method!=null?cfn_parse().FromCloudFormation.getString(properties.Method):void 0),ret.addPropertyResult("name","Name",properties.Name!=null?cfn_parse().FromCloudFormation.getString(properties.Name):void 0),ret.addPropertyResult("path","Path",properties.Path!=null?cfn_parse().FromCloudFormation.getString(properties.Path):void 0),ret.addPropertyResult("statusCode","StatusCode",properties.StatusCode!=null?cfn_parse().FromCloudFormation.getString(properties.StatusCode):void 0),ret.addPropertyResult("type","Type",properties.Type!=null?cfn_parse().FromCloudFormation.getString(properties.Type):void 0),ret.addUnrecognizedPropertiesAsExtra(properties),ret}function CfnDocumentationPartPropsValidator(properties){if(!cdk().canInspect(properties))return cdk().VALIDATION_SUCCESS;const errors=new(cdk()).ValidationResults;return properties&&typeof properties=="object"&&!Array.isArray(properties)||errors.collect(new(cdk()).ValidationResult("Expected an object, but received: "+JSON.stringify(properties))),errors.collect(cdk().propertyValidator("location",cdk().requiredValidator)(properties.location)),errors.collect(cdk().propertyValidator("location",CfnDocumentationPartLocationPropertyValidator)(properties.location)),errors.collect(cdk().propertyValidator("properties",cdk().requiredValidator)(properties.properties)),errors.collect(cdk().propertyValidator("properties",cdk().validateString)(properties.properties)),errors.collect(cdk().propertyValidator("restApiId",cdk().requiredValidator)(properties.restApiId)),errors.collect(cdk().propertyValidator("restApiId",cdk().validateString)(properties.restApiId)),errors.wrap('supplied properties not correct for "CfnDocumentationPartProps"')}function convertCfnDocumentationPartPropsToCloudFormation(properties){return cdk().canInspect(properties)?(CfnDocumentationPartPropsValidator(properties).assertSuccess(),{Location:convertCfnDocumentationPartLocationPropertyToCloudFormation(properties.location),Properties:cdk().stringToCloudFormation(properties.properties),RestApiId:cdk().stringToCloudFormation(properties.restApiId)}):properties}function CfnDocumentationPartPropsFromCloudFormation(properties){if(cdk().isResolvableObject(properties))return new(cfn_parse()).FromCloudFormationResult(properties);if(properties=properties??{},!(properties&&typeof properties=="object"&&!Array.isArray(properties)))return new(cfn_parse()).FromCloudFormationResult(properties);const ret=new(cfn_parse()).FromCloudFormationPropertyObject;return ret.addPropertyResult("location","Location",properties.Location!=null?CfnDocumentationPartLocationPropertyFromCloudFormation(properties.Location):void 0),ret.addPropertyResult("properties","Properties",properties.Properties!=null?cfn_parse().FromCloudFormation.getString(properties.Properties):void 0),ret.addPropertyResult("restApiId","RestApiId",properties.RestApiId!=null?cfn_parse().FromCloudFormation.getString(properties.RestApiId):void 0),ret.addUnrecognizedPropertiesAsExtra(properties),ret}class CfnDocumentationVersion extends cdk().CfnResource{static _fromCloudFormation(scope,id,resourceAttributes,options){resourceAttributes=resourceAttributes||{};const resourceProperties=options.parser.parseValue(resourceAttributes.Properties),propsResult=CfnDocumentationVersionPropsFromCloudFormation(resourceProperties);if(cdk().isResolvableObject(propsResult.value))throw new(cdk_errors()).ValidationError("Unexpected IResolvable",scope);const ret=new CfnDocumentationVersion(scope,id,propsResult.value);for(const[propKey,propVal]of Object.entries(propsResult.extraProperties))ret.addPropertyOverride(propKey,propVal);return options.parser.handleAttributes(ret,resourceAttributes,id),ret}constructor(scope,id,props){super(scope,id,{type:CfnDocumentationVersion.CFN_RESOURCE_TYPE_NAME,properties:props});try{jsiiDeprecationWarnings().aws_cdk_lib_aws_apigateway_CfnDocumentationVersionProps(props)}catch(error){throw process.env.JSII_DEBUG!=="1"&&error.name==="DeprecationError"&&Error.captureStackTrace(error,CfnDocumentationVersion),error}cdk().requireProperty(props,"documentationVersion",this),cdk().requireProperty(props,"restApiId",this),this.description=props.description,this.documentationVersion=props.documentationVersion,this.restApiId=props.restApiId}get cfnProperties(){return{description:this.description,documentationVersion:this.documentationVersion,restApiId:this.restApiId}}inspect(inspector){try{jsiiDeprecationWarnings().aws_cdk_lib_TreeInspector(inspector)}catch(error){throw process.env.JSII_DEBUG!=="1"&&error.name==="DeprecationError"&&Error.captureStackTrace(error,this.inspect),error}inspector.addAttribute("aws:cdk:cloudformation:type",CfnDocumentationVersion.CFN_RESOURCE_TYPE_NAME),inspector.addAttribute("aws:cdk:cloudformation:props",this.cfnProperties)}renderProperties(props){return convertCfnDocumentationVersionPropsToCloudFormation(props)}}exports.CfnDocumentationVersion=CfnDocumentationVersion,_h=JSII_RTTI_SYMBOL_1,CfnDocumentationVersion[_h]={fqn:"aws-cdk-lib.aws_apigateway.CfnDocumentationVersion",version:"2.201.0"},CfnDocumentationVersion.CFN_RESOURCE_TYPE_NAME="AWS::ApiGateway::DocumentationVersion";function CfnDocumentationVersionPropsValidator(properties){if(!cdk().canInspect(properties))return cdk().VALIDATION_SUCCESS;const errors=new(cdk()).ValidationResults;return properties&&typeof properties=="object"&&!Array.isArray(properties)||errors.collect(new(cdk()).ValidationResult("Expected an object, but received: "+JSON.stringify(properties))),errors.collect(cdk().propertyValidator("description",cdk().validateString)(properties.description)),errors.collect(cdk().propertyValidator("documentationVersion",cdk().requiredValidator)(properties.documentationVersion)),errors.collect(cdk().propertyValidator("documentationVersion",cdk().validateString)(properties.documentationVersion)),errors.collect(cdk().propertyValidator("restApiId",cdk().requiredValidator)(properties.restApiId)),errors.collect(cdk().propertyValidator("restApiId",cdk().validateString)(properties.restApiId)),errors.wrap('supplied properties not correct for "CfnDocumentationVersionProps"')}function convertCfnDocumentationVersionPropsToCloudFormation(properties){return cdk().canInspect(properties)?(CfnDocumentationVersionPropsValidator(properties).assertSuccess(),{Description:cdk().stringToCloudFormation(properties.description),DocumentationVersion:cdk().stringToCloudFormation(properties.documentationVersion),RestApiId:cdk().stringToCloudFormation(properties.restApiId)}):properties}function CfnDocumentationVersionPropsFromCloudFormation(properties){if(cdk().isResolvableObject(properties))return new(cfn_parse()).FromCloudFormationResult(properties);if(properties=properties??{},!(properties&&typeof properties=="object"&&!Array.isArray(properties)))return new(cfn_parse()).FromCloudFormationResult(properties);const ret=new(cfn_parse()).FromCloudFormationPropertyObject;return ret.addPropertyResult("description","Description",properties.Description!=null?cfn_parse().FromCloudFormation.getString(properties.Description):void 0),ret.addPropertyResult("documentationVersion","DocumentationVersion",properties.DocumentationVersion!=null?cfn_parse().FromCloudFormation.getString(properties.DocumentationVersion):void 0),ret.addPropertyResult("restApiId","RestApiId",properties.RestApiId!=null?cfn_parse().FromCloudFormation.getString(properties.RestApiId):void 0),ret.addUnrecognizedPropertiesAsExtra(properties),ret}class CfnDomainName extends cdk().CfnResource{static _fromCloudFormation(scope,id,resourceAttributes,options){resourceAttributes=resourceAttributes||{};const resourceProperties=options.parser.parseValue(resourceAttributes.Properties),propsResult=CfnDomainNamePropsFromCloudFormation(resourceProperties);if(cdk().isResolvableObject(propsResult.value))throw new(cdk_errors()).ValidationError("Unexpected IResolvable",scope);const ret=new CfnDomainName(scope,id,propsResult.value);for(const[propKey,propVal]of Object.entries(propsResult.extraProperties))ret.addPropertyOverride(propKey,propVal);return options.parser.handleAttributes(ret,resourceAttributes,id),ret}constructor(scope,id,props={}){super(scope,id,{type:CfnDomainName.CFN_RESOURCE_TYPE_NAME,properties:props});try{jsiiDeprecationWarnings().aws_cdk_lib_aws_apigateway_CfnDomainNameProps(props)}catch(error){throw process.env.JSII_DEBUG!=="1"&&error.name==="DeprecationError"&&Error.captureStackTrace(error,CfnDomainName),error}this.attrDistributionDomainName=cdk().Token.asString(this.getAtt("DistributionDomainName",cdk().ResolutionTypeHint.STRING)),this.attrDistributionHostedZoneId=cdk().Token.asString(this.getAtt("DistributionHostedZoneId",cdk().ResolutionTypeHint.STRING)),this.attrDomainNameArn=cdk().Token.asString(this.getAtt("DomainNameArn",cdk().ResolutionTypeHint.STRING)),this.attrRegionalDomainName=cdk().Token.asString(this.getAtt("RegionalDomainName",cdk().ResolutionTypeHint.STRING)),this.attrRegionalHostedZoneId=cdk().Token.asString(this.getAtt("RegionalHostedZoneId",cdk().ResolutionTypeHint.STRING)),this.certificateArn=props.certificateArn,this.domainName=props.domainName,this.endpointConfiguration=props.endpointConfiguration,this.mutualTlsAuthentication=props.mutualTlsAuthentication,this.ownershipVerificationCertificateArn=props.ownershipVerificationCertificateArn,this.regionalCertificateArn=props.regionalCertificateArn,this.routingMode=props.routingMode,this.securityPolicy=props.securityPolicy,this.tags=new(cdk()).TagManager(cdk().TagType.STANDARD,"AWS::ApiGateway::DomainName",props.tags,{tagPropertyName:"tags"}),this.tagsRaw=props.tags}get cfnProperties(){return{certificateArn:this.certificateArn,domainName:this.domainName,endpointConfiguration:this.endpointConfiguration,mutualTlsAuthentication:this.mutualTlsAuthentication,ownershipVerificationCertificateArn:this.ownershipVerificationCertificateArn,regionalCertificateArn:this.regionalCertificateArn,routingMode:this.routingMode,securityPolicy:this.securityPolicy,tags:this.tags.renderTags()}}inspect(inspector){try{jsiiDeprecationWarnings().aws_cdk_lib_TreeInspector(inspector)}catch(error){throw process.env.JSII_DEBUG!=="1"&&error.name==="DeprecationError"&&Error.captureStackTrace(error,this.inspect),error}inspector.addAttribute("aws:cdk:cloudformation:type",CfnDomainName.CFN_RESOURCE_TYPE_NAME),inspector.addAttribute("aws:cdk:cloudformation:props",this.cfnProperties)}renderProperties(props){return convertCfnDomainNamePropsToCloudFormation(props)}}exports.CfnDomainName=CfnDomainName,_j=JSII_RTTI_SYMBOL_1,CfnDomainName[_j]={fqn:"aws-cdk-lib.aws_apigateway.CfnDomainName",version:"2.201.0"},CfnDomainName.CFN_RESOURCE_TYPE_NAME="AWS::ApiGateway::DomainName";function CfnDomainNameMutualTlsAuthenticationPropertyValidator(properties){if(!cdk().canInspect(properties))return cdk().VALIDATION_SUCCESS;const errors=new(cdk()).ValidationResults;return properties&&typeof properties=="object"&&!Array.isArray(properties)||errors.collect(new(cdk()).ValidationResult("Expected an object, but received: "+JSON.stringify(properties))),errors.collect(cdk().propertyValidator("truststoreUri",cdk().validateString)(properties.truststoreUri)),errors.collect(cdk().propertyValidator("truststoreVersion",cdk().validateString)(properties.truststoreVersion)),errors.wrap('supplied properties not correct for "MutualTlsAuthenticationProperty"')}function convertCfnDomainNameMutualTlsAuthenticationPropertyToCloudFormation(properties){return cdk().canInspect(properties)?(CfnDomainNameMutualTlsAuthenticationPropertyValidator(properties).assertSuccess(),{TruststoreUri:cdk().stringToCloudFormation(properties.truststoreUri),TruststoreVersion:cdk().stringToCloudFormation(properties.truststoreVersion)}):properties}function CfnDomainNameMutualTlsAuthenticationPropertyFromCloudFormation(properties){if(cdk().isResolvableObject(properties))return new(cfn_parse()).FromCloudFormationResult(properties);if(properties=properties??{},!(properties&&typeof properties=="object"&&!Array.isArray(properties)))return new(cfn_parse()).FromCloudFormationResult(properties);const ret=new(cfn_parse()).FromCloudFormationPropertyObject;return ret.addPropertyResult("truststoreUri","TruststoreUri",properties.TruststoreUri!=null?cfn_parse().FromCloudFormation.getString(properties.TruststoreUri):void 0),ret.addPropertyResult("truststoreVersion","TruststoreVersion",properties.TruststoreVersion!=null?cfn_parse().FromCloudFormation.getString(properties.TruststoreVersion):void 0),ret.addUnrecognizedPropertiesAsExtra(properties),ret}function CfnDomainNameEndpointConfigurationPropertyValidator(properties){if(!cdk().canInspect(properties))return cdk().VALIDATION_SUCCESS;const errors=new(cdk()).ValidationResults;return properties&&typeof properties=="object"&&!Array.isArray(properties)||errors.collect(new(cdk()).ValidationResult("Expected an object, but received: "+JSON.stringify(properties))),errors.collect(cdk().propertyValidator("ipAddressType",cdk().validateString)(properties.ipAddressType)),errors.collect(cdk().propertyValidator("types",cdk().listValidator(cdk().validateString))(properties.types)),errors.wrap('supplied properties not correct for "EndpointConfigurationProperty"')}function convertCfnDomainNameEndpointConfigurationPropertyToCloudFormation(properties){return cdk().canInspect(properties)?(CfnDomainNameEndpointConfigurationPropertyValidator(properties).assertSuccess(),{IpAddressType:cdk().stringToCloudFormation(properties.ipAddressType),Types:cdk().listMapper(cdk().stringToCloudFormation)(properties.types)}):properties}function CfnDomainNameEndpointConfigurationPropertyFromCloudFormation(properties){if(cdk().isResolvableObject(properties))return new(cfn_parse()).FromCloudFormationResult(properties);if(properties=properties??{},!(properties&&typeof properties=="object"&&!Array.isArray(properties)))return new(cfn_parse()).FromCloudFormationResult(properties);const ret=new(cfn_parse()).FromCloudFormationPropertyObject;return ret.addPropertyResult("ipAddressType","IpAddressType",properties.IpAddressType!=null?cfn_parse().FromCloudFormation.getString(properties.IpAddressType):void 0),ret.addPropertyResult("types","Types",properties.Types!=null?cfn_parse().FromCloudFormation.getArray(cfn_parse().FromCloudFormation.getString)(properties.Types):void 0),ret.addUnrecognizedPropertiesAsExtra(properties),ret}function CfnDomainNamePropsValidator(properties){if(!cdk().canInspect(properties))return cdk().VALIDATION_SUCCESS;const errors=new(cdk()).ValidationResults;return properties&&typeof properties=="object"&&!Array.isArray(properties)||errors.collect(new(cdk()).ValidationResult("Expected an object, but received: "+JSON.stringify(properties))),errors.collect(cdk().propertyValidator("certificateArn",cdk().validateString)(properties.certificateArn)),errors.collect(cdk().propertyValidator("domainName",cdk().validateString)(properties.domainName)),errors.collect(cdk().propertyValidator("endpointConfiguration",CfnDomainNameEndpointConfigurationPropertyValidator)(properties.endpointConfiguration)),errors.collect(cdk().propertyValidator("mutualTlsAuthentication",CfnDomainNameMutualTlsAuthenticationPropertyValidator)(properties.mutualTlsAuthentication)),errors.collect(cdk().propertyValidator("ownershipVerificationCertificateArn",cdk().validateString)(properties.ownershipVerificationCertificateArn)),errors.collect(cdk().propertyValidator("regionalCertificateArn",cdk().validateString)(properties.regionalCertificateArn)),errors.collect(cdk().propertyValidator("routingMode",cdk().validateString)(properties.routingMode)),errors.collect(cdk().propertyValidator("securityPolicy",cdk().validateString)(properties.securityPolicy)),errors.collect(cdk().propertyValidator("tags",cdk().listValidator(cdk().validateCfnTag))(properties.tags)),errors.wrap('supplied properties not correct for "CfnDomainNameProps"')}function convertCfnDomainNamePropsToCloudFormation(properties){return cdk().canInspect(properties)?(CfnDomainNamePropsValidator(properties).assertSuccess(),{CertificateArn:cdk().stringToCloudFormation(properties.certificateArn),DomainName:cdk().stringToCloudFormation(properties.domainName),EndpointConfiguration:convertCfnDomainNameEndpointConfigurationPropertyToCloudFormation(properties.endpointConfiguration),MutualTlsAuthentication:convertCfnDomainNameMutualTlsAuthenticationPropertyToCloudFormation(properties.mutualTlsAuthentication),OwnershipVerificationCertificateArn:cdk().stringToCloudFormation(properties.ownershipVerificationCertificateArn),RegionalCertificateArn:cdk().stringToCloudFormation(properties.regionalCertificateArn),RoutingMode:cdk().stringToCloudFormation(properties.routingMode),SecurityPolicy:cdk().stringToCloudFormation(properties.securityPolicy),Tags:cdk().listMapper(cdk().cfnTagToCloudFormation)(properties.tags)}):properties}function CfnDomainNamePropsFromCloudFormation(properties){if(cdk().isResolvableObject(properties))return new(cfn_parse()).FromCloudFormationResult(properties);if(properties=properties??{},!(properties&&typeof properties=="object"&&!Array.isArray(properties)))return new(cfn_parse()).FromCloudFormationResult(properties);const ret=new(cfn_parse()).FromCloudFormationPropertyObject;return ret.addPropertyResult("certificateArn","CertificateArn",properties.CertificateArn!=null?cfn_parse().FromCloudFormation.getString(properties.CertificateArn):void 0),ret.addPropertyResult("domainName","DomainName",properties.DomainName!=null?cfn_parse().FromCloudFormation.getString(properties.DomainName):void 0),ret.addPropertyResult("endpointConfiguration","EndpointConfiguration",properties.EndpointConfiguration!=null?CfnDomainNameEndpointConfigurationPropertyFromCloudFormation(properties.EndpointConfiguration):void 0),ret.addPropertyResult("mutualTlsAuthentication","MutualTlsAuthentication",properties.MutualTlsAuthentication!=null?CfnDomainNameMutualTlsAuthenticationPropertyFromCloudFormation(properties.MutualTlsAuthentication):void 0),ret.addPropertyResult("ownershipVerificationCertificateArn","OwnershipVerificationCertificateArn",properties.OwnershipVerificationCertificateArn!=null?cfn_parse().FromCloudFormation.getString(properties.OwnershipVerificationCertificateArn):void 0),ret.addPropertyResult("regionalCertificateArn","RegionalCertificateArn",properties.RegionalCertificateArn!=null?cfn_parse().FromCloudFormation.getString(properties.RegionalCertificateArn):void 0),ret.addPropertyResult("routingMode","RoutingMode",properties.RoutingMode!=null?cfn_parse().FromCloudFormation.getString(properties.RoutingMode):void 0),ret.addPropertyResult("securityPolicy","SecurityPolicy",properties.SecurityPolicy!=null?cfn_parse().FromCloudFormation.getString(properties.SecurityPolicy):void 0),ret.addPropertyResult("tags","Tags",properties.Tags!=null?cfn_parse().FromCloudFormation.getArray(cfn_parse().FromCloudFormation.getCfnTag)(properties.Tags):void 0),ret.addUnrecognizedPropertiesAsExtra(properties),ret}class CfnGatewayResponse extends cdk().CfnResource{static _fromCloudFormation(scope,id,resourceAttributes,options){resourceAttributes=resourceAttributes||{};const resourceProperties=options.parser.parseValue(resourceAttributes.Properties),propsResult=CfnGatewayResponsePropsFromCloudFormation(resourceProperties);if(cdk().isResolvableObject(propsResult.value))throw new(cdk_errors()).ValidationError("Unexpected IResolvable",scope);const ret=new CfnGatewayResponse(scope,id,propsResult.value);for(const[propKey,propVal]of Object.entries(propsResult.extraProperties))ret.addPropertyOverride(propKey,propVal);return options.parser.handleAttributes(ret,resourceAttributes,id),ret}constructor(scope,id,props){super(scope,id,{type:CfnGatewayResponse.CFN_RESOURCE_TYPE_NAME,properties:props});try{jsiiDeprecationWarnings().aws_cdk_lib_aws_apigateway_CfnGatewayResponseProps(props)}catch(error){throw process.env.JSII_DEBUG!=="1"&&error.name==="DeprecationError"&&Error.captureStackTrace(error,CfnGatewayResponse),error}cdk().requireProperty(props,"responseType",this),cdk().requireProperty(props,"restApiId",this),this.attrId=cdk().Token.asString(this.getAtt("Id",cdk().ResolutionTypeHint.STRING)),this.responseParameters=props.responseParameters,this.responseTemplates=props.responseTemplates,this.responseType=props.responseType,this.restApiId=props.restApiId,this.statusCode=props.statusCode}get cfnProperties(){return{responseParameters:this.responseParameters,responseTemplates:this.responseTemplates,responseType:this.responseType,restApiId:this.restApiId,statusCode:this.statusCode}}inspect(inspector){try{jsiiDeprecationWarnings().aws_cdk_lib_TreeInspector(inspector)}catch(error){throw process.env.JSII_DEBUG!=="1"&&error.name==="DeprecationError"&&Error.captureStackTrace(error,this.inspect),error}inspector.addAttribute("aws:cdk:cloudformation:type",CfnGatewayResponse.CFN_RESOURCE_TYPE_NAME),inspector.addAttribute("aws:cdk:cloudformation:props",this.cfnProperties)}renderProperties(props){return convertCfnGatewayResponsePropsToCloudFormation(props)}}exports.CfnGatewayResponse=CfnGatewayResponse,_k=JSII_RTTI_SYMBOL_1,CfnGatewayResponse[_k]={fqn:"aws-cdk-lib.aws_apigateway.CfnGatewayResponse",version:"2.201.0"},CfnGatewayResponse.CFN_RESOURCE_TYPE_NAME="AWS::ApiGateway::GatewayResponse";function CfnGatewayResponsePropsValidator(properties){if(!cdk().canInspect(properties))return cdk().VALIDATION_SUCCESS;const errors=new(cdk()).ValidationResults;return properties&&typeof properties=="object"&&!Array.isArray(properties)||errors.collect(new(cdk()).ValidationResult("Expected an object, but received: "+JSON.stringify(properties))),errors.collect(cdk().propertyValidator("responseParameters",cdk().hashValidator(cdk().validateString))(properties.responseParameters)),errors.collect(cdk().propertyValidator("responseTemplates",cdk().hashValidator(cdk().validateString))(properties.responseTemplates)),errors.collect(cdk().propertyValidator("responseType",cdk().requiredValidator)(properties.responseType)),errors.collect(cdk().propertyValidator("responseType",cdk().validateString)(properties.responseType)),errors.collect(cdk().propertyValidator("restApiId",cdk().requiredValidator)(properties.restApiId)),errors.collect(cdk().propertyValidator("restApiId",cdk().validateString)(properties.restApiId)),errors.collect(cdk().propertyValidator("statusCode",cdk().validateString)(properties.statusCode)),errors.wrap('supplied properties not correct for "CfnGatewayResponseProps"')}function convertCfnGatewayResponsePropsToCloudFormation(properties){return cdk().canInspect(properties)?(CfnGatewayResponsePropsValidator(properties).assertSuccess(),{ResponseParameters:cdk().hashMapper(cdk().stringToCloudFormation)(properties.responseParameters),ResponseTemplates:cdk().hashMapper(cdk().stringToCloudFormation)(properties.responseTemplates),ResponseType:cdk().stringToCloudFormation(properties.responseType),RestApiId:cdk().stringToCloudFormation(properties.restApiId),StatusCode:cdk().stringToCloudFormation(properties.statusCode)}):properties}function CfnGatewayResponsePropsFromCloudFormation(properties){if(cdk().isResolvableObject(properties))return new(cfn_parse()).FromCloudFormationResult(properties);if(properties=properties??{},!(properties&&typeof properties=="object"&&!Array.isArray(properties)))return new(cfn_parse()).FromCloudFormationResult(properties);const ret=new(cfn_parse()).FromCloudFormationPropertyObject;return ret.addPropertyResult("responseParameters","ResponseParameters",properties.ResponseParameters!=null?cfn_parse().FromCloudFormation.getMap(cfn_parse().FromCloudFormation.getString)(properties.ResponseParameters):void 0),ret.addPropertyResult("responseTemplates","ResponseTemplates",properties.ResponseTemplates!=null?cfn_parse().FromCloudFormation.getMap(cfn_parse().FromCloudFormation.getString)(properties.ResponseTemplates):void 0),ret.addPropertyResult("responseType","ResponseType",properties.ResponseType!=null?cfn_parse().FromCloudFormation.getString(properties.ResponseType):void 0),ret.addPropertyResult("restApiId","RestApiId",properties.RestApiId!=null?cfn_parse().FromCloudFormation.getString(properties.RestApiId):void 0),ret.addPropertyResult("statusCode","StatusCode",properties.StatusCode!=null?cfn_parse().FromCloudFormation.getString(properties.StatusCode):void 0),ret.addUnrecognizedPropertiesAsExtra(properties),ret}class CfnMethod extends cdk().CfnResource{static _fromCloudFormation(scope,id,resourceAttributes,options){resourceAttributes=resourceAttributes||{};const resourceProperties=options.parser.parseValue(resourceAttributes.Properties),propsResult=CfnMethodPropsFromCloudFormation(resourceProperties);if(cdk().isResolvableObject(propsResult.value))throw new(cdk_errors()).ValidationError("Unexpected IResolvable",scope);const ret=new CfnMethod(scope,id,propsResult.value);for(const[propKey,propVal]of Object.entries(propsResult.extraProperties))ret.addPropertyOverride(propKey,propVal);return options.parser.handleAttributes(ret,resourceAttributes,id),ret}constructor(scope,id,props){super(scope,id,{type:CfnMethod.CFN_RESOURCE_TYPE_NAME,properties:props});try{jsiiDeprecationWarnings().aws_cdk_lib_aws_apigateway_CfnMethodProps(props)}catch(error){throw process.env.JSII_DEBUG!=="1"&&error.name==="DeprecationError"&&Error.captureStackTrace(error,CfnMethod),error}cdk().requireProperty(props,"httpMethod",this),cdk().requireProperty(props,"resourceId",this),cdk().requireProperty(props,"restApiId",this),this.apiKeyRequired=props.apiKeyRequired,this.authorizationScopes=props.authorizationScopes,this.authorizationType=props.authorizationType,this.authorizerId=props.authorizerId,this.httpMethod=props.httpMethod,this.integration=props.integration,this.methodResponses=props.methodResponses,this.operationName=props.operationName,this.requestModels=props.requestModels,this.requestParameters=props.requestParameters,this.requestValidatorId=props.requestValidatorId,this.resourceId=props.resourceId,this.restApiId=props.restApiId}get cfnProperties(){return{apiKeyRequired:this.apiKeyRequired,authorizationScopes:this.authorizationScopes,authorizationType:this.authorizationType,authorizerId:this.authorizerId,httpMethod:this.httpMethod,integration:this.integration,methodResponses:this.methodResponses,operationName:this.operationName,requestModels:this.requestModels,requestParameters:this.requestParameters,requestValidatorId:this.requestValidatorId,resourceId:this.resourceId,restApiId:this.restApiId}}inspect(inspector){try{jsiiDeprecationWarnings().aws_cdk_lib_TreeInspector(inspector)}catch(error){throw process.env.JSII_DEBUG!=="1"&&error.name==="DeprecationError"&&Error.captureStackTrace(error,this.inspect),error}inspector.addAttribute("aws:cdk:cloudformation:type",CfnMethod.CFN_RESOURCE_TYPE_NAME),inspector.addAttribute("aws:cdk:cloudformation:props",this.cfnProperties)}renderProperties(props){return convertCfnMethodPropsToCloudFormation(props)}}exports.CfnMethod=CfnMethod,_l=JSII_RTTI_SYMBOL_1,CfnMethod[_l]={fqn:"aws-cdk-lib.aws_apigateway.CfnMethod",version:"2.201.0"},CfnMethod.CFN_RESOURCE_TYPE_NAME="AWS::ApiGateway::Method";function CfnMethodIntegrationResponsePropertyValidator(properties){if(!cdk().canInspect(properties))return cdk().VALIDATION_SUCCESS;const errors=new(cdk()).ValidationResults;return properties&&typeof properties=="object"&&!Array.isArray(properties)||errors.collect(new(cdk()).ValidationResult("Expected an object, but received: "+JSON.stringify(properties))),errors.collect(cdk().propertyValidator("contentHandling",cdk().validateString)(properties.contentHandling)),errors.collect(cdk().propertyValidator("responseParameters",cdk().hashValidator(cdk().validateString))(properties.responseParameters)),errors.collect(cdk().propertyValidator("responseTemplates",cdk().hashValidator(cdk().validateString))(properties.responseTemplates)),errors.collect(cdk().propertyValidator("selectionPattern",cdk().validateString)(properties.selectionPattern)),errors.collect(cdk().propertyValidator("statusCode",cdk().requiredValidator)(properties.statusCode)),errors.collect(cdk().propertyValidator("statusCode",cdk().validateString)(properties.statusCode)),errors.wrap('supplied properties not correct for "IntegrationResponseProperty"')}function convertCfnMethodIntegrationResponsePropertyToCloudFormation(properties){return cdk().canInspect(properties)?(CfnMethodIntegrationResponsePropertyValidator(properties).assertSuccess(),{ContentHandling:cdk().stringToCloudFormation(properties.contentHandling),ResponseParameters:cdk().hashMapper(cdk().stringToCloudFormation)(properties.responseParameters),ResponseTemplates:cdk().hashMapper(cdk().stringToCloudFormation)(properties.responseTemplates),SelectionPattern:cdk().stringToCloudFormation(properties.selectionPattern),StatusCode:cdk().stringToCloudFormation(properties.statusCode)}):properties}function CfnMethodIntegrationResponsePropertyFromCloudFormation(properties){if(cdk().isResolvableObject(properties))return new(cfn_parse()).FromCloudFormationResult(properties);if(properties=properties??{},!(properties&&typeof properties=="object"&&!Array.isArray(properties)))return new(cfn_parse()).FromCloudFormationResult(properties);const ret=new(cfn_parse()).FromCloudFormationPropertyObject;return ret.addPropertyResult("contentHandling","ContentHandling",properties.ContentHandling!=null?cfn_parse().FromCloudFormation.getString(properties.ContentHandling):void 0),ret.addPropertyResult("responseParameters","ResponseParameters",properties.ResponseParameters!=null?cfn_parse().FromCloudFormation.getMap(cfn_parse().FromCloudFormation.getString)(properties.ResponseParameters):void 0),ret.addPropertyResult("responseTemplates","ResponseTemplates",properties.ResponseTemplates!=null?cfn_parse().FromCloudFormation.getMap(cfn_parse().FromCloudFormation.getString)(properties.ResponseTemplates):void 0),ret.addPropertyResult("selectionPattern","SelectionPattern",properties.SelectionPattern!=null?cfn_parse().FromCloudFormation.getString(properties.SelectionPattern):void 0),ret.addPropertyResult("statusCode","StatusCode",properties.StatusCode!=null?cfn_parse().FromCloudFormation.getString(properties.StatusCode):void 0),ret.addUnrecognizedPropertiesAsExtra(properties),ret}function CfnMethodIntegrationPropertyValidator(properties){if(!cdk().canInspect(properties))return cdk().VALIDATION_SUCCESS;const errors=new(cdk()).ValidationResults;return properties&&typeof properties=="object"&&!Array.isArray(properties)||errors.collect(new(cdk()).ValidationResult("Expected an object, but received: "+JSON.stringify(properties))),errors.collect(cdk().propertyValidator("cacheKeyParameters",cdk().listValidator(cdk().validateString))(properties.cacheKeyParameters)),errors.collect(cdk().propertyValidator("cacheNamespace",cdk().validateString)(properties.cacheNamespace)),errors.collect(cdk().propertyValidator("connectionId",cdk().validateString)(properties.connectionId)),errors.collect(cdk().propertyValidator("connectionType",cdk().validateString)(properties.connectionType)),errors.collect(cdk().propertyValidator("contentHandling",cdk().validateString)(properties.contentHandling)),errors.collect(cdk().propertyValidator("credentials",cdk().validateString)(properties.credentials)),errors.collect(cdk().propertyValidator("integrationHttpMethod",cdk().validateString)(properties.integrationHttpMethod)),errors.collect(cdk().propertyValidator("integrationResponses",cdk().listValidator(CfnMethodIntegrationResponsePropertyValidator))(properties.integrationResponses)),errors.collect(cdk().propertyValidator("passthroughBehavior",cdk().validateString)(properties.passthroughBehavior)),errors.collect(cdk().propertyValidator("requestParameters",cdk().hashValidator(cdk().validateString))(properties.requestParameters)),errors.collect(cdk().propertyValidator("requestTemplates",cdk().hashValidator(cdk().validateString))(properties.requestTemplates)),errors.collect(cdk().propertyValidator("timeoutInMillis",cdk().validateNumber)(properties.timeoutInMillis)),errors.collect(cdk().propertyValidator("type",cdk().requiredValidator)(properties.type)),errors.collect(cdk().propertyValidator("type",cdk().validateString)(properties.type)),errors.collect(cdk().propertyValidator("uri",cdk().validateString)(properties.uri)),errors.wrap('supplied properties not correct for "IntegrationProperty"')}function convertCfnMethodIntegrationPropertyToCloudFormation(properties){return cdk().canInspect(properties)?(CfnMethodIntegrationPropertyValidator(properties).assertSuccess(),{CacheKeyParameters:cdk().listMapper(cdk().stringToCloudFormation)(properties.cacheKeyParameters),CacheNamespace:cdk().stringToCloudFormation(properties.cacheNamespace),ConnectionId:cdk().stringToCloudFormation(properties.connectionId),ConnectionType:cdk().stringToCloudFormation(properties.connectionType),ContentHandling:cdk().stringToCloudFormation(properties.contentHandling),Credentials:cdk().stringToCloudFormation(properties.credentials),IntegrationHttpMethod:cdk().stringToCloudFormation(properties.integrationHttpMethod),IntegrationResponses:cdk().listMapper(convertCfnMethodIntegrationResponsePropertyToCloudFormation)(properties.integrationResponses),PassthroughBehavior:cdk().stringToCloudFormation(properties.passthroughBehavior),RequestParameters:cdk().hashMapper(cdk().stringToCloudFormation)(properties.requestParameters),RequestTemplates:cdk().hashMapper(cdk().stringToCloudFormation)(properties.requestTemplates),TimeoutInMillis:cdk().numberToCloudFormation(properties.timeoutInMillis),Type:cdk().stringToCloudFormation(properties.type),Uri:cdk().stringToCloudFormation(properties.uri)}):properties}function CfnMethodIntegrationPropertyFromCloudFormation(properties){if(cdk().isResolvableObject(properties))return new(cfn_parse()).FromCloudFormationResult(properties);if(properties=properties??{},!(properties&&typeof properties=="object"&&!Array.isArray(properties)))return new(cfn_parse()).FromCloudFormationResult(properties);const ret=new(cfn_parse()).FromCloudFormationPropertyObject;return ret.addPropertyResult("cacheKeyParameters","CacheKeyParameters",properties.CacheKeyParameters!=null?cfn_parse().FromCloudFormation.getArray(cfn_parse().FromCloudFormation.getString)(properties.CacheKeyParameters):void 0),ret.addPropertyResult("cacheNamespace","CacheNamespace",properties.CacheNamespace!=null?cfn_parse().FromCloudFormation.getString(properties.CacheNamespace):void 0),ret.addPropertyResult("connectionId","ConnectionId",properties.ConnectionId!=null?cfn_parse().FromCloudFormation.getString(properties.ConnectionId):void 0),ret.addPropertyResult("connectionType","ConnectionType",properties.ConnectionType!=null?cfn_parse().FromCloudFormation.getString(properties.ConnectionType):void 0),ret.addPropertyResult("contentHandling","ContentHandling",properties.ContentHandling!=null?cfn_parse().FromCloudFormation.getString(properties.ContentHandling):void 0),ret.addPropertyResult("credentials","Credentials",properties.Credentials!=null?cfn_parse().FromCloudFormation.getString(properties.Credentials):void 0),ret.addPropertyResult("integrationHttpMethod","IntegrationHttpMethod",properties.IntegrationHttpMethod!=null?cfn_parse().FromCloudFormation.getString(properties.IntegrationHttpMethod):void 0),ret.addPropertyResult("integrationResponses","IntegrationResponses",properties.IntegrationResponses!=null?cfn_parse().FromCloudFormation.getArray(CfnMethodIntegrationResponsePropertyFromCloudFormation)(properties.IntegrationResponses):void 0),ret.addPropertyResult("passthroughBehavior","PassthroughBehavior",properties.PassthroughBehavior!=null?cfn_parse().FromCloudFormation.getString(properties.PassthroughBehavior):void 0),ret.addPropertyResult("requestParameters","RequestParameters",properties.RequestParameters!=null?cfn_parse().FromCloudFormation.getMap(cfn_parse().FromCloudFormation.getString)(properties.RequestParameters):void 0),ret.addPropertyResult("requestTemplates","RequestTemplates",properties.RequestTemplates!=null?cfn_parse().FromCloudFormation.getMap(cfn_parse().FromCloudFormation.getString)(properties.RequestTemplates):void 0),ret.addPropertyResult("timeoutInMillis","TimeoutInMillis",properties.TimeoutInMillis!=null?cfn_parse().FromCloudFormation.getNumber(properties.TimeoutInMillis):void 0),ret.addPropertyResult("type","Type",properties.Type!=null?cfn_parse().FromCloudFormation.getString(properties.Type):void 0),ret.addPropertyResult("uri","Uri",properties.Uri!=null?cfn_parse().FromCloudFormation.getString(properties.Uri):void 0),ret.addUnrecognizedPropertiesAsExtra(properties),ret}function CfnMethodMethodResponsePropertyValidator(properties){if(!cdk().canInspect(properties))return cdk().VALIDATION_SUCCESS;const errors=new(cdk()).ValidationResults;return properties&&typeof properties=="object"&&!Array.isArray(properties)||errors.collect(new(cdk()).ValidationResult("Expected an object, but received: "+JSON.stringify(properties))),errors.collect(cdk().propertyValidator("responseModels",cdk().hashValidator(cdk().validateString))(properties.responseModels)),errors.collect(cdk().propertyValidator("responseParameters",cdk().hashValidator(cdk().validateBoolean))(properties.responseParameters)),errors.collect(cdk().propertyValidator("statusCode",cdk().requiredValidator)(properties.statusCode)),errors.collect(cdk().propertyValidator("statusCode",cdk().validateString)(properties.statusCode)),errors.wrap('supplied properties not correct for "MethodResponseProperty"')}function convertCfnMethodMethodResponsePropertyToCloudFormation(properties){return cdk().canInspect(properties)?(CfnMethodMethodResponsePropertyValidator(properties).assertSuccess(),{ResponseModels:cdk().hashMapper(cdk().stringToCloudFormation)(properties.responseModels),ResponseParameters:cdk().hashMapper(cdk().booleanToCloudFormation)(properties.responseParameters),StatusCode:cdk().stringToCloudFormation(properties.statusCode)}):properties}function CfnMethodMethodResponsePropertyFromCloudFormation(properties){if(cdk().isResolvableObject(properties))return new(cfn_parse()).FromCloudFormationResult(properties);if(properties=properties??{},!(properties&&typeof properties=="object"&&!Array.isArray(properties)))return new(cfn_parse()).FromCloudFormationResult(properties);const ret=new(cfn_parse()).FromCloudFormationPropertyObject;return ret.addPropertyResult("responseModels","ResponseModels",properties.ResponseModels!=null?cfn_parse().FromCloudFormation.getMap(cfn_parse().FromCloudFormation.getString)(properties.ResponseModels):void 0),ret.addPropertyResult("responseParameters","ResponseParameters",properties.ResponseParameters!=null?cfn_parse().FromCloudFormation.getMap(cfn_parse().FromCloudFormation.getBoolean)(properties.ResponseParameters):void 0),ret.addPropertyResult("statusCode","StatusCode",properties.StatusCode!=null?cfn_parse().FromCloudFormation.getString(properties.StatusCode):void 0),ret.addUnrecognizedPropertiesAsExtra(properties),ret}function CfnMethodPropsValidator(properties){if(!cdk().canInspect(properties))return cdk().VALIDATION_SUCCESS;const errors=new(cdk()).ValidationResults;return properties&&typeof properties=="object"&&!Array.isArray(properties)||errors.collect(new(cdk()).ValidationResult("Expected an object, but received: "+JSON.stringify(properties))),errors.collect(cdk().propertyValidator("apiKeyRequired",cdk().validateBoolean)(properties.apiKeyRequired)),errors.collect(cdk().propertyValidator("authorizationScopes",cdk().listValidator(cdk().validateString))(properties.authorizationScopes)),errors.collect(cdk().propertyValidator("authorizationType",cdk().validateString)(properties.authorizationType)),errors.collect(cdk().propertyValidator("authorizerId",cdk().validateString)(properties.authorizerId)),errors.collect(cdk().propertyValidator("httpMethod",cdk().requiredValidator)(properties.httpMethod)),errors.collect(cdk().propertyValidator("httpMethod",cdk().validateString)(properties.httpMethod)),errors.collect(cdk().propertyValidator("integration",CfnMethodIntegrationPropertyValidator)(properties.integration)),errors.collect(cdk().propertyValidator("methodResponses",cdk().listValidator(CfnMethodMethodResponsePropertyValidator))(properties.methodResponses)),errors.collect(cdk().propertyValidator("operationName",cdk().validateString)(properties.operationName)),errors.collect(cdk().propertyValidator("requestModels",cdk().hashValidator(cdk().validateString))(properties.requestModels)),errors.collect(cdk().propertyValidator("requestParameters",cdk().hashValidator(cdk().validateBoolean))(properties.requestParameters)),errors.collect(cdk().propertyValidator("requestValidatorId",cdk().validateString)(properties.requestValidatorId)),errors.collect(cdk().propertyValidator("resourceId",cdk().requiredValidator)(properties.resourceId)),errors.collect(cdk().propertyValidator("resourceId",cdk().validateString)(properties.resourceId)),errors.collect(cdk().propertyValidator("restApiId",cdk().requiredValidator)(properties.restApiId)),errors.collect(cdk().propertyValidator("restApiId",cdk().validateString)(properties.restApiId)),errors.wrap('supplied properties not correct for "CfnMethodProps"')}function convertCfnMethodPropsToCloudFormation(properties){return cdk().canInspect(properties)?(CfnMethodPropsValidator(properties).assertSuccess(),{ApiKeyRequired:cdk().booleanToCloudFormation(properties.apiKeyRequired),AuthorizationScopes:cdk().listMapper(cdk().stringToCloudFormation)(properties.authorizationScopes),AuthorizationType:cdk().stringToCloudFormation(properties.authorizationType),AuthorizerId:cdk().stringToCloudFormation(properties.authorizerId),HttpMethod:cdk().stringToCloudFormation(properties.httpMethod),Integration:convertCfnMethodIntegrationPropertyToCloudFormation(properties.integration),MethodResponses:cdk().listMapper(convertCfnMethodMethodResponsePropertyToCloudFormation)(properties.methodResponses),OperationName:cdk().stringToCloudFormation(properties.operationName),RequestModels:cdk().hashMapper(cdk().stringToCloudFormation)(properties.requestModels),RequestParameters:cdk().hashMapper(cdk().booleanToCloudFormation)(properties.requestParameters),RequestValidatorId:cdk().stringToCloudFormation(properties.requestValidatorId),ResourceId:cdk().stringToCloudFormation(properties.resourceId),RestApiId:cdk().stringToCloudFormation(properties.restApiId)}):properties}function CfnMethodPropsFromCloudFormation(properties){if(cdk().isResolvableObject(properties))return new(cfn_parse()).FromCloudFormationResult(properties);if(properties=properties??{},!(properties&&typeof properties=="object"&&!Array.isArray(properties)))return new(cfn_parse()).FromCloudFormationResult(properties);const ret=new(cfn_parse()).FromCloudFormationPropertyObject;return ret.addPropertyResult("apiKeyRequired","ApiKeyRequired",properties.ApiKeyRequired!=null?cfn_parse().FromCloudFormation.getBoolean(properties.ApiKeyRequired):void 0),ret.addPropertyResult("authorizationScopes","AuthorizationScopes",properties.AuthorizationScopes!=null?cfn_parse().FromCloudFormation.getArray(cfn_parse().FromCloudFormation.getString)(properties.AuthorizationScopes):void 0),ret.addPropertyResult("authorizationType","AuthorizationType",properties.AuthorizationType!=null?cfn_parse().FromCloudFormation.getString(properties.AuthorizationType):void 0),ret.addPropertyResult("authorizerId","AuthorizerId",properties.AuthorizerId!=null?cfn_parse().FromCloudFormation.getString(properties.AuthorizerId):void 0),ret.addPropertyResult("httpMethod","HttpMethod",properties.HttpMethod!=null?cfn_parse().FromCloudFormation.getString(properties.HttpMethod):void 0),ret.addPropertyResult("integration","Integration",properties.Integration!=null?CfnMethodIntegrationPropertyFromCloudFormation(properties.Integration):void 0),ret.addPropertyResult("methodResponses","MethodResponses",properties.MethodResponses!=null?cfn_parse().FromCloudFormation.getArray(CfnMethodMethodResponsePropertyFromCloudFormation)(properties.MethodResponses):void 0),ret.addPropertyResult("operationName","OperationName",properties.OperationName!=null?cfn_parse().FromCloudFormation.getString(properties.OperationName):void 0),ret.addPropertyResult("requestModels","RequestModels",properties.RequestModels!=null?cfn_parse().FromCloudFormation.getMap(cfn_parse().FromCloudFormation.getString)(properties.RequestModels):void 0),ret.addPropertyResult("requestParameters","RequestParameters",properties.RequestParameters!=null?cfn_parse().FromCloudFormation.getMap(cfn_parse().FromCloudFormation.getBoolean)(properties.RequestParameters):void 0),ret.addPropertyResult("requestValidatorId","RequestValidatorId",properties.RequestValidatorId!=null?cfn_parse().FromCloudFormation.getString(properties.RequestValidatorId):void 0),ret.addPropertyResult("resourceId","ResourceId",properties.ResourceId!=null?cfn_parse().FromCloudFormation.getString(properties.ResourceId):void 0),ret.addPropertyResult("restApiId","RestApiId",properties.RestApiId!=null?cfn_parse().FromCloudFormation.getString(properties.RestApiId):void 0),ret.addUnrecognizedPropertiesAsExtra(properties),ret}class CfnModel extends cdk().CfnResource{static _fromCloudFormation(scope,id,resourceAttributes,options){resourceAttributes=resourceAttributes||{};const resourceProperties=options.parser.parseValue(resourceAttributes.Properties),propsResult=CfnModelPropsFromCloudFormation(resourceProperties);if(cdk().isResolvableObject(propsResult.value))throw new(cdk_errors()).ValidationError("Unexpected IResolvable",scope);const ret=new CfnModel(scope,id,propsResult.value);for(const[propKey,propVal]of Object.entries(propsResult.extraProperties))ret.addPropertyOverride(propKey,propVal);return options.parser.handleAttributes(ret,resourceAttributes,id),ret}constructor(scope,id,props){super(scope,id,{type:CfnModel.CFN_RESOURCE_TYPE_NAME,properties:props});try{jsiiDeprecationWarnings().aws_cdk_lib_aws_apigateway_CfnModelProps(props)}catch(error){throw process.env.JSII_DEBUG!=="1"&&error.name==="DeprecationError"&&Error.captureStackTrace(error,CfnModel),error}cdk().requireProperty(props,"restApiId",this),this.contentType=props.contentType,this.description=props.description,this.name=props.name,this.restApiId=props.restApiId,this.schema=props.schema}get cfnProperties(){return{contentType:this.contentType,description:this.description,name:this.name,restApiId:this.restApiId,schema:this.schema}}inspect(inspector){try{jsiiDeprecationWarnings().aws_cdk_lib_TreeInspector(inspector)}catch(error){throw process.env.JSII_DEBUG!=="1"&&error.name==="DeprecationError"&&Error.captureStackTrace(error,this.inspect),error}inspector.addAttribute("aws:cdk:cloudformation:type",CfnModel.CFN_RESOURCE_TYPE_NAME),inspector.addAttribute("aws:cdk:cloudformation:props",this.cfnProperties)}renderProperties(props){return convertCfnModelPropsToCloudFormation(props)}}exports.CfnModel=CfnModel,_m=JSII_RTTI_SYMBOL_1,CfnModel[_m]={fqn:"aws-cdk-lib.aws_apigateway.CfnModel",version:"2.201.0"},CfnModel.CFN_RESOURCE_TYPE_NAME="AWS::ApiGateway::Model";function CfnModelPropsValidator(properties){if(!cdk().canInspect(properties))return cdk().VALIDATION_SUCCESS;const errors=new(cdk()).ValidationResults;return properties&&typeof properties=="object"&&!Array.isArray(properties)||errors.collect(new(cdk()).ValidationResult("Expected an object, but received: "+JSON.stringify(properties))),errors.collect(cdk().propertyValidator("contentType",cdk().validateString)(properties.contentType)),errors.collect(cdk().propertyValidator("description",cdk().validateString)(properties.description)),errors.collect(cdk().propertyValidator("name",cdk().validateString)(properties.name)),errors.collect(cdk().propertyValidator("restApiId",cdk().requiredValidator)(properties.restApiId)),errors.collect(cdk().propertyValidator("restApiId",cdk().validateString)(properties.restApiId)),errors.collect(cdk().propertyValidator("schema",cdk().validateObject)(properties.schema)),errors.wrap('supplied properties not correct for "CfnModelProps"')}function convertCfnModelPropsToCloudFormation(properties){return cdk().canInspect(properties)?(CfnModelPropsValidator(properties).assertSuccess(),{ContentType:cdk().stringToCloudFormation(properties.contentType),Description:cdk().stringToCloudFormation(properties.description),Name:cdk().stringToCloudFormation(properties.name),RestApiId:cdk().stringToCloudFormation(properties.restApiId),Schema:cdk().objectToCloudFormation(properties.schema)}):properties}function CfnModelPropsFromCloudFormation(properties){if(cdk().isResolvableObject(properties))return new(cfn_parse()).FromCloudFormationResult(properties);if(properties=properties??{},!(properties&&typeof properties=="object"&&!Array.isArray(properties)))return new(cfn_parse()).FromCloudFormationResult(properties);const ret=new(cfn_parse()).FromCloudFormationPropertyObject;return ret.addPropertyResult("contentType","ContentType",properties.ContentType!=null?cfn_parse().FromCloudFormation.getString(properties.ContentType):void 0),ret.addPropertyResult("description","Description",properties.Description!=null?cfn_parse().FromCloudFormation.getString(properties.Description):void 0),ret.addPropertyResult("name","Name",properties.Name!=null?cfn_parse().FromCloudFormation.getString(properties.Name):void 0),ret.addPropertyResult("restApiId","RestApiId",properties.RestApiId!=null?cfn_parse().FromCloudFormation.getString(properties.RestApiId):void 0),ret.addPropertyResult("schema","Schema",properties.Schema!=null?cfn_parse().FromCloudFormation.getAny(properties.Schema):void 0),ret.addUnrecognizedPropertiesAsExtra(properties),ret}class CfnRequestValidator extends cdk().CfnResource{static _fromCloudFormation(scope,id,resourceAttributes,options){resourceAttributes=resourceAttributes||{};const resourceProperties=options.parser.parseValue(resourceAttributes.Properties),propsResult=CfnRequestValidatorPropsFromCloudFormation(resourceProperties);if(cdk().isResolvableObject(propsResult.value))throw new(cdk_errors()).ValidationError("Unexpected IResolvable",scope);const ret=new CfnRequestValidator(scope,id,propsResult.value);for(const[propKey,propVal]of Object.entries(propsResult.extraProperties))ret.addPropertyOverride(propKey,propVal);return options.parser.handleAttributes(ret,resourceAttributes,id),ret}constructor(scope,id,props){super(scope,id,{type:CfnRequestValidator.CFN_RESOURCE_TYPE_NAME,properties:props});try{jsiiDeprecationWarnings().aws_cdk_lib_aws_apigateway_CfnRequestValidatorProps(props)}catch(error){throw process.env.JSII_DEBUG!=="1"&&error.name==="DeprecationError"&&Error.captureStackTrace(error,CfnRequestValidator),error}cdk().requireProperty(props,"restApiId",this),this.attrRequestValidatorId=cdk().Token.asString(this.getAtt("RequestValidatorId",cdk().ResolutionTypeHint.STRING)),this.name=props.name,this.restApiId=props.restApiId,this.validateRequestBody=props.validateRequestBody,this.validateRequestParameters=props.validateRequestParameters}get cfnProperties(){return{name:this.name,restApiId:this.restApiId,validateRequestBody:this.validateRequestBody,validateRequestParameters:this.validateRequestParameters}}inspect(inspector){try{jsiiDeprecationWarnings().aws_cdk_lib_TreeInspector(inspector)}catch(error){throw process.env.JSII_DEBUG!=="1"&&error.name==="DeprecationError"&&Error.captureStackTrace(error,this.inspect),error}inspector.addAttribute("aws:cdk:cloudformation:type",CfnRequestValidator.CFN_RESOURCE_TYPE_NAME),inspector.addAttribute("aws:cdk:cloudformation:props",this.cfnProperties)}renderProperties(props){return convertCfnRequestValidatorPropsToCloudFormation(props)}}exports.CfnRequestValidator=CfnRequestValidator,_o=JSII_RTTI_SYMBOL_1,CfnRequestValidator[_o]={fqn:"aws-cdk-lib.aws_apigateway.CfnRequestValidator",version:"2.201.0"},CfnRequestValidator.CFN_RESOURCE_TYPE_NAME="AWS::ApiGateway::RequestValidator";function CfnRequestValidatorPropsValidator(properties){if(!cdk().canInspect(properties))return cdk().VALIDATION_SUCCESS;const errors=new(cdk()).ValidationResults;return properties&&typeof properties=="object"&&!Array.isArray(properties)||errors.collect(new(cdk()).ValidationResult("Expected an object, but received: "+JSON.stringify(properties))),errors.collect(cdk().propertyValidator("name",cdk().validateString)(properties.name)),errors.collect(cdk().propertyValidator("restApiId",cdk().requiredValidator)(properties.restApiId)),errors.collect(cdk().propertyValidator("restApiId",cdk().validateString)(properties.restApiId)),errors.collect(cdk().propertyValidator("validateRequestBody",cdk().validateBoolean)(properties.validateRequestBody)),errors.collect(cdk().propertyValidator("validateRequestParameters",cdk().validateBoolean)(properties.validateRequestParameters)),errors.wrap('supplied properties not correct for "CfnRequestValidatorProps"')}function convertCfnRequestValidatorPropsToCloudFormation(properties){return cdk().canInspect(properties)?(CfnRequestValidatorPropsValidator(properties).assertSuccess(),{Name:cdk().stringToCloudFormation(properties.name),RestApiId:cdk().stringToCloudFormation(properties.restApiId),ValidateRequestBody:cdk().booleanToCloudFormation(properties.validateRequestBody),ValidateRequestParameters:cdk().booleanToCloudFormation(properties.validateRequestParameters)}):properties}function CfnRequestValidatorPropsFromCloudFormation(properties){if(cdk().isResolvableObject(properties))return new(cfn_parse()).FromCloudFormationResult(properties);if(properties=properties??{},!(properties&&typeof properties=="object"&&!Array.isArray(properties)))return new(cfn_parse()).FromCloudFormationResult(properties);const ret=new(cfn_parse()).FromCloudFormationPropertyObject;return ret.addPropertyResult("name","Name",properties.Name!=null?cfn_parse().FromCloudFormation.getString(properties.Name):void 0),ret.addPropertyResult("restApiId","RestApiId",properties.RestApiId!=null?cfn_parse().FromCloudFormation.getString(properties.RestApiId):void 0),ret.addPropertyResult("validateRequestBody","ValidateRequestBody",properties.ValidateRequestBody!=null?cfn_parse().FromCloudFormation.getBoolean(properties.ValidateRequestBody):void 0),ret.addPropertyResult("validateRequestParameters","ValidateRequestParameters",properties.ValidateRequestParameters!=null?cfn_parse().FromCloudFormation.getBoolean(properties.ValidateRequestParameters):void 0),ret.addUnrecognizedPropertiesAsExtra(properties),ret}class CfnResource extends cdk().CfnResource{static _fromCloudFormation(scope,id,resourceAttributes,options){resourceAttributes=resourceAttributes||{};const resourceProperties=options.parser.parseValue(resourceAttributes.Properties),propsResult=CfnResourcePropsFromCloudFormation(resourceProperties);if(cdk().isResolvableObject(propsResult.value))throw new(cdk_errors()).ValidationError("Unexpected IResolvable",scope);const ret=new CfnResource(scope,id,propsResult.value);for(const[propKey,propVal]of Object.entries(propsResult.extraProperties))ret.addPropertyOverride(propKey,propVal);return options.parser.handleAttributes(ret,resourceAttributes,id),ret}constructor(scope,id,props){super(scope,id,{type:CfnResource.CFN_RESOURCE_TYPE_NAME,properties:props});try{jsiiDeprecationWarnings().aws_cdk_lib_aws_apigateway_CfnResourceProps(props)}catch(error){throw process.env.JSII_DEBUG!=="1"&&error.name==="DeprecationError"&&Error.captureStackTrace(error,CfnResource),error}cdk().requireProperty(props,"parentId",this),cdk().requireProperty(props,"pathPart",this),cdk().requireProperty(props,"restApiId",this),this.attrResourceId=cdk().Token.asString(this.getAtt("ResourceId",cdk().ResolutionTypeHint.STRING)),this.parentId=props.parentId,this.pathPart=props.pathPart,this.restApiId=props.restApiId}get cfnProperties(){return{parentId:this.parentId,pathPart:this.pathPart,restApiId:this.restApiId}}inspect(inspector){try{jsiiDeprecationWarnings().aws_cdk_lib_TreeInspector(inspector)}catch(error){throw process.env.JSII_DEBUG!=="1"&&error.name==="DeprecationError"&&Error.captureStackTrace(error,this.inspect),error}inspector.addAttribute("aws:cdk:cloudformation:type",CfnResource.CFN_RESOURCE_TYPE_NAME),inspector.addAttribute("aws:cdk:cloudformation:props",this.cfnProperties)}renderProperties(props){return convertCfnResourcePropsToCloudFormation(props)}}exports.CfnResource=CfnResource,_p=JSII_RTTI_SYMBOL_1,CfnResource[_p]={fqn:"aws-cdk-lib.aws_apigateway.CfnResource",version:"2.201.0"},CfnResource.CFN_RESOURCE_TYPE_NAME="AWS::ApiGateway::Resource";function CfnResourcePropsValidator(properties){if(!cdk().canInspect(properties))return cdk().VALIDATION_SUCCESS;const errors=new(cdk()).ValidationResults;return properties&&typeof properties=="object"&&!Array.isArray(properties)||errors.collect(new(cdk()).ValidationResult("Expected an object, but received: "+JSON.stringify(properties))),errors.collect(cdk().propertyValidator("parentId",cdk().requiredValidator)(properties.parentId)),errors.collect(cdk().propertyValidator("parentId",cdk().validateString)(properties.parentId)),errors.collect(cdk().propertyValidator("pathPart",cdk().requiredValidator)(properties.pathPart)),errors.collect(cdk().propertyValidator("pathPart",cdk().validateString)(properties.pathPart)),errors.collect(cdk().propertyValidator("restApiId",cdk().requiredValidator)(properties.restApiId)),errors.collect(cdk().propertyValidator("restApiId",cdk().validateString)(properties.restApiId)),errors.wrap('supplied properties not correct for "CfnResourceProps"')}function convertCfnResourcePropsToCloudFormation(properties){return cdk().canInspect(properties)?(CfnResourcePropsValidator(properties).assertSuccess(),{ParentId:cdk().stringToCloudFormation(properties.parentId),PathPart:cdk().stringToCloudFormation(properties.pathPart),RestApiId:cdk().stringToCloudFormation(properties.restApiId)}):properties}function CfnResourcePropsFromCloudFormation(properties){if(cdk().isResolvableObject(properties))return new(cfn_parse()).FromCloudFormationResult(properties);if(properties=properties??{},!(properties&&typeof properties=="object"&&!Array.isArray(properties)))return new(cfn_parse()).FromCloudFormationResult(properties);const ret=new(cfn_parse()).FromCloudFormationPropertyObject;return ret.addPropertyResult("parentId","ParentId",properties.ParentId!=null?cfn_parse().FromCloudFormation.getString(properties.ParentId):void 0),ret.addPropertyResult("pathPart","PathPart",properties.PathPart!=null?cfn_parse().FromCloudFormation.getString(properties.PathPart):void 0),ret.addPropertyResult("restApiId","RestApiId",properties.RestApiId!=null?cfn_parse().FromCloudFormation.getString(properties.RestApiId):void 0),ret.addUnrecognizedPropertiesAsExtra(properties),ret}class CfnRestApi extends cdk().CfnResource{static _fromCloudFormation(scope,id,resourceAttributes,options){resourceAttributes=resourceAttributes||{};const resourceProperties=options.parser.parseValue(resourceAttributes.Properties),propsResult=CfnRestApiPropsFromCloudFormation(resourceProperties);if(cdk().isResolvableObject(propsResult.value))throw new(cdk_errors()).ValidationError("Unexpected IResolvable",scope);const ret=new CfnRestApi(scope,id,propsResult.value);for(const[propKey,propVal]of Object.entries(propsResult.extraProperties))ret.addPropertyOverride(propKey,propVal);return options.parser.handleAttributes(ret,resourceAttributes,id),ret}constructor(scope,id,props={}){super(scope,id,{type:CfnRestApi.CFN_RESOURCE_TYPE_NAME,properties:props});try{jsiiDeprecationWarnings().aws_cdk_lib_aws_apigateway_CfnRestApiProps(props)}catch(error){throw process.env.JSII_DEBUG!=="1"&&error.name==="DeprecationError"&&Error.captureStackTrace(error,CfnRestApi),error}this.attrRestApiId=cdk().Token.asString(this.getAtt("RestApiId",cdk().ResolutionTypeHint.STRING)),this.attrRootResourceId=cdk().Token.asString(this.getAtt("RootResourceId",cdk().ResolutionTypeHint.STRING)),this.apiKeySourceType=props.apiKeySourceType,this.binaryMediaTypes=props.binaryMediaTypes,this.body=props.body,this.bodyS3Location=props.bodyS3Location,this.cloneFrom=props.cloneFrom,this.description=props.description,this.disableExecuteApiEndpoint=props.disableExecuteApiEndpoint,this.endpointConfiguration=props.endpointConfiguration,this.failOnWarnings=props.failOnWarnings,this.minimumCompressionSize=props.minimumCompressionSize,this.mode=props.mode,this.name=props.name,this.parameters=props.parameters,this.policy=props.policy,this.tags=new(cdk()).TagManager(cdk().TagType.STANDARD,"AWS::ApiGateway::RestApi",props.tags,{tagPropertyName:"tags"}),this.tagsRaw=props.tags}get cfnProperties(){return{apiKeySourceType:this.apiKeySourceType,binaryMediaTypes:this.binaryMediaTypes,body:this.body,bodyS3Location:this.bodyS3Location,cloneFrom:this.cloneFrom,description:this.description,disableExecuteApiEndpoint:this.disableExecuteApiEndpoint,endpointConfiguration:this.endpointConfiguration,failOnWarnings:this.failOnWarnings,minimumCompressionSize:this.minimumCompressionSize,mode:this.mode,name:this.name,parameters:this.parameters,policy:this.policy,tags:this.tags.renderTags()}}inspect(inspector){try{jsiiDeprecationWarnings().aws_cdk_lib_TreeInspector(inspector)}catch(error){throw process.env.JSII_DEBUG!=="1"&&error.name==="DeprecationError"&&Error.captureStackTrace(error,this.inspect),error}inspector.addAttribute("aws:cdk:cloudformation:type",CfnRestApi.CFN_RESOURCE_TYPE_NAME),inspector.addAttribute("aws:cdk:cloudformation:props",this.cfnProperties)}renderProperties(props){return convertCfnRestApiPropsToCloudFormation(props)}}exports.CfnRestApi=CfnRestApi,_q=JSII_RTTI_SYMBOL_1,CfnRestApi[_q]={fqn:"aws-cdk-lib.aws_apigateway.CfnRestApi",version:"2.201.0"},CfnRestApi.CFN_RESOURCE_TYPE_NAME="AWS::ApiGateway::RestApi";function CfnRestApiS3LocationPropertyValidator(properties){if(!cdk().canInspect(properties))return cdk().VALIDATION_SUCCESS;const errors=new(cdk()).ValidationResults;return properties&&typeof properties=="object"&&!Array.isArray(properties)||errors.collect(new(cdk()).ValidationResult("Expected an object, but received: "+JSON.stringify(properties))),errors.collect(cdk().propertyValidator("bucket",cdk().validateString)(properties.bucket)),errors.collect(cdk().propertyValidator("eTag",cdk().validateString)(properties.eTag)),errors.collect(cdk().propertyValidator("key",cdk().validateString)(properties.key)),errors.collect(cdk().propertyValidator("version",cdk().validateString)(properties.version)),errors.wrap('supplied properties not correct for "S3LocationProperty"')}function convertCfnRestApiS3LocationPropertyToCloudFormation(properties){return cdk().canInspect(properties)?(CfnRestApiS3LocationPropertyValidator(properties).assertSuccess(),{Bucket:cdk().stringToCloudFormation(properties.bucket),ETag:cdk().stringToCloudFormation(properties.eTag),Key:cdk().stringToCloudFormation(properties.key),Version:cdk().stringToCloudFormation(properties.version)}):properties}function CfnRestApiS3LocationPropertyFromCloudFormation(properties){if(cdk().isResolvableObject(properties))return new(cfn_parse()).FromCloudFormationResult(properties);if(properties=properties??{},!(properties&&typeof properties=="object"&&!Array.isArray(properties)))return new(cfn_parse()).FromCloudFormationResult(properties);const ret=new(cfn_parse()).FromCloudFormationPropertyObject;return ret.addPropertyResult("bucket","Bucket",properties.Bucket!=null?cfn_parse().FromCloudFormation.getString(properties.Bucket):void 0),ret.addPropertyResult("eTag","ETag",properties.ETag!=null?cfn_parse().FromCloudFormation.getString(properties.ETag):void 0),ret.addPropertyResult("key","Key",properties.Key!=null?cfn_parse().FromCloudFormation.getString(properties.Key):void 0),ret.addPropertyResult("version","Version",properties.Version!=null?cfn_parse().FromCloudFormation.getString(properties.Version):void 0),ret.addUnrecognizedPropertiesAsExtra(properties),ret}function CfnRestApiEndpointConfigurationPropertyValidator(properties){if(!cdk().canInspect(properties))return cdk().VALIDATION_SUCCESS;const errors=new(cdk()).ValidationResults;return properties&&typeof properties=="object"&&!Array.isArray(properties)||errors.collect(new(cdk()).ValidationResult("Expected an object, but received: "+JSON.stringify(properties))),errors.collect(cdk().propertyValidator("ipAddressType",cdk().validateString)(properties.ipAddressType)),errors.collect(cdk().propertyValidator("types",cdk().listValidator(cdk().validateString))(properties.types)),errors.collect(cdk().propertyValidator("vpcEndpointIds",cdk().listValidator(cdk().validateString))(properties.vpcEndpointIds)),errors.wrap('supplied properties not correct for "EndpointConfigurationProperty"')}function convertCfnRestApiEndpointConfigurationPropertyToCloudFormation(properties){return cdk().canInspect(properties)?(CfnRestApiEndpointConfigurationPropertyValidator(properties).assertSuccess(),{IpAddressType:cdk().stringToCloudFormation(properties.ipAddressType),Types:cdk().listMapper(cdk().stringToCloudFormation)(properties.types),VpcEndpointIds:cdk().listMapper(cdk().stringToCloudFormation)(properties.vpcEndpointIds)}):properties}function CfnRestApiEndpointConfigurationPropertyFromCloudFormation(properties){if(cdk().isResolvableObject(properties))return new(cfn_parse()).FromCloudFormationResult(properties);if(properties=properties??{},!(properties&&typeof properties=="object"&&!Array.isArray(properties)))return new(cfn_parse()).FromCloudFormationResult(properties);const ret=new(cfn_parse()).FromCloudFormationPropertyObject;return ret.addPropertyResult("ipAddressType","IpAddressType",properties.IpAddressType!=null?cfn_parse().FromCloudFormation.getString(properties.IpAddressType):void 0),ret.addPropertyResult("types","Types",properties.Types!=null?cfn_parse().FromCloudFormation.getArray(cfn_parse().FromCloudFormation.getString)(properties.Types):void 0),ret.addPropertyResult("vpcEndpointIds","VpcEndpointIds",properties.VpcEndpointIds!=null?cfn_parse().FromCloudFormation.getArray(cfn_parse().FromCloudFormation.getString)(properties.VpcEndpointIds):void 0),ret.addUnrecognizedPropertiesAsExtra(properties),ret}function CfnRestApiPropsValidator(properties){if(!cdk().canInspect(properties))return cdk().VALIDATION_SUCCESS;const errors=new(cdk()).ValidationResults;return properties&&typeof properties=="object"&&!Array.isArray(properties)||errors.collect(new(cdk()).ValidationResult("Expected an object, but received: "+JSON.stringify(properties))),errors.collect(cdk().propertyValidator("apiKeySourceType",cdk().validateString)(properties.apiKeySourceType)),errors.collect(cdk().propertyValidator("binaryMediaTypes",cdk().listValidator(cdk().validateString))(properties.binaryMediaTypes)),errors.collect(cdk().propertyValidator("body",cdk().validateObject)(properties.body)),errors.collect(cdk().propertyValidator("bodyS3Location",CfnRestApiS3LocationPropertyValidator)(properties.bodyS3Location)),errors.collect(cdk().propertyValidator("cloneFrom",cdk().validateString)(properties.cloneFrom)),errors.collect(cdk().propertyValidator("description",cdk().validateString)(properties.description)),errors.collect(cdk().propertyValidator("disableExecuteApiEndpoint",cdk().validateBoolean)(properties.disableExecuteApiEndpoint)),errors.collect(cdk().propertyValidator("endpointConfiguration",CfnRestApiEndpointConfigurationPropertyValidator)(properties.endpointConfiguration)),errors.collect(cdk().propertyValidator("failOnWarnings",cdk().validateBoolean)(properties.failOnWarnings)),errors.collect(cdk().propertyValidator("minimumCompressionSize",cdk().validateNumber)(properties.minimumCompressionSize)),errors.collect(cdk().propertyValidator("mode",cdk().validateString)(properties.mode)),errors.collect(cdk().propertyValidator("name",cdk().validateString)(properties.name)),errors.collect(cdk().propertyValidator("parameters",cdk().hashValidator(cdk().validateString))(properties.parameters)),errors.collect(cdk().propertyValidator("policy",cdk().validateObject)(properties.policy)),errors.collect(cdk().propertyValidator("tags",cdk().listValidator(cdk().validateCfnTag))(properties.tags)),errors.wrap('supplied properties not correct for "CfnRestApiProps"')}function convertCfnRestApiPropsToCloudFormation(properties){return cdk().canInspect(properties)?(CfnRestApiPropsValidator(properties).assertSuccess(),{ApiKeySourceType:cdk().stringToCloudFormation(properties.apiKeySourceType),BinaryMediaTypes:cdk().listMapper(cdk().stringToCloudFormation)(properties.binaryMediaTypes),Body:cdk().objectToCloudFormation(properties.body),BodyS3Location:convertCfnRestApiS3LocationPropertyToCloudFormation(properties.bodyS3Location),CloneFrom:cdk().stringToCloudFormation(properties.cloneFrom),Description:cdk().stringToCloudFormation(properties.description),DisableExecuteApiEndpoint:cdk().booleanToCloudFormation(properties.disableExecuteApiEndpoint),EndpointConfiguration:convertCfnRestApiEndpointConfigurationPropertyToCloudFormation(properties.endpointConfiguration),FailOnWarnings:cdk().booleanToCloudFormation(properties.failOnWarnings),MinimumCompressionSize:cdk().numberToCloudFormation(properties.minimumCompressionSize),Mode:cdk().stringToCloudFormation(properties.mode),Name:cdk().stringToCloudFormation(properties.name),Parameters:cdk().hashMapper(cdk().stringToCloudFormation)(properties.parameters),Policy:cdk().objectToCloudFormation(properties.policy),Tags:cdk().listMapper(cdk().cfnTagToCloudFormation)(properties.tags)}):properties}function CfnRestApiPropsFromCloudFormation(properties){if(cdk().isResolvableObject(properties))return new(cfn_parse()).FromCloudFormationResult(properties);if(properties=properties??{},!(properties&&typeof properties=="object"&&!Array.isArray(properties)))return new(cfn_parse()).FromCloudFormationResult(properties);const ret=new(cfn_parse()).FromCloudFormationPropertyObject;return ret.addPropertyResult("apiKeySourceType","ApiKeySourceType",properties.ApiKeySourceType!=null?cfn_parse().FromCloudFormation.getString(properties.ApiKeySourceType):void 0),ret.addPropertyResult("binaryMediaTypes","BinaryMediaTypes",properties.BinaryMediaTypes!=null?cfn_parse().FromCloudFormation.getArray(cfn_parse().FromCloudFormation.getString)(properties.BinaryMediaTypes):void 0),ret.addPropertyResult("body","Body",properties.Body!=null?cfn_parse().FromCloudFormation.getAny(properties.Body):void 0),ret.addPropertyResult("bodyS3Location","BodyS3Location",properties.BodyS3Location!=null?CfnRestApiS3LocationPropertyFromCloudFormation(properties.BodyS3Location):void 0),ret.addPropertyResult("cloneFrom","CloneFrom",properties.CloneFrom!=null?cfn_parse().FromCloudFormation.getString(properties.CloneFrom):void 0),ret.addPropertyResult("description","Description",properties.Description!=null?cfn_parse().FromCloudFormation.getString(properties.Description):void 0),ret.addPropertyResult("disableExecuteApiEndpoint","DisableExecuteApiEndpoint",properties.DisableExecuteApiEndpoint!=null?cfn_parse().FromCloudFormation.getBoolean(properties.DisableExecuteApiEndpoint):void 0),ret.addPropertyResult("endpointConfiguration","EndpointConfiguration",properties.EndpointConfiguration!=null?CfnRestApiEndpointConfigurationPropertyFromCloudFormation(properties.EndpointConfiguration):void 0),ret.addPropertyResult("failOnWarnings","FailOnWarnings",properties.FailOnWarnings!=null?cfn_parse().FromCloudFormation.getBoolean(properties.FailOnWarnings):void 0),ret.addPropertyResult("minimumCompressionSize","MinimumCompressionSize",properties.MinimumCompressionSize!=null?cfn_parse().FromCloudFormation.getNumber(properties.MinimumCompressionSize):void 0),ret.addPropertyResult("mode","Mode",properties.Mode!=null?cfn_parse().FromCloudFormation.getString(properties.Mode):void 0),ret.addPropertyResult("name","Name",properties.Name!=null?cfn_parse().FromCloudFormation.getString(properties.Name):void 0),ret.addPropertyResult("parameters","Parameters",properties.Parameters!=null?cfn_parse().FromCloudFormation.getMap(cfn_parse().FromCloudFormation.getString)(properties.Parameters):void 0),ret.addPropertyResult("policy","Policy",properties.Policy!=null?cfn_parse().FromCloudFormation.getAny(properties.Policy):void 0),ret.addPropertyResult("tags","Tags",properties.Tags!=null?cfn_parse().FromCloudFormation.getArray(cfn_parse().FromCloudFormation.getCfnTag)(properties.Tags):void 0),ret.addUnrecognizedPropertiesAsExtra(properties),ret}class CfnStage extends cdk().CfnResource{static _fromCloudFormation(scope,id,resourceAttributes,options){resourceAttributes=resourceAttributes||{};const resourceProperties=options.parser.parseValue(resourceAttributes.Properties),propsResult=CfnStagePropsFromCloudFormation(resourceProperties);if(cdk().isResolvableObject(propsResult.value))throw new(cdk_errors()).ValidationError("Unexpected IResolvable",scope);const ret=new CfnStage(scope,id,propsResult.value);for(const[propKey,propVal]of Object.entries(propsResult.extraProperties))ret.addPropertyOverride(propKey,propVal);return options.parser.handleAttributes(ret,resourceAttributes,id),ret}constructor(scope,id,props){super(scope,id,{type:CfnStage.CFN_RESOURCE_TYPE_NAME,properties:props});try{jsiiDeprecationWarnings().aws_cdk_lib_aws_apigateway_CfnStageProps(props)}catch(error){throw process.env.JSII_DEBUG!=="1"&&error.name==="DeprecationError"&&Error.captureStackTrace(error,CfnStage),error}cdk().requireProperty(props,"restApiId",this),this.accessLogSetting=props.accessLogSetting,this.cacheClusterEnabled=props.cacheClusterEnabled,this.cacheClusterSize=props.cacheClusterSize,this.canarySetting=props.canarySetting,this.clientCertificateId=props.clientCertificateId,this.deploymentId=props.deploymentId,this.description=props.description,this.documentationVersion=props.documentationVersion,this.methodSettings=props.methodSettings,this.restApiId=props.restApiId,this.stageName=props.stageName,this.tags=new(cdk()).TagManager(cdk().TagType.STANDARD,"AWS::ApiGateway::Stage",props.tags,{tagPropertyName:"tags"}),this.tagsRaw=props.tags,this.tracingEnabled=props.tracingEnabled,this.variables=props.variables}get cfnProperties(){return{accessLogSetting:this.accessLogSetting,cacheClusterEnabled:this.cacheClusterEnabled,cacheClusterSize:this.cacheClusterSize,canarySetting:this.canarySetting,clientCertificateId:this.clientCertificateId,deploymentId:this.deploymentId,description:this.description,documentationVersion:this.documentationVersion,methodSettings:this.methodSettings,restApiId:this.restApiId,stageName:this.stageName,tags:this.tags.renderTags(),tracingEnabled:this.tracingEnabled,variables:this.variables}}inspect(inspector){try{jsiiDeprecationWarnings().aws_cdk_lib_TreeInspector(inspector)}catch(error){throw process.env.JSII_DEBUG!=="1"&&error.name==="DeprecationError"&&Error.captureStackTrace(error,this.inspect),error}inspector.addAttribute("aws:cdk:cloudformation:type",CfnStage.CFN_RESOURCE_TYPE_NAME),inspector.addAttribute("aws:cdk:cloudformation:props",this.cfnProperties)}renderProperties(props){return convertCfnStagePropsToCloudFormation(props)}}exports.CfnStage=CfnStage,_r=JSII_RTTI_SYMBOL_1,CfnStage[_r]={fqn:"aws-cdk-lib.aws_apigateway.CfnStage",version:"2.201.0"},CfnStage.CFN_RESOURCE_TYPE_NAME="AWS::ApiGateway::Stage";function CfnStageCanarySettingPropertyValidator(properties){if(!cdk().canInspect(properties))return cdk().VALIDATION_SUCCESS;const errors=new(cdk()).ValidationResults;return properties&&typeof properties=="object"&&!Array.isArray(properties)||errors.collect(new(cdk()).ValidationResult("Expected an object, but received: "+JSON.stringify(properties))),errors.collect(cdk().propertyValidator("deploymentId",cdk().validateString)(properties.deploymentId)),errors.collect(cdk().propertyValidator("percentTraffic",cdk().validateNumber)(properties.percentTraffic)),errors.collect(cdk().propertyValidator("stageVariableOverrides",cdk().hashValidator(cdk().validateString))(properties.stageVariableOverrides)),errors.collect(cdk().propertyValidator("useStageCache",cdk().validateBoolean)(properties.useStageCache)),errors.wrap('supplied properties not correct for "CanarySettingProperty"')}function convertCfnStageCanarySettingPropertyToCloudFormation(properties){return cdk().canInspect(properties)?(CfnStageCanarySettingPropertyValidator(properties).assertSuccess(),{DeploymentId:cdk().stringToCloudFormation(properties.deploymentId),PercentTraffic:cdk().numberToCloudFormation(properties.percentTraffic),StageVariableOverrides:cdk().hashMapper(cdk().stringToCloudFormation)(properties.stageVariableOverrides),UseStageCache:cdk().booleanToCloudFormation(properties.useStageCache)}):properties}function CfnStageCanarySettingPropertyFromCloudFormation(properties){if(cdk().isResolvableObject(properties))return new(cfn_parse()).FromCloudFormationResult(properties);if(properties=properties??{},!(properties&&typeof properties=="object"&&!Array.isArray(properties)))return new(cfn_parse()).FromCloudFormationResult(properties);const ret=new(cfn_parse()).FromCloudFormationPropertyObject;return ret.addPropertyResult("deploymentId","DeploymentId",properties.DeploymentId!=null?cfn_parse().FromCloudFormation.getString(properties.DeploymentId):void 0),ret.addPropertyResult("percentTraffic","PercentTraffic",properties.PercentTraffic!=null?cfn_parse().FromCloudFormation.getNumber(properties.PercentTraffic):void 0),ret.addPropertyResult("stageVariableOverrides","StageVariableOverrides",properties.StageVariableOverrides!=null?cfn_parse().FromCloudFormation.getMap(cfn_parse().FromCloudFormation.getString)(properties.StageVariableOverrides):void 0),ret.addPropertyResult("useStageCache","UseStageCache",properties.UseStageCache!=null?cfn_parse().FromCloudFormation.getBoolean(properties.UseStageCache):void 0),ret.addUnrecognizedPropertiesAsExtra(properties),ret}function CfnStageMethodSettingPropertyValidator(properties){if(!cdk().canInspect(properties))return cdk().VALIDATION_SUCCESS;const errors=new(cdk()).ValidationResults;return properties&&typeof properties=="object"&&!Array.isArray(properties)||errors.collect(new(cdk()).ValidationResult("Expected an object, but received: "+JSON.stringify(properties))),errors.collect(cdk().propertyValidator("cacheDataEncrypted",cdk().validateBoolean)(properties.cacheDataEncrypted)),errors.collect(cdk().propertyValidator("cacheTtlInSeconds",cdk().validateNumber)(properties.cacheTtlInSeconds)),errors.collect(cdk().propertyValidator("cachingEnabled",cdk().validateBoolean)(properties.cachingEnabled)),errors.collect(cdk().propertyValidator("dataTraceEnabled",cdk().validateBoolean)(properties.dataTraceEnabled)),errors.collect(cdk().propertyValidator("httpMethod",cdk().validateString)(properties.httpMethod)),errors.collect(cdk().propertyValidator("loggingLevel",cdk().validateString)(properties.loggingLevel)),errors.collect(cdk().propertyValidator("metricsEnabled",cdk().validateBoolean)(properties.metricsEnabled)),errors.collect(cdk().propertyValidator("resourcePath",cdk().validateString)(properties.resourcePath)),errors.collect(cdk().propertyValidator("throttlingBurstLimit",cdk().validateNumber)(properties.throttlingBurstLimit)),errors.collect(cdk().propertyValidator("throttlingRateLimit",cdk().validateNumber)(properties.throttlingRateLimit)),errors.wrap('supplied properties not correct for "MethodSettingProperty"')}function convertCfnStageMethodSettingPropertyToCloudFormation(properties){return cdk().canInspect(properties)?(CfnStageMethodSettingPropertyValidator(properties).assertSuccess(),{CacheDataEncrypted:cdk().booleanToCloudFormation(properties.cacheDataEncrypted),CacheTtlInSeconds:cdk().numberToCloudFormation(properties.cacheTtlInSeconds),CachingEnabled:cdk().booleanToCloudFormation(properties.cachingEnabled),DataTraceEnabled:cdk().booleanToCloudFormation(properties.dataTraceEnabled),HttpMethod:cdk().stringToCloudFormation(properties.httpMethod),LoggingLevel:cdk().stringToCloudFormation(properties.loggingLevel),MetricsEnabled:cdk().booleanToCloudFormation(properties.metricsEnabled),ResourcePath:cdk().stringToCloudFormation(properties.resourcePath),ThrottlingBurstLimit:cdk().numberToCloudFormation(properties.throttlingBurstLimit),ThrottlingRateLimit:cdk().numberToCloudFormation(properties.throttlingRateLimit)}):properties}function CfnStageMethodSettingPropertyFromCloudFormation(properties){if(cdk().isResolvableObject(properties))return new(cfn_parse()).FromCloudFormationResult(properties);if(properties=properties??{},!(properties&&typeof properties=="object"&&!Array.isArray(properties)))return new(cfn_parse()).FromCloudFormationResult(properties);const ret=new(cfn_parse()).FromCloudFormationPropertyObject;return ret.addPropertyResult("cacheDataEncrypted","CacheDataEncrypted",properties.CacheDataEncrypted!=null?cfn_parse().FromCloudFormation.getBoolean(properties.CacheDataEncrypted):void 0),ret.addPropertyResult("cacheTtlInSeconds","CacheTtlInSeconds",properties.CacheTtlInSeconds!=null?cfn_parse().FromCloudFormation.getNumber(properties.CacheTtlInSeconds):void 0),ret.addPropertyResult("cachingEnabled","CachingEnabled",properties.CachingEnabled!=null?cfn_parse().FromCloudFormation.getBoolean(properties.CachingEnabled):void 0),ret.addPropertyResult("dataTraceEnabled","DataTraceEnabled",properties.DataTraceEnabled!=null?cfn_parse().FromCloudFormation.getBoolean(properties.DataTraceEnabled):void 0),ret.addPropertyResult("httpMethod","HttpMethod",properties.HttpMethod!=null?cfn_parse().FromCloudFormation.getString(properties.HttpMethod):void 0),ret.addPropertyResult("loggingLevel","LoggingLevel",properties.LoggingLevel!=null?cfn_parse().FromCloudFormation.getString(properties.LoggingLevel):void 0),ret.addPropertyResult("metricsEnabled","MetricsEnabled",properties.MetricsEnabled!=null?cfn_parse().FromCloudFormation.getBoolean(properties.MetricsEnabled):void 0),ret.addPropertyResult("resourcePath","ResourcePath",properties.ResourcePath!=null?cfn_parse().FromCloudFormation.getString(properties.ResourcePath):void 0),ret.addPropertyResult("throttlingBurstLimit","ThrottlingBurstLimit",properties.ThrottlingBurstLimit!=null?cfn_parse().FromCloudFormation.getNumber(properties.ThrottlingBurstLimit):void 0),ret.addPropertyResult("throttlingRateLimit","ThrottlingRateLimit",properties.ThrottlingRateLimit!=null?cfn_parse().FromCloudFormation.getNumber(properties.ThrottlingRateLimit):void 0),ret.addUnrecognizedPropertiesAsExtra(properties),ret}function CfnStageAccessLogSettingPropertyValidator(properties){if(!cdk().canInspect(properties))return cdk().VALIDATION_SUCCESS;const errors=new(cdk()).ValidationResults;return properties&&typeof properties=="object"&&!Array.isArray(properties)||errors.collect(new(cdk()).ValidationResult("Expected an object, but received: "+JSON.stringify(properties))),errors.collect(cdk().propertyValidator("destinationArn",cdk().validateString)(properties.destinationArn)),errors.collect(cdk().propertyValidator("format",cdk().validateString)(properties.format)),errors.wrap('supplied properties not correct for "AccessLogSettingProperty"')}function convertCfnStageAccessLogSettingPropertyToCloudFormation(properties){return cdk().canInspect(properties)?(CfnStageAccessLogSettingPropertyValidator(properties).assertSuccess(),{DestinationArn:cdk().stringToCloudFormation(properties.destinationArn),Format:cdk().stringToCloudFormation(properties.format)}):properties}function CfnStageAccessLogSettingPropertyFromCloudFormation(properties){if(cdk().isResolvableObject(properties))return new(cfn_parse()).FromCloudFormationResult(properties);if(properties=properties??{},!(properties&&typeof properties=="object"&&!Array.isArray(properties)))return new(cfn_parse()).FromCloudFormationResult(properties);const ret=new(cfn_parse()).FromCloudFormationPropertyObject;return ret.addPropertyResult("destinationArn","DestinationArn",properties.DestinationArn!=null?cfn_parse().FromCloudFormation.getString(properties.DestinationArn):void 0),ret.addPropertyResult("format","Format",properties.Format!=null?cfn_parse().FromCloudFormation.getString(properties.Format):void 0),ret.addUnrecognizedPropertiesAsExtra(properties),ret}function CfnStagePropsValidator(properties){if(!cdk().canInspect(properties))return cdk().VALIDATION_SUCCESS;const errors=new(cdk()).ValidationResults;return properties&&typeof properties=="object"&&!Array.isArray(properties)||errors.collect(new(cdk()).ValidationResult("Expected an object, but received: "+JSON.stringify(properties))),errors.collect(cdk().propertyValidator("accessLogSetting",CfnStageAccessLogSettingPropertyValidator)(properties.accessLogSetting)),errors.collect(cdk().propertyValidator("cacheClusterEnabled",cdk().validateBoolean)(properties.cacheClusterEnabled)),errors.collect(cdk().propertyValidator("cacheClusterSize",cdk().validateString)(properties.cacheClusterSize)),errors.collect(cdk().propertyValidator("canarySetting",CfnStageCanarySettingPropertyValidator)(properties.canarySetting)),errors.collect(cdk().propertyValidator("clientCertificateId",cdk().validateString)(properties.clientCertificateId)),errors.collect(cdk().propertyValidator("deploymentId",cdk().validateString)(properties.deploymentId)),errors.collect(cdk().propertyValidator("description",cdk().validateString)(properties.description)),errors.collect(cdk().propertyValidator("documentationVersion",cdk().validateString)(properties.documentationVersion)),errors.collect(cdk().propertyValidator("methodSettings",cdk().listValidator(CfnStageMethodSettingPropertyValidator))(properties.methodSettings)),errors.collect(cdk().propertyValidator("restApiId",cdk().requiredValidator)(properties.restApiId)),errors.collect(cdk().propertyValidator("restApiId",cdk().validateString)(properties.restApiId)),errors.collect(cdk().propertyValidator("stageName",cdk().validateString)(properties.stageName)),errors.collect(cdk().propertyValidator("tags",cdk().listValidator(cdk().validateCfnTag))(properties.tags)),errors.collect(cdk().propertyValidator("tracingEnabled",cdk().validateBoolean)(properties.tracingEnabled)),errors.collect(cdk().propertyValidator("variables",cdk().hashValidator(cdk().validateString))(properties.variables)),errors.wrap('supplied properties not correct for "CfnStageProps"')}function convertCfnStagePropsToCloudFormation(properties){return cdk().canInspect(properties)?(CfnStagePropsValidator(properties).assertSuccess(),{AccessLogSetting:convertCfnStageAccessLogSettingPropertyToCloudFormation(properties.accessLogSetting),CacheClusterEnabled:cdk().booleanToCloudFormation(properties.cacheClusterEnabled),CacheClusterSize:cdk().stringToCloudFormation(properties.cacheClusterSize),CanarySetting:convertCfnStageCanarySettingPropertyToCloudFormation(properties.canarySetting),ClientCertificateId:cdk().stringToCloudFormation(properties.clientCertificateId),DeploymentId:cdk().stringToCloudFormation(properties.deploymentId),Description:cdk().stringToCloudFormation(properties.description),DocumentationVersion:cdk().stringToCloudFormation(properties.documentationVersion),MethodSettings:cdk().listMapper(convertCfnStageMethodSettingPropertyToCloudFormation)(properties.methodSettings),RestApiId:cdk().stringToCloudFormation(properties.restApiId),StageName:cdk().stringToCloudFormation(properties.stageName),Tags:cdk().listMapper(cdk().cfnTagToCloudFormation)(properties.tags),TracingEnabled:cdk().booleanToCloudFormation(properties.tracingEnabled),Variables:cdk().hashMapper(cdk().stringToCloudFormation)(properties.variables)}):properties}function CfnStagePropsFromCloudFormation(properties){if(cdk().isResolvableObject(properties))return new(cfn_parse()).FromCloudFormationResult(properties);if(properties=properties??{},!(properties&&typeof properties=="object"&&!Array.isArray(properties)))return new(cfn_parse()).FromCloudFormationResult(properties);const ret=new(cfn_parse()).FromCloudFormationPropertyObject;return ret.addPropertyResult("accessLogSetting","AccessLogSetting",properties.AccessLogSetting!=null?CfnStageAccessLogSettingPropertyFromCloudFormation(properties.AccessLogSetting):void 0),ret.addPropertyResult("cacheClusterEnabled","CacheClusterEnabled",properties.CacheClusterEnabled!=null?cfn_parse().FromCloudFormation.getBoolean(properties.CacheClusterEnabled):void 0),ret.addPropertyResult("cacheClusterSize","CacheClusterSize",properties.CacheClusterSize!=null?cfn_parse().FromCloudFormation.getString(properties.CacheClusterSize):void 0),ret.addPropertyResult("canarySetting","CanarySetting",properties.CanarySetting!=null?CfnStageCanarySettingPropertyFromCloudFormation(properties.CanarySetting):void 0),ret.addPropertyResult("clientCertificateId","ClientCertificateId",properties.ClientCertificateId!=null?cfn_parse().FromCloudFormation.getString(properties.ClientCertificateId):void 0),ret.addPropertyResult("deploymentId","DeploymentId",properties.DeploymentId!=null?cfn_parse().FromCloudFormation.getString(properties.DeploymentId):void 0),ret.addPropertyResult("description","Description",properties.Description!=null?cfn_parse().FromCloudFormation.getString(properties.Description):void 0),ret.addPropertyResult("documentationVersion","DocumentationVersion",properties.DocumentationVersion!=null?cfn_parse().FromCloudFormation.getString(properties.DocumentationVersion):void 0),ret.addPropertyResult("methodSettings","MethodSettings",properties.MethodSettings!=null?cfn_parse().FromCloudFormation.getArray(CfnStageMethodSettingPropertyFromCloudFormation)(properties.MethodSettings):void 0),ret.addPropertyResult("restApiId","RestApiId",properties.RestApiId!=null?cfn_parse().FromCloudFormation.getString(properties.RestApiId):void 0),ret.addPropertyResult("stageName","StageName",properties.StageName!=null?cfn_parse().FromCloudFormation.getString(properties.StageName):void 0),ret.addPropertyResult("tags","Tags",properties.Tags!=null?cfn_parse().FromCloudFormation.getArray(cfn_parse().FromCloudFormation.getCfnTag)(properties.Tags):void 0),ret.addPropertyResult("tracingEnabled","TracingEnabled",properties.TracingEnabled!=null?cfn_parse().FromCloudFormation.getBoolean(properties.TracingEnabled):void 0),ret.addPropertyResult("variables","Variables",properties.Variables!=null?cfn_parse().FromCloudFormation.getMap(cfn_parse().FromCloudFormation.getString)(properties.Variables):void 0),ret.addUnrecognizedPropertiesAsExtra(properties),ret}class CfnUsagePlan extends cdk().CfnResource{static _fromCloudFormation(scope,id,resourceAttributes,options){resourceAttributes=resourceAttributes||{};const resourceProperties=options.parser.parseValue(resourceAttributes.Properties),propsResult=CfnUsagePlanPropsFromCloudFormation(resourceProperties);if(cdk().isResolvableObject(propsResult.value))throw new(cdk_errors()).ValidationError("Unexpected IResolvable",scope);const ret=new CfnUsagePlan(scope,id,propsResult.value);for(const[propKey,propVal]of Object.entries(propsResult.extraProperties))ret.addPropertyOverride(propKey,propVal);return options.parser.handleAttributes(ret,resourceAttributes,id),ret}constructor(scope,id,props={}){super(scope,id,{type:CfnUsagePlan.CFN_RESOURCE_TYPE_NAME,properties:props});try{jsiiDeprecationWarnings().aws_cdk_lib_aws_apigateway_CfnUsagePlanProps(props)}catch(error){throw process.env.JSII_DEBUG!=="1"&&error.name==="DeprecationError"&&Error.captureStackTrace(error,CfnUsagePlan),error}this.attrId=cdk().Token.asString(this.getAtt("Id",cdk().ResolutionTypeHint.STRING)),this.apiStages=props.apiStages,this.description=props.description,this.quota=props.quota,this.tags=new(cdk()).TagManager(cdk().TagType.STANDARD,"AWS::ApiGateway::UsagePlan",props.tags,{tagPropertyName:"tags"}),this.tagsRaw=props.tags,this.throttle=props.throttle,this.usagePlanName=props.usagePlanName}get cfnProperties(){return{apiStages:this.apiStages,description:this.description,quota:this.quota,tags:this.tags.renderTags(),throttle:this.throttle,usagePlanName:this.usagePlanName}}inspect(inspector){try{jsiiDeprecationWarnings().aws_cdk_lib_TreeInspector(inspector)}catch(error){throw process.env.JSII_DEBUG!=="1"&&error.name==="DeprecationError"&&Error.captureStackTrace(error,this.inspect),error}inspector.addAttribute("aws:cdk:cloudformation:type",CfnUsagePlan.CFN_RESOURCE_TYPE_NAME),inspector.addAttribute("aws:cdk:cloudformation:props",this.cfnProperties)}renderProperties(props){return convertCfnUsagePlanPropsToCloudFormation(props)}}exports.CfnUsagePlan=CfnUsagePlan,_s=JSII_RTTI_SYMBOL_1,CfnUsagePlan[_s]={fqn:"aws-cdk-lib.aws_apigateway.CfnUsagePlan",version:"2.201.0"},CfnUsagePlan.CFN_RESOURCE_TYPE_NAME="AWS::ApiGateway::UsagePlan";function CfnUsagePlanQuotaSettingsPropertyValidator(properties){if(!cdk().canInspect(properties))return cdk().VALIDATION_SUCCESS;const errors=new(cdk()).ValidationResults;return properties&&typeof properties=="object"&&!Array.isArray(properties)||errors.collect(new(cdk()).ValidationResult("Expected an object, but received: "+JSON.stringify(properties))),errors.collect(cdk().propertyValidator("limit",cdk().validateNumber)(properties.limit)),errors.collect(cdk().propertyValidator("offset",cdk().validateNumber)(properties.offset)),errors.collect(cdk().propertyValidator("period",cdk().validateString)(properties.period)),errors.wrap('supplied properties not correct for "QuotaSettingsProperty"')}function convertCfnUsagePlanQuotaSettingsPropertyToCloudFormation(properties){return cdk().canInspect(properties)?(CfnUsagePlanQuotaSettingsPropertyValidator(properties).assertSuccess(),{Limit:cdk().numberToCloudFormation(properties.limit),Offset:cdk().numberToCloudFormation(properties.offset),Period:cdk().stringToCloudFormation(properties.period)}):properties}function CfnUsagePlanQuotaSettingsPropertyFromCloudFormation(properties){if(cdk().isResolvableObject(properties))return new(cfn_parse()).FromCloudFormationResult(properties);if(properties=properties??{},!(properties&&typeof properties=="object"&&!Array.isArray(properties)))return new(cfn_parse()).FromCloudFormationResult(properties);const ret=new(cfn_parse()).FromCloudFormationPropertyObject;return ret.addPropertyResult("limit","Limit",properties.Limit!=null?cfn_parse().FromCloudFormation.getNumber(properties.Limit):void 0),ret.addPropertyResult("offset","Offset",properties.Offset!=null?cfn_parse().FromCloudFormation.getNumber(properties.Offset):void 0),ret.addPropertyResult("period","Period",properties.Period!=null?cfn_parse().FromCloudFormation.getString(properties.Period):void 0),ret.addUnrecognizedPropertiesAsExtra(properties),ret}function CfnUsagePlanThrottleSettingsPropertyValidator(properties){if(!cdk().canInspect(properties))return cdk().VALIDATION_SUCCESS;const errors=new(cdk()).ValidationResults;return properties&&typeof properties=="object"&&!Array.isArray(properties)||errors.collect(new(cdk()).ValidationResult("Expected an object, but received: "+JSON.stringify(properties))),errors.collect(cdk().propertyValidator("burstLimit",cdk().validateNumber)(properties.burstLimit)),errors.collect(cdk().propertyValidator("rateLimit",cdk().validateNumber)(properties.rateLimit)),errors.wrap('supplied properties not correct for "ThrottleSettingsProperty"')}function convertCfnUsagePlanThrottleSettingsPropertyToCloudFormation(properties){return cdk().canInspect(properties)?(CfnUsagePlanThrottleSettingsPropertyValidator(properties).assertSuccess(),{BurstLimit:cdk().numberToCloudFormation(properties.burstLimit),RateLimit:cdk().numberToCloudFormation(properties.rateLimit)}):properties}function CfnUsagePlanThrottleSettingsPropertyFromCloudFormation(properties){if(cdk().isResolvableObject(properties))return new(cfn_parse()).FromCloudFormationResult(properties);if(properties=properties??{},!(properties&&typeof properties=="object"&&!Array.isArray(properties)))return new(cfn_parse()).FromCloudFormationResult(properties);const ret=new(cfn_parse()).FromCloudFormationPropertyObject;return ret.addPropertyResult("burstLimit","BurstLimit",properties.BurstLimit!=null?cfn_parse().FromCloudFormation.getNumber(properties.BurstLimit):void 0),ret.addPropertyResult("rateLimit","RateLimit",properties.RateLimit!=null?cfn_parse().FromCloudFormation.getNumber(properties.RateLimit):void 0),ret.addUnrecognizedPropertiesAsExtra(properties),ret}function CfnUsagePlanApiStagePropertyValidator(properties){if(!cdk().canInspect(properties))return cdk().VALIDATION_SUCCESS;const errors=new(cdk()).ValidationResults;return properties&&typeof properties=="object"&&!Array.isArray(properties)||errors.collect(new(cdk()).ValidationResult("Expected an object, but received: "+JSON.stringify(properties))),errors.collect(cdk().propertyValidator("apiId",cdk().validateString)(properties.apiId)),errors.collect(cdk().propertyValidator("stage",cdk().validateString)(properties.stage)),errors.collect(cdk().propertyValidator("throttle",cdk().hashValidator(CfnUsagePlanThrottleSettingsPropertyValidator))(properties.throttle)),errors.wrap('supplied properties not correct for "ApiStageProperty"')}function convertCfnUsagePlanApiStagePropertyToCloudFormation(properties){return cdk().canInspect(properties)?(CfnUsagePlanApiStagePropertyValidator(properties).assertSuccess(),{ApiId:cdk().stringToCloudFormation(properties.apiId),Stage:cdk().stringToCloudFormation(properties.stage),Throttle:cdk().hashMapper(convertCfnUsagePlanThrottleSettingsPropertyToCloudFormation)(properties.throttle)}):properties}function CfnUsagePlanApiStagePropertyFromCloudFormation(properties){if(cdk().isResolvableObject(properties))return new(cfn_parse()).FromCloudFormationResult(properties);if(properties=properties??{},!(properties&&typeof properties=="object"&&!Array.isArray(properties)))return new(cfn_parse()).FromCloudFormationResult(properties);const ret=new(cfn_parse()).FromCloudFormationPropertyObject;return ret.addPropertyResult("apiId","ApiId",properties.ApiId!=null?cfn_parse().FromCloudFormation.getString(properties.ApiId):void 0),ret.addPropertyResult("stage","Stage",properties.Stage!=null?cfn_parse().FromCloudFormation.getString(properties.Stage):void 0),ret.addPropertyResult("throttle","Throttle",properties.Throttle!=null?cfn_parse().FromCloudFormation.getMap(CfnUsagePlanThrottleSettingsPropertyFromCloudFormation)(properties.Throttle):void 0),ret.addUnrecognizedPropertiesAsExtra(properties),ret}function CfnUsagePlanPropsValidator(properties){if(!cdk().canInspect(properties))return cdk().VALIDATION_SUCCESS;const errors=new(cdk()).ValidationResults;return properties&&typeof properties=="object"&&!Array.isArray(properties)||errors.collect(new(cdk()).ValidationResult("Expected an object, but received: "+JSON.stringify(properties))),errors.collect(cdk().propertyValidator("apiStages",cdk().listValidator(CfnUsagePlanApiStagePropertyValidator))(properties.apiStages)),errors.collect(cdk().propertyValidator("description",cdk().validateString)(properties.description)),errors.collect(cdk().propertyValidator("quota",CfnUsagePlanQuotaSettingsPropertyValidator)(properties.quota)),errors.collect(cdk().propertyValidator("tags",cdk().listValidator(cdk().validateCfnTag))(properties.tags)),errors.collect(cdk().propertyValidator("throttle",CfnUsagePlanThrottleSettingsPropertyValidator)(properties.throttle)),errors.collect(cdk().propertyValidator("usagePlanName",cdk().validateString)(properties.usagePlanName)),errors.wrap('supplied properties not correct for "CfnUsagePlanProps"')}function convertCfnUsagePlanPropsToCloudFormation(properties){return cdk().canInspect(properties)?(CfnUsagePlanPropsValidator(properties).assertSuccess(),{ApiStages:cdk().listMapper(convertCfnUsagePlanApiStagePropertyToCloudFormation)(properties.apiStages),Description:cdk().stringToCloudFormation(properties.description),Quota:convertCfnUsagePlanQuotaSettingsPropertyToCloudFormation(properties.quota),Tags:cdk().listMapper(cdk().cfnTagToCloudFormation)(properties.tags),Throttle:convertCfnUsagePlanThrottleSettingsPropertyToCloudFormation(properties.throttle),UsagePlanName:cdk().stringToCloudFormation(properties.usagePlanName)}):properties}function CfnUsagePlanPropsFromCloudFormation(properties){if(cdk().isResolvableObject(properties))return new(cfn_parse()).FromCloudFormationResult(properties);if(properties=properties??{},!(properties&&typeof properties=="object"&&!Array.isArray(properties)))return new(cfn_parse()).FromCloudFormationResult(properties);const ret=new(cfn_parse()).FromCloudFormationPropertyObject;return ret.addPropertyResult("apiStages","ApiStages",properties.ApiStages!=null?cfn_parse().FromCloudFormation.getArray(CfnUsagePlanApiStagePropertyFromCloudFormation)(properties.ApiStages):void 0),ret.addPropertyResult("description","Description",properties.Description!=null?cfn_parse().FromCloudFormation.getString(properties.Description):void 0),ret.addPropertyResult("quota","Quota",properties.Quota!=null?CfnUsagePlanQuotaSettingsPropertyFromCloudFormation(properties.Quota):void 0),ret.addPropertyResult("tags","Tags",properties.Tags!=null?cfn_parse().FromCloudFormation.getArray(cfn_parse().FromCloudFormation.getCfnTag)(properties.Tags):void 0),ret.addPropertyResult("throttle","Throttle",properties.Throttle!=null?CfnUsagePlanThrottleSettingsPropertyFromCloudFormation(properties.Throttle):void 0),ret.addPropertyResult("usagePlanName","UsagePlanName",properties.UsagePlanName!=null?cfn_parse().FromCloudFormation.getString(properties.UsagePlanName):void 0),ret.addUnrecognizedPropertiesAsExtra(properties),ret}class CfnUsagePlanKey extends cdk().CfnResource{static _fromCloudFormation(scope,id,resourceAttributes,options){resourceAttributes=resourceAttributes||{};const resourceProperties=options.parser.parseValue(resourceAttributes.Properties),propsResult=CfnUsagePlanKeyPropsFromCloudFormation(resourceProperties);if(cdk().isResolvableObject(propsResult.value))throw new(cdk_errors()).ValidationError("Unexpected IResolvable",scope);const ret=new CfnUsagePlanKey(scope,id,propsResult.value);for(const[propKey,propVal]of Object.entries(propsResult.extraProperties))ret.addPropertyOverride(propKey,propVal);return options.parser.handleAttributes(ret,resourceAttributes,id),ret}constructor(scope,id,props){super(scope,id,{type:CfnUsagePlanKey.CFN_RESOURCE_TYPE_NAME,properties:props});try{jsiiDeprecationWarnings().aws_cdk_lib_aws_apigateway_CfnUsagePlanKeyProps(props)}catch(error){throw process.env.JSII_DEBUG!=="1"&&error.name==="DeprecationError"&&Error.captureStackTrace(error,CfnUsagePlanKey),error}cdk().requireProperty(props,"keyId",this),cdk().requireProperty(props,"keyType",this),cdk().requireProperty(props,"usagePlanId",this),this.attrId=cdk().Token.asString(this.getAtt("Id",cdk().ResolutionTypeHint.STRING)),this.keyId=props.keyId,this.keyType=props.keyType,this.usagePlanId=props.usagePlanId}get cfnProperties(){return{keyId:this.keyId,keyType:this.keyType,usagePlanId:this.usagePlanId}}inspect(inspector){try{jsiiDeprecationWarnings().aws_cdk_lib_TreeInspector(inspector)}catch(error){throw process.env.JSII_DEBUG!=="1"&&error.name==="DeprecationError"&&Error.captureStackTrace(error,this.inspect),error}inspector.addAttribute("aws:cdk:cloudformation:type",CfnUsagePlanKey.CFN_RESOURCE_TYPE_NAME),inspector.addAttribute("aws:cdk:cloudformation:props",this.cfnProperties)}renderProperties(props){return convertCfnUsagePlanKeyPropsToCloudFormation(props)}}exports.CfnUsagePlanKey=CfnUsagePlanKey,_t=JSII_RTTI_SYMBOL_1,CfnUsagePlanKey[_t]={fqn:"aws-cdk-lib.aws_apigateway.CfnUsagePlanKey",version:"2.201.0"},CfnUsagePlanKey.CFN_RESOURCE_TYPE_NAME="AWS::ApiGateway::UsagePlanKey";function CfnUsagePlanKeyPropsValidator(properties){if(!cdk().canInspect(properties))return cdk().VALIDATION_SUCCESS;const errors=new(cdk()).ValidationResults;return properties&&typeof properties=="object"&&!Array.isArray(properties)||errors.collect(new(cdk()).ValidationResult("Expected an object, but received: "+JSON.stringify(properties))),errors.collect(cdk().propertyValidator("keyId",cdk().requiredValidator)(properties.keyId)),errors.collect(cdk().propertyValidator("keyId",cdk().validateString)(properties.keyId)),errors.collect(cdk().propertyValidator("keyType",cdk().requiredValidator)(properties.keyType)),errors.collect(cdk().propertyValidator("keyType",cdk().validateString)(properties.keyType)),errors.collect(cdk().propertyValidator("usagePlanId",cdk().requiredValidator)(properties.usagePlanId)),errors.collect(cdk().propertyValidator("usagePlanId",cdk().validateString)(properties.usagePlanId)),errors.wrap('supplied properties not correct for "CfnUsagePlanKeyProps"')}function convertCfnUsagePlanKeyPropsToCloudFormation(properties){return cdk().canInspect(properties)?(CfnUsagePlanKeyPropsValidator(properties).assertSuccess(),{KeyId:cdk().stringToCloudFormation(properties.keyId),KeyType:cdk().stringToCloudFormation(properties.keyType),UsagePlanId:cdk().stringToCloudFormation(properties.usagePlanId)}):properties}function CfnUsagePlanKeyPropsFromCloudFormation(properties){if(cdk().isResolvableObject(properties))return new(cfn_parse()).FromCloudFormationResult(properties);if(properties=properties??{},!(properties&&typeof properties=="object"&&!Array.isArray(properties)))return new(cfn_parse()).FromCloudFormationResult(properties);const ret=new(cfn_parse()).FromCloudFormationPropertyObject;return ret.addPropertyResult("keyId","KeyId",properties.KeyId!=null?cfn_parse().FromCloudFormation.getString(properties.KeyId):void 0),ret.addPropertyResult("keyType","KeyType",properties.KeyType!=null?cfn_parse().FromCloudFormation.getString(properties.KeyType):void 0),ret.addPropertyResult("usagePlanId","UsagePlanId",properties.UsagePlanId!=null?cfn_parse().FromCloudFormation.getString(properties.UsagePlanId):void 0),ret.addUnrecognizedPropertiesAsExtra(properties),ret}class CfnVpcLink extends cdk().CfnResource{static _fromCloudFormation(scope,id,resourceAttributes,options){resourceAttributes=resourceAttributes||{};const resourceProperties=options.parser.parseValue(resourceAttributes.Properties),propsResult=CfnVpcLinkPropsFromCloudFormation(resourceProperties);if(cdk().isResolvableObject(propsResult.value))throw new(cdk_errors()).ValidationError("Unexpected IResolvable",scope);const ret=new CfnVpcLink(scope,id,propsResult.value);for(const[propKey,propVal]of Object.entries(propsResult.extraProperties))ret.addPropertyOverride(propKey,propVal);return options.parser.handleAttributes(ret,resourceAttributes,id),ret}constructor(scope,id,props){super(scope,id,{type:CfnVpcLink.CFN_RESOURCE_TYPE_NAME,properties:props});try{jsiiDeprecationWarnings().aws_cdk_lib_aws_apigateway_CfnVpcLinkProps(props)}catch(error){throw process.env.JSII_DEBUG!=="1"&&error.name==="DeprecationError"&&Error.captureStackTrace(error,CfnVpcLink),error}cdk().requireProperty(props,"name",this),cdk().requireProperty(props,"targetArns",this),this.attrVpcLinkId=cdk().Token.asString(this.getAtt("VpcLinkId",cdk().ResolutionTypeHint.STRING)),this.description=props.description,this.name=props.name,this.tags=new(cdk()).TagManager(cdk().TagType.STANDARD,"AWS::ApiGateway::VpcLink",props.tags,{tagPropertyName:"tags"}),this.tagsRaw=props.tags,this.targetArns=props.targetArns}get cfnProperties(){return{description:this.description,name:this.name,tags:this.tags.renderTags(),targetArns:this.targetArns}}inspect(inspector){try{jsiiDeprecationWarnings().aws_cdk_lib_TreeInspector(inspector)}catch(error){throw process.env.JSII_DEBUG!=="1"&&error.name==="DeprecationError"&&Error.captureStackTrace(error,this.inspect),error}inspector.addAttribute("aws:cdk:cloudformation:type",CfnVpcLink.CFN_RESOURCE_TYPE_NAME),inspector.addAttribute("aws:cdk:cloudformation:props",this.cfnProperties)}renderProperties(props){return convertCfnVpcLinkPropsToCloudFormation(props)}}exports.CfnVpcLink=CfnVpcLink,_u=JSII_RTTI_SYMBOL_1,CfnVpcLink[_u]={fqn:"aws-cdk-lib.aws_apigateway.CfnVpcLink",version:"2.201.0"},CfnVpcLink.CFN_RESOURCE_TYPE_NAME="AWS::ApiGateway::VpcLink";function CfnVpcLinkPropsValidator(properties){if(!cdk().canInspect(properties))return cdk().VALIDATION_SUCCESS;const errors=new(cdk()).ValidationResults;return properties&&typeof properties=="object"&&!Array.isArray(properties)||errors.collect(new(cdk()).ValidationResult("Expected an object, but received: "+JSON.stringify(properties))),errors.collect(cdk().propertyValidator("description",cdk().validateString)(properties.description)),errors.collect(cdk().propertyValidator("name",cdk().requiredValidator)(properties.name)),errors.collect(cdk().propertyValidator("name",cdk().validateString)(properties.name)),errors.collect(cdk().propertyValidator("tags",cdk().listValidator(cdk().validateCfnTag))(properties.tags)),errors.collect(cdk().propertyValidator("targetArns",cdk().requiredValidator)(properties.targetArns)),errors.collect(cdk().propertyValidator("targetArns",cdk().listValidator(cdk().validateString))(properties.targetArns)),errors.wrap('supplied properties not correct for "CfnVpcLinkProps"')}function convertCfnVpcLinkPropsToCloudFormation(properties){return cdk().canInspect(properties)?(CfnVpcLinkPropsValidator(properties).assertSuccess(),{Description:cdk().stringToCloudFormation(properties.description),Name:cdk().stringToCloudFormation(properties.name),Tags:cdk().listMapper(cdk().cfnTagToCloudFormation)(properties.tags),TargetArns:cdk().listMapper(cdk().stringToCloudFormation)(properties.targetArns)}):properties}function CfnVpcLinkPropsFromCloudFormation(properties){if(cdk().isResolvableObject(properties))return new(cfn_parse()).FromCloudFormationResult(properties);if(properties=properties??{},!(properties&&typeof properties=="object"&&!Array.isArray(properties)))return new(cfn_parse()).FromCloudFormationResult(properties);const ret=new(cfn_parse()).FromCloudFormationPropertyObject;return ret.addPropertyResult("description","Description",properties.Description!=null?cfn_parse().FromCloudFormation.getString(properties.Description):void 0),ret.addPropertyResult("name","Name",properties.Name!=null?cfn_parse().FromCloudFormation.getString(properties.Name):void 0),ret.addPropertyResult("tags","Tags",properties.Tags!=null?cfn_parse().FromCloudFormation.getArray(cfn_parse().FromCloudFormation.getCfnTag)(properties.Tags):void 0),ret.addPropertyResult("targetArns","TargetArns",properties.TargetArns!=null?cfn_parse().FromCloudFormation.getArray(cfn_parse().FromCloudFormation.getString)(properties.TargetArns):void 0),ret.addUnrecognizedPropertiesAsExtra(properties),ret}class CfnBasePathMappingV2 extends cdk().CfnResource{static _fromCloudFormation(scope,id,resourceAttributes,options){resourceAttributes=resourceAttributes||{};const resourceProperties=options.parser.parseValue(resourceAttributes.Properties),propsResult=CfnBasePathMappingV2PropsFromCloudFormation(resourceProperties);if(cdk().isResolvableObject(propsResult.value))throw new(cdk_errors()).ValidationError("Unexpected IResolvable",scope);const ret=new CfnBasePathMappingV2(scope,id,propsResult.value);for(const[propKey,propVal]of Object.entries(propsResult.extraProperties))ret.addPropertyOverride(propKey,propVal);return options.parser.handleAttributes(ret,resourceAttributes,id),ret}constructor(scope,id,props){super(scope,id,{type:CfnBasePathMappingV2.CFN_RESOURCE_TYPE_NAME,properties:props});try{jsiiDeprecationWarnings().aws_cdk_lib_aws_apigateway_CfnBasePathMappingV2Props(props)}catch(error){throw process.env.JSII_DEBUG!=="1"&&error.name==="DeprecationError"&&Error.captureStackTrace(error,CfnBasePathMappingV2),error}cdk().requireProperty(props,"domainNameArn",this),cdk().requireProperty(props,"restApiId",this),this.attrBasePathMappingArn=cdk().Token.asString(this.getAtt("BasePathMappingArn",cdk().ResolutionTypeHint.STRING)),this.basePath=props.basePath,this.domainNameArn=props.domainNameArn,this.restApiId=props.restApiId,this.stage=props.stage}get cfnProperties(){return{basePath:this.basePath,domainNameArn:this.domainNameArn,restApiId:this.restApiId,stage:this.stage}}inspect(inspector){try{jsiiDeprecationWarnings().aws_cdk_lib_TreeInspector(inspector)}catch(error){throw process.env.JSII_DEBUG!=="1"&&error.name==="DeprecationError"&&Error.captureStackTrace(error,this.inspect),error}inspector.addAttribute("aws:cdk:cloudformation:type",CfnBasePathMappingV2.CFN_RESOURCE_TYPE_NAME),inspector.addAttribute("aws:cdk:cloudformation:props",this.cfnProperties)}renderProperties(props){return convertCfnBasePathMappingV2PropsToCloudFormation(props)}}exports.CfnBasePathMappingV2=CfnBasePathMappingV2,_v=JSII_RTTI_SYMBOL_1,CfnBasePathMappingV2[_v]={fqn:"aws-cdk-lib.aws_apigateway.CfnBasePathMappingV2",version:"2.201.0"},CfnBasePathMappingV2.CFN_RESOURCE_TYPE_NAME="AWS::ApiGateway::BasePathMappingV2";function CfnBasePathMappingV2PropsValidator(properties){if(!cdk().canInspect(properties))return cdk().VALIDATION_SUCCESS;const errors=new(cdk()).ValidationResults;return properties&&typeof properties=="object"&&!Array.isArray(properties)||errors.collect(new(cdk()).ValidationResult("Expected an object, but received: "+JSON.stringify(properties))),errors.collect(cdk().propertyValidator("basePath",cdk().validateString)(properties.basePath)),errors.collect(cdk().propertyValidator("domainNameArn",cdk().requiredValidator)(properties.domainNameArn)),errors.collect(cdk().propertyValidator("domainNameArn",cdk().validateString)(properties.domainNameArn)),errors.collect(cdk().propertyValidator("restApiId",cdk().requiredValidator)(properties.restApiId)),errors.collect(cdk().propertyValidator("restApiId",cdk().validateString)(properties.restApiId)),errors.collect(cdk().propertyValidator("stage",cdk().validateString)(properties.stage)),errors.wrap('supplied properties not correct for "CfnBasePathMappingV2Props"')}function convertCfnBasePathMappingV2PropsToCloudFormation(properties){return cdk().canInspect(properties)?(CfnBasePathMappingV2PropsValidator(properties).assertSuccess(),{BasePath:cdk().stringToCloudFormation(properties.basePath),DomainNameArn:cdk().stringToCloudFormation(properties.domainNameArn),RestApiId:cdk().stringToCloudFormation(properties.restApiId),Stage:cdk().stringToCloudFormation(properties.stage)}):properties}function CfnBasePathMappingV2PropsFromCloudFormation(properties){if(cdk().isResolvableObject(properties))return new(cfn_parse()).FromCloudFormationResult(properties);if(properties=properties??{},!(properties&&typeof properties=="object"&&!Array.isArray(properties)))return new(cfn_parse()).FromCloudFormationResult(properties);const ret=new(cfn_parse()).FromCloudFormationPropertyObject;return ret.addPropertyResult("basePath","BasePath",properties.BasePath!=null?cfn_parse().FromCloudFormation.getString(properties.BasePath):void 0),ret.addPropertyResult("domainNameArn","DomainNameArn",properties.DomainNameArn!=null?cfn_parse().FromCloudFormation.getString(properties.DomainNameArn):void 0),ret.addPropertyResult("restApiId","RestApiId",properties.RestApiId!=null?cfn_parse().FromCloudFormation.getString(properties.RestApiId):void 0),ret.addPropertyResult("stage","Stage",properties.Stage!=null?cfn_parse().FromCloudFormation.getString(properties.Stage):void 0),ret.addUnrecognizedPropertiesAsExtra(properties),ret}class CfnDomainNameAccessAssociation extends cdk().CfnResource{static _fromCloudFormation(scope,id,resourceAttributes,options){resourceAttributes=resourceAttributes||{};const resourceProperties=options.parser.parseValue(resourceAttributes.Properties),propsResult=CfnDomainNameAccessAssociationPropsFromCloudFormation(resourceProperties);if(cdk().isResolvableObject(propsResult.value))throw new(cdk_errors()).ValidationError("Unexpected IResolvable",scope);const ret=new CfnDomainNameAccessAssociation(scope,id,propsResult.value);for(const[propKey,propVal]of Object.entries(propsResult.extraProperties))ret.addPropertyOverride(propKey,propVal);return options.parser.handleAttributes(ret,resourceAttributes,id),ret}constructor(scope,id,props){super(scope,id,{type:CfnDomainNameAccessAssociation.CFN_RESOURCE_TYPE_NAME,properties:props});try{jsiiDeprecationWarnings().aws_cdk_lib_aws_apigateway_CfnDomainNameAccessAssociationProps(props)}catch(error){throw process.env.JSII_DEBUG!=="1"&&error.name==="DeprecationError"&&Error.captureStackTrace(error,CfnDomainNameAccessAssociation),error}cdk().requireProperty(props,"accessAssociationSource",this),cdk().requireProperty(props,"accessAssociationSourceType",this),cdk().requireProperty(props,"domainNameArn",this),this.attrDomainNameAccessAssociationArn=cdk().Token.asString(this.getAtt("DomainNameAccessAssociationArn",cdk().ResolutionTypeHint.STRING)),this.accessAssociationSource=props.accessAssociationSource,this.accessAssociationSourceType=props.accessAssociationSourceType,this.cdkTagManager=new(cdk()).TagManager(cdk().TagType.STANDARD,"AWS::ApiGateway::DomainNameAccessAssociation",void 0,{tagPropertyName:"tags"}),this.domainNameArn=props.domainNameArn,this.tags=props.tags}get cfnProperties(){return{accessAssociationSource:this.accessAssociationSource,accessAssociationSourceType:this.accessAssociationSourceType,tags:this.cdkTagManager.renderTags(this.tags),domainNameArn:this.domainNameArn}}inspect(inspector){try{jsiiDeprecationWarnings().aws_cdk_lib_TreeInspector(inspector)}catch(error){throw process.env.JSII_DEBUG!=="1"&&error.name==="DeprecationError"&&Error.captureStackTrace(error,this.inspect),error}inspector.addAttribute("aws:cdk:cloudformation:type",CfnDomainNameAccessAssociation.CFN_RESOURCE_TYPE_NAME),inspector.addAttribute("aws:cdk:cloudformation:props",this.cfnProperties)}renderProperties(props){return convertCfnDomainNameAccessAssociationPropsToCloudFormation(props)}}exports.CfnDomainNameAccessAssociation=CfnDomainNameAccessAssociation,_w=JSII_RTTI_SYMBOL_1,CfnDomainNameAccessAssociation[_w]={fqn:"aws-cdk-lib.aws_apigateway.CfnDomainNameAccessAssociation",version:"2.201.0"},CfnDomainNameAccessAssociation.CFN_RESOURCE_TYPE_NAME="AWS::ApiGateway::DomainNameAccessAssociation";function CfnDomainNameAccessAssociationPropsValidator(properties){if(!cdk().canInspect(properties))return cdk().VALIDATION_SUCCESS;const errors=new(cdk()).ValidationResults;return properties&&typeof properties=="object"&&!Array.isArray(properties)||errors.collect(new(cdk()).ValidationResult("Expected an object, but received: "+JSON.stringify(properties))),errors.collect(cdk().propertyValidator("accessAssociationSource",cdk().requiredValidator)(properties.accessAssociationSource)),errors.collect(cdk().propertyValidator("accessAssociationSource",cdk().validateString)(properties.accessAssociationSource)),errors.collect(cdk().propertyValidator("accessAssociationSourceType",cdk().requiredValidator)(properties.accessAssociationSourceType)),errors.collect(cdk().propertyValidator("accessAssociationSourceType",cdk().validateString)(properties.accessAssociationSourceType)),errors.collect(cdk().propertyValidator("domainNameArn",cdk().requiredValidator)(properties.domainNameArn)),errors.collect(cdk().propertyValidator("domainNameArn",cdk().validateString)(properties.domainNameArn)),errors.collect(cdk().propertyValidator("tags",cdk().listValidator(cdk().validateCfnTag))(properties.tags)),errors.wrap('supplied properties not correct for "CfnDomainNameAccessAssociationProps"')}function convertCfnDomainNameAccessAssociationPropsToCloudFormation(properties){return cdk().canInspect(properties)?(CfnDomainNameAccessAssociationPropsValidator(properties).assertSuccess(),{AccessAssociationSource:cdk().stringToCloudFormation(properties.accessAssociationSource),AccessAssociationSourceType:cdk().stringToCloudFormation(properties.accessAssociationSourceType),DomainNameArn:cdk().stringToCloudFormation(properties.domainNameArn),Tags:cdk().listMapper(cdk().cfnTagToCloudFormation)(properties.tags)}):properties}function CfnDomainNameAccessAssociationPropsFromCloudFormation(properties){if(cdk().isResolvableObject(properties))return new(cfn_parse()).FromCloudFormationResult(properties);if(properties=properties??{},!(properties&&typeof properties=="object"&&!Array.isArray(properties)))return new(cfn_parse()).FromCloudFormationResult(properties);const ret=new(cfn_parse()).FromCloudFormationPropertyObject;return ret.addPropertyResult("accessAssociationSource","AccessAssociationSource",properties.AccessAssociationSource!=null?cfn_parse().FromCloudFormation.getString(properties.AccessAssociationSource):void 0),ret.addPropertyResult("accessAssociationSourceType","AccessAssociationSourceType",properties.AccessAssociationSourceType!=null?cfn_parse().FromCloudFormation.getString(properties.AccessAssociationSourceType):void 0),ret.addPropertyResult("domainNameArn","DomainNameArn",properties.DomainNameArn!=null?cfn_parse().FromCloudFormation.getString(properties.DomainNameArn):void 0),ret.addPropertyResult("tags","Tags",properties.Tags!=null?cfn_parse().FromCloudFormation.getArray(cfn_parse().FromCloudFormation.getCfnTag)(properties.Tags):void 0),ret.addUnrecognizedPropertiesAsExtra(properties),ret}class CfnDomainNameV2 extends cdk().CfnResource{static _fromCloudFormation(scope,id,resourceAttributes,options){resourceAttributes=resourceAttributes||{};const resourceProperties=options.parser.parseValue(resourceAttributes.Properties),propsResult=CfnDomainNameV2PropsFromCloudFormation(resourceProperties);if(cdk().isResolvableObject(propsResult.value))throw new(cdk_errors()).ValidationError("Unexpected IResolvable",scope);const ret=new CfnDomainNameV2(scope,id,propsResult.value);for(const[propKey,propVal]of Object.entries(propsResult.extraProperties))ret.addPropertyOverride(propKey,propVal);return options.parser.handleAttributes(ret,resourceAttributes,id),ret}constructor(scope,id,props={}){super(scope,id,{type:CfnDomainNameV2.CFN_RESOURCE_TYPE_NAME,properties:props});try{jsiiDeprecationWarnings().aws_cdk_lib_aws_apigateway_CfnDomainNameV2Props(props)}catch(error){throw process.env.JSII_DEBUG!=="1"&&error.name==="DeprecationError"&&Error.captureStackTrace(error,CfnDomainNameV2),error}this.attrDomainNameArn=cdk().Token.asString(this.getAtt("DomainNameArn",cdk().ResolutionTypeHint.STRING)),this.attrDomainNameId=cdk().Token.asString(this.getAtt("DomainNameId",cdk().ResolutionTypeHint.STRING)),this.cdkTagManager=new(cdk()).TagManager(cdk().TagType.STANDARD,"AWS::ApiGateway::DomainNameV2",void 0,{tagPropertyName:"tags"}),this.certificateArn=props.certificateArn,this.domainName=props.domainName,this.endpointConfiguration=props.endpointConfiguration,this.policy=props.policy,this.routingMode=props.routingMode,this.securityPolicy=props.securityPolicy,this.tags=props.tags}get cfnProperties(){return{tags:this.cdkTagManager.renderTags(this.tags),certificateArn:this.certificateArn,domainName:this.domainName,endpointConfiguration:this.endpointConfiguration,policy:this.policy,routingMode:this.routingMode,securityPolicy:this.securityPolicy}}inspect(inspector){try{jsiiDeprecationWarnings().aws_cdk_lib_TreeInspector(inspector)}catch(error){throw process.env.JSII_DEBUG!=="1"&&error.name==="DeprecationError"&&Error.captureStackTrace(error,this.inspect),error}inspector.addAttribute("aws:cdk:cloudformation:type",CfnDomainNameV2.CFN_RESOURCE_TYPE_NAME),inspector.addAttribute("aws:cdk:cloudformation:props",this.cfnProperties)}renderProperties(props){return convertCfnDomainNameV2PropsToCloudFormation(props)}}exports.CfnDomainNameV2=CfnDomainNameV2,_x=JSII_RTTI_SYMBOL_1,CfnDomainNameV2[_x]={fqn:"aws-cdk-lib.aws_apigateway.CfnDomainNameV2",version:"2.201.0"},CfnDomainNameV2.CFN_RESOURCE_TYPE_NAME="AWS::ApiGateway::DomainNameV2";function CfnDomainNameV2EndpointConfigurationPropertyValidator(properties){if(!cdk().canInspect(properties))return cdk().VALIDATION_SUCCESS;const errors=new(cdk()).ValidationResults;return properties&&typeof properties=="object"&&!Array.isArray(properties)||errors.collect(new(cdk()).ValidationResult("Expected an object, but received: "+JSON.stringify(properties))),errors.collect(cdk().propertyValidator("ipAddressType",cdk().validateString)(properties.ipAddressType)),errors.collect(cdk().propertyValidator("types",cdk().listValidator(cdk().validateString))(properties.types)),errors.wrap('supplied properties not correct for "EndpointConfigurationProperty"')}function convertCfnDomainNameV2EndpointConfigurationPropertyToCloudFormation(properties){return cdk().canInspect(properties)?(CfnDomainNameV2EndpointConfigurationPropertyValidator(properties).assertSuccess(),{IpAddressType:cdk().stringToCloudFormation(properties.ipAddressType),Types:cdk().listMapper(cdk().stringToCloudFormation)(properties.types)}):properties}function CfnDomainNameV2EndpointConfigurationPropertyFromCloudFormation(properties){if(cdk().isResolvableObject(properties))return new(cfn_parse()).FromCloudFormationResult(properties);if(properties=properties??{},!(properties&&typeof properties=="object"&&!Array.isArray(properties)))return new(cfn_parse()).FromCloudFormationResult(properties);const ret=new(cfn_parse()).FromCloudFormationPropertyObject;return ret.addPropertyResult("ipAddressType","IpAddressType",properties.IpAddressType!=null?cfn_parse().FromCloudFormation.getString(properties.IpAddressType):void 0),ret.addPropertyResult("types","Types",properties.Types!=null?cfn_parse().FromCloudFormation.getArray(cfn_parse().FromCloudFormation.getString)(properties.Types):void 0),ret.addUnrecognizedPropertiesAsExtra(properties),ret}function CfnDomainNameV2PropsValidator(properties){if(!cdk().canInspect(properties))return cdk().VALIDATION_SUCCESS;const errors=new(cdk()).ValidationResults;return properties&&typeof properties=="object"&&!Array.isArray(properties)||errors.collect(new(cdk()).ValidationResult("Expected an object, but received: "+JSON.stringify(properties))),errors.collect(cdk().propertyValidator("certificateArn",cdk().validateString)(properties.certificateArn)),errors.collect(cdk().propertyValidator("domainName",cdk().validateString)(properties.domainName)),errors.collect(cdk().propertyValidator("endpointConfiguration",CfnDomainNameV2EndpointConfigurationPropertyValidator)(properties.endpointConfiguration)),errors.collect(cdk().propertyValidator("policy",cdk().unionValidator(cdk().validateString,cdk().validateObject))(properties.policy)),errors.collect(cdk().propertyValidator("routingMode",cdk().validateString)(properties.routingMode)),errors.collect(cdk().propertyValidator("securityPolicy",cdk().validateString)(properties.securityPolicy)),errors.collect(cdk().propertyValidator("tags",cdk().listValidator(cdk().validateCfnTag))(properties.tags)),errors.wrap('supplied properties not correct for "CfnDomainNameV2Props"')}function convertCfnDomainNameV2PropsToCloudFormation(properties){return cdk().canInspect(properties)?(CfnDomainNameV2PropsValidator(properties).assertSuccess(),{CertificateArn:cdk().stringToCloudFormation(properties.certificateArn),DomainName:cdk().stringToCloudFormation(properties.domainName),EndpointConfiguration:convertCfnDomainNameV2EndpointConfigurationPropertyToCloudFormation(properties.endpointConfiguration),Policy:cdk().unionMapper([cdk().validateString,cdk().validateObject],[cdk().stringToCloudFormation,cdk().objectToCloudFormation])(properties.policy),RoutingMode:cdk().stringToCloudFormation(properties.routingMode),SecurityPolicy:cdk().stringToCloudFormation(properties.securityPolicy),Tags:cdk().listMapper(cdk().cfnTagToCloudFormation)(properties.tags)}):properties}function CfnDomainNameV2PropsFromCloudFormation(properties){if(cdk().isResolvableObject(properties))return new(cfn_parse()).FromCloudFormationResult(properties);if(properties=properties??{},!(properties&&typeof properties=="object"&&!Array.isArray(properties)))return new(cfn_parse()).FromCloudFormationResult(properties);const ret=new(cfn_parse()).FromCloudFormationPropertyObject;return ret.addPropertyResult("certificateArn","CertificateArn",properties.CertificateArn!=null?cfn_parse().FromCloudFormation.getString(properties.CertificateArn):void 0),ret.addPropertyResult("domainName","DomainName",properties.DomainName!=null?cfn_parse().FromCloudFormation.getString(properties.DomainName):void 0),ret.addPropertyResult("endpointConfiguration","EndpointConfiguration",properties.EndpointConfiguration!=null?CfnDomainNameV2EndpointConfigurationPropertyFromCloudFormation(properties.EndpointConfiguration):void 0),ret.addPropertyResult("policy","Policy",properties.Policy!=null?cfn_parse().FromCloudFormation.getTypeUnion([cdk().validateString,cdk().validateObject],[cfn_parse().FromCloudFormation.getString,cfn_parse().FromCloudFormation.getAny])(properties.Policy):void 0),ret.addPropertyResult("routingMode","RoutingMode",properties.RoutingMode!=null?cfn_parse().FromCloudFormation.getString(properties.RoutingMode):void 0),ret.addPropertyResult("securityPolicy","SecurityPolicy",properties.SecurityPolicy!=null?cfn_parse().FromCloudFormation.getString(properties.SecurityPolicy):void 0),ret.addPropertyResult("tags","Tags",properties.Tags!=null?cfn_parse().FromCloudFormation.getArray(cfn_parse().FromCloudFormation.getCfnTag)(properties.Tags):void 0),ret.addUnrecognizedPropertiesAsExtra(properties),ret}
