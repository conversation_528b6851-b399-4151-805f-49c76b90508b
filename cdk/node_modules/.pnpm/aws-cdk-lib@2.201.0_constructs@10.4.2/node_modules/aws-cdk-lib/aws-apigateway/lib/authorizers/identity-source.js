"use strict";var _a;Object.defineProperty(exports,"__esModule",{value:!0}),exports.IdentitySource=void 0;const JSII_RTTI_SYMBOL_1=Symbol.for("jsii.rtti");var errors_1=()=>{var tmp=require("../../../core/lib/errors");return errors_1=()=>tmp,tmp};class IdentitySource{static header(headerName){return IdentitySource.toString(headerName,"method.request.header")}static queryString(queryString){return IdentitySource.toString(queryString,"method.request.querystring")}static stageVariable(stageVariable){return IdentitySource.toString(stageVariable,"stageVariables")}static context(context){return IdentitySource.toString(context,"context")}static toString(source,type){if(!source.trim())throw new(errors_1()).UnscopedValidationError("IdentitySources must be a non-empty string.");return`${type}.${source}`}}exports.IdentitySource=IdentitySource,_a=JSII_RTTI_SYMBOL_1,IdentitySource[_a]={fqn:"aws-cdk-lib.aws_apigateway.IdentitySource",version:"2.201.0"};
