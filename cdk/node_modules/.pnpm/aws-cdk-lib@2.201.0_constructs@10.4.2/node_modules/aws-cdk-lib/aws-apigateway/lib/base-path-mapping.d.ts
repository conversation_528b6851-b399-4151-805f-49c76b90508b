import { Construct } from 'constructs';
import { IDomainName } from './domain-name';
import { IRestApi } from './restapi';
import { Stage } from './stage';
import { Resource } from '../../core';
export interface BasePathMappingOptions {
    /**
     * The base path name that callers of the API must provide in the URL after
     * the domain name (e.g. `example.com/base-path`). If you specify this
     * property, it can't be an empty string.
     *
     * @default - map requests from the domain root (e.g. `example.com`). If this
     * is undefined, no additional mappings will be allowed on this domain name.
     */
    readonly basePath?: string;
    /**
     * The Deployment stage of API
     * [disable-awslint:ref-via-interface]
     * @default - map to deploymentStage of restApi otherwise stage needs to pass in URL
     */
    readonly stage?: Stage;
    /**
     * Whether to attach the base path mapping to a stage.
     * Use this property to create a base path mapping without attaching it to the Rest API default stage.
     * This property is ignored if `stage` is provided.
     * @default - true
     */
    readonly attachToStage?: boolean;
}
export interface BasePathMappingProps extends BasePathMappingOptions {
    /**
     * The DomainName to associate with this base path mapping.
     */
    readonly domainName: IDomainName;
    /**
     * The RestApi resource to target.
     */
    readonly restApi: IRestApi;
}
/**
 * This resource creates a base path that clients who call your API must use in
 * the invocation URL.
 *
 * Unless you're importing a domain with `DomainName.fromDomainNameAttributes()`,
 * you can use `DomainName.addBasePathMapping()` to define mappings.
 */
export declare class BasePathMapping extends Resource {
    /** Uniquely identifies this class. */
    static readonly PROPERTY_INJECTION_ID: string;
    constructor(scope: Construct, id: string, props: BasePathMappingProps);
}
