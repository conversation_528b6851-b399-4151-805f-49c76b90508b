"use strict";var _a;Object.defineProperty(exports,"__esModule",{value:!0}),exports.ConnectionType=exports.PassthroughBehavior=exports.IntegrationType=exports.ContentHandling=exports.Integration=void 0;var jsiiDeprecationWarnings=()=>{var tmp=require("../../.warnings.jsii.js");return jsiiDeprecationWarnings=()=>tmp,tmp};const JSII_RTTI_SYMBOL_1=Symbol.for("jsii.rtti");var vpc_link_1=()=>{var tmp=require("./vpc-link");return vpc_link_1=()=>tmp,tmp},core_1=()=>{var tmp=require("../../core");return core_1=()=>tmp,tmp},errors_1=()=>{var tmp=require("../../core/lib/errors");return errors_1=()=>tmp,tmp};class Integration{constructor(props){this.props=props;try{jsiiDeprecationWarnings().aws_cdk_lib_aws_apigateway_IntegrationProps(props)}catch(error){throw process.env.JSII_DEBUG!=="1"&&error.name==="DeprecationError"&&Error.captureStackTrace(error,Integration),error}const options=this.props.options||{};if(options.credentialsPassthrough!==void 0&&options.credentialsRole!==void 0)throw new(errors_1()).UnscopedValidationError("'credentialsPassthrough' and 'credentialsRole' are mutually exclusive");if(options.connectionType===ConnectionType.VPC_LINK&&options.vpcLink===void 0)throw new(errors_1()).UnscopedValidationError("'connectionType' of VPC_LINK requires 'vpcLink' prop to be set");if(options.connectionType===ConnectionType.INTERNET&&options.vpcLink!==void 0)throw new(errors_1()).UnscopedValidationError("cannot set 'vpcLink' where 'connectionType' is INTERNET");if(options.timeout&&!options.timeout.isUnresolved()&&options.timeout.toMilliseconds()<50)throw new(errors_1()).UnscopedValidationError("Integration timeout must be greater than 50 milliseconds.");if(props.type!==IntegrationType.MOCK&&!props.integrationHttpMethod)throw new(errors_1()).UnscopedValidationError("integrationHttpMethod is required for non-mock integration types.")}bind(method){try{jsiiDeprecationWarnings().aws_cdk_lib_aws_apigateway_Method(method)}catch(error){throw process.env.JSII_DEBUG!=="1"&&error.name==="DeprecationError"&&Error.captureStackTrace(error,this.bind),error}let uri=this.props.uri;const options=this.props.options;return options?.connectionType===ConnectionType.VPC_LINK&&uri===void 0&&(uri=core_1().Lazy.string({produce:()=>{const vpcLink=options.vpcLink;if(vpcLink instanceof vpc_link_1().VpcLink){const targets=vpcLink._targetDnsNames;if(targets.length>1)throw new(errors_1()).ValidationError("'uri' is required when there are more than one NLBs in the VPC Link",method);return`http://${targets[0]}`}else throw new(errors_1()).ValidationError("'uri' is required when the 'connectionType' is VPC_LINK",method)}})),{options:{...options,connectionType:options?.vpcLink?ConnectionType.VPC_LINK:options?.connectionType},type:this.props.type,uri,integrationHttpMethod:this.props.integrationHttpMethod}}}exports.Integration=Integration,_a=JSII_RTTI_SYMBOL_1,Integration[_a]={fqn:"aws-cdk-lib.aws_apigateway.Integration",version:"2.201.0"};var ContentHandling;(function(ContentHandling2){ContentHandling2.CONVERT_TO_BINARY="CONVERT_TO_BINARY",ContentHandling2.CONVERT_TO_TEXT="CONVERT_TO_TEXT"})(ContentHandling||(exports.ContentHandling=ContentHandling={}));var IntegrationType;(function(IntegrationType2){IntegrationType2.AWS="AWS",IntegrationType2.AWS_PROXY="AWS_PROXY",IntegrationType2.HTTP="HTTP",IntegrationType2.HTTP_PROXY="HTTP_PROXY",IntegrationType2.MOCK="MOCK"})(IntegrationType||(exports.IntegrationType=IntegrationType={}));var PassthroughBehavior;(function(PassthroughBehavior2){PassthroughBehavior2.WHEN_NO_MATCH="WHEN_NO_MATCH",PassthroughBehavior2.NEVER="NEVER",PassthroughBehavior2.WHEN_NO_TEMPLATES="WHEN_NO_TEMPLATES"})(PassthroughBehavior||(exports.PassthroughBehavior=PassthroughBehavior={}));var ConnectionType;(function(ConnectionType2){ConnectionType2.INTERNET="INTERNET",ConnectionType2.VPC_LINK="VPC_LINK"})(ConnectionType||(exports.ConnectionType=ConnectionType={}));
