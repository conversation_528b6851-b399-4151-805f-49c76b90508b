"use strict";var __decorate=exports&&exports.__decorate||function(decorators,target,key,desc){var c=arguments.length,r=c<3?target:desc===null?desc=Object.getOwnPropertyDescriptor(target,key):desc,d;if(typeof Reflect=="object"&&typeof Reflect.decorate=="function")r=Reflect.decorate(decorators,target,key,desc);else for(var i=decorators.length-1;i>=0;i--)(d=decorators[i])&&(r=(c<3?d(r):c>3?d(target,key,r):d(target,key))||r);return c>3&&r&&Object.defineProperty(target,key,r),r},_a,_b,_c;Object.defineProperty(exports,"__esModule",{value:!0}),exports.RestApiMode=exports.EndpointType=exports.ApiKeySourceType=exports.IpAddressType=exports.RestApi=exports.SpecRestApi=exports.RestApiBase=void 0;var jsiiDeprecationWarnings=()=>{var tmp=require("../../.warnings.jsii.js");return jsiiDeprecationWarnings=()=>tmp,tmp};const JSII_RTTI_SYMBOL_1=Symbol.for("jsii.rtti");var api_key_1=()=>{var tmp=require("./api-key");return api_key_1=()=>tmp,tmp},apigateway_canned_metrics_generated_1=()=>{var tmp=require("./apigateway-canned-metrics.generated");return apigateway_canned_metrics_generated_1=()=>tmp,tmp},apigateway_generated_1=()=>{var tmp=require("./apigateway.generated");return apigateway_generated_1=()=>tmp,tmp},deployment_1=()=>{var tmp=require("./deployment");return deployment_1=()=>tmp,tmp},domain_name_1=()=>{var tmp=require("./domain-name");return domain_name_1=()=>tmp,tmp},gateway_response_1=()=>{var tmp=require("./gateway-response");return gateway_response_1=()=>tmp,tmp},model_1=()=>{var tmp=require("./model");return model_1=()=>tmp,tmp},requestvalidator_1=()=>{var tmp=require("./requestvalidator");return requestvalidator_1=()=>tmp,tmp},resource_1=()=>{var tmp=require("./resource");return resource_1=()=>tmp,tmp},stage_1=()=>{var tmp=require("./stage");return stage_1=()=>tmp,tmp},usage_plan_1=()=>{var tmp=require("./usage-plan");return usage_plan_1=()=>tmp,tmp},cloudwatch=()=>{var tmp=require("../../aws-cloudwatch");return cloudwatch=()=>tmp,tmp},iam=()=>{var tmp=require("../../aws-iam");return iam=()=>tmp,tmp},core_1=()=>{var tmp=require("../../core");return core_1=()=>tmp,tmp},errors_1=()=>{var tmp=require("../../core/lib/errors");return errors_1=()=>tmp,tmp},metadata_resource_1=()=>{var tmp=require("../../core/lib/metadata-resource");return metadata_resource_1=()=>tmp,tmp},prop_injectable_1=()=>{var tmp=require("../../core/lib/prop-injectable");return prop_injectable_1=()=>tmp,tmp},prop_injectors_helpers_1=()=>{var tmp=require("../../core/lib/prop-injectors-helpers");return prop_injectors_helpers_1=()=>tmp,tmp},cx_api_1=()=>{var tmp=require("../../cx-api");return cx_api_1=()=>tmp,tmp};const RESTAPI_SYMBOL=Symbol.for("@aws-cdk/aws-apigateway.RestApiBase"),APIGATEWAY_RESTAPI_SYMBOL=Symbol.for("@aws-cdk/aws-apigateway.RestApi");class RestApiBase extends core_1().Resource{static _isRestApiBase(x){return x!==null&&typeof x=="object"&&RESTAPI_SYMBOL in x}get latestDeployment(){return this._latestDeployment}get domainName(){return this._domainName}get url(){return this.urlForPath()}constructor(scope,id,props={}){try{jsiiDeprecationWarnings().aws_cdk_lib_aws_apigateway_RestApiBaseProps(props)}catch(error){throw process.env.JSII_DEBUG!=="1"&&error.name==="DeprecationError"&&Error.captureStackTrace(error,RestApiBase),error}const restApiName=props.restApiName??id;super(scope,id,{physicalName:restApiName}),this._allowedVpcEndpoints=new Set,this.restApiName=restApiName,Object.defineProperty(this,RESTAPI_SYMBOL,{value:!0})}urlForPath(path="/"){if(!this.deploymentStage)throw new(errors_1()).ValidationError('Cannot determine deployment stage for API from "deploymentStage". Use "deploy" or explicitly set "deploymentStage"',this);return this.deploymentStage.urlForPath(path)}addDomainName(id,options){try{jsiiDeprecationWarnings().aws_cdk_lib_aws_apigateway_DomainNameOptions(options)}catch(error){throw process.env.JSII_DEBUG!=="1"&&error.name==="DeprecationError"&&Error.captureStackTrace(error,this.addDomainName),error}const domainName=new(domain_name_1()).DomainName(this,id,{...options,mapping:this});return this._domainName||(this._domainName=domainName),domainName}addUsagePlan(id,props={}){try{jsiiDeprecationWarnings().aws_cdk_lib_aws_apigateway_UsagePlanProps(props)}catch(error){throw process.env.JSII_DEBUG!=="1"&&error.name==="DeprecationError"&&Error.captureStackTrace(error,this.addUsagePlan),error}return new(usage_plan_1()).UsagePlan(this,id,props)}arnForExecuteApi(method="*",path="/*",stage="*"){if(!core_1().Token.isUnresolved(path)&&!path.startsWith("/"))throw new(errors_1()).ValidationError(`"path" must begin with a "/": '${path}'`,this);return method.toUpperCase()==="ANY"&&(method="*"),core_1().Stack.of(this).formatArn({service:"execute-api",resource:this.restApiId,arnFormat:core_1().ArnFormat.SLASH_RESOURCE_NAME,resourceName:`${stage}/${method}${path}`})}addGatewayResponse(id,options){try{jsiiDeprecationWarnings().aws_cdk_lib_aws_apigateway_GatewayResponseOptions(options)}catch(error){throw process.env.JSII_DEBUG!=="1"&&error.name==="DeprecationError"&&Error.captureStackTrace(error,this.addGatewayResponse),error}return new(gateway_response_1()).GatewayResponse(this,id,{restApi:this,...options})}addApiKey(id,options){try{jsiiDeprecationWarnings().aws_cdk_lib_aws_apigateway_ApiKeyOptions(options)}catch(error){throw process.env.JSII_DEBUG!=="1"&&error.name==="DeprecationError"&&Error.captureStackTrace(error,this.addApiKey),error}return new(api_key_1()).ApiKey(this,id,{stages:[this.deploymentStage],...options})}grantInvokeFromVpcEndpointsOnly(vpcEndpoints){vpcEndpoints.forEach(endpoint=>this._allowedVpcEndpoints.add(endpoint));const endpoints=core_1().Lazy.list({produce:()=>Array.from(this._allowedVpcEndpoints).map(endpoint=>endpoint.vpcEndpointId)});this.addToResourcePolicy(new(iam()).PolicyStatement({principals:[new(iam()).AnyPrincipal],actions:["execute-api:Invoke"],resources:["execute-api:/*"],effect:iam().Effect.DENY,conditions:{StringNotEquals:{"aws:SourceVpce":endpoints}}})),this.addToResourcePolicy(new(iam()).PolicyStatement({principals:[new(iam()).AnyPrincipal],actions:["execute-api:Invoke"],resources:["execute-api:/*"],effect:iam().Effect.ALLOW}))}metric(metricName,props){try{jsiiDeprecationWarnings().aws_cdk_lib_aws_cloudwatch_MetricOptions(props)}catch(error){throw process.env.JSII_DEBUG!=="1"&&error.name==="DeprecationError"&&Error.captureStackTrace(error,this.metric),error}return new(cloudwatch()).Metric({namespace:"AWS/ApiGateway",metricName,dimensionsMap:{ApiName:this.restApiName},...props}).attachTo(this)}metricClientError(props){try{jsiiDeprecationWarnings().aws_cdk_lib_aws_cloudwatch_MetricOptions(props)}catch(error){throw process.env.JSII_DEBUG!=="1"&&error.name==="DeprecationError"&&Error.captureStackTrace(error,this.metricClientError),error}return this.cannedMetric(apigateway_canned_metrics_generated_1().ApiGatewayMetrics._4XxErrorSum,props)}metricServerError(props){try{jsiiDeprecationWarnings().aws_cdk_lib_aws_cloudwatch_MetricOptions(props)}catch(error){throw process.env.JSII_DEBUG!=="1"&&error.name==="DeprecationError"&&Error.captureStackTrace(error,this.metricServerError),error}return this.cannedMetric(apigateway_canned_metrics_generated_1().ApiGatewayMetrics._5XxErrorSum,props)}metricCacheHitCount(props){try{jsiiDeprecationWarnings().aws_cdk_lib_aws_cloudwatch_MetricOptions(props)}catch(error){throw process.env.JSII_DEBUG!=="1"&&error.name==="DeprecationError"&&Error.captureStackTrace(error,this.metricCacheHitCount),error}return this.cannedMetric(apigateway_canned_metrics_generated_1().ApiGatewayMetrics.cacheHitCountSum,props)}metricCacheMissCount(props){try{jsiiDeprecationWarnings().aws_cdk_lib_aws_cloudwatch_MetricOptions(props)}catch(error){throw process.env.JSII_DEBUG!=="1"&&error.name==="DeprecationError"&&Error.captureStackTrace(error,this.metricCacheMissCount),error}return this.cannedMetric(apigateway_canned_metrics_generated_1().ApiGatewayMetrics.cacheMissCountSum,props)}metricCount(props){try{jsiiDeprecationWarnings().aws_cdk_lib_aws_cloudwatch_MetricOptions(props)}catch(error){throw process.env.JSII_DEBUG!=="1"&&error.name==="DeprecationError"&&Error.captureStackTrace(error,this.metricCount),error}return this.cannedMetric(apigateway_canned_metrics_generated_1().ApiGatewayMetrics.countSum,{statistic:"SampleCount",...props})}metricIntegrationLatency(props){try{jsiiDeprecationWarnings().aws_cdk_lib_aws_cloudwatch_MetricOptions(props)}catch(error){throw process.env.JSII_DEBUG!=="1"&&error.name==="DeprecationError"&&Error.captureStackTrace(error,this.metricIntegrationLatency),error}return this.cannedMetric(apigateway_canned_metrics_generated_1().ApiGatewayMetrics.integrationLatencyAverage,props)}metricLatency(props){try{jsiiDeprecationWarnings().aws_cdk_lib_aws_cloudwatch_MetricOptions(props)}catch(error){throw process.env.JSII_DEBUG!=="1"&&error.name==="DeprecationError"&&Error.captureStackTrace(error,this.metricLatency),error}return this.cannedMetric(apigateway_canned_metrics_generated_1().ApiGatewayMetrics.latencyAverage,props)}_attachMethod(method){}_attachDeployment(deployment){}_attachStage(stage){this.cloudWatchAccount&&stage.node.addDependency(this.cloudWatchAccount)}_configureCloudWatchRole(apiResource,cloudWatchRole,cloudWatchRoleRemovalPolicy){const cloudWatchRoleDefault=!core_1().FeatureFlags.of(this).isEnabled(cx_api_1().APIGATEWAY_DISABLE_CLOUDWATCH_ROLE);if(cloudWatchRole=cloudWatchRole??cloudWatchRoleDefault,!cloudWatchRole){if(cloudWatchRoleRemovalPolicy)throw new(errors_1()).ValidationError("'cloudWatchRole' must be enabled for 'cloudWatchRoleRemovalPolicy' to be applied.",this);return}cloudWatchRoleRemovalPolicy=cloudWatchRoleRemovalPolicy??core_1().RemovalPolicy.RETAIN;const role=new(iam()).Role(this,"CloudWatchRole",{assumedBy:new(iam()).ServicePrincipal("apigateway.amazonaws.com"),managedPolicies:[iam().ManagedPolicy.fromAwsManagedPolicyName("service-role/AmazonAPIGatewayPushToCloudWatchLogs")]});role.applyRemovalPolicy(cloudWatchRoleRemovalPolicy),this.cloudWatchAccount=new(apigateway_generated_1()).CfnAccount(this,"Account",{cloudWatchRoleArn:role.roleArn}),this.cloudWatchAccount.applyRemovalPolicy(cloudWatchRoleRemovalPolicy),this.cloudWatchAccount.node.addDependency(apiResource)}configureCloudWatchRole(apiResource){this._configureCloudWatchRole(apiResource)}configureDeployment(props){this._configureDeployment(props)}_configureDeployment(props){if(props.deploy??!0){this._latestDeployment=new(deployment_1()).Deployment(this,"Deployment",{description:props.deployOptions?.description??props.description??"Automatically created by the RestApi construct",api:this,retainDeployments:props.retainDeployments});const stageName=props.deployOptions&&props.deployOptions.stageName||"prod";this.deploymentStage=new(stage_1()).Stage(this,`DeploymentStage.${stageName}`,{deployment:this._latestDeployment,...props.deployOptions}),new(core_1()).CfnOutput(this,"Endpoint",{exportName:props.endpointExportName,value:this.urlForPath()})}else if(props.deployOptions)throw new(errors_1()).ValidationError("Cannot set 'deployOptions' if 'deploy' is disabled",this)}_configureEndpoints(props){if(props.endpointTypes&&props.endpointConfiguration)throw new(errors_1()).ValidationError("Only one of the RestApi props, endpointTypes or endpointConfiguration, is allowed",this);if(props.endpointConfiguration){const endpointConfiguration=props.endpointConfiguration,isPrivateApi=endpointConfiguration.types.includes(EndpointType.PRIVATE),isIpv4Only=endpointConfiguration.ipAddressType===IpAddressType.IPV4;if(isPrivateApi&&isIpv4Only)throw new(errors_1()).ValidationError("Private APIs can only have a dualstack IP address type.",this);return{ipAddressType:props.endpointConfiguration.ipAddressType,types:props.endpointConfiguration.types,vpcEndpointIds:props.endpointConfiguration?.vpcEndpoints?.map(vpcEndpoint=>vpcEndpoint.vpcEndpointId)}}if(props.endpointTypes)return{types:props.endpointTypes}}cannedMetric(fn,props){return new(cloudwatch()).Metric({...fn({ApiName:this.restApiName}),...props}).attachTo(this)}}exports.RestApiBase=RestApiBase,_a=JSII_RTTI_SYMBOL_1,RestApiBase[_a]={fqn:"aws-cdk-lib.aws_apigateway.RestApiBase",version:"2.201.0"};let SpecRestApi=class SpecRestApi2 extends RestApiBase{constructor(scope,id,props){super(scope,id,props);try{jsiiDeprecationWarnings().aws_cdk_lib_aws_apigateway_SpecRestApiProps(props)}catch(error){throw process.env.JSII_DEBUG!=="1"&&error.name==="DeprecationError"&&Error.captureStackTrace(error,SpecRestApi2),error}(0,metadata_resource_1().addConstructMetadata)(this,props);const apiDefConfig=props.apiDefinition.bind(this);this.resourcePolicy=props.policy;const resource=new(apigateway_generated_1()).CfnRestApi(this,"Resource",{name:this.restApiName,policy:core_1().Lazy.any({produce:()=>this.resourcePolicy}),failOnWarnings:props.failOnWarnings,minimumCompressionSize:props.minCompressionSize?.toBytes(),body:apiDefConfig.inlineDefinition??void 0,bodyS3Location:apiDefConfig.inlineDefinition?void 0:apiDefConfig.s3Location,endpointConfiguration:this._configureEndpoints(props),parameters:props.parameters,disableExecuteApiEndpoint:props.disableExecuteApiEndpoint,mode:props.mode});props.apiDefinition.bindAfterCreate(this,this),this.node.defaultChild=resource,this.restApiId=resource.ref,this.restApiRootResourceId=resource.attrRootResourceId,this.root=new RootResource(this,{},this.restApiRootResourceId),this._configureCloudWatchRole(resource,props.cloudWatchRole,props.cloudWatchRoleRemovalPolicy),this._configureDeployment(props),props.domainName&&this.addDomainName("CustomDomain",props.domainName)}addToResourcePolicy(statement){try{jsiiDeprecationWarnings().aws_cdk_lib_aws_iam_PolicyStatement(statement)}catch(error){throw process.env.JSII_DEBUG!=="1"&&error.name==="DeprecationError"&&Error.captureStackTrace(error,this.addToResourcePolicy),error}return this.resourcePolicy=this.resourcePolicy??new(iam()).PolicyDocument,this.resourcePolicy.addStatements(statement),{statementAdded:!0,policyDependable:this}}};exports.SpecRestApi=SpecRestApi,_b=JSII_RTTI_SYMBOL_1,SpecRestApi[_b]={fqn:"aws-cdk-lib.aws_apigateway.SpecRestApi",version:"2.201.0"},SpecRestApi.PROPERTY_INJECTION_ID="aws-cdk-lib.aws-apigateway.SpecRestApi",__decorate([(0,metadata_resource_1().MethodMetadata)()],SpecRestApi.prototype,"addToResourcePolicy",null),exports.SpecRestApi=SpecRestApi=__decorate([prop_injectable_1().propertyInjectable],SpecRestApi);let RestApi=class RestApi2 extends RestApiBase{static isRestApi(x){return x!==null&&typeof x=="object"&&APIGATEWAY_RESTAPI_SYMBOL in x}static fromRestApiId(scope,id,restApiId){class Import extends RestApiBase{constructor(){super(...arguments),this.restApiId=restApiId}addToResourcePolicy(_statement){return{statementAdded:!1}}get root(){throw new(errors_1()).ValidationError("root is not configured when imported using `fromRestApiId()`. Use `fromRestApiAttributes()` API instead.",scope)}get restApiRootResourceId(){throw new(errors_1()).ValidationError("restApiRootResourceId is not configured when imported using `fromRestApiId()`. Use `fromRestApiAttributes()` API instead.",scope)}}return new Import(scope,id)}static fromRestApiAttributes(scope,id,attrs){try{jsiiDeprecationWarnings().aws_cdk_lib_aws_apigateway_RestApiAttributes(attrs)}catch(error){throw process.env.JSII_DEBUG!=="1"&&error.name==="DeprecationError"&&Error.captureStackTrace(error,this.fromRestApiAttributes),error}class Import extends RestApiBase{constructor(){super(...arguments),this.restApiId=attrs.restApiId,this.restApiName=attrs.restApiName??id,this.restApiRootResourceId=attrs.rootResourceId,this.root=new RootResource(this,{},this.restApiRootResourceId)}addToResourcePolicy(_statement){return{statementAdded:!1}}}return new Import(scope,id)}constructor(scope,id,props={}){super(scope,id,props),this.methods=new Array,this.deployments=new Array;try{jsiiDeprecationWarnings().aws_cdk_lib_aws_apigateway_RestApiProps(props)}catch(error){throw process.env.JSII_DEBUG!=="1"&&error.name==="DeprecationError"&&Error.captureStackTrace(error,RestApi2),error}if((0,metadata_resource_1().addConstructMetadata)(this,props),props.minCompressionSize!==void 0&&props.minimumCompressionSize!==void 0)throw new(errors_1()).ValidationError("both properties minCompressionSize and minimumCompressionSize cannot be set at once.",scope);this.resourcePolicy=props.policy;const resource=new(apigateway_generated_1()).CfnRestApi(this,"Resource",{name:this.physicalName,description:props.description,policy:core_1().Lazy.any({produce:()=>this.resourcePolicy}),failOnWarnings:props.failOnWarnings,minimumCompressionSize:props.minCompressionSize?.toBytes()??props.minimumCompressionSize,binaryMediaTypes:props.binaryMediaTypes,endpointConfiguration:this._configureEndpoints(props),apiKeySourceType:props.apiKeySourceType,cloneFrom:props.cloneFrom?.restApiId,parameters:props.parameters,disableExecuteApiEndpoint:props.disableExecuteApiEndpoint});this.node.defaultChild=resource,this.restApiId=resource.ref,this._configureCloudWatchRole(resource,props.cloudWatchRole,props.cloudWatchRoleRemovalPolicy),this._configureDeployment(props),props.domainName&&this.addDomainName("CustomDomain",props.domainName),this.root=new RootResource(this,props,resource.attrRootResourceId),this.restApiRootResourceId=resource.attrRootResourceId,this.node.addValidation({validate:()=>this.validateRestApi()}),Object.defineProperty(this,APIGATEWAY_RESTAPI_SYMBOL,{value:!0})}addToResourcePolicy(statement){try{jsiiDeprecationWarnings().aws_cdk_lib_aws_iam_PolicyStatement(statement)}catch(error){throw process.env.JSII_DEBUG!=="1"&&error.name==="DeprecationError"&&Error.captureStackTrace(error,this.addToResourcePolicy),error}return this.resourcePolicy=this.resourcePolicy??new(iam()).PolicyDocument,this.resourcePolicy.addStatements(statement),{statementAdded:!0,policyDependable:this}}addModel(id,props){try{jsiiDeprecationWarnings().aws_cdk_lib_aws_apigateway_ModelOptions(props)}catch(error){throw process.env.JSII_DEBUG!=="1"&&error.name==="DeprecationError"&&Error.captureStackTrace(error,this.addModel),error}return new(model_1()).Model(this,id,{...props,restApi:this})}addRequestValidator(id,props){try{jsiiDeprecationWarnings().aws_cdk_lib_aws_apigateway_RequestValidatorOptions(props)}catch(error){throw process.env.JSII_DEBUG!=="1"&&error.name==="DeprecationError"&&Error.captureStackTrace(error,this.addRequestValidator),error}return new(requestvalidator_1()).RequestValidator(this,id,{...props,restApi:this})}_attachMethod(method){this.methods.push(method);for(const dep of this.deployments)dep._addMethodDependency(method)}_attachDeployment(deployment){this.deployments.push(deployment);for(const method of this.methods)deployment._addMethodDependency(method)}validateRestApi(){return this.methods.length===0?["The REST API doesn't contain any methods"]:[]}};exports.RestApi=RestApi,_c=JSII_RTTI_SYMBOL_1,RestApi[_c]={fqn:"aws-cdk-lib.aws_apigateway.RestApi",version:"2.201.0"},RestApi.PROPERTY_INJECTION_ID="aws-cdk-lib.aws-apigateway.RestApi",__decorate([(0,metadata_resource_1().MethodMetadata)()],RestApi.prototype,"addToResourcePolicy",null),__decorate([(0,metadata_resource_1().MethodMetadata)()],RestApi.prototype,"addModel",null),__decorate([(0,metadata_resource_1().MethodMetadata)()],RestApi.prototype,"addRequestValidator",null),exports.RestApi=RestApi=__decorate([prop_injectable_1().propertyInjectable],RestApi);var IpAddressType;(function(IpAddressType2){IpAddressType2.IPV4="ipv4",IpAddressType2.DUAL_STACK="dualstack"})(IpAddressType||(exports.IpAddressType=IpAddressType={}));var ApiKeySourceType;(function(ApiKeySourceType2){ApiKeySourceType2.HEADER="HEADER",ApiKeySourceType2.AUTHORIZER="AUTHORIZER"})(ApiKeySourceType||(exports.ApiKeySourceType=ApiKeySourceType={}));var EndpointType;(function(EndpointType2){EndpointType2.EDGE="EDGE",EndpointType2.REGIONAL="REGIONAL",EndpointType2.PRIVATE="PRIVATE"})(EndpointType||(exports.EndpointType=EndpointType={}));var RestApiMode;(function(RestApiMode2){RestApiMode2.OVERWRITE="overwrite",RestApiMode2.MERGE="merge"})(RestApiMode||(exports.RestApiMode=RestApiMode={}));class RootResource extends resource_1().ResourceBase{constructor(api,props,resourceId){super(api,"Default"),props=(0,prop_injectors_helpers_1().applyInjectors)(RootResource.PROPERTY_INJECTION_ID,props,{scope:api,id:resourceId}),(0,metadata_resource_1().addConstructMetadata)(this,resourceId),this.parentResource=void 0,this.defaultIntegration=props.defaultIntegration,this.defaultMethodOptions=props.defaultMethodOptions,this.defaultCorsPreflightOptions=props.defaultCorsPreflightOptions,this.api=api,this.resourceId=resourceId,this.path="/",api instanceof RestApi&&(this._restApi=api),this.defaultCorsPreflightOptions&&this.addCorsPreflight(this.defaultCorsPreflightOptions)}get restApi(){if(!this._restApi)throw new(errors_1()).ValidationError("RestApi is not available on Resource not connected to an instance of RestApi. Use `api` instead",this);return this._restApi}}RootResource.PROPERTY_INJECTION_ID="aws-cdk-lib.aws-apigateway.RootResource";function ignore(_x){}
