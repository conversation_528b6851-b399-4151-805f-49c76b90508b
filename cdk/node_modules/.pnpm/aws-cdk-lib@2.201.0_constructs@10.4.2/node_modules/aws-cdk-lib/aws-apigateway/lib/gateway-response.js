"use strict";var __decorate=exports&&exports.__decorate||function(decorators,target,key,desc){var c=arguments.length,r=c<3?target:desc===null?desc=Object.getOwnPropertyDescriptor(target,key):desc,d;if(typeof Reflect=="object"&&typeof Reflect.decorate=="function")r=Reflect.decorate(decorators,target,key,desc);else for(var i=decorators.length-1;i>=0;i--)(d=decorators[i])&&(r=(c<3?d(r):c>3?d(target,key,r):d(target,key))||r);return c>3&&r&&Object.defineProperty(target,key,r),r},_a,_b;Object.defineProperty(exports,"__esModule",{value:!0}),exports.ResponseType=exports.GatewayResponse=void 0;var jsiiDeprecationWarnings=()=>{var tmp=require("../../.warnings.jsii.js");return jsiiDeprecationWarnings=()=>tmp,tmp};const JSII_RTTI_SYMBOL_1=Symbol.for("jsii.rtti");var apigateway_generated_1=()=>{var tmp=require("./apigateway.generated");return apigateway_generated_1=()=>tmp,tmp},core_1=()=>{var tmp=require("../../core");return core_1=()=>tmp,tmp},metadata_resource_1=()=>{var tmp=require("../../core/lib/metadata-resource");return metadata_resource_1=()=>tmp,tmp},prop_injectable_1=()=>{var tmp=require("../../core/lib/prop-injectable");return prop_injectable_1=()=>tmp,tmp};let GatewayResponse=class GatewayResponse2 extends core_1().Resource{constructor(scope,id,props){super(scope,id);try{jsiiDeprecationWarnings().aws_cdk_lib_aws_apigateway_GatewayResponseProps(props)}catch(error){throw process.env.JSII_DEBUG!=="1"&&error.name==="DeprecationError"&&Error.captureStackTrace(error,GatewayResponse2),error}(0,metadata_resource_1().addConstructMetadata)(this,props);const gatewayResponseProps={restApiId:props.restApi.restApiId,responseType:props.type.responseType,responseParameters:this.buildResponseParameters(props.responseHeaders),responseTemplates:props.templates,statusCode:props.statusCode},resource=new(apigateway_generated_1()).CfnGatewayResponse(this,"Resource",gatewayResponseProps),deployment=props.restApi.latestDeployment;deployment&&(deployment.node.addDependency(resource),deployment.addToLogicalId({gatewayResponse:{...gatewayResponseProps}})),this.node.defaultChild=resource}buildResponseParameters(responseHeaders){if(!responseHeaders)return;const responseParameters={};for(const[header,value]of Object.entries(responseHeaders))responseParameters[`gatewayresponse.header.${header}`]=value;return responseParameters}};exports.GatewayResponse=GatewayResponse,_a=JSII_RTTI_SYMBOL_1,GatewayResponse[_a]={fqn:"aws-cdk-lib.aws_apigateway.GatewayResponse",version:"2.201.0"},GatewayResponse.PROPERTY_INJECTION_ID="aws-cdk-lib.aws-apigateway.GatewayResponse",exports.GatewayResponse=GatewayResponse=__decorate([prop_injectable_1().propertyInjectable],GatewayResponse);class ResponseType{static of(type){return new ResponseType(type.toUpperCase())}constructor(type){this.responseType=type}}exports.ResponseType=ResponseType,_b=JSII_RTTI_SYMBOL_1,ResponseType[_b]={fqn:"aws-cdk-lib.aws_apigateway.ResponseType",version:"2.201.0"},ResponseType.ACCESS_DENIED=new ResponseType("ACCESS_DENIED"),ResponseType.API_CONFIGURATION_ERROR=new ResponseType("API_CONFIGURATION_ERROR"),ResponseType.AUTHORIZER_FAILURE=new ResponseType("AUTHORIZER_FAILURE"),ResponseType.AUTHORIZER_CONFIGURATION_ERROR=new ResponseType("AUTHORIZER_CONFIGURATION_ERROR"),ResponseType.BAD_REQUEST_PARAMETERS=new ResponseType("BAD_REQUEST_PARAMETERS"),ResponseType.BAD_REQUEST_BODY=new ResponseType("BAD_REQUEST_BODY"),ResponseType.DEFAULT_4XX=new ResponseType("DEFAULT_4XX"),ResponseType.DEFAULT_5XX=new ResponseType("DEFAULT_5XX"),ResponseType.EXPIRED_TOKEN=new ResponseType("EXPIRED_TOKEN"),ResponseType.INVALID_SIGNATURE=new ResponseType("INVALID_SIGNATURE"),ResponseType.INTEGRATION_FAILURE=new ResponseType("INTEGRATION_FAILURE"),ResponseType.INTEGRATION_TIMEOUT=new ResponseType("INTEGRATION_TIMEOUT"),ResponseType.INVALID_API_KEY=new ResponseType("INVALID_API_KEY"),ResponseType.MISSING_AUTHENTICATION_TOKEN=new ResponseType("MISSING_AUTHENTICATION_TOKEN"),ResponseType.QUOTA_EXCEEDED=new ResponseType("QUOTA_EXCEEDED"),ResponseType.REQUEST_TOO_LARGE=new ResponseType("REQUEST_TOO_LARGE"),ResponseType.RESOURCE_NOT_FOUND=new ResponseType("RESOURCE_NOT_FOUND"),ResponseType.THROTTLED=new ResponseType("THROTTLED"),ResponseType.UNAUTHORIZED=new ResponseType("UNAUTHORIZED"),ResponseType.UNSUPPORTED_MEDIA_TYPE=new ResponseType("UNSUPPORTED_MEDIA_TYPE"),ResponseType.WAF_FILTERED=new ResponseType("WAF_FILTERED");
