"use strict";var __decorate=exports&&exports.__decorate||function(decorators,target,key,desc){var c=arguments.length,r=c<3?target:desc===null?desc=Object.getOwnPropertyDescriptor(target,key):desc,d;if(typeof Reflect=="object"&&typeof Reflect.decorate=="function")r=Reflect.decorate(decorators,target,key,desc);else for(var i=decorators.length-1;i>=0;i--)(d=decorators[i])&&(r=(c<3?d(r):c>3?d(target,key,r):d(target,key))||r);return c>3&&r&&Object.defineProperty(target,key,r),r},_a;Object.defineProperty(exports,"__esModule",{value:!0}),exports.Deployment=void 0;var jsiiDeprecationWarnings=()=>{var tmp=require("../../.warnings.jsii.js");return jsiiDeprecationWarnings=()=>tmp,tmp};const JSII_RTTI_SYMBOL_1=Symbol.for("jsii.rtti");var apigateway_generated_1=()=>{var tmp=require("./apigateway.generated");return apigateway_generated_1=()=>tmp,tmp},restapi_1=()=>{var tmp=require("./restapi");return restapi_1=()=>tmp,tmp},core_1=()=>{var tmp=require("../../core");return core_1=()=>tmp,tmp},errors_1=()=>{var tmp=require("../../core/lib/errors");return errors_1=()=>tmp,tmp},helpers_internal_1=()=>{var tmp=require("../../core/lib/helpers-internal");return helpers_internal_1=()=>tmp,tmp},metadata_resource_1=()=>{var tmp=require("../../core/lib/metadata-resource");return metadata_resource_1=()=>tmp,tmp},prop_injectable_1=()=>{var tmp=require("../../core/lib/prop-injectable");return prop_injectable_1=()=>tmp,tmp};let Deployment=class Deployment2 extends core_1().Resource{constructor(scope,id,props){super(scope,id);try{jsiiDeprecationWarnings().aws_cdk_lib_aws_apigateway_DeploymentProps(props)}catch(error){throw process.env.JSII_DEBUG!=="1"&&error.name==="DeprecationError"&&Error.captureStackTrace(error,Deployment2),error}(0,metadata_resource_1().addConstructMetadata)(this,props),this.resource=new LatestDeploymentResource(this,"Resource",{description:props.description,restApi:props.api,stageName:props.stageName}),props.retainDeployments&&this.resource.applyRemovalPolicy(core_1().RemovalPolicy.RETAIN),this.api=props.api,this.deploymentId=core_1().Lazy.string({produce:()=>this.resource.ref}),props.api instanceof restapi_1().RestApiBase&&props.api._attachDeployment(this)}addToLogicalId(data){this.resource.addToLogicalId(data)}_addMethodDependency(method){this.node.addDependency(method.node.defaultChild)}};exports.Deployment=Deployment,_a=JSII_RTTI_SYMBOL_1,Deployment[_a]={fqn:"aws-cdk-lib.aws_apigateway.Deployment",version:"2.201.0"},Deployment.PROPERTY_INJECTION_ID="aws-cdk-lib.aws-apigateway.Deployment",__decorate([(0,metadata_resource_1().MethodMetadata)()],Deployment.prototype,"addToLogicalId",null),exports.Deployment=Deployment=__decorate([prop_injectable_1().propertyInjectable],Deployment);class LatestDeploymentResource extends apigateway_generated_1().CfnDeployment{constructor(scope,id,props){super(scope,id,{description:props.description,restApiId:props.restApi.restApiId,stageName:props.stageName}),this.hashComponents=new Array,this.api=props.restApi,this.originalLogicalId=this.stack.getLogicalId(this),this.overrideLogicalId(core_1().Lazy.uncachedString({produce:()=>this.calculateLogicalId()}))}addToLogicalId(data){if(this.node.locked)throw new(errors_1()).ValidationError("Cannot modify the logical ID when the construct is locked",this);this.hashComponents.push(data)}calculateLogicalId(){const hash=[...this.hashComponents];if(this.api instanceof restapi_1().RestApi||this.api instanceof restapi_1().SpecRestApi){const cfnRestApiCF=this.api.node.defaultChild._toCloudFormation();hash.push(this.stack.resolve(cfnRestApiCF))}let lid=this.originalLogicalId;return hash.length>0&&(lid+=(0,helpers_internal_1().md5hash)(hash.map(x=>this.stack.resolve(x)).map(c=>JSON.stringify(c)).join(""))),lid}}
