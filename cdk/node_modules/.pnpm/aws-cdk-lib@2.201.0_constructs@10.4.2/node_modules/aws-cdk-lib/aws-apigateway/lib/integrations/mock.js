"use strict";var _a;Object.defineProperty(exports,"__esModule",{value:!0}),exports.MockIntegration=void 0;var jsiiDeprecationWarnings=()=>{var tmp=require("../../../.warnings.jsii.js");return jsiiDeprecationWarnings=()=>tmp,tmp};const JSII_RTTI_SYMBOL_1=Symbol.for("jsii.rtti");var integration_1=()=>{var tmp=require("../integration");return integration_1=()=>tmp,tmp};class MockIntegration extends integration_1().Integration{constructor(options){super({type:integration_1().IntegrationType.MOCK,options});try{jsiiDeprecationWarnings().aws_cdk_lib_aws_apigateway_IntegrationOptions(options)}catch(error){throw process.env.JSII_DEBUG!=="1"&&error.name==="DeprecationError"&&Error.captureStackTrace(error,MockIntegration),error}}}exports.MockIntegration=MockIntegration,_a=JSII_RTTI_SYMBOL_1,MockIntegration[_a]={fqn:"aws-cdk-lib.aws_apigateway.MockIntegration",version:"2.201.0"};
