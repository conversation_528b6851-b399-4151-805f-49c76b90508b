"use strict";var _a;Object.defineProperty(exports,"__esModule",{value:!0}),exports.AwsIntegration=void 0;var jsiiDeprecationWarnings=()=>{var tmp=require("../../../.warnings.jsii.js");return jsiiDeprecationWarnings=()=>tmp,tmp};const JSII_RTTI_SYMBOL_1=Symbol.for("jsii.rtti");var cdk=()=>{var tmp=require("../../../core");return cdk=()=>tmp,tmp},core_1=()=>{var tmp=require("../../../core");return core_1=()=>tmp,tmp},errors_1=()=>{var tmp=require("../../../core/lib/errors");return errors_1=()=>tmp,tmp},integration_1=()=>{var tmp=require("../integration");return integration_1=()=>tmp,tmp},util_1=()=>{var tmp=require("../util");return util_1=()=>tmp,tmp};class AwsIntegration extends integration_1().Integration{constructor(props){try{jsiiDeprecationWarnings().aws_cdk_lib_aws_apigateway_AwsIntegrationProps(props)}catch(error){throw process.env.JSII_DEBUG!=="1"&&error.name==="DeprecationError"&&Error.captureStackTrace(error,AwsIntegration),error}const backend=props.subdomain?`${props.subdomain}.${props.service}`:props.service,type=props.proxy?integration_1().IntegrationType.AWS_PROXY:integration_1().IntegrationType.AWS,{apiType,apiValue}=(0,util_1().parseAwsApiCall)(props.path,props.action,props.actionParameters);super({type,integrationHttpMethod:props.integrationHttpMethod||"POST",uri:cdk().Lazy.string({produce:()=>{if(!this.scope)throw new(errors_1()).UnscopedValidationError("AwsIntegration must be used in API");return cdk().Stack.of(this.scope).formatArn({service:"apigateway",account:backend,resource:apiType,arnFormat:core_1().ArnFormat.SLASH_RESOURCE_NAME,resourceName:apiValue,region:props.region})}}),options:props.options})}bind(method){try{jsiiDeprecationWarnings().aws_cdk_lib_aws_apigateway_Method(method)}catch(error){throw process.env.JSII_DEBUG!=="1"&&error.name==="DeprecationError"&&Error.captureStackTrace(error,this.bind),error}const bindResult=super.bind(method);return this.scope=method,bindResult}}exports.AwsIntegration=AwsIntegration,_a=JSII_RTTI_SYMBOL_1,AwsIntegration[_a]={fqn:"aws-cdk-lib.aws_apigateway.AwsIntegration",version:"2.201.0"};
