"use strict";var __decorate=exports&&exports.__decorate||function(decorators,target,key,desc){var c=arguments.length,r=c<3?target:desc===null?desc=Object.getOwnPropertyDescriptor(target,key):desc,d;if(typeof Reflect=="object"&&typeof Reflect.decorate=="function")r=Reflect.decorate(decorators,target,key,desc);else for(var i=decorators.length-1;i>=0;i--)(d=decorators[i])&&(r=(c<3?d(r):c>3?d(target,key,r):d(target,key))||r);return c>3&&r&&Object.defineProperty(target,key,r),r},_a;Object.defineProperty(exports,"__esModule",{value:!0}),exports.BasePathMapping=void 0;var jsiiDeprecationWarnings=()=>{var tmp=require("../../.warnings.jsii.js");return jsiiDeprecationWarnings=()=>tmp,tmp};const JSII_RTTI_SYMBOL_1=Symbol.for("jsii.rtti");var apigateway_generated_1=()=>{var tmp=require("./apigateway.generated");return apigateway_generated_1=()=>tmp,tmp},restapi_1=()=>{var tmp=require("./restapi");return restapi_1=()=>tmp,tmp},core_1=()=>{var tmp=require("../../core");return core_1=()=>tmp,tmp},errors_1=()=>{var tmp=require("../../core/lib/errors");return errors_1=()=>tmp,tmp},metadata_resource_1=()=>{var tmp=require("../../core/lib/metadata-resource");return metadata_resource_1=()=>tmp,tmp},prop_injectable_1=()=>{var tmp=require("../../core/lib/prop-injectable");return prop_injectable_1=()=>tmp,tmp};let BasePathMapping=class BasePathMapping2 extends core_1().Resource{constructor(scope,id,props){super(scope,id);try{jsiiDeprecationWarnings().aws_cdk_lib_aws_apigateway_BasePathMappingProps(props)}catch(error){throw process.env.JSII_DEBUG!=="1"&&error.name==="DeprecationError"&&Error.captureStackTrace(error,BasePathMapping2),error}if((0,metadata_resource_1().addConstructMetadata)(this,props),props.basePath&&!core_1().Token.isUnresolved(props.basePath)){if(props.basePath.startsWith("/")||props.basePath.endsWith("/"))throw new(errors_1()).ValidationError(`A base path cannot start or end with /", received: ${props.basePath}`,scope);if(props.basePath.match(/\/{2,}/))throw new(errors_1()).ValidationError(`A base path cannot have more than one consecutive /", received: ${props.basePath}`,scope);if(!props.basePath.match(/^[a-zA-Z0-9$_.+!*'()-/]+$/))throw new(errors_1()).ValidationError(`A base path may only contain letters, numbers, and one of "$-_.+!*'()/", received: ${props.basePath}`,scope)}const attachToStage=props.attachToStage??!0,stage=props.stage??(props.restApi instanceof restapi_1().RestApiBase&&attachToStage?props.restApi.deploymentStage:void 0);new(apigateway_generated_1()).CfnBasePathMapping(this,"Resource",{basePath:props.basePath,domainName:props.domainName.domainName,restApiId:props.restApi.restApiId,stage:stage?.stageName})}};exports.BasePathMapping=BasePathMapping,_a=JSII_RTTI_SYMBOL_1,BasePathMapping[_a]={fqn:"aws-cdk-lib.aws_apigateway.BasePathMapping",version:"2.201.0"},BasePathMapping.PROPERTY_INJECTION_ID="aws-cdk-lib.aws-apigateway.BasePathMapping",exports.BasePathMapping=BasePathMapping=__decorate([prop_injectable_1().propertyInjectable],BasePathMapping);
