"use strict";var __decorate=exports&&exports.__decorate||function(decorators,target,key,desc){var c=arguments.length,r=c<3?target:desc===null?desc=Object.getOwnPropertyDescriptor(target,key):desc,d;if(typeof Reflect=="object"&&typeof Reflect.decorate=="function")r=Reflect.decorate(decorators,target,key,desc);else for(var i=decorators.length-1;i>=0;i--)(d=decorators[i])&&(r=(c<3?d(r):c>3?d(target,key,r):d(target,key))||r);return c>3&&r&&Object.defineProperty(target,key,r),r},_a,_b;Object.defineProperty(exports,"__esModule",{value:!0}),exports.RateLimitedApiKey=exports.ApiKey=void 0;var jsiiDeprecationWarnings=()=>{var tmp=require("../../.warnings.jsii.js");return jsiiDeprecationWarnings=()=>tmp,tmp};const JSII_RTTI_SYMBOL_1=Symbol.for("jsii.rtti");var apigateway_generated_1=()=>{var tmp=require("./apigateway.generated");return apigateway_generated_1=()=>tmp,tmp},usage_plan_1=()=>{var tmp=require("./usage-plan");return usage_plan_1=()=>tmp,tmp},iam=()=>{var tmp=require("../../aws-iam");return iam=()=>tmp,tmp},core_1=()=>{var tmp=require("../../core");return core_1=()=>tmp,tmp},errors_1=()=>{var tmp=require("../../core/lib/errors");return errors_1=()=>tmp,tmp},metadata_resource_1=()=>{var tmp=require("../../core/lib/metadata-resource");return metadata_resource_1=()=>tmp,tmp},prop_injectable_1=()=>{var tmp=require("../../core/lib/prop-injectable");return prop_injectable_1=()=>tmp,tmp};class ApiKeyBase extends core_1().Resource{grantRead(grantee){return iam().Grant.addToPrincipal({grantee,actions:readPermissions,resourceArns:[this.keyArn]})}grantWrite(grantee){return iam().Grant.addToPrincipal({grantee,actions:writePermissions,resourceArns:[this.keyArn]})}grantReadWrite(grantee){return iam().Grant.addToPrincipal({grantee,actions:[...readPermissions,...writePermissions],resourceArns:[this.keyArn]})}}let ApiKey=class ApiKey2 extends ApiKeyBase{static fromApiKeyId(scope,id,apiKeyId){class Import extends ApiKeyBase{constructor(){super(...arguments),this.keyId=apiKeyId,this.keyArn=core_1().Stack.of(this).formatArn({service:"apigateway",account:"",resource:"/apikeys",arnFormat:core_1().ArnFormat.SLASH_RESOURCE_NAME,resourceName:apiKeyId})}}return new Import(scope,id)}constructor(scope,id,props={}){super(scope,id,{physicalName:props.apiKeyName});try{jsiiDeprecationWarnings().aws_cdk_lib_aws_apigateway_ApiKeyProps(props)}catch(error){throw process.env.JSII_DEBUG!=="1"&&error.name==="DeprecationError"&&Error.captureStackTrace(error,ApiKey2),error}(0,metadata_resource_1().addConstructMetadata)(this,props);const resource=new(apigateway_generated_1()).CfnApiKey(this,"Resource",{customerId:props.customerId,description:props.description,enabled:props.enabled??!0,generateDistinctId:props.generateDistinctId,name:this.physicalName,stageKeys:this.renderStageKeys(props.resources,props.stages),value:props.value});this.keyId=resource.ref,this.keyArn=core_1().Stack.of(this).formatArn({service:"apigateway",account:"",resource:"/apikeys",arnFormat:core_1().ArnFormat.SLASH_RESOURCE_NAME,resourceName:this.keyId})}renderStageKeys(resources,stages){if(!(!resources&&!stages)){if(resources&&stages)throw new(errors_1()).ValidationError('Only one of "resources" or "stages" should be provided',this);return resources?resources.map(resource=>{const restApi=resource;if(!restApi.deploymentStage)throw new(errors_1()).ValidationError(`Cannot add an ApiKey to a RestApi that does not contain a "deploymentStage".
Either set the RestApi.deploymentStage or create an ApiKey from a Stage`,this);const restApiId=restApi.restApiId,stageName=restApi.deploymentStage.stageName.toString();return{restApiId,stageName}}):stages?stages.map(stage=>({restApiId:stage.restApi.restApiId,stageName:stage.stageName})):void 0}}};exports.ApiKey=ApiKey,_a=JSII_RTTI_SYMBOL_1,ApiKey[_a]={fqn:"aws-cdk-lib.aws_apigateway.ApiKey",version:"2.201.0"},ApiKey.PROPERTY_INJECTION_ID="aws-cdk-lib.aws-apigateway.ApiKey",exports.ApiKey=ApiKey=__decorate([prop_injectable_1().propertyInjectable],ApiKey);let RateLimitedApiKey=class RateLimitedApiKey2 extends ApiKeyBase{constructor(scope,id,props={}){super(scope,id,{physicalName:props.apiKeyName});try{jsiiDeprecationWarnings().aws_cdk_lib_aws_apigateway_RateLimitedApiKeyProps(props)}catch(error){throw process.env.JSII_DEBUG!=="1"&&error.name==="DeprecationError"&&Error.captureStackTrace(error,RateLimitedApiKey2),error}(0,metadata_resource_1().addConstructMetadata)(this,props);const resource=new ApiKey(this,"Resource",props);(props.apiStages||props.quota||props.throttle)&&new(usage_plan_1()).UsagePlan(this,"UsagePlanResource",{apiStages:props.apiStages,quota:props.quota,throttle:props.throttle}).addApiKey(resource),this.keyId=resource.keyId,this.keyArn=resource.keyArn}};exports.RateLimitedApiKey=RateLimitedApiKey,_b=JSII_RTTI_SYMBOL_1,RateLimitedApiKey[_b]={fqn:"aws-cdk-lib.aws_apigateway.RateLimitedApiKey",version:"2.201.0"},RateLimitedApiKey.PROPERTY_INJECTION_ID="aws-cdk-lib.aws-apigateway.RateLimitedApiKey",exports.RateLimitedApiKey=RateLimitedApiKey=__decorate([prop_injectable_1().propertyInjectable],RateLimitedApiKey);const readPermissions=["apigateway:GET"],writePermissions=["apigateway:POST","apigateway:PUT","apigateway:PATCH","apigateway:DELETE"];
