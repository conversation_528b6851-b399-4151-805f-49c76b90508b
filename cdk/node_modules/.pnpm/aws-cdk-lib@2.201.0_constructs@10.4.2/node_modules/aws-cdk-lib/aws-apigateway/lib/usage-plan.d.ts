import { Construct } from 'constructs';
import { <PERSON><PERSON><PERSON><PERSON><PERSON> } from './api-key';
import { Method } from './method';
import { IRestApi } from './restapi';
import { Stage } from './stage';
import { IResource, Resource } from '../../core';
/**
 * Container for defining throttling parameters to API stages or methods.
 * @link https://docs.aws.amazon.com/apigateway/latest/developerguide/api-gateway-request-throttling.html
 */
export interface ThrottleSettings {
    /**
     * The API request steady-state rate limit (average requests per second over an extended period of time)
     * @default none
     */
    readonly rateLimit?: number;
    /**
     * The maximum API request rate limit over a time ranging from one to a few seconds.
     * @default none
     */
    readonly burstLimit?: number;
}
/**
 * Time period for which quota settings apply.
 */
export declare enum Period {
    DAY = "DAY",
    WEEK = "WEEK",
    MONTH = "MONTH"
}
/**
 * Specifies the maximum number of requests that clients can make to API Gateway APIs.
 */
export interface QuotaSettings {
    /**
     * The maximum number of requests that users can make within the specified time period.
     * @default none
     */
    readonly limit?: number;
    /**
     * For the initial time period, the number of requests to subtract from the specified limit.
     * @default none
     */
    readonly offset?: number;
    /**
     * The time period for which the maximum limit of requests applies.
     * @default none
     */
    readonly period?: Period;
}
/**
 * Represents per-method throttling for a resource.
 */
export interface ThrottlingPerMethod {
    /**
     * [disable-awslint:ref-via-interface]
     * The method for which you specify the throttling settings.
     * @default none
     */
    readonly method: Method;
    /**
     * Specifies the overall request rate (average requests per second) and burst capacity.
     * @default none
     */
    readonly throttle: ThrottleSettings;
}
/**
 * Represents the API stages that a usage plan applies to.
 */
export interface UsagePlanPerApiStage {
    /**
     * @default none
     */
    readonly api?: IRestApi;
    /**
     *
     * [disable-awslint:ref-via-interface]
     * @default none
     */
    readonly stage?: Stage;
    /**
     * @default none
     */
    readonly throttle?: ThrottlingPerMethod[];
}
export interface UsagePlanProps {
    /**
     * API Stages to be associated with the usage plan.
     * @default none
     */
    readonly apiStages?: UsagePlanPerApiStage[];
    /**
     * Represents usage plan purpose.
     * @default none
     */
    readonly description?: string;
    /**
     * Number of requests clients can make in a given time period.
     * @default none
     */
    readonly quota?: QuotaSettings;
    /**
     * Overall throttle settings for the API.
     * @default none
     */
    readonly throttle?: ThrottleSettings;
    /**
     * Name for this usage plan.
     * @default none
     */
    readonly name?: string;
}
/**
 * Options to the UsagePlan.addApiKey() method
 */
export interface AddApiKeyOptions {
    /**
     * Override the CloudFormation logical id of the AWS::ApiGateway::UsagePlanKey resource
     * @default - autogenerated by the CDK
     */
    readonly overrideLogicalId?: string;
}
/**
 * A UsagePlan, either managed by this CDK app, or imported.
 */
export interface IUsagePlan extends IResource {
    /**
     * Id of the usage plan
     * @attribute
     */
    readonly usagePlanId: string;
    /**
     * Adds an ApiKey.
     *
     * @param apiKey the api key to associate with this usage plan
     * @param options options that control the behaviour of this method
     */
    addApiKey(apiKey: IApiKey, options?: AddApiKeyOptions): void;
}
declare abstract class UsagePlanBase extends Resource implements IUsagePlan {
    /**
     * Id of the usage plan
     * @attribute
     */
    abstract readonly usagePlanId: string;
    /**
     * Adds an ApiKey.
     *
     * @param apiKey the api key to associate with this usage plan
     * @param options options that control the behaviour of this method
     */
    addApiKey(apiKey: IApiKey, options?: AddApiKeyOptions): void;
}
export declare class UsagePlan extends UsagePlanBase {
    /** Uniquely identifies this class. */
    static readonly PROPERTY_INJECTION_ID: string;
    /**
     * Import an externally defined usage plan using its ARN.
     *
     * @param scope  the construct that will "own" the imported usage plan.
     * @param id     the id of the imported usage plan in the construct tree.
     * @param usagePlanId the id of an existing usage plan.
     */
    static fromUsagePlanId(scope: Construct, id: string, usagePlanId: string): IUsagePlan;
    /**
     * @attribute
     */
    readonly usagePlanId: string;
    private readonly apiStages;
    constructor(scope: Construct, id: string, props?: UsagePlanProps);
    /**
     * Adds an apiStage.
     */
    addApiStage(apiStage: UsagePlanPerApiStage): void;
    private renderApiStages;
    private createStage;
    private renderQuota;
    private renderThrottle;
    private renderThrottlePerMethod;
}
export {};
