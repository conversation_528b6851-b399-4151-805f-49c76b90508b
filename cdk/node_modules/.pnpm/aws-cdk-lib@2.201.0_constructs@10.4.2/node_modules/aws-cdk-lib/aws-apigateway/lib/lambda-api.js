"use strict";var __decorate=exports&&exports.__decorate||function(decorators,target,key,desc){var c=arguments.length,r=c<3?target:desc===null?desc=Object.getOwnPropertyDescriptor(target,key):desc,d;if(typeof Reflect=="object"&&typeof Reflect.decorate=="function")r=Reflect.decorate(decorators,target,key,desc);else for(var i=decorators.length-1;i>=0;i--)(d=decorators[i])&&(r=(c<3?d(r):c>3?d(target,key,r):d(target,key))||r);return c>3&&r&&Object.defineProperty(target,key,r),r},_a;Object.defineProperty(exports,"__esModule",{value:!0}),exports.LambdaRestApi=void 0;var jsiiDeprecationWarnings=()=>{var tmp=require("../../.warnings.jsii.js");return jsiiDeprecationWarnings=()=>tmp,tmp};const JSII_RTTI_SYMBOL_1=Symbol.for("jsii.rtti");var integrations_1=()=>{var tmp=require("./integrations");return integrations_1=()=>tmp,tmp},restapi_1=()=>{var tmp=require("./restapi");return restapi_1=()=>tmp,tmp},errors_1=()=>{var tmp=require("../../core/lib/errors");return errors_1=()=>tmp,tmp},metadata_resource_1=()=>{var tmp=require("../../core/lib/metadata-resource");return metadata_resource_1=()=>tmp,tmp},prop_injectable_1=()=>{var tmp=require("../../core/lib/prop-injectable");return prop_injectable_1=()=>tmp,tmp};let LambdaRestApi=class LambdaRestApi2 extends restapi_1().RestApi{constructor(scope,id,props){try{jsiiDeprecationWarnings().aws_cdk_lib_aws_apigateway_LambdaRestApiProps(props)}catch(error){throw process.env.JSII_DEBUG!=="1"&&error.name==="DeprecationError"&&Error.captureStackTrace(error,LambdaRestApi2),error}if(props.options?.defaultIntegration||props.defaultIntegration)throw new(errors_1()).ValidationError('Cannot specify "defaultIntegration" since Lambda integration is automatically defined',scope);super(scope,id,{defaultIntegration:new(integrations_1()).LambdaIntegration(props.handler,props.integrationOptions),...props.options,...props}),(0,metadata_resource_1().addConstructMetadata)(this,props),props.proxy!==!1&&(this.root.addProxy(),this.root.addResource=addResourceThrows,this.root.addMethod=addMethodThrows,this.root.addProxy=addProxyThrows),this.node.addValidation({validate(){for(const value of Object.values(props.deployOptions?.variables??{})){const regexp=/[A-Za-z0-9-._~:/?#&=,]+/;if(value.match(regexp)===null)return["Stage variable value "+value+" does not match the regex."]}return[]}})}};exports.LambdaRestApi=LambdaRestApi,_a=JSII_RTTI_SYMBOL_1,LambdaRestApi[_a]={fqn:"aws-cdk-lib.aws_apigateway.LambdaRestApi",version:"2.201.0"},LambdaRestApi.PROPERTY_INJECTION_ID="aws-cdk-lib.aws-apigateway.LambdaRestApi",exports.LambdaRestApi=LambdaRestApi=__decorate([prop_injectable_1().propertyInjectable],LambdaRestApi);function addResourceThrows(){throw new(errors_1()).UnscopedValidationError("Cannot call 'addResource' on a proxying LambdaRestApi; set 'proxy' to false")}function addMethodThrows(){throw new(errors_1()).UnscopedValidationError("Cannot call 'addMethod' on a proxying LambdaRestApi; set 'proxy' to false")}function addProxyThrows(){throw new(errors_1()).UnscopedValidationError("Cannot call 'addProxy' on a proxying LambdaRestApi; set 'proxy' to false")}
