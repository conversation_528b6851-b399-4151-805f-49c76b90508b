"use strict";var _a;Object.defineProperty(exports,"__esModule",{value:!0}),exports.LambdaIntegration=void 0;var jsiiDeprecationWarnings=()=>{var tmp=require("../../../.warnings.jsii.js");return jsiiDeprecationWarnings=()=>tmp,tmp};const JSII_RTTI_SYMBOL_1=Symbol.for("jsii.rtti");var aws_1=()=>{var tmp=require("./aws");return aws_1=()=>tmp,tmp},iam=()=>{var tmp=require("../../../aws-iam");return iam=()=>tmp,tmp},lambda=()=>{var tmp=require("../../../aws-lambda");return lambda=()=>tmp,tmp},core_1=()=>{var tmp=require("../../../core");return core_1=()=>tmp,tmp};class LambdaIntegration extends aws_1().AwsIntegration{constructor(handler,options={}){try{jsiiDeprecationWarnings().aws_cdk_lib_aws_lambda_IFunction(handler),jsiiDeprecationWarnings().aws_cdk_lib_aws_apigateway_LambdaIntegrationOptions(options)}catch(error){throw process.env.JSII_DEBUG!=="1"&&error.name==="DeprecationError"&&Error.captureStackTrace(error,LambdaIntegration),error}const proxy=options.proxy??!0;super({proxy,service:"lambda",path:`2015-03-31/functions/${handler.functionArn}/invocations`,options}),this.handler=handler,this.enableTest=options.allowTestInvoke??!0}bind(method){try{jsiiDeprecationWarnings().aws_cdk_lib_aws_apigateway_Method(method)}catch(error){throw process.env.JSII_DEBUG!=="1"&&error.name==="DeprecationError"&&Error.captureStackTrace(error,this.bind),error}const bindResult=super.bind(method),principal=new(iam()).ServicePrincipal("apigateway.amazonaws.com"),desc=`${core_1().Names.nodeUniqueId(method.api.node)}.${method.httpMethod}.${method.resource.path.replace(/\//g,".")}`;this.handler.addPermission(`ApiPermission.${desc}`,{principal,scope:method,sourceArn:core_1().Lazy.string({produce:()=>method.methodArn})}),this.enableTest&&this.handler.addPermission(`ApiPermission.Test.${desc}`,{principal,scope:method,sourceArn:method.testMethodArn});let functionName;this.handler instanceof lambda().Function?functionName=this.handler.node.defaultChild.functionName:functionName=this.handler.functionName;let deploymentToken;return core_1().Token.isUnresolved(functionName)||(deploymentToken=JSON.stringify({functionName})),{...bindResult,deploymentToken}}}exports.LambdaIntegration=LambdaIntegration,_a=JSII_RTTI_SYMBOL_1,LambdaIntegration[_a]={fqn:"aws-cdk-lib.aws_apigateway.LambdaIntegration",version:"2.201.0"};
