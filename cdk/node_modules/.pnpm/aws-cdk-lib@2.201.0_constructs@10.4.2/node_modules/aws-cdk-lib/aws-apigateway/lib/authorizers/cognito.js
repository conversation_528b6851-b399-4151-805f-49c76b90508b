"use strict";var __decorate=exports&&exports.__decorate||function(decorators,target,key,desc){var c=arguments.length,r=c<3?target:desc===null?desc=Object.getOwnPropertyDescriptor(target,key):desc,d;if(typeof Reflect=="object"&&typeof Reflect.decorate=="function")r=Reflect.decorate(decorators,target,key,desc);else for(var i=decorators.length-1;i>=0;i--)(d=decorators[i])&&(r=(c<3?d(r):c>3?d(target,key,r):d(target,key))||r);return c>3&&r&&Object.defineProperty(target,key,r),r},_a;Object.defineProperty(exports,"__esModule",{value:!0}),exports.CognitoUserPoolsAuthorizer=void 0;var jsiiDeprecationWarnings=()=>{var tmp=require("../../../.warnings.jsii.js");return jsiiDeprecationWarnings=()=>tmp,tmp};const JSII_RTTI_SYMBOL_1=Symbol.for("jsii.rtti");var identity_source_1=()=>{var tmp=require("./identity-source");return identity_source_1=()=>tmp,tmp},core_1=()=>{var tmp=require("../../../core");return core_1=()=>tmp,tmp},errors_1=()=>{var tmp=require("../../../core/lib/errors");return errors_1=()=>tmp,tmp},metadata_resource_1=()=>{var tmp=require("../../../core/lib/metadata-resource");return metadata_resource_1=()=>tmp,tmp},prop_injectable_1=()=>{var tmp=require("../../../core/lib/prop-injectable");return prop_injectable_1=()=>tmp,tmp},cx_api_1=()=>{var tmp=require("../../../cx-api");return cx_api_1=()=>tmp,tmp},apigateway_generated_1=()=>{var tmp=require("../apigateway.generated");return apigateway_generated_1=()=>tmp,tmp},authorizer_1=()=>{var tmp=require("../authorizer");return authorizer_1=()=>tmp,tmp},method_1=()=>{var tmp=require("../method");return method_1=()=>tmp,tmp};let CognitoUserPoolsAuthorizer=class CognitoUserPoolsAuthorizer2 extends authorizer_1().Authorizer{constructor(scope,id,props){super(scope,id);try{jsiiDeprecationWarnings().aws_cdk_lib_aws_apigateway_CognitoUserPoolsAuthorizerProps(props)}catch(error){throw process.env.JSII_DEBUG!=="1"&&error.name==="DeprecationError"&&Error.captureStackTrace(error,CognitoUserPoolsAuthorizer2),error}(0,metadata_resource_1().addConstructMetadata)(this,props);const restApiId=this.lazyRestApiId(),authorizerProps={name:props.authorizerName??core_1().Names.uniqueId(this),restApiId,type:"COGNITO_USER_POOLS",providerArns:props.cognitoUserPools.map(userPool=>userPool.userPoolArn),authorizerResultTtlInSeconds:props.resultsCacheTtl?.toSeconds(),identitySource:props.identitySource||identity_source_1().IdentitySource.header("Authorization")};this.authorizerProps=authorizerProps;const resource=new(apigateway_generated_1()).CfnAuthorizer(this,"Resource",authorizerProps);this.authorizerId=resource.ref,this.authorizerArn=core_1().Stack.of(this).formatArn({service:"execute-api",resource:restApiId,resourceName:`authorizers/${this.authorizerId}`}),this.authorizationType=method_1().AuthorizationType.COGNITO}_attachToApi(restApi){if(this.restApiId&&this.restApiId!==restApi.restApiId)throw new(errors_1()).ValidationError("Cannot attach authorizer to two different rest APIs",restApi);this.restApiId=restApi.restApiId;const addToLogicalId=core_1().FeatureFlags.of(this).isEnabled(cx_api_1().APIGATEWAY_AUTHORIZER_CHANGE_DEPLOYMENT_LOGICAL_ID),deployment=restApi.latestDeployment;deployment&&addToLogicalId&&(deployment.node.addDependency(this),deployment.addToLogicalId({authorizer:this.authorizerProps}))}lazyRestApiId(){return core_1().Lazy.string({produce:()=>{if(!this.restApiId)throw new(errors_1()).ValidationError(`Authorizer (${this.node.path}) must be attached to a RestApi`,this);return this.restApiId}})}};exports.CognitoUserPoolsAuthorizer=CognitoUserPoolsAuthorizer,_a=JSII_RTTI_SYMBOL_1,CognitoUserPoolsAuthorizer[_a]={fqn:"aws-cdk-lib.aws_apigateway.CognitoUserPoolsAuthorizer",version:"2.201.0"},CognitoUserPoolsAuthorizer.PROPERTY_INJECTION_ID="aws-cdk-lib.aws-apigateway.CognitoUserPoolsAuthorizer",exports.CognitoUserPoolsAuthorizer=CognitoUserPoolsAuthorizer=__decorate([prop_injectable_1().propertyInjectable],CognitoUserPoolsAuthorizer);
