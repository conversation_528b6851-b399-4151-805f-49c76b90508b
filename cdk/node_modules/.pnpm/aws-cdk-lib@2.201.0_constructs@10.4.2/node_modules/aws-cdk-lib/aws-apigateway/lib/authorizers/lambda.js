"use strict";var __decorate=exports&&exports.__decorate||function(decorators,target,key,desc){var c=arguments.length,r=c<3?target:desc===null?desc=Object.getOwnPropertyDescriptor(target,key):desc,d;if(typeof Reflect=="object"&&typeof Reflect.decorate=="function")r=Reflect.decorate(decorators,target,key,desc);else for(var i=decorators.length-1;i>=0;i--)(d=decorators[i])&&(r=(c<3?d(r):c>3?d(target,key,r):d(target,key))||r);return c>3&&r&&Object.defineProperty(target,key,r),r},_a,_b;Object.defineProperty(exports,"__esModule",{value:!0}),exports.RequestAuthorizer=exports.TokenAuthorizer=void 0;var jsiiDeprecationWarnings=()=>{var tmp=require("../../../.warnings.jsii.js");return jsiiDeprecationWarnings=()=>tmp,tmp};const JSII_RTTI_SYMBOL_1=Symbol.for("jsii.rtti");var identity_source_1=()=>{var tmp=require("./identity-source");return identity_source_1=()=>tmp,tmp},iam=()=>{var tmp=require("../../../aws-iam");return iam=()=>tmp,tmp},lambda=()=>{var tmp=require("../../../aws-lambda");return lambda=()=>tmp,tmp},core_1=()=>{var tmp=require("../../../core");return core_1=()=>tmp,tmp},errors_1=()=>{var tmp=require("../../../core/lib/errors");return errors_1=()=>tmp,tmp},metadata_resource_1=()=>{var tmp=require("../../../core/lib/metadata-resource");return metadata_resource_1=()=>tmp,tmp},prop_injectable_1=()=>{var tmp=require("../../../core/lib/prop-injectable");return prop_injectable_1=()=>tmp,tmp},cx_api_1=()=>{var tmp=require("../../../cx-api");return cx_api_1=()=>tmp,tmp},apigateway_generated_1=()=>{var tmp=require("../apigateway.generated");return apigateway_generated_1=()=>tmp,tmp},authorizer_1=()=>{var tmp=require("../authorizer");return authorizer_1=()=>tmp,tmp};class LambdaAuthorizer extends authorizer_1().Authorizer{constructor(scope,id,props){if(super(scope,id),this.handler=props.handler,this.role=props.assumeRole,props.resultsCacheTtl&&props.resultsCacheTtl?.toSeconds()>3600)throw new(errors_1()).ValidationError("Lambda authorizer property 'resultsCacheTtl' must not be greater than 3600 seconds (1 hour)",scope)}_attachToApi(restApi){if(this.restApiId&&this.restApiId!==restApi.restApiId)throw new(errors_1()).ValidationError("Cannot attach authorizer to two different rest APIs",this);this.restApiId=restApi.restApiId;const deployment=restApi.latestDeployment,addToLogicalId=core_1().FeatureFlags.of(this).isEnabled(cx_api_1().APIGATEWAY_AUTHORIZER_CHANGE_DEPLOYMENT_LOGICAL_ID);if(deployment&&addToLogicalId){let functionName;this.handler instanceof lambda().Function?functionName=this.handler.node.defaultChild.functionName:functionName=this.handler.functionName,deployment.node.addDependency(this),deployment.addToLogicalId({authorizer:this.authorizerProps,authorizerToken:functionName})}}setupPermissions(){this.role?iam().Role.isRole(this.role)&&this.addLambdaInvokePermission(this.role):this.addDefaultPermissionRole()}addDefaultPermissionRole(){this.handler.addPermission(`${core_1().Names.uniqueId(this)}:Permissions`,{principal:new(iam()).ServicePrincipal("apigateway.amazonaws.com"),sourceArn:this.authorizerArn})}addLambdaInvokePermission(role){role.attachInlinePolicy(new(iam()).Policy(this,"authorizerInvokePolicy",{statements:[new(iam()).PolicyStatement({resources:this.handler.resourceArnsForGrantInvoke,actions:["lambda:InvokeFunction"]})]}))}lazyRestApiId(){return core_1().Lazy.string({produce:()=>{if(!this.restApiId)throw new(errors_1()).ValidationError(`Authorizer (${this.node.path}) must be attached to a RestApi`,this);return this.restApiId}})}}let TokenAuthorizer=class TokenAuthorizer2 extends LambdaAuthorizer{constructor(scope,id,props){super(scope,id,props);try{jsiiDeprecationWarnings().aws_cdk_lib_aws_apigateway_TokenAuthorizerProps(props)}catch(error){throw process.env.JSII_DEBUG!=="1"&&error.name==="DeprecationError"&&Error.captureStackTrace(error,TokenAuthorizer2),error}(0,metadata_resource_1().addConstructMetadata)(this,props);const restApiId=this.lazyRestApiId(),authorizerProps={name:props.authorizerName??core_1().Names.uniqueId(this),restApiId,type:"TOKEN",authorizerUri:lambdaAuthorizerArn(props.handler),authorizerCredentials:props.assumeRole?.roleArn,authorizerResultTtlInSeconds:props.resultsCacheTtl?.toSeconds()??core_1().Duration.minutes(5).toSeconds(),identitySource:props.identitySource||identity_source_1().IdentitySource.header("Authorization"),identityValidationExpression:props.validationRegex};this.authorizerProps=authorizerProps;const resource=new(apigateway_generated_1()).CfnAuthorizer(this,"Resource",authorizerProps);this.authorizerId=resource.ref,this.authorizerArn=core_1().Stack.of(this).formatArn({service:"execute-api",resource:restApiId,resourceName:`authorizers/${this.authorizerId}`}),this.setupPermissions()}};exports.TokenAuthorizer=TokenAuthorizer,_a=JSII_RTTI_SYMBOL_1,TokenAuthorizer[_a]={fqn:"aws-cdk-lib.aws_apigateway.TokenAuthorizer",version:"2.201.0"},TokenAuthorizer.PROPERTY_INJECTION_ID="aws-cdk-lib.aws-apigateway.TokenAuthorizer",exports.TokenAuthorizer=TokenAuthorizer=__decorate([prop_injectable_1().propertyInjectable],TokenAuthorizer);let RequestAuthorizer=class RequestAuthorizer2 extends LambdaAuthorizer{constructor(scope,id,props){super(scope,id,props);try{jsiiDeprecationWarnings().aws_cdk_lib_aws_apigateway_RequestAuthorizerProps(props)}catch(error){throw process.env.JSII_DEBUG!=="1"&&error.name==="DeprecationError"&&Error.captureStackTrace(error,RequestAuthorizer2),error}if((0,metadata_resource_1().addConstructMetadata)(this,props),(props.resultsCacheTtl===void 0||props.resultsCacheTtl.toSeconds()!==0)&&props.identitySources.length===0)throw new(errors_1()).ValidationError("At least one Identity Source is required for a REQUEST-based Lambda authorizer if caching is enabled.",scope);const restApiId=this.lazyRestApiId(),authorizerProps={name:props.authorizerName??core_1().Names.uniqueId(this),restApiId,type:"REQUEST",authorizerUri:lambdaAuthorizerArn(props.handler),authorizerCredentials:props.assumeRole?.roleArn,authorizerResultTtlInSeconds:props.resultsCacheTtl?.toSeconds()??core_1().Duration.minutes(5).toSeconds(),identitySource:props.identitySources.map(is=>is.toString()).join(",")};this.authorizerProps=authorizerProps;const resource=new(apigateway_generated_1()).CfnAuthorizer(this,"Resource",authorizerProps);this.authorizerId=resource.ref,this.authorizerArn=core_1().Stack.of(this).formatArn({service:"execute-api",resource:restApiId,resourceName:`authorizers/${this.authorizerId}`}),this.setupPermissions()}};exports.RequestAuthorizer=RequestAuthorizer,_b=JSII_RTTI_SYMBOL_1,RequestAuthorizer[_b]={fqn:"aws-cdk-lib.aws_apigateway.RequestAuthorizer",version:"2.201.0"},RequestAuthorizer.PROPERTY_INJECTION_ID="aws-cdk-lib.aws-apigateway.RequestAuthorizer",exports.RequestAuthorizer=RequestAuthorizer=__decorate([prop_injectable_1().propertyInjectable],RequestAuthorizer);function lambdaAuthorizerArn(handler){const{region,partition}=core_1().Arn.split(handler.functionArn,core_1().ArnFormat.COLON_RESOURCE_NAME);return`arn:${partition}:apigateway:${region}:lambda:path/2015-03-31/functions/${handler.functionArn}/invocations`}
