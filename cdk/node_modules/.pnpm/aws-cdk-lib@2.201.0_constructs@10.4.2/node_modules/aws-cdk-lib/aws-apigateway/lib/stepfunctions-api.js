"use strict";var __decorate=exports&&exports.__decorate||function(decorators,target,key,desc){var c=arguments.length,r=c<3?target:desc===null?desc=Object.getOwnPropertyDescriptor(target,key):desc,d;if(typeof Reflect=="object"&&typeof Reflect.decorate=="function")r=Reflect.decorate(decorators,target,key,desc);else for(var i=decorators.length-1;i>=0;i--)(d=decorators[i])&&(r=(c<3?d(r):c>3?d(target,key,r):d(target,key))||r);return c>3&&r&&Object.defineProperty(target,key,r),r},_a;Object.defineProperty(exports,"__esModule",{value:!0}),exports.StepFunctionsRestApi=void 0;var jsiiDeprecationWarnings=()=>{var tmp=require("../../.warnings.jsii.js");return jsiiDeprecationWarnings=()=>tmp,tmp};const JSII_RTTI_SYMBOL_1=Symbol.for("jsii.rtti");var _1=()=>{var tmp=require(".");return _1=()=>tmp,tmp},stepfunctions_1=()=>{var tmp=require("./integrations/stepfunctions");return stepfunctions_1=()=>tmp,tmp},sfn=()=>{var tmp=require("../../aws-stepfunctions");return sfn=()=>tmp,tmp},errors_1=()=>{var tmp=require("../../core/lib/errors");return errors_1=()=>tmp,tmp},metadata_resource_1=()=>{var tmp=require("../../core/lib/metadata-resource");return metadata_resource_1=()=>tmp,tmp},prop_injectable_1=()=>{var tmp=require("../../core/lib/prop-injectable");return prop_injectable_1=()=>tmp,tmp};let StepFunctionsRestApi=class StepFunctionsRestApi2 extends _1().RestApi{constructor(scope,id,props){try{jsiiDeprecationWarnings().aws_cdk_lib_aws_apigateway_StepFunctionsRestApiProps(props)}catch(error){throw process.env.JSII_DEBUG!=="1"&&error.name==="DeprecationError"&&Error.captureStackTrace(error,StepFunctionsRestApi2),error}if(props.defaultIntegration)throw new(errors_1()).ValidationError('Cannot specify "defaultIntegration" since Step Functions integration is automatically defined',scope);if(props.stateMachine.node.defaultChild.stateMachineType!==sfn().StateMachineType.EXPRESS)throw new(errors_1()).ValidationError('State Machine must be of type "EXPRESS". Please use StateMachineType.EXPRESS as the stateMachineType',scope);const stepfunctionsIntegration=stepfunctions_1().StepFunctionsIntegration.startExecution(props.stateMachine,{credentialsRole:props.role,requestContext:props.requestContext,path:props.path??!0,querystring:props.querystring??!0,headers:props.headers,authorizer:props.authorizer,useDefaultMethodResponses:props.useDefaultMethodResponses});super(scope,id,props),(0,metadata_resource_1().addConstructMetadata)(this,props),this.root.addMethod("ANY",stepfunctionsIntegration)}};exports.StepFunctionsRestApi=StepFunctionsRestApi,_a=JSII_RTTI_SYMBOL_1,StepFunctionsRestApi[_a]={fqn:"aws-cdk-lib.aws_apigateway.StepFunctionsRestApi",version:"2.201.0"},StepFunctionsRestApi.PROPERTY_INJECTION_ID="aws-cdk-lib.aws-apigateway.StepFunctionsRestApi",exports.StepFunctionsRestApi=StepFunctionsRestApi=__decorate([prop_injectable_1().propertyInjectable],StepFunctionsRestApi);
