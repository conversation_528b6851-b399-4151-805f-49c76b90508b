"use strict";var __decorate=exports&&exports.__decorate||function(decorators,target,key,desc){var c=arguments.length,r=c<3?target:desc===null?desc=Object.getOwnPropertyDescriptor(target,key):desc,d;if(typeof Reflect=="object"&&typeof Reflect.decorate=="function")r=Reflect.decorate(decorators,target,key,desc);else for(var i=decorators.length-1;i>=0;i--)(d=decorators[i])&&(r=(c<3?d(r):c>3?d(target,key,r):d(target,key))||r);return c>3&&r&&Object.defineProperty(target,key,r),r},_a;Object.defineProperty(exports,"__esModule",{value:!0}),exports.VpcLink=void 0;var jsiiDeprecationWarnings=()=>{var tmp=require("../../.warnings.jsii.js");return jsiiDeprecationWarnings=()=>tmp,tmp};const JSII_RTTI_SYMBOL_1=Symbol.for("jsii.rtti");var apigateway_generated_1=()=>{var tmp=require("./apigateway.generated");return apigateway_generated_1=()=>tmp,tmp},core_1=()=>{var tmp=require("../../core");return core_1=()=>tmp,tmp},metadata_resource_1=()=>{var tmp=require("../../core/lib/metadata-resource");return metadata_resource_1=()=>tmp,tmp},prop_injectable_1=()=>{var tmp=require("../../core/lib/prop-injectable");return prop_injectable_1=()=>tmp,tmp};let VpcLink=class VpcLink2 extends core_1().Resource{static fromVpcLinkId(scope,id,vpcLinkId){class Import extends core_1().Resource{constructor(){super(...arguments),this.vpcLinkId=vpcLinkId}}return new Import(scope,id)}constructor(scope,id,props={}){super(scope,id,{physicalName:props.vpcLinkName||core_1().Lazy.string({produce:()=>core_1().Names.nodeUniqueId(this.node)})}),this._targets=new Array;try{jsiiDeprecationWarnings().aws_cdk_lib_aws_apigateway_VpcLinkProps(props)}catch(error){throw process.env.JSII_DEBUG!=="1"&&error.name==="DeprecationError"&&Error.captureStackTrace(error,VpcLink2),error}(0,metadata_resource_1().addConstructMetadata)(this,props);const cfnResource=new(apigateway_generated_1()).CfnVpcLink(this,"Resource",{name:this.physicalName,description:props.description,targetArns:core_1().Lazy.list({produce:()=>this.renderTargets()})});this.vpcLinkId=cfnResource.ref,props.targets&&this.addTargets(...props.targets),this.node.addValidation({validate:()=>this.validateVpcLink()})}addTargets(...targets){try{jsiiDeprecationWarnings().aws_cdk_lib_aws_elasticloadbalancingv2_INetworkLoadBalancer(targets)}catch(error){throw process.env.JSII_DEBUG!=="1"&&error.name==="DeprecationError"&&Error.captureStackTrace(error,this.addTargets),error}this._targets.push(...targets)}get _targetDnsNames(){return this._targets.map(t=>t.loadBalancerDnsName)}validateVpcLink(){return this._targets.length===0?["No targets added to vpc link"]:[]}renderTargets(){return this._targets.map(nlb=>nlb.loadBalancerArn)}};exports.VpcLink=VpcLink,_a=JSII_RTTI_SYMBOL_1,VpcLink[_a]={fqn:"aws-cdk-lib.aws_apigateway.VpcLink",version:"2.201.0"},VpcLink.PROPERTY_INJECTION_ID="aws-cdk-lib.aws-apigateway.VpcLink",__decorate([(0,metadata_resource_1().MethodMetadata)()],VpcLink.prototype,"addTargets",null),exports.VpcLink=VpcLink=__decorate([prop_injectable_1().propertyInjectable],VpcLink);
