"use strict";var _a,_b,_c;Object.defineProperty(exports,"__esModule",{value:!0}),exports.CfnScraper=exports.CfnWorkspace=exports.CfnRuleGroupsNamespace=void 0;var jsiiDeprecationWarnings=()=>{var tmp=require("../../.warnings.jsii.js");return jsiiDeprecationWarnings=()=>tmp,tmp};const JSII_RTTI_SYMBOL_1=Symbol.for("jsii.rtti");var cdk=()=>{var tmp=require("../../core");return cdk=()=>tmp,tmp},cfn_parse=()=>{var tmp=require("../../core/lib/helpers-internal");return cfn_parse=()=>tmp,tmp},cdk_errors=()=>{var tmp=require("../../core/lib/errors");return cdk_errors=()=>tmp,tmp};class CfnRuleGroupsNamespace extends cdk().CfnResource{static _fromCloudFormation(scope,id,resourceAttributes,options){resourceAttributes=resourceAttributes||{};const resourceProperties=options.parser.parseValue(resourceAttributes.Properties),propsResult=CfnRuleGroupsNamespacePropsFromCloudFormation(resourceProperties);if(cdk().isResolvableObject(propsResult.value))throw new(cdk_errors()).ValidationError("Unexpected IResolvable",scope);const ret=new CfnRuleGroupsNamespace(scope,id,propsResult.value);for(const[propKey,propVal]of Object.entries(propsResult.extraProperties))ret.addPropertyOverride(propKey,propVal);return options.parser.handleAttributes(ret,resourceAttributes,id),ret}constructor(scope,id,props){super(scope,id,{type:CfnRuleGroupsNamespace.CFN_RESOURCE_TYPE_NAME,properties:props});try{jsiiDeprecationWarnings().aws_cdk_lib_aws_aps_CfnRuleGroupsNamespaceProps(props)}catch(error){throw process.env.JSII_DEBUG!=="1"&&error.name==="DeprecationError"&&Error.captureStackTrace(error,CfnRuleGroupsNamespace),error}cdk().requireProperty(props,"data",this),cdk().requireProperty(props,"name",this),cdk().requireProperty(props,"workspace",this),this.attrArn=cdk().Token.asString(this.getAtt("Arn",cdk().ResolutionTypeHint.STRING)),this.data=props.data,this.name=props.name,this.tags=new(cdk()).TagManager(cdk().TagType.STANDARD,"AWS::APS::RuleGroupsNamespace",props.tags,{tagPropertyName:"tags"}),this.tagsRaw=props.tags,this.workspace=props.workspace}get cfnProperties(){return{data:this.data,name:this.name,tags:this.tags.renderTags(),workspace:this.workspace}}inspect(inspector){try{jsiiDeprecationWarnings().aws_cdk_lib_TreeInspector(inspector)}catch(error){throw process.env.JSII_DEBUG!=="1"&&error.name==="DeprecationError"&&Error.captureStackTrace(error,this.inspect),error}inspector.addAttribute("aws:cdk:cloudformation:type",CfnRuleGroupsNamespace.CFN_RESOURCE_TYPE_NAME),inspector.addAttribute("aws:cdk:cloudformation:props",this.cfnProperties)}renderProperties(props){return convertCfnRuleGroupsNamespacePropsToCloudFormation(props)}}exports.CfnRuleGroupsNamespace=CfnRuleGroupsNamespace,_a=JSII_RTTI_SYMBOL_1,CfnRuleGroupsNamespace[_a]={fqn:"aws-cdk-lib.aws_aps.CfnRuleGroupsNamespace",version:"2.201.0"},CfnRuleGroupsNamespace.CFN_RESOURCE_TYPE_NAME="AWS::APS::RuleGroupsNamespace";function CfnRuleGroupsNamespacePropsValidator(properties){if(!cdk().canInspect(properties))return cdk().VALIDATION_SUCCESS;const errors=new(cdk()).ValidationResults;return properties&&typeof properties=="object"&&!Array.isArray(properties)||errors.collect(new(cdk()).ValidationResult("Expected an object, but received: "+JSON.stringify(properties))),errors.collect(cdk().propertyValidator("data",cdk().requiredValidator)(properties.data)),errors.collect(cdk().propertyValidator("data",cdk().validateString)(properties.data)),errors.collect(cdk().propertyValidator("name",cdk().requiredValidator)(properties.name)),errors.collect(cdk().propertyValidator("name",cdk().validateString)(properties.name)),errors.collect(cdk().propertyValidator("tags",cdk().listValidator(cdk().validateCfnTag))(properties.tags)),errors.collect(cdk().propertyValidator("workspace",cdk().requiredValidator)(properties.workspace)),errors.collect(cdk().propertyValidator("workspace",cdk().validateString)(properties.workspace)),errors.wrap('supplied properties not correct for "CfnRuleGroupsNamespaceProps"')}function convertCfnRuleGroupsNamespacePropsToCloudFormation(properties){return cdk().canInspect(properties)?(CfnRuleGroupsNamespacePropsValidator(properties).assertSuccess(),{Data:cdk().stringToCloudFormation(properties.data),Name:cdk().stringToCloudFormation(properties.name),Tags:cdk().listMapper(cdk().cfnTagToCloudFormation)(properties.tags),Workspace:cdk().stringToCloudFormation(properties.workspace)}):properties}function CfnRuleGroupsNamespacePropsFromCloudFormation(properties){if(cdk().isResolvableObject(properties))return new(cfn_parse()).FromCloudFormationResult(properties);if(properties=properties??{},!(properties&&typeof properties=="object"&&!Array.isArray(properties)))return new(cfn_parse()).FromCloudFormationResult(properties);const ret=new(cfn_parse()).FromCloudFormationPropertyObject;return ret.addPropertyResult("data","Data",properties.Data!=null?cfn_parse().FromCloudFormation.getString(properties.Data):void 0),ret.addPropertyResult("name","Name",properties.Name!=null?cfn_parse().FromCloudFormation.getString(properties.Name):void 0),ret.addPropertyResult("tags","Tags",properties.Tags!=null?cfn_parse().FromCloudFormation.getArray(cfn_parse().FromCloudFormation.getCfnTag)(properties.Tags):void 0),ret.addPropertyResult("workspace","Workspace",properties.Workspace!=null?cfn_parse().FromCloudFormation.getString(properties.Workspace):void 0),ret.addUnrecognizedPropertiesAsExtra(properties),ret}class CfnWorkspace extends cdk().CfnResource{static _fromCloudFormation(scope,id,resourceAttributes,options){resourceAttributes=resourceAttributes||{};const resourceProperties=options.parser.parseValue(resourceAttributes.Properties),propsResult=CfnWorkspacePropsFromCloudFormation(resourceProperties);if(cdk().isResolvableObject(propsResult.value))throw new(cdk_errors()).ValidationError("Unexpected IResolvable",scope);const ret=new CfnWorkspace(scope,id,propsResult.value);for(const[propKey,propVal]of Object.entries(propsResult.extraProperties))ret.addPropertyOverride(propKey,propVal);return options.parser.handleAttributes(ret,resourceAttributes,id),ret}constructor(scope,id,props={}){super(scope,id,{type:CfnWorkspace.CFN_RESOURCE_TYPE_NAME,properties:props});try{jsiiDeprecationWarnings().aws_cdk_lib_aws_aps_CfnWorkspaceProps(props)}catch(error){throw process.env.JSII_DEBUG!=="1"&&error.name==="DeprecationError"&&Error.captureStackTrace(error,CfnWorkspace),error}this.attrArn=cdk().Token.asString(this.getAtt("Arn",cdk().ResolutionTypeHint.STRING)),this.attrPrometheusEndpoint=cdk().Token.asString(this.getAtt("PrometheusEndpoint",cdk().ResolutionTypeHint.STRING)),this.attrWorkspaceId=cdk().Token.asString(this.getAtt("WorkspaceId",cdk().ResolutionTypeHint.STRING)),this.alertManagerDefinition=props.alertManagerDefinition,this.alias=props.alias,this.kmsKeyArn=props.kmsKeyArn,this.loggingConfiguration=props.loggingConfiguration,this.queryLoggingConfiguration=props.queryLoggingConfiguration,this.tags=new(cdk()).TagManager(cdk().TagType.STANDARD,"AWS::APS::Workspace",props.tags,{tagPropertyName:"tags"}),this.tagsRaw=props.tags,this.workspaceConfiguration=props.workspaceConfiguration}get cfnProperties(){return{alertManagerDefinition:this.alertManagerDefinition,alias:this.alias,kmsKeyArn:this.kmsKeyArn,loggingConfiguration:this.loggingConfiguration,queryLoggingConfiguration:this.queryLoggingConfiguration,tags:this.tags.renderTags(),workspaceConfiguration:this.workspaceConfiguration}}inspect(inspector){try{jsiiDeprecationWarnings().aws_cdk_lib_TreeInspector(inspector)}catch(error){throw process.env.JSII_DEBUG!=="1"&&error.name==="DeprecationError"&&Error.captureStackTrace(error,this.inspect),error}inspector.addAttribute("aws:cdk:cloudformation:type",CfnWorkspace.CFN_RESOURCE_TYPE_NAME),inspector.addAttribute("aws:cdk:cloudformation:props",this.cfnProperties)}renderProperties(props){return convertCfnWorkspacePropsToCloudFormation(props)}}exports.CfnWorkspace=CfnWorkspace,_b=JSII_RTTI_SYMBOL_1,CfnWorkspace[_b]={fqn:"aws-cdk-lib.aws_aps.CfnWorkspace",version:"2.201.0"},CfnWorkspace.CFN_RESOURCE_TYPE_NAME="AWS::APS::Workspace";function CfnWorkspaceLoggingConfigurationPropertyValidator(properties){if(!cdk().canInspect(properties))return cdk().VALIDATION_SUCCESS;const errors=new(cdk()).ValidationResults;return properties&&typeof properties=="object"&&!Array.isArray(properties)||errors.collect(new(cdk()).ValidationResult("Expected an object, but received: "+JSON.stringify(properties))),errors.collect(cdk().propertyValidator("logGroupArn",cdk().validateString)(properties.logGroupArn)),errors.wrap('supplied properties not correct for "LoggingConfigurationProperty"')}function convertCfnWorkspaceLoggingConfigurationPropertyToCloudFormation(properties){return cdk().canInspect(properties)?(CfnWorkspaceLoggingConfigurationPropertyValidator(properties).assertSuccess(),{LogGroupArn:cdk().stringToCloudFormation(properties.logGroupArn)}):properties}function CfnWorkspaceLoggingConfigurationPropertyFromCloudFormation(properties){if(cdk().isResolvableObject(properties))return new(cfn_parse()).FromCloudFormationResult(properties);if(properties=properties??{},!(properties&&typeof properties=="object"&&!Array.isArray(properties)))return new(cfn_parse()).FromCloudFormationResult(properties);const ret=new(cfn_parse()).FromCloudFormationPropertyObject;return ret.addPropertyResult("logGroupArn","LogGroupArn",properties.LogGroupArn!=null?cfn_parse().FromCloudFormation.getString(properties.LogGroupArn):void 0),ret.addUnrecognizedPropertiesAsExtra(properties),ret}function CfnWorkspaceLimitsPerLabelSetEntryPropertyValidator(properties){if(!cdk().canInspect(properties))return cdk().VALIDATION_SUCCESS;const errors=new(cdk()).ValidationResults;return properties&&typeof properties=="object"&&!Array.isArray(properties)||errors.collect(new(cdk()).ValidationResult("Expected an object, but received: "+JSON.stringify(properties))),errors.collect(cdk().propertyValidator("maxSeries",cdk().validateNumber)(properties.maxSeries)),errors.wrap('supplied properties not correct for "LimitsPerLabelSetEntryProperty"')}function convertCfnWorkspaceLimitsPerLabelSetEntryPropertyToCloudFormation(properties){return cdk().canInspect(properties)?(CfnWorkspaceLimitsPerLabelSetEntryPropertyValidator(properties).assertSuccess(),{MaxSeries:cdk().numberToCloudFormation(properties.maxSeries)}):properties}function CfnWorkspaceLimitsPerLabelSetEntryPropertyFromCloudFormation(properties){if(cdk().isResolvableObject(properties))return new(cfn_parse()).FromCloudFormationResult(properties);if(properties=properties??{},!(properties&&typeof properties=="object"&&!Array.isArray(properties)))return new(cfn_parse()).FromCloudFormationResult(properties);const ret=new(cfn_parse()).FromCloudFormationPropertyObject;return ret.addPropertyResult("maxSeries","MaxSeries",properties.MaxSeries!=null?cfn_parse().FromCloudFormation.getNumber(properties.MaxSeries):void 0),ret.addUnrecognizedPropertiesAsExtra(properties),ret}function CfnWorkspaceLabelPropertyValidator(properties){if(!cdk().canInspect(properties))return cdk().VALIDATION_SUCCESS;const errors=new(cdk()).ValidationResults;return properties&&typeof properties=="object"&&!Array.isArray(properties)||errors.collect(new(cdk()).ValidationResult("Expected an object, but received: "+JSON.stringify(properties))),errors.collect(cdk().propertyValidator("name",cdk().requiredValidator)(properties.name)),errors.collect(cdk().propertyValidator("name",cdk().validateString)(properties.name)),errors.collect(cdk().propertyValidator("value",cdk().requiredValidator)(properties.value)),errors.collect(cdk().propertyValidator("value",cdk().validateString)(properties.value)),errors.wrap('supplied properties not correct for "LabelProperty"')}function convertCfnWorkspaceLabelPropertyToCloudFormation(properties){return cdk().canInspect(properties)?(CfnWorkspaceLabelPropertyValidator(properties).assertSuccess(),{Name:cdk().stringToCloudFormation(properties.name),Value:cdk().stringToCloudFormation(properties.value)}):properties}function CfnWorkspaceLabelPropertyFromCloudFormation(properties){if(cdk().isResolvableObject(properties))return new(cfn_parse()).FromCloudFormationResult(properties);if(properties=properties??{},!(properties&&typeof properties=="object"&&!Array.isArray(properties)))return new(cfn_parse()).FromCloudFormationResult(properties);const ret=new(cfn_parse()).FromCloudFormationPropertyObject;return ret.addPropertyResult("name","Name",properties.Name!=null?cfn_parse().FromCloudFormation.getString(properties.Name):void 0),ret.addPropertyResult("value","Value",properties.Value!=null?cfn_parse().FromCloudFormation.getString(properties.Value):void 0),ret.addUnrecognizedPropertiesAsExtra(properties),ret}function CfnWorkspaceLimitsPerLabelSetPropertyValidator(properties){if(!cdk().canInspect(properties))return cdk().VALIDATION_SUCCESS;const errors=new(cdk()).ValidationResults;return properties&&typeof properties=="object"&&!Array.isArray(properties)||errors.collect(new(cdk()).ValidationResult("Expected an object, but received: "+JSON.stringify(properties))),errors.collect(cdk().propertyValidator("labelSet",cdk().requiredValidator)(properties.labelSet)),errors.collect(cdk().propertyValidator("labelSet",cdk().listValidator(CfnWorkspaceLabelPropertyValidator))(properties.labelSet)),errors.collect(cdk().propertyValidator("limits",cdk().requiredValidator)(properties.limits)),errors.collect(cdk().propertyValidator("limits",CfnWorkspaceLimitsPerLabelSetEntryPropertyValidator)(properties.limits)),errors.wrap('supplied properties not correct for "LimitsPerLabelSetProperty"')}function convertCfnWorkspaceLimitsPerLabelSetPropertyToCloudFormation(properties){return cdk().canInspect(properties)?(CfnWorkspaceLimitsPerLabelSetPropertyValidator(properties).assertSuccess(),{LabelSet:cdk().listMapper(convertCfnWorkspaceLabelPropertyToCloudFormation)(properties.labelSet),Limits:convertCfnWorkspaceLimitsPerLabelSetEntryPropertyToCloudFormation(properties.limits)}):properties}function CfnWorkspaceLimitsPerLabelSetPropertyFromCloudFormation(properties){if(cdk().isResolvableObject(properties))return new(cfn_parse()).FromCloudFormationResult(properties);if(properties=properties??{},!(properties&&typeof properties=="object"&&!Array.isArray(properties)))return new(cfn_parse()).FromCloudFormationResult(properties);const ret=new(cfn_parse()).FromCloudFormationPropertyObject;return ret.addPropertyResult("labelSet","LabelSet",properties.LabelSet!=null?cfn_parse().FromCloudFormation.getArray(CfnWorkspaceLabelPropertyFromCloudFormation)(properties.LabelSet):void 0),ret.addPropertyResult("limits","Limits",properties.Limits!=null?CfnWorkspaceLimitsPerLabelSetEntryPropertyFromCloudFormation(properties.Limits):void 0),ret.addUnrecognizedPropertiesAsExtra(properties),ret}function CfnWorkspaceWorkspaceConfigurationPropertyValidator(properties){if(!cdk().canInspect(properties))return cdk().VALIDATION_SUCCESS;const errors=new(cdk()).ValidationResults;return properties&&typeof properties=="object"&&!Array.isArray(properties)||errors.collect(new(cdk()).ValidationResult("Expected an object, but received: "+JSON.stringify(properties))),errors.collect(cdk().propertyValidator("limitsPerLabelSets",cdk().listValidator(CfnWorkspaceLimitsPerLabelSetPropertyValidator))(properties.limitsPerLabelSets)),errors.collect(cdk().propertyValidator("retentionPeriodInDays",cdk().validateNumber)(properties.retentionPeriodInDays)),errors.wrap('supplied properties not correct for "WorkspaceConfigurationProperty"')}function convertCfnWorkspaceWorkspaceConfigurationPropertyToCloudFormation(properties){return cdk().canInspect(properties)?(CfnWorkspaceWorkspaceConfigurationPropertyValidator(properties).assertSuccess(),{LimitsPerLabelSets:cdk().listMapper(convertCfnWorkspaceLimitsPerLabelSetPropertyToCloudFormation)(properties.limitsPerLabelSets),RetentionPeriodInDays:cdk().numberToCloudFormation(properties.retentionPeriodInDays)}):properties}function CfnWorkspaceWorkspaceConfigurationPropertyFromCloudFormation(properties){if(cdk().isResolvableObject(properties))return new(cfn_parse()).FromCloudFormationResult(properties);if(properties=properties??{},!(properties&&typeof properties=="object"&&!Array.isArray(properties)))return new(cfn_parse()).FromCloudFormationResult(properties);const ret=new(cfn_parse()).FromCloudFormationPropertyObject;return ret.addPropertyResult("limitsPerLabelSets","LimitsPerLabelSets",properties.LimitsPerLabelSets!=null?cfn_parse().FromCloudFormation.getArray(CfnWorkspaceLimitsPerLabelSetPropertyFromCloudFormation)(properties.LimitsPerLabelSets):void 0),ret.addPropertyResult("retentionPeriodInDays","RetentionPeriodInDays",properties.RetentionPeriodInDays!=null?cfn_parse().FromCloudFormation.getNumber(properties.RetentionPeriodInDays):void 0),ret.addUnrecognizedPropertiesAsExtra(properties),ret}function CfnWorkspaceCloudWatchLogDestinationPropertyValidator(properties){if(!cdk().canInspect(properties))return cdk().VALIDATION_SUCCESS;const errors=new(cdk()).ValidationResults;return properties&&typeof properties=="object"&&!Array.isArray(properties)||errors.collect(new(cdk()).ValidationResult("Expected an object, but received: "+JSON.stringify(properties))),errors.collect(cdk().propertyValidator("logGroupArn",cdk().requiredValidator)(properties.logGroupArn)),errors.collect(cdk().propertyValidator("logGroupArn",cdk().validateString)(properties.logGroupArn)),errors.wrap('supplied properties not correct for "CloudWatchLogDestinationProperty"')}function convertCfnWorkspaceCloudWatchLogDestinationPropertyToCloudFormation(properties){return cdk().canInspect(properties)?(CfnWorkspaceCloudWatchLogDestinationPropertyValidator(properties).assertSuccess(),{LogGroupArn:cdk().stringToCloudFormation(properties.logGroupArn)}):properties}function CfnWorkspaceCloudWatchLogDestinationPropertyFromCloudFormation(properties){if(cdk().isResolvableObject(properties))return new(cfn_parse()).FromCloudFormationResult(properties);if(properties=properties??{},!(properties&&typeof properties=="object"&&!Array.isArray(properties)))return new(cfn_parse()).FromCloudFormationResult(properties);const ret=new(cfn_parse()).FromCloudFormationPropertyObject;return ret.addPropertyResult("logGroupArn","LogGroupArn",properties.LogGroupArn!=null?cfn_parse().FromCloudFormation.getString(properties.LogGroupArn):void 0),ret.addUnrecognizedPropertiesAsExtra(properties),ret}function CfnWorkspaceLoggingFilterPropertyValidator(properties){if(!cdk().canInspect(properties))return cdk().VALIDATION_SUCCESS;const errors=new(cdk()).ValidationResults;return properties&&typeof properties=="object"&&!Array.isArray(properties)||errors.collect(new(cdk()).ValidationResult("Expected an object, but received: "+JSON.stringify(properties))),errors.collect(cdk().propertyValidator("qspThreshold",cdk().requiredValidator)(properties.qspThreshold)),errors.collect(cdk().propertyValidator("qspThreshold",cdk().validateNumber)(properties.qspThreshold)),errors.wrap('supplied properties not correct for "LoggingFilterProperty"')}function convertCfnWorkspaceLoggingFilterPropertyToCloudFormation(properties){return cdk().canInspect(properties)?(CfnWorkspaceLoggingFilterPropertyValidator(properties).assertSuccess(),{QspThreshold:cdk().numberToCloudFormation(properties.qspThreshold)}):properties}function CfnWorkspaceLoggingFilterPropertyFromCloudFormation(properties){if(cdk().isResolvableObject(properties))return new(cfn_parse()).FromCloudFormationResult(properties);if(properties=properties??{},!(properties&&typeof properties=="object"&&!Array.isArray(properties)))return new(cfn_parse()).FromCloudFormationResult(properties);const ret=new(cfn_parse()).FromCloudFormationPropertyObject;return ret.addPropertyResult("qspThreshold","QspThreshold",properties.QspThreshold!=null?cfn_parse().FromCloudFormation.getNumber(properties.QspThreshold):void 0),ret.addUnrecognizedPropertiesAsExtra(properties),ret}function CfnWorkspaceLoggingDestinationPropertyValidator(properties){if(!cdk().canInspect(properties))return cdk().VALIDATION_SUCCESS;const errors=new(cdk()).ValidationResults;return properties&&typeof properties=="object"&&!Array.isArray(properties)||errors.collect(new(cdk()).ValidationResult("Expected an object, but received: "+JSON.stringify(properties))),errors.collect(cdk().propertyValidator("cloudWatchLogs",cdk().requiredValidator)(properties.cloudWatchLogs)),errors.collect(cdk().propertyValidator("cloudWatchLogs",CfnWorkspaceCloudWatchLogDestinationPropertyValidator)(properties.cloudWatchLogs)),errors.collect(cdk().propertyValidator("filters",cdk().requiredValidator)(properties.filters)),errors.collect(cdk().propertyValidator("filters",CfnWorkspaceLoggingFilterPropertyValidator)(properties.filters)),errors.wrap('supplied properties not correct for "LoggingDestinationProperty"')}function convertCfnWorkspaceLoggingDestinationPropertyToCloudFormation(properties){return cdk().canInspect(properties)?(CfnWorkspaceLoggingDestinationPropertyValidator(properties).assertSuccess(),{CloudWatchLogs:convertCfnWorkspaceCloudWatchLogDestinationPropertyToCloudFormation(properties.cloudWatchLogs),Filters:convertCfnWorkspaceLoggingFilterPropertyToCloudFormation(properties.filters)}):properties}function CfnWorkspaceLoggingDestinationPropertyFromCloudFormation(properties){if(cdk().isResolvableObject(properties))return new(cfn_parse()).FromCloudFormationResult(properties);if(properties=properties??{},!(properties&&typeof properties=="object"&&!Array.isArray(properties)))return new(cfn_parse()).FromCloudFormationResult(properties);const ret=new(cfn_parse()).FromCloudFormationPropertyObject;return ret.addPropertyResult("cloudWatchLogs","CloudWatchLogs",properties.CloudWatchLogs!=null?CfnWorkspaceCloudWatchLogDestinationPropertyFromCloudFormation(properties.CloudWatchLogs):void 0),ret.addPropertyResult("filters","Filters",properties.Filters!=null?CfnWorkspaceLoggingFilterPropertyFromCloudFormation(properties.Filters):void 0),ret.addUnrecognizedPropertiesAsExtra(properties),ret}function CfnWorkspaceQueryLoggingConfigurationPropertyValidator(properties){if(!cdk().canInspect(properties))return cdk().VALIDATION_SUCCESS;const errors=new(cdk()).ValidationResults;return properties&&typeof properties=="object"&&!Array.isArray(properties)||errors.collect(new(cdk()).ValidationResult("Expected an object, but received: "+JSON.stringify(properties))),errors.collect(cdk().propertyValidator("destinations",cdk().requiredValidator)(properties.destinations)),errors.collect(cdk().propertyValidator("destinations",cdk().listValidator(CfnWorkspaceLoggingDestinationPropertyValidator))(properties.destinations)),errors.wrap('supplied properties not correct for "QueryLoggingConfigurationProperty"')}function convertCfnWorkspaceQueryLoggingConfigurationPropertyToCloudFormation(properties){return cdk().canInspect(properties)?(CfnWorkspaceQueryLoggingConfigurationPropertyValidator(properties).assertSuccess(),{Destinations:cdk().listMapper(convertCfnWorkspaceLoggingDestinationPropertyToCloudFormation)(properties.destinations)}):properties}function CfnWorkspaceQueryLoggingConfigurationPropertyFromCloudFormation(properties){if(cdk().isResolvableObject(properties))return new(cfn_parse()).FromCloudFormationResult(properties);if(properties=properties??{},!(properties&&typeof properties=="object"&&!Array.isArray(properties)))return new(cfn_parse()).FromCloudFormationResult(properties);const ret=new(cfn_parse()).FromCloudFormationPropertyObject;return ret.addPropertyResult("destinations","Destinations",properties.Destinations!=null?cfn_parse().FromCloudFormation.getArray(CfnWorkspaceLoggingDestinationPropertyFromCloudFormation)(properties.Destinations):void 0),ret.addUnrecognizedPropertiesAsExtra(properties),ret}function CfnWorkspacePropsValidator(properties){if(!cdk().canInspect(properties))return cdk().VALIDATION_SUCCESS;const errors=new(cdk()).ValidationResults;return properties&&typeof properties=="object"&&!Array.isArray(properties)||errors.collect(new(cdk()).ValidationResult("Expected an object, but received: "+JSON.stringify(properties))),errors.collect(cdk().propertyValidator("alertManagerDefinition",cdk().validateString)(properties.alertManagerDefinition)),errors.collect(cdk().propertyValidator("alias",cdk().validateString)(properties.alias)),errors.collect(cdk().propertyValidator("kmsKeyArn",cdk().validateString)(properties.kmsKeyArn)),errors.collect(cdk().propertyValidator("loggingConfiguration",CfnWorkspaceLoggingConfigurationPropertyValidator)(properties.loggingConfiguration)),errors.collect(cdk().propertyValidator("queryLoggingConfiguration",CfnWorkspaceQueryLoggingConfigurationPropertyValidator)(properties.queryLoggingConfiguration)),errors.collect(cdk().propertyValidator("tags",cdk().listValidator(cdk().validateCfnTag))(properties.tags)),errors.collect(cdk().propertyValidator("workspaceConfiguration",CfnWorkspaceWorkspaceConfigurationPropertyValidator)(properties.workspaceConfiguration)),errors.wrap('supplied properties not correct for "CfnWorkspaceProps"')}function convertCfnWorkspacePropsToCloudFormation(properties){return cdk().canInspect(properties)?(CfnWorkspacePropsValidator(properties).assertSuccess(),{AlertManagerDefinition:cdk().stringToCloudFormation(properties.alertManagerDefinition),Alias:cdk().stringToCloudFormation(properties.alias),KmsKeyArn:cdk().stringToCloudFormation(properties.kmsKeyArn),LoggingConfiguration:convertCfnWorkspaceLoggingConfigurationPropertyToCloudFormation(properties.loggingConfiguration),QueryLoggingConfiguration:convertCfnWorkspaceQueryLoggingConfigurationPropertyToCloudFormation(properties.queryLoggingConfiguration),Tags:cdk().listMapper(cdk().cfnTagToCloudFormation)(properties.tags),WorkspaceConfiguration:convertCfnWorkspaceWorkspaceConfigurationPropertyToCloudFormation(properties.workspaceConfiguration)}):properties}function CfnWorkspacePropsFromCloudFormation(properties){if(cdk().isResolvableObject(properties))return new(cfn_parse()).FromCloudFormationResult(properties);if(properties=properties??{},!(properties&&typeof properties=="object"&&!Array.isArray(properties)))return new(cfn_parse()).FromCloudFormationResult(properties);const ret=new(cfn_parse()).FromCloudFormationPropertyObject;return ret.addPropertyResult("alertManagerDefinition","AlertManagerDefinition",properties.AlertManagerDefinition!=null?cfn_parse().FromCloudFormation.getString(properties.AlertManagerDefinition):void 0),ret.addPropertyResult("alias","Alias",properties.Alias!=null?cfn_parse().FromCloudFormation.getString(properties.Alias):void 0),ret.addPropertyResult("kmsKeyArn","KmsKeyArn",properties.KmsKeyArn!=null?cfn_parse().FromCloudFormation.getString(properties.KmsKeyArn):void 0),ret.addPropertyResult("loggingConfiguration","LoggingConfiguration",properties.LoggingConfiguration!=null?CfnWorkspaceLoggingConfigurationPropertyFromCloudFormation(properties.LoggingConfiguration):void 0),ret.addPropertyResult("queryLoggingConfiguration","QueryLoggingConfiguration",properties.QueryLoggingConfiguration!=null?CfnWorkspaceQueryLoggingConfigurationPropertyFromCloudFormation(properties.QueryLoggingConfiguration):void 0),ret.addPropertyResult("tags","Tags",properties.Tags!=null?cfn_parse().FromCloudFormation.getArray(cfn_parse().FromCloudFormation.getCfnTag)(properties.Tags):void 0),ret.addPropertyResult("workspaceConfiguration","WorkspaceConfiguration",properties.WorkspaceConfiguration!=null?CfnWorkspaceWorkspaceConfigurationPropertyFromCloudFormation(properties.WorkspaceConfiguration):void 0),ret.addUnrecognizedPropertiesAsExtra(properties),ret}class CfnScraper extends cdk().CfnResource{static _fromCloudFormation(scope,id,resourceAttributes,options){resourceAttributes=resourceAttributes||{};const resourceProperties=options.parser.parseValue(resourceAttributes.Properties),propsResult=CfnScraperPropsFromCloudFormation(resourceProperties);if(cdk().isResolvableObject(propsResult.value))throw new(cdk_errors()).ValidationError("Unexpected IResolvable",scope);const ret=new CfnScraper(scope,id,propsResult.value);for(const[propKey,propVal]of Object.entries(propsResult.extraProperties))ret.addPropertyOverride(propKey,propVal);return options.parser.handleAttributes(ret,resourceAttributes,id),ret}constructor(scope,id,props){super(scope,id,{type:CfnScraper.CFN_RESOURCE_TYPE_NAME,properties:props});try{jsiiDeprecationWarnings().aws_cdk_lib_aws_aps_CfnScraperProps(props)}catch(error){throw process.env.JSII_DEBUG!=="1"&&error.name==="DeprecationError"&&Error.captureStackTrace(error,CfnScraper),error}cdk().requireProperty(props,"destination",this),cdk().requireProperty(props,"scrapeConfiguration",this),cdk().requireProperty(props,"source",this),this.attrArn=cdk().Token.asString(this.getAtt("Arn",cdk().ResolutionTypeHint.STRING)),this.attrRoleArn=cdk().Token.asString(this.getAtt("RoleArn",cdk().ResolutionTypeHint.STRING)),this.attrScraperId=cdk().Token.asString(this.getAtt("ScraperId",cdk().ResolutionTypeHint.STRING)),this.alias=props.alias,this.cdkTagManager=new(cdk()).TagManager(cdk().TagType.STANDARD,"AWS::APS::Scraper",void 0,{tagPropertyName:"tags"}),this.destination=props.destination,this.roleConfiguration=props.roleConfiguration,this.scrapeConfiguration=props.scrapeConfiguration,this.source=props.source,this.tags=props.tags}get cfnProperties(){return{alias:this.alias,tags:this.cdkTagManager.renderTags(this.tags),destination:this.destination,roleConfiguration:this.roleConfiguration,scrapeConfiguration:this.scrapeConfiguration,source:this.source}}inspect(inspector){try{jsiiDeprecationWarnings().aws_cdk_lib_TreeInspector(inspector)}catch(error){throw process.env.JSII_DEBUG!=="1"&&error.name==="DeprecationError"&&Error.captureStackTrace(error,this.inspect),error}inspector.addAttribute("aws:cdk:cloudformation:type",CfnScraper.CFN_RESOURCE_TYPE_NAME),inspector.addAttribute("aws:cdk:cloudformation:props",this.cfnProperties)}renderProperties(props){return convertCfnScraperPropsToCloudFormation(props)}}exports.CfnScraper=CfnScraper,_c=JSII_RTTI_SYMBOL_1,CfnScraper[_c]={fqn:"aws-cdk-lib.aws_aps.CfnScraper",version:"2.201.0"},CfnScraper.CFN_RESOURCE_TYPE_NAME="AWS::APS::Scraper";function CfnScraperScrapeConfigurationPropertyValidator(properties){if(!cdk().canInspect(properties))return cdk().VALIDATION_SUCCESS;const errors=new(cdk()).ValidationResults;return properties&&typeof properties=="object"&&!Array.isArray(properties)||errors.collect(new(cdk()).ValidationResult("Expected an object, but received: "+JSON.stringify(properties))),errors.collect(cdk().propertyValidator("configurationBlob",cdk().requiredValidator)(properties.configurationBlob)),errors.collect(cdk().propertyValidator("configurationBlob",cdk().validateString)(properties.configurationBlob)),errors.wrap('supplied properties not correct for "ScrapeConfigurationProperty"')}function convertCfnScraperScrapeConfigurationPropertyToCloudFormation(properties){return cdk().canInspect(properties)?(CfnScraperScrapeConfigurationPropertyValidator(properties).assertSuccess(),{ConfigurationBlob:cdk().stringToCloudFormation(properties.configurationBlob)}):properties}function CfnScraperScrapeConfigurationPropertyFromCloudFormation(properties){if(cdk().isResolvableObject(properties))return new(cfn_parse()).FromCloudFormationResult(properties);if(properties=properties??{},!(properties&&typeof properties=="object"&&!Array.isArray(properties)))return new(cfn_parse()).FromCloudFormationResult(properties);const ret=new(cfn_parse()).FromCloudFormationPropertyObject;return ret.addPropertyResult("configurationBlob","ConfigurationBlob",properties.ConfigurationBlob!=null?cfn_parse().FromCloudFormation.getString(properties.ConfigurationBlob):void 0),ret.addUnrecognizedPropertiesAsExtra(properties),ret}function CfnScraperRoleConfigurationPropertyValidator(properties){if(!cdk().canInspect(properties))return cdk().VALIDATION_SUCCESS;const errors=new(cdk()).ValidationResults;return properties&&typeof properties=="object"&&!Array.isArray(properties)||errors.collect(new(cdk()).ValidationResult("Expected an object, but received: "+JSON.stringify(properties))),errors.collect(cdk().propertyValidator("sourceRoleArn",cdk().validateString)(properties.sourceRoleArn)),errors.collect(cdk().propertyValidator("targetRoleArn",cdk().validateString)(properties.targetRoleArn)),errors.wrap('supplied properties not correct for "RoleConfigurationProperty"')}function convertCfnScraperRoleConfigurationPropertyToCloudFormation(properties){return cdk().canInspect(properties)?(CfnScraperRoleConfigurationPropertyValidator(properties).assertSuccess(),{SourceRoleArn:cdk().stringToCloudFormation(properties.sourceRoleArn),TargetRoleArn:cdk().stringToCloudFormation(properties.targetRoleArn)}):properties}function CfnScraperRoleConfigurationPropertyFromCloudFormation(properties){if(cdk().isResolvableObject(properties))return new(cfn_parse()).FromCloudFormationResult(properties);if(properties=properties??{},!(properties&&typeof properties=="object"&&!Array.isArray(properties)))return new(cfn_parse()).FromCloudFormationResult(properties);const ret=new(cfn_parse()).FromCloudFormationPropertyObject;return ret.addPropertyResult("sourceRoleArn","SourceRoleArn",properties.SourceRoleArn!=null?cfn_parse().FromCloudFormation.getString(properties.SourceRoleArn):void 0),ret.addPropertyResult("targetRoleArn","TargetRoleArn",properties.TargetRoleArn!=null?cfn_parse().FromCloudFormation.getString(properties.TargetRoleArn):void 0),ret.addUnrecognizedPropertiesAsExtra(properties),ret}function CfnScraperEksConfigurationPropertyValidator(properties){if(!cdk().canInspect(properties))return cdk().VALIDATION_SUCCESS;const errors=new(cdk()).ValidationResults;return properties&&typeof properties=="object"&&!Array.isArray(properties)||errors.collect(new(cdk()).ValidationResult("Expected an object, but received: "+JSON.stringify(properties))),errors.collect(cdk().propertyValidator("clusterArn",cdk().requiredValidator)(properties.clusterArn)),errors.collect(cdk().propertyValidator("clusterArn",cdk().validateString)(properties.clusterArn)),errors.collect(cdk().propertyValidator("securityGroupIds",cdk().listValidator(cdk().validateString))(properties.securityGroupIds)),errors.collect(cdk().propertyValidator("subnetIds",cdk().requiredValidator)(properties.subnetIds)),errors.collect(cdk().propertyValidator("subnetIds",cdk().listValidator(cdk().validateString))(properties.subnetIds)),errors.wrap('supplied properties not correct for "EksConfigurationProperty"')}function convertCfnScraperEksConfigurationPropertyToCloudFormation(properties){return cdk().canInspect(properties)?(CfnScraperEksConfigurationPropertyValidator(properties).assertSuccess(),{ClusterArn:cdk().stringToCloudFormation(properties.clusterArn),SecurityGroupIds:cdk().listMapper(cdk().stringToCloudFormation)(properties.securityGroupIds),SubnetIds:cdk().listMapper(cdk().stringToCloudFormation)(properties.subnetIds)}):properties}function CfnScraperEksConfigurationPropertyFromCloudFormation(properties){if(cdk().isResolvableObject(properties))return new(cfn_parse()).FromCloudFormationResult(properties);if(properties=properties??{},!(properties&&typeof properties=="object"&&!Array.isArray(properties)))return new(cfn_parse()).FromCloudFormationResult(properties);const ret=new(cfn_parse()).FromCloudFormationPropertyObject;return ret.addPropertyResult("clusterArn","ClusterArn",properties.ClusterArn!=null?cfn_parse().FromCloudFormation.getString(properties.ClusterArn):void 0),ret.addPropertyResult("securityGroupIds","SecurityGroupIds",properties.SecurityGroupIds!=null?cfn_parse().FromCloudFormation.getArray(cfn_parse().FromCloudFormation.getString)(properties.SecurityGroupIds):void 0),ret.addPropertyResult("subnetIds","SubnetIds",properties.SubnetIds!=null?cfn_parse().FromCloudFormation.getArray(cfn_parse().FromCloudFormation.getString)(properties.SubnetIds):void 0),ret.addUnrecognizedPropertiesAsExtra(properties),ret}function CfnScraperSourcePropertyValidator(properties){if(!cdk().canInspect(properties))return cdk().VALIDATION_SUCCESS;const errors=new(cdk()).ValidationResults;return properties&&typeof properties=="object"&&!Array.isArray(properties)||errors.collect(new(cdk()).ValidationResult("Expected an object, but received: "+JSON.stringify(properties))),errors.collect(cdk().propertyValidator("eksConfiguration",cdk().requiredValidator)(properties.eksConfiguration)),errors.collect(cdk().propertyValidator("eksConfiguration",CfnScraperEksConfigurationPropertyValidator)(properties.eksConfiguration)),errors.wrap('supplied properties not correct for "SourceProperty"')}function convertCfnScraperSourcePropertyToCloudFormation(properties){return cdk().canInspect(properties)?(CfnScraperSourcePropertyValidator(properties).assertSuccess(),{EksConfiguration:convertCfnScraperEksConfigurationPropertyToCloudFormation(properties.eksConfiguration)}):properties}function CfnScraperSourcePropertyFromCloudFormation(properties){if(cdk().isResolvableObject(properties))return new(cfn_parse()).FromCloudFormationResult(properties);if(properties=properties??{},!(properties&&typeof properties=="object"&&!Array.isArray(properties)))return new(cfn_parse()).FromCloudFormationResult(properties);const ret=new(cfn_parse()).FromCloudFormationPropertyObject;return ret.addPropertyResult("eksConfiguration","EksConfiguration",properties.EksConfiguration!=null?CfnScraperEksConfigurationPropertyFromCloudFormation(properties.EksConfiguration):void 0),ret.addUnrecognizedPropertiesAsExtra(properties),ret}function CfnScraperAmpConfigurationPropertyValidator(properties){if(!cdk().canInspect(properties))return cdk().VALIDATION_SUCCESS;const errors=new(cdk()).ValidationResults;return properties&&typeof properties=="object"&&!Array.isArray(properties)||errors.collect(new(cdk()).ValidationResult("Expected an object, but received: "+JSON.stringify(properties))),errors.collect(cdk().propertyValidator("workspaceArn",cdk().requiredValidator)(properties.workspaceArn)),errors.collect(cdk().propertyValidator("workspaceArn",cdk().validateString)(properties.workspaceArn)),errors.wrap('supplied properties not correct for "AmpConfigurationProperty"')}function convertCfnScraperAmpConfigurationPropertyToCloudFormation(properties){return cdk().canInspect(properties)?(CfnScraperAmpConfigurationPropertyValidator(properties).assertSuccess(),{WorkspaceArn:cdk().stringToCloudFormation(properties.workspaceArn)}):properties}function CfnScraperAmpConfigurationPropertyFromCloudFormation(properties){if(cdk().isResolvableObject(properties))return new(cfn_parse()).FromCloudFormationResult(properties);if(properties=properties??{},!(properties&&typeof properties=="object"&&!Array.isArray(properties)))return new(cfn_parse()).FromCloudFormationResult(properties);const ret=new(cfn_parse()).FromCloudFormationPropertyObject;return ret.addPropertyResult("workspaceArn","WorkspaceArn",properties.WorkspaceArn!=null?cfn_parse().FromCloudFormation.getString(properties.WorkspaceArn):void 0),ret.addUnrecognizedPropertiesAsExtra(properties),ret}function CfnScraperDestinationPropertyValidator(properties){if(!cdk().canInspect(properties))return cdk().VALIDATION_SUCCESS;const errors=new(cdk()).ValidationResults;return properties&&typeof properties=="object"&&!Array.isArray(properties)||errors.collect(new(cdk()).ValidationResult("Expected an object, but received: "+JSON.stringify(properties))),errors.collect(cdk().propertyValidator("ampConfiguration",cdk().requiredValidator)(properties.ampConfiguration)),errors.collect(cdk().propertyValidator("ampConfiguration",CfnScraperAmpConfigurationPropertyValidator)(properties.ampConfiguration)),errors.wrap('supplied properties not correct for "DestinationProperty"')}function convertCfnScraperDestinationPropertyToCloudFormation(properties){return cdk().canInspect(properties)?(CfnScraperDestinationPropertyValidator(properties).assertSuccess(),{AmpConfiguration:convertCfnScraperAmpConfigurationPropertyToCloudFormation(properties.ampConfiguration)}):properties}function CfnScraperDestinationPropertyFromCloudFormation(properties){if(cdk().isResolvableObject(properties))return new(cfn_parse()).FromCloudFormationResult(properties);if(properties=properties??{},!(properties&&typeof properties=="object"&&!Array.isArray(properties)))return new(cfn_parse()).FromCloudFormationResult(properties);const ret=new(cfn_parse()).FromCloudFormationPropertyObject;return ret.addPropertyResult("ampConfiguration","AmpConfiguration",properties.AmpConfiguration!=null?CfnScraperAmpConfigurationPropertyFromCloudFormation(properties.AmpConfiguration):void 0),ret.addUnrecognizedPropertiesAsExtra(properties),ret}function CfnScraperPropsValidator(properties){if(!cdk().canInspect(properties))return cdk().VALIDATION_SUCCESS;const errors=new(cdk()).ValidationResults;return properties&&typeof properties=="object"&&!Array.isArray(properties)||errors.collect(new(cdk()).ValidationResult("Expected an object, but received: "+JSON.stringify(properties))),errors.collect(cdk().propertyValidator("alias",cdk().validateString)(properties.alias)),errors.collect(cdk().propertyValidator("destination",cdk().requiredValidator)(properties.destination)),errors.collect(cdk().propertyValidator("destination",CfnScraperDestinationPropertyValidator)(properties.destination)),errors.collect(cdk().propertyValidator("roleConfiguration",CfnScraperRoleConfigurationPropertyValidator)(properties.roleConfiguration)),errors.collect(cdk().propertyValidator("scrapeConfiguration",cdk().requiredValidator)(properties.scrapeConfiguration)),errors.collect(cdk().propertyValidator("scrapeConfiguration",CfnScraperScrapeConfigurationPropertyValidator)(properties.scrapeConfiguration)),errors.collect(cdk().propertyValidator("source",cdk().requiredValidator)(properties.source)),errors.collect(cdk().propertyValidator("source",CfnScraperSourcePropertyValidator)(properties.source)),errors.collect(cdk().propertyValidator("tags",cdk().listValidator(cdk().validateCfnTag))(properties.tags)),errors.wrap('supplied properties not correct for "CfnScraperProps"')}function convertCfnScraperPropsToCloudFormation(properties){return cdk().canInspect(properties)?(CfnScraperPropsValidator(properties).assertSuccess(),{Alias:cdk().stringToCloudFormation(properties.alias),Destination:convertCfnScraperDestinationPropertyToCloudFormation(properties.destination),RoleConfiguration:convertCfnScraperRoleConfigurationPropertyToCloudFormation(properties.roleConfiguration),ScrapeConfiguration:convertCfnScraperScrapeConfigurationPropertyToCloudFormation(properties.scrapeConfiguration),Source:convertCfnScraperSourcePropertyToCloudFormation(properties.source),Tags:cdk().listMapper(cdk().cfnTagToCloudFormation)(properties.tags)}):properties}function CfnScraperPropsFromCloudFormation(properties){if(cdk().isResolvableObject(properties))return new(cfn_parse()).FromCloudFormationResult(properties);if(properties=properties??{},!(properties&&typeof properties=="object"&&!Array.isArray(properties)))return new(cfn_parse()).FromCloudFormationResult(properties);const ret=new(cfn_parse()).FromCloudFormationPropertyObject;return ret.addPropertyResult("alias","Alias",properties.Alias!=null?cfn_parse().FromCloudFormation.getString(properties.Alias):void 0),ret.addPropertyResult("destination","Destination",properties.Destination!=null?CfnScraperDestinationPropertyFromCloudFormation(properties.Destination):void 0),ret.addPropertyResult("roleConfiguration","RoleConfiguration",properties.RoleConfiguration!=null?CfnScraperRoleConfigurationPropertyFromCloudFormation(properties.RoleConfiguration):void 0),ret.addPropertyResult("scrapeConfiguration","ScrapeConfiguration",properties.ScrapeConfiguration!=null?CfnScraperScrapeConfigurationPropertyFromCloudFormation(properties.ScrapeConfiguration):void 0),ret.addPropertyResult("source","Source",properties.Source!=null?CfnScraperSourcePropertyFromCloudFormation(properties.Source):void 0),ret.addPropertyResult("tags","Tags",properties.Tags!=null?cfn_parse().FromCloudFormation.getArray(cfn_parse().FromCloudFormation.getCfnTag)(properties.Tags):void 0),ret.addUnrecognizedPropertiesAsExtra(properties),ret}
