import * as cdk from "../../core";
import * as constructs from "constructs";
import * as cfn_parse from "../../core/lib/helpers-internal";
/**
 * Access points simplify managing data access at scale for shared datasets in Amazon S3 .
 *
 * Access points are unique hostnames you create to enforce distinct permissions and network controls for all requests made through an access point. You can create hundreds of access points per bucket, each with a distinct name and permissions customized for each application. Each access point works in conjunction with the bucket policy that is attached to the underlying bucket. For more information, see [Managing access to shared datasets in directory buckets with access points](https://docs.aws.amazon.com/AmazonS3/latest/userguide/access-points-directory-buckets.html) .
 *
 * @cloudformationResource AWS::S3Express::AccessPoint
 * @stability external
 * @see http://docs.aws.amazon.com/AWSCloudFormation/latest/UserGuide/aws-resource-s3express-accesspoint.html
 */
export declare class CfnAccessPoint extends cdk.CfnResource implements cdk.IInspectable {
    /**
     * The CloudFormation resource type name for this resource class.
     */
    static readonly CFN_RESOURCE_TYPE_NAME: string;
    /**
     * Build a CfnAccessPoint from CloudFormation properties
     *
     * A factory method that creates a new instance of this class from an object
     * containing the CloudFormation properties of this resource.
     * Used in the @aws-cdk/cloudformation-include module.
     *
     * @internal
     */
    static _fromCloudFormation(scope: constructs.Construct, id: string, resourceAttributes: any, options: cfn_parse.FromCloudFormationOptions): CfnAccessPoint;
    /**
     * The ARN of the access point.
     *
     * @cloudformationAttribute Arn
     */
    readonly attrArn: string;
    /**
     * The network configuration of the access point.
     *
     * @cloudformationAttribute NetworkOrigin
     */
    readonly attrNetworkOrigin: string;
    /**
     * The name of the bucket that you want to associate the access point with.
     */
    bucket: string;
    /**
     * The AWS account ID that owns the bucket associated with this access point.
     */
    bucketAccountId?: string;
    /**
     * An access point name consists of a base name you provide, followed by the zoneID ( AWS Local Zone) followed by the prefix `--xa-s3` .
     */
    name?: string;
    /**
     * The access point policy associated with the specified access point.
     */
    policy?: any | cdk.IResolvable;
    /**
     * Public access is blocked by default to access points for directory buckets.
     */
    publicAccessBlockConfiguration?: cdk.IResolvable | CfnAccessPoint.PublicAccessBlockConfigurationProperty;
    /**
     * You can use the access point scope to restrict access to specific prefixes, API operations, or a combination of both.
     */
    scope?: cdk.IResolvable | CfnAccessPoint.ScopeProperty;
    /**
     * If you include this field, Amazon S3 restricts access to this access point to requests from the specified virtual private cloud (VPC).
     */
    vpcConfiguration?: cdk.IResolvable | CfnAccessPoint.VpcConfigurationProperty;
    /**
     * @param scope Scope in which this resource is defined
     * @param id Construct identifier for this resource (unique in its scope)
     * @param props Resource properties
     */
    constructor(scope: constructs.Construct, id: string, props: CfnAccessPointProps);
    protected get cfnProperties(): Record<string, any>;
    /**
     * Examines the CloudFormation resource and discloses attributes
     *
     * @param inspector tree inspector to collect and process attributes
     */
    inspect(inspector: cdk.TreeInspector): void;
    protected renderProperties(props: Record<string, any>): Record<string, any>;
}
export declare namespace CfnAccessPoint {
    /**
     * The Virtual Private Cloud (VPC) configuration for a bucket access point.
     *
     * @struct
     * @stability external
     * @see http://docs.aws.amazon.com/AWSCloudFormation/latest/UserGuide/aws-properties-s3express-accesspoint-vpcconfiguration.html
     */
    interface VpcConfigurationProperty {
        /**
         * If this field is specified, this access point will only allow connections from the specified VPC ID.
         *
         * @see http://docs.aws.amazon.com/AWSCloudFormation/latest/UserGuide/aws-properties-s3express-accesspoint-vpcconfiguration.html#cfn-s3express-accesspoint-vpcconfiguration-vpcid
         */
        readonly vpcId?: string;
    }
    /**
     * Public access is blocked by default to access points for directory buckets.
     *
     * @struct
     * @stability external
     * @see http://docs.aws.amazon.com/AWSCloudFormation/latest/UserGuide/aws-properties-s3express-accesspoint-publicaccessblockconfiguration.html
     */
    interface PublicAccessBlockConfigurationProperty {
        /**
         * Specifies whether Amazon S3 should block public access control lists (ACLs) for this bucket and objects in this bucket.
         *
         * Setting this element to `TRUE` causes the following behavior:
         *
         * - PUT Bucket ACL and PUT Object ACL calls fail if the specified ACL is public.
         * - PUT Object calls fail if the request includes a public ACL.
         * - PUT Bucket calls fail if the request includes a public ACL.
         *
         * Enabling this setting doesn't affect existing policies or ACLs.
         *
         * @see http://docs.aws.amazon.com/AWSCloudFormation/latest/UserGuide/aws-properties-s3express-accesspoint-publicaccessblockconfiguration.html#cfn-s3express-accesspoint-publicaccessblockconfiguration-blockpublicacls
         */
        readonly blockPublicAcls?: boolean | cdk.IResolvable;
        /**
         * Specifies whether Amazon S3 should block public bucket policies for this bucket.
         *
         * Setting this element to `TRUE` causes Amazon S3 to reject calls to PUT Bucket policy if the specified bucket policy allows public access.
         *
         * Enabling this setting doesn't affect existing bucket policies.
         *
         * @see http://docs.aws.amazon.com/AWSCloudFormation/latest/UserGuide/aws-properties-s3express-accesspoint-publicaccessblockconfiguration.html#cfn-s3express-accesspoint-publicaccessblockconfiguration-blockpublicpolicy
         */
        readonly blockPublicPolicy?: boolean | cdk.IResolvable;
        /**
         * Specifies whether Amazon S3 should ignore public ACLs for this bucket and objects in this bucket.
         *
         * Setting this element to `TRUE` causes Amazon S3 to ignore all public ACLs on this bucket and objects in this bucket.
         *
         * Enabling this setting doesn't affect the persistence of any existing ACLs and doesn't prevent new public ACLs from being set.
         *
         * @see http://docs.aws.amazon.com/AWSCloudFormation/latest/UserGuide/aws-properties-s3express-accesspoint-publicaccessblockconfiguration.html#cfn-s3express-accesspoint-publicaccessblockconfiguration-ignorepublicacls
         */
        readonly ignorePublicAcls?: boolean | cdk.IResolvable;
        /**
         * Specifies whether Amazon S3 should restrict public bucket policies for this bucket.
         *
         * Setting this element to `TRUE` restricts access to this bucket to only AWS service principals and authorized users within this account if the bucket has a public policy.
         *
         * Enabling this setting doesn't affect previously stored bucket policies, except that public and cross-account access within any public bucket policy, including non-public delegation to specific accounts, is blocked.
         *
         * @see http://docs.aws.amazon.com/AWSCloudFormation/latest/UserGuide/aws-properties-s3express-accesspoint-publicaccessblockconfiguration.html#cfn-s3express-accesspoint-publicaccessblockconfiguration-restrictpublicbuckets
         */
        readonly restrictPublicBuckets?: boolean | cdk.IResolvable;
    }
    /**
     * You can use the access point scope to restrict access to specific prefixes, API operations, or a combination of both.
     *
     * For more information, see [Manage the scope of your access points for directory buckets.](https://docs.aws.amazon.com/AmazonS3/latest/userguide/access-points-directory-buckets-manage-scope.html)
     *
     * @struct
     * @stability external
     * @see http://docs.aws.amazon.com/AWSCloudFormation/latest/UserGuide/aws-properties-s3express-accesspoint-scope.html
     */
    interface ScopeProperty {
        /**
         * You can include one or more API operations as permissions.
         *
         * @see http://docs.aws.amazon.com/AWSCloudFormation/latest/UserGuide/aws-properties-s3express-accesspoint-scope.html#cfn-s3express-accesspoint-scope-permissions
         */
        readonly permissions?: Array<string>;
        /**
         * You can specify any amount of prefixes, but the total length of characters of all prefixes must be less than 256 bytes in size.
         *
         * @see http://docs.aws.amazon.com/AWSCloudFormation/latest/UserGuide/aws-properties-s3express-accesspoint-scope.html#cfn-s3express-accesspoint-scope-prefixes
         */
        readonly prefixes?: Array<string>;
    }
}
/**
 * Properties for defining a `CfnAccessPoint`
 *
 * @struct
 * @stability external
 * @see http://docs.aws.amazon.com/AWSCloudFormation/latest/UserGuide/aws-resource-s3express-accesspoint.html
 */
export interface CfnAccessPointProps {
    /**
     * The name of the bucket that you want to associate the access point with.
     *
     * @see http://docs.aws.amazon.com/AWSCloudFormation/latest/UserGuide/aws-resource-s3express-accesspoint.html#cfn-s3express-accesspoint-bucket
     */
    readonly bucket: string;
    /**
     * The AWS account ID that owns the bucket associated with this access point.
     *
     * @see http://docs.aws.amazon.com/AWSCloudFormation/latest/UserGuide/aws-resource-s3express-accesspoint.html#cfn-s3express-accesspoint-bucketaccountid
     */
    readonly bucketAccountId?: string;
    /**
     * An access point name consists of a base name you provide, followed by the zoneID ( AWS Local Zone) followed by the prefix `--xa-s3` .
     *
     * For example, accesspointname--zoneID--xa-s3.
     *
     * @see http://docs.aws.amazon.com/AWSCloudFormation/latest/UserGuide/aws-resource-s3express-accesspoint.html#cfn-s3express-accesspoint-name
     */
    readonly name?: string;
    /**
     * The access point policy associated with the specified access point.
     *
     * @see http://docs.aws.amazon.com/AWSCloudFormation/latest/UserGuide/aws-resource-s3express-accesspoint.html#cfn-s3express-accesspoint-policy
     */
    readonly policy?: any | cdk.IResolvable;
    /**
     * Public access is blocked by default to access points for directory buckets.
     *
     * @see http://docs.aws.amazon.com/AWSCloudFormation/latest/UserGuide/aws-resource-s3express-accesspoint.html#cfn-s3express-accesspoint-publicaccessblockconfiguration
     */
    readonly publicAccessBlockConfiguration?: cdk.IResolvable | CfnAccessPoint.PublicAccessBlockConfigurationProperty;
    /**
     * You can use the access point scope to restrict access to specific prefixes, API operations, or a combination of both.
     *
     * For more information, see [Manage the scope of your access points for directory buckets.](https://docs.aws.amazon.com/AmazonS3/latest/userguide/access-points-directory-buckets-manage-scope.html)
     *
     * @see http://docs.aws.amazon.com/AWSCloudFormation/latest/UserGuide/aws-resource-s3express-accesspoint.html#cfn-s3express-accesspoint-scope
     */
    readonly scope?: cdk.IResolvable | CfnAccessPoint.ScopeProperty;
    /**
     * If you include this field, Amazon S3 restricts access to this access point to requests from the specified virtual private cloud (VPC).
     *
     * @see http://docs.aws.amazon.com/AWSCloudFormation/latest/UserGuide/aws-resource-s3express-accesspoint.html#cfn-s3express-accesspoint-vpcconfiguration
     */
    readonly vpcConfiguration?: cdk.IResolvable | CfnAccessPoint.VpcConfigurationProperty;
}
/**
 * The `AWS::S3Express::BucketPolicy` resource defines an Amazon S3 bucket policy to an Amazon S3 directory bucket.
 *
 * - **Permissions** - If you are using an identity other than the root user of the AWS account that owns the bucket, the calling identity must both have the required permissions on the specified bucket and belong to the bucket owner's account in order to use this operation. For more information about directory bucket policies and permissions, see [AWS Identity and Access Management (IAM) for S3 Express One Zone](https://docs.aws.amazon.com/AmazonS3/latest/userguide/s3-express-security-iam.html) in the *Amazon S3 User Guide* .
 *
 * > To ensure that bucket owners don't inadvertently lock themselves out of their own buckets, the root principal in a bucket owner's AWS account can perform the `GetBucketPolicy` , `PutBucketPolicy` , and `DeleteBucketPolicy` API actions, even if their bucket policy explicitly denies the root principal's access. Bucket owner root principals can only be blocked from performing these API actions by VPC endpoint policies and AWS Organizations policies.
 *
 * The required permissions for CloudFormation to use are based on the operations that are performed on the stack.
 *
 * - Create
 *
 * - s3express:GetBucketPolicy
 * - s3express:PutBucketPolicy
 * - Read
 *
 * - s3express:GetBucketPolicy
 * - Update
 *
 * - s3express:GetBucketPolicy
 * - s3express:PutBucketPolicy
 * - Delete
 *
 * - s3express:GetBucketPolicy
 * - s3express:DeleteBucketPolicy
 * - List
 *
 * - s3express:GetBucketPolicy
 * - s3express:ListAllMyDirectoryBuckets
 *
 * For more information about example bucket policies, see [Example bucket policies for S3 Express One Zone](https://docs.aws.amazon.com/AmazonS3/latest/userguide/s3-express-security-iam-example-bucket-policies.html) in the *Amazon S3 User Guide* .
 *
 * The following operations are related to `AWS::S3Express::BucketPolicy` :
 *
 * - [PutBucketPolicy](https://docs.aws.amazon.com/AmazonS3/latest/API/API_PutBucketPolicy.html)
 * - [GetBucketPolicy](https://docs.aws.amazon.com/AmazonS3/latest/API/API_GetBucketPolicy.html)
 * - [DeleteBucketPolicy](https://docs.aws.amazon.com/AmazonS3/latest/API/API_DeleteBucketPolicy.html)
 * - [ListDirectoryBuckets](https://docs.aws.amazon.com/AmazonS3/latest/API/API_ListDirectoryBuckets.html)
 *
 * @cloudformationResource AWS::S3Express::BucketPolicy
 * @stability external
 * @see http://docs.aws.amazon.com/AWSCloudFormation/latest/UserGuide/aws-resource-s3express-bucketpolicy.html
 */
export declare class CfnBucketPolicy extends cdk.CfnResource implements cdk.IInspectable {
    /**
     * The CloudFormation resource type name for this resource class.
     */
    static readonly CFN_RESOURCE_TYPE_NAME: string;
    /**
     * Build a CfnBucketPolicy from CloudFormation properties
     *
     * A factory method that creates a new instance of this class from an object
     * containing the CloudFormation properties of this resource.
     * Used in the @aws-cdk/cloudformation-include module.
     *
     * @internal
     */
    static _fromCloudFormation(scope: constructs.Construct, id: string, resourceAttributes: any, options: cfn_parse.FromCloudFormationOptions): CfnBucketPolicy;
    /**
     * The name of the S3 directory bucket to which the policy applies.
     */
    bucket: string;
    /**
     * A policy document containing permissions to add to the specified bucket.
     */
    policyDocument: any | cdk.IResolvable | string;
    /**
     * @param scope Scope in which this resource is defined
     * @param id Construct identifier for this resource (unique in its scope)
     * @param props Resource properties
     */
    constructor(scope: constructs.Construct, id: string, props: CfnBucketPolicyProps);
    protected get cfnProperties(): Record<string, any>;
    /**
     * Examines the CloudFormation resource and discloses attributes
     *
     * @param inspector tree inspector to collect and process attributes
     */
    inspect(inspector: cdk.TreeInspector): void;
    protected renderProperties(props: Record<string, any>): Record<string, any>;
}
/**
 * Properties for defining a `CfnBucketPolicy`
 *
 * @struct
 * @stability external
 * @see http://docs.aws.amazon.com/AWSCloudFormation/latest/UserGuide/aws-resource-s3express-bucketpolicy.html
 */
export interface CfnBucketPolicyProps {
    /**
     * The name of the S3 directory bucket to which the policy applies.
     *
     * @see http://docs.aws.amazon.com/AWSCloudFormation/latest/UserGuide/aws-resource-s3express-bucketpolicy.html#cfn-s3express-bucketpolicy-bucket
     */
    readonly bucket: string;
    /**
     * A policy document containing permissions to add to the specified bucket.
     *
     * In IAM, you must provide policy documents in JSON format. However, in CloudFormation you can provide the policy in JSON or YAML format because CloudFormation converts YAML to JSON before submitting it to IAM. For more information, see the AWS::IAM::Policy [PolicyDocument](https://docs.aws.amazon.com/AWSCloudFormation/latest/UserGuide/aws-resource-iam-policy.html#cfn-iam-policy-policydocument) resource description in this guide and [Policies and Permissions in Amazon S3](https://docs.aws.amazon.com/AmazonS3/latest/dev/access-policy-language-overview.html) in the *Amazon S3 User Guide* .
     *
     * @see http://docs.aws.amazon.com/AWSCloudFormation/latest/UserGuide/aws-resource-s3express-bucketpolicy.html#cfn-s3express-bucketpolicy-policydocument
     */
    readonly policyDocument: any | cdk.IResolvable | string;
}
/**
 * The `AWS::S3Express::DirectoryBucket` resource defines an Amazon S3 directory bucket in the same AWS Region where you create the AWS CloudFormation stack.
 *
 * To control how AWS CloudFormation handles the bucket when the stack is deleted, you can set a deletion policy for your bucket. You can choose to *retain* the bucket or to *delete* the bucket. For more information, see [DeletionPolicy attribute](https://docs.aws.amazon.com/AWSCloudFormation/latest/UserGuide/aws-attribute-deletionpolicy.html) .
 *
 * > You can only delete empty buckets. Deletion fails for buckets that have contents.
 *
 * - **Permissions** - The required permissions for CloudFormation to use are based on the operations that are performed on the stack.
 *
 * - Create
 *
 * - s3express:CreateBucket
 * - s3express:ListAllMyDirectoryBuckets
 * - Read
 *
 * - s3express:ListAllMyDirectoryBuckets
 * - ec2:DescribeAvailabilityZones
 * - Delete
 *
 * - s3express:DeleteBucket
 * - s3express:ListAllMyDirectoryBuckets
 * - List
 *
 * - s3express:ListAllMyDirectoryBuckets
 * - PutBucketEncryption
 *
 * - s3express:PutEncryptionConfiguration
 * - To set a directory bucket default encryption with SSE-KMS, you must also have the kms:GenerateDataKey and kms:Decrypt permissions in IAM identity-based policies and AWS KMS key policies for the target AWS KMS key.
 * - GetBucketEncryption
 *
 * - s3express:GetBucketEncryption
 * - DeleteBucketEncryption
 *
 * - s3express:PutEncryptionConfiguration
 *
 * The following operations are related to `AWS::S3Express::DirectoryBucket` :
 *
 * - [CreateBucket](https://docs.aws.amazon.com/AmazonS3/latest/API/API_CreateBucket.html)
 * - [ListDirectoryBuckets](https://docs.aws.amazon.com/AmazonS3/latest/API/API_ListDirectoryBuckets.html)
 * - [DeleteBucket](https://docs.aws.amazon.com/AmazonS3/latest/API/API_DeleteBucket.html)
 *
 * @cloudformationResource AWS::S3Express::DirectoryBucket
 * @stability external
 * @see http://docs.aws.amazon.com/AWSCloudFormation/latest/UserGuide/aws-resource-s3express-directorybucket.html
 */
export declare class CfnDirectoryBucket extends cdk.CfnResource implements cdk.IInspectable {
    /**
     * The CloudFormation resource type name for this resource class.
     */
    static readonly CFN_RESOURCE_TYPE_NAME: string;
    /**
     * Build a CfnDirectoryBucket from CloudFormation properties
     *
     * A factory method that creates a new instance of this class from an object
     * containing the CloudFormation properties of this resource.
     * Used in the @aws-cdk/cloudformation-include module.
     *
     * @internal
     */
    static _fromCloudFormation(scope: constructs.Construct, id: string, resourceAttributes: any, options: cfn_parse.FromCloudFormationOptions): CfnDirectoryBucket;
    /**
     * Returns the Amazon Resource Name (ARN) of the specified bucket.
     *
     * Example: `arn:aws:s3express: *us-west-2* : *account_id* :bucket/ *bucket_base_name* -- *usw2-az1* --x-s3`
     *
     * @cloudformationAttribute Arn
     */
    readonly attrArn: string;
    /**
     * Returns the code for the Availability Zone or the Local Zone where the directory bucket was created.
     *
     * Example value for an Availability Zone code: *us-east-1f*
     *
     * > An Availability Zone code might not represent the same physical location for different AWS accounts. For more information, see [Availability Zones and Regions](https://docs.aws.amazon.com/AmazonS3/latest/userguide/s3-express-Endpoints.html) in the *Amazon S3 User Guide* .
     *
     * @cloudformationAttribute AvailabilityZoneName
     */
    readonly attrAvailabilityZoneName: string;
    /**
     * Specifies default encryption for a bucket using server-side encryption with Amazon S3 managed keys (SSE-S3) or AWS KMS keys (SSE-KMS).
     */
    bucketEncryption?: CfnDirectoryBucket.BucketEncryptionProperty | cdk.IResolvable;
    /**
     * A name for the bucket.
     */
    bucketName?: string;
    /**
     * The number of Zone (Availability Zone or Local Zone) that's used for redundancy for the bucket.
     */
    dataRedundancy: string;
    /**
     * Container for lifecycle rules. You can add as many as 1000 rules.
     */
    lifecycleConfiguration?: cdk.IResolvable | CfnDirectoryBucket.LifecycleConfigurationProperty;
    /**
     * The name of the location where the bucket will be created.
     */
    locationName: string;
    /**
     * @param scope Scope in which this resource is defined
     * @param id Construct identifier for this resource (unique in its scope)
     * @param props Resource properties
     */
    constructor(scope: constructs.Construct, id: string, props: CfnDirectoryBucketProps);
    protected get cfnProperties(): Record<string, any>;
    /**
     * Examines the CloudFormation resource and discloses attributes
     *
     * @param inspector tree inspector to collect and process attributes
     */
    inspect(inspector: cdk.TreeInspector): void;
    protected renderProperties(props: Record<string, any>): Record<string, any>;
}
export declare namespace CfnDirectoryBucket {
    /**
     * Specifies default encryption for a bucket using server-side encryption with Amazon S3 managed keys (SSE-S3) or AWS KMS keys (SSE-KMS).
     *
     * For information about default encryption for directory buckets, see [Setting and monitoring default encryption for directory buckets](https://docs.aws.amazon.com/AmazonS3/latest/userguide/s3-express-bucket-encryption.html) in the *Amazon S3 User Guide* .
     *
     * @struct
     * @stability external
     * @see http://docs.aws.amazon.com/AWSCloudFormation/latest/UserGuide/aws-properties-s3express-directorybucket-bucketencryption.html
     */
    interface BucketEncryptionProperty {
        /**
         * Specifies the default server-side-encryption configuration.
         *
         * @see http://docs.aws.amazon.com/AWSCloudFormation/latest/UserGuide/aws-properties-s3express-directorybucket-bucketencryption.html#cfn-s3express-directorybucket-bucketencryption-serversideencryptionconfiguration
         */
        readonly serverSideEncryptionConfiguration: Array<cdk.IResolvable | CfnDirectoryBucket.ServerSideEncryptionRuleProperty> | cdk.IResolvable;
    }
    /**
     * Specifies the default server-side encryption configuration.
     *
     * @struct
     * @stability external
     * @see http://docs.aws.amazon.com/AWSCloudFormation/latest/UserGuide/aws-properties-s3express-directorybucket-serversideencryptionrule.html
     */
    interface ServerSideEncryptionRuleProperty {
        /**
         * Specifies whether Amazon S3 should use an S3 Bucket Key with server-side encryption using KMS (SSE-KMS) for new objects in the bucket.
         *
         * S3 Bucket Keys are always enabled for `GET` and `PUT` operations on a directory bucket and can’t be disabled. It's only allowed to set the `BucketKeyEnabled` element to `true` .
         *
         * S3 Bucket Keys aren't supported, when you copy SSE-KMS encrypted objects from general purpose buckets to directory buckets, from directory buckets to general purpose buckets, or between directory buckets, through [CopyObject](https://docs.aws.amazon.com/AmazonS3/latest/API/API_CopyObject.html) , [UploadPartCopy](https://docs.aws.amazon.com/AmazonS3/latest/API/API_UploadPartCopy.html) , [the Copy operation in Batch Operations](https://docs.aws.amazon.com/AmazonS3/latest/userguide/directory-buckets-objects-Batch-Ops) , or [the import jobs](https://docs.aws.amazon.com/AmazonS3/latest/userguide/create-import-job) . In this case, Amazon S3 makes a call to AWS KMS every time a copy request is made for a KMS-encrypted object.
         *
         * For more information, see [Amazon S3 Bucket Keys](https://docs.aws.amazon.com/AmazonS3/latest/userguide/s3-express-UsingKMSEncryption.html#s3-express-sse-kms-bucket-keys) in the *Amazon S3 User Guide* .
         *
         * @see http://docs.aws.amazon.com/AWSCloudFormation/latest/UserGuide/aws-properties-s3express-directorybucket-serversideencryptionrule.html#cfn-s3express-directorybucket-serversideencryptionrule-bucketkeyenabled
         */
        readonly bucketKeyEnabled?: boolean | cdk.IResolvable;
        /**
         * Specifies the default server-side encryption to apply to new objects in the bucket.
         *
         * If a PUT Object request doesn't specify any server-side encryption, this default encryption will be applied.
         *
         * @see http://docs.aws.amazon.com/AWSCloudFormation/latest/UserGuide/aws-properties-s3express-directorybucket-serversideencryptionrule.html#cfn-s3express-directorybucket-serversideencryptionrule-serversideencryptionbydefault
         */
        readonly serverSideEncryptionByDefault?: cdk.IResolvable | CfnDirectoryBucket.ServerSideEncryptionByDefaultProperty;
    }
    /**
     * Describes the default server-side encryption to apply to new objects in the bucket.
     *
     * If a PUT Object request doesn't specify any server-side encryption, this default encryption will be applied. For more information, see [PutBucketEncryption](https://docs.aws.amazon.com/AmazonS3/latest/API/RESTBucketPUTencryption.html) in the *Amazon S3 API Reference* .
     *
     * @struct
     * @stability external
     * @see http://docs.aws.amazon.com/AWSCloudFormation/latest/UserGuide/aws-properties-s3express-directorybucket-serversideencryptionbydefault.html
     */
    interface ServerSideEncryptionByDefaultProperty {
        /**
         * AWS Key Management Service (KMS) customer managed key ID to use for the default encryption.
         *
         * This parameter is allowed only if `SSEAlgorithm` is set to `aws:kms` .
         *
         * You can specify this parameter with the key ID or the Amazon Resource Name (ARN) of the KMS key. You can’t use the key alias of the KMS key.
         *
         * - Key ID: `1234abcd-12ab-34cd-56ef-1234567890ab`
         * - Key ARN: `arn:aws:kms:us-east-2:************:key/1234abcd-12ab-34cd-56ef-1234567890ab`
         *
         * If you are using encryption with cross-account or AWS service operations, you must use a fully qualified KMS key ARN. For more information, see [Using encryption for cross-account operations](https://docs.aws.amazon.com/AmazonS3/latest/userguide/s3-express-bucket-encryption.html#s3-express-bucket-encryption-update-bucket-policy) .
         *
         * > Your SSE-KMS configuration can only support 1 [customer managed key](https://docs.aws.amazon.com/kms/latest/developerguide/concepts.html#customer-cmk) per directory bucket for the lifetime of the bucket. [AWS managed key](https://docs.aws.amazon.com/kms/latest/developerguide/concepts.html#aws-managed-cmk) ( `aws/s3` ) isn't supported. Also, after you specify a customer managed key for SSE-KMS and upload objects with this configuration, you can't override the customer managed key for your SSE-KMS configuration. To use a new customer manager key for your data, we recommend copying your existing objects to a new directory bucket with a new customer managed key. > Amazon S3 only supports symmetric encryption KMS keys. For more information, see [Asymmetric keys in AWS KMS](https://docs.aws.amazon.com//kms/latest/developerguide/symmetric-asymmetric.html) in the *AWS Key Management Service Developer Guide* .
         *
         * @see http://docs.aws.amazon.com/AWSCloudFormation/latest/UserGuide/aws-properties-s3express-directorybucket-serversideencryptionbydefault.html#cfn-s3express-directorybucket-serversideencryptionbydefault-kmsmasterkeyid
         */
        readonly kmsMasterKeyId?: string;
        /**
         * Server-side encryption algorithm to use for the default encryption.
         *
         * > For directory buckets, there are only two supported values for server-side encryption: `AES256` and `aws:kms` .
         *
         * @see http://docs.aws.amazon.com/AWSCloudFormation/latest/UserGuide/aws-properties-s3express-directorybucket-serversideencryptionbydefault.html#cfn-s3express-directorybucket-serversideencryptionbydefault-ssealgorithm
         */
        readonly sseAlgorithm: string;
    }
    /**
     * Container for lifecycle rules. You can add as many as 1000 rules.
     *
     * For more information see, [Creating and managing a lifecycle configuration for directory buckets](https://docs.aws.amazon.com/AmazonS3/latest/userguide/directory-buckets-objects-lifecycle.html          ) in the *Amazon S3 User Guide* .
     *
     * @struct
     * @stability external
     * @see http://docs.aws.amazon.com/AWSCloudFormation/latest/UserGuide/aws-properties-s3express-directorybucket-lifecycleconfiguration.html
     */
    interface LifecycleConfigurationProperty {
        /**
         * A lifecycle rule for individual objects in an Amazon S3 Express bucket.
         *
         * @see http://docs.aws.amazon.com/AWSCloudFormation/latest/UserGuide/aws-properties-s3express-directorybucket-lifecycleconfiguration.html#cfn-s3express-directorybucket-lifecycleconfiguration-rules
         */
        readonly rules: Array<cdk.IResolvable | CfnDirectoryBucket.RuleProperty> | cdk.IResolvable;
    }
    /**
     * Specifies lifecycle rules for an Amazon S3 bucket.
     *
     * For more information, see [Put Bucket Lifecycle Configuration](https://docs.aws.amazon.com/AmazonS3/latest/API/RESTBucketPUTlifecycle.html) in the *Amazon S3 API Reference* . For examples, see [Put Bucket Lifecycle Configuration Examples](https://docs.aws.amazon.com//AmazonS3/latest/API/API_PutBucketLifecycleConfiguration.html#API_PutBucketLifecycleConfiguration_Examples) .
     *
     * You must specify at least one of the following properties: `AbortIncompleteMultipartUpload` , or `ExpirationInDays` .
     *
     * @struct
     * @stability external
     * @see http://docs.aws.amazon.com/AWSCloudFormation/latest/UserGuide/aws-properties-s3express-directorybucket-rule.html
     */
    interface RuleProperty {
        /**
         * Specifies the days since the initiation of an incomplete multipart upload that Amazon S3 will wait before permanently removing all parts of the upload.
         *
         * @see http://docs.aws.amazon.com/AWSCloudFormation/latest/UserGuide/aws-properties-s3express-directorybucket-rule.html#cfn-s3express-directorybucket-rule-abortincompletemultipartupload
         */
        readonly abortIncompleteMultipartUpload?: CfnDirectoryBucket.AbortIncompleteMultipartUploadProperty | cdk.IResolvable;
        /**
         * Indicates the number of days after creation when objects are deleted from Amazon S3 and Amazon S3 Glacier.
         *
         * If you specify an expiration and transition time, you must use the same time unit for both properties (either in days or by date). The expiration time must also be later than the transition time.
         *
         * @see http://docs.aws.amazon.com/AWSCloudFormation/latest/UserGuide/aws-properties-s3express-directorybucket-rule.html#cfn-s3express-directorybucket-rule-expirationindays
         */
        readonly expirationInDays?: number;
        /**
         * Unique identifier for the rule.
         *
         * The value can't be longer than 255 characters.
         *
         * @see http://docs.aws.amazon.com/AWSCloudFormation/latest/UserGuide/aws-properties-s3express-directorybucket-rule.html#cfn-s3express-directorybucket-rule-id
         */
        readonly id?: string;
        /**
         * Specifies the minimum object size in bytes for this rule to apply to.
         *
         * Objects must be larger than this value in bytes. For more information about size based rules, see [Lifecycle configuration using size-based rules](https://docs.aws.amazon.com/AmazonS3/latest/userguide/lifecycle-configuration-examples.html#lc-size-rules) in the *Amazon S3 User Guide* .
         *
         * @see http://docs.aws.amazon.com/AWSCloudFormation/latest/UserGuide/aws-properties-s3express-directorybucket-rule.html#cfn-s3express-directorybucket-rule-objectsizegreaterthan
         */
        readonly objectSizeGreaterThan?: string;
        /**
         * Specifies the maximum object size in bytes for this rule to apply to.
         *
         * Objects must be smaller than this value in bytes. For more information about sized based rules, see [Lifecycle configuration using size-based rules](https://docs.aws.amazon.com/AmazonS3/latest/userguide/lifecycle-configuration-examples.html#lc-size-rules) in the *Amazon S3 User Guide* .
         *
         * @see http://docs.aws.amazon.com/AWSCloudFormation/latest/UserGuide/aws-properties-s3express-directorybucket-rule.html#cfn-s3express-directorybucket-rule-objectsizelessthan
         */
        readonly objectSizeLessThan?: string;
        /**
         * Object key prefix that identifies one or more objects to which this rule applies.
         *
         * > Replacement must be made for object keys containing special characters (such as carriage returns) when using XML requests. For more information, see [XML related object key constraints](https://docs.aws.amazon.com/AmazonS3/latest/userguide/object-keys.html#object-key-xml-related-constraints) .
         *
         * @see http://docs.aws.amazon.com/AWSCloudFormation/latest/UserGuide/aws-properties-s3express-directorybucket-rule.html#cfn-s3express-directorybucket-rule-prefix
         */
        readonly prefix?: string;
        /**
         * If `Enabled` , the rule is currently being applied.
         *
         * If `Disabled` , the rule is not currently being applied.
         *
         * @see http://docs.aws.amazon.com/AWSCloudFormation/latest/UserGuide/aws-properties-s3express-directorybucket-rule.html#cfn-s3express-directorybucket-rule-status
         */
        readonly status: string;
    }
    /**
     * Specifies the days since the initiation of an incomplete multipart upload that Amazon S3 will wait before permanently removing all parts of the upload.
     *
     * For more information, see [Aborting Incomplete Multipart Uploads Using a Bucket Lifecycle Configuration](https://docs.aws.amazon.com/AmazonS3/latest/dev/mpuoverview.html#mpu-abort-incomplete-mpu-lifecycle-config) in the *Amazon S3 User Guide* .
     *
     * @struct
     * @stability external
     * @see http://docs.aws.amazon.com/AWSCloudFormation/latest/UserGuide/aws-properties-s3express-directorybucket-abortincompletemultipartupload.html
     */
    interface AbortIncompleteMultipartUploadProperty {
        /**
         * Specifies the number of days after which Amazon S3 aborts an incomplete multipart upload.
         *
         * @see http://docs.aws.amazon.com/AWSCloudFormation/latest/UserGuide/aws-properties-s3express-directorybucket-abortincompletemultipartupload.html#cfn-s3express-directorybucket-abortincompletemultipartupload-daysafterinitiation
         */
        readonly daysAfterInitiation: number;
    }
}
/**
 * Properties for defining a `CfnDirectoryBucket`
 *
 * @struct
 * @stability external
 * @see http://docs.aws.amazon.com/AWSCloudFormation/latest/UserGuide/aws-resource-s3express-directorybucket.html
 */
export interface CfnDirectoryBucketProps {
    /**
     * Specifies default encryption for a bucket using server-side encryption with Amazon S3 managed keys (SSE-S3) or AWS KMS keys (SSE-KMS).
     *
     * For information about default encryption for directory buckets, see [Setting and monitoring default encryption for directory buckets](https://docs.aws.amazon.com/AmazonS3/latest/userguide/s3-express-bucket-encryption.html) in the *Amazon S3 User Guide* .
     *
     * @see http://docs.aws.amazon.com/AWSCloudFormation/latest/UserGuide/aws-resource-s3express-directorybucket.html#cfn-s3express-directorybucket-bucketencryption
     */
    readonly bucketEncryption?: CfnDirectoryBucket.BucketEncryptionProperty | cdk.IResolvable;
    /**
     * A name for the bucket.
     *
     * The bucket name must contain only lowercase letters, numbers, and hyphens (-). A directory bucket name must be unique in the chosen Zone (Availability Zone or Local Zone). The bucket name must also follow the format `*bucket_base_name* -- *zone_id* --x-s3` (for example, `*bucket_base_name* -- *usw2-az1* --x-s3` ). If you don't specify a name, AWS CloudFormation generates a unique ID and uses that ID for the bucket name. For information about bucket naming restrictions, see [Directory bucket naming rules](https://docs.aws.amazon.com/AmazonS3/latest/userguide/directory-bucket-naming-rules.html) in the *Amazon S3 User Guide* .
     *
     * > If you specify a name, you can't perform updates that require replacement of this resource. You can perform updates that require no or some interruption. If you need to replace the resource, specify a new name.
     *
     * @see http://docs.aws.amazon.com/AWSCloudFormation/latest/UserGuide/aws-resource-s3express-directorybucket.html#cfn-s3express-directorybucket-bucketname
     */
    readonly bucketName?: string;
    /**
     * The number of Zone (Availability Zone or Local Zone) that's used for redundancy for the bucket.
     *
     * @see http://docs.aws.amazon.com/AWSCloudFormation/latest/UserGuide/aws-resource-s3express-directorybucket.html#cfn-s3express-directorybucket-dataredundancy
     */
    readonly dataRedundancy: string;
    /**
     * Container for lifecycle rules. You can add as many as 1000 rules.
     *
     * For more information see, [Creating and managing a lifecycle configuration for directory buckets](https://docs.aws.amazon.com/AmazonS3/latest/userguide/directory-buckets-objects-lifecycle.html          ) in the *Amazon S3 User Guide* .
     *
     * @see http://docs.aws.amazon.com/AWSCloudFormation/latest/UserGuide/aws-resource-s3express-directorybucket.html#cfn-s3express-directorybucket-lifecycleconfiguration
     */
    readonly lifecycleConfiguration?: cdk.IResolvable | CfnDirectoryBucket.LifecycleConfigurationProperty;
    /**
     * The name of the location where the bucket will be created.
     *
     * For directory buckets, the name of the location is the Zone ID of the Availability Zone (AZ) or Local Zone (LZ) where the bucket will be created. An example AZ ID value is `usw2-az1` .
     *
     * @see http://docs.aws.amazon.com/AWSCloudFormation/latest/UserGuide/aws-resource-s3express-directorybucket.html#cfn-s3express-directorybucket-locationname
     */
    readonly locationName: string;
}
