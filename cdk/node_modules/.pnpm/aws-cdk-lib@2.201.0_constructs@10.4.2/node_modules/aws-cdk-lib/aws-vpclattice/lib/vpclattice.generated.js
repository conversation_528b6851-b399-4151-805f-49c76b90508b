"use strict";var _a,_b,_c,_d,_e,_f,_g,_h,_j,_k,_l,_m,_o;Object.defineProperty(exports,"__esModule",{value:!0}),exports.CfnServiceNetworkResourceAssociation=exports.CfnResourceGateway=exports.CfnResourceConfiguration=exports.CfnTargetGroup=exports.CfnServiceNetworkVpcAssociation=exports.CfnServiceNetworkServiceAssociation=exports.CfnServiceNetwork=exports.CfnService=exports.CfnRule=exports.CfnResourcePolicy=exports.CfnListener=exports.CfnAuthPolicy=exports.CfnAccessLogSubscription=void 0;var jsiiDeprecationWarnings=()=>{var tmp=require("../../.warnings.jsii.js");return jsiiDeprecationWarnings=()=>tmp,tmp};const JSII_RTTI_SYMBOL_1=Symbol.for("jsii.rtti");var cdk=()=>{var tmp=require("../../core");return cdk=()=>tmp,tmp},cfn_parse=()=>{var tmp=require("../../core/lib/helpers-internal");return cfn_parse=()=>tmp,tmp},cdk_errors=()=>{var tmp=require("../../core/lib/errors");return cdk_errors=()=>tmp,tmp};class CfnAccessLogSubscription extends cdk().CfnResource{static _fromCloudFormation(scope,id,resourceAttributes,options){resourceAttributes=resourceAttributes||{};const resourceProperties=options.parser.parseValue(resourceAttributes.Properties),propsResult=CfnAccessLogSubscriptionPropsFromCloudFormation(resourceProperties);if(cdk().isResolvableObject(propsResult.value))throw new(cdk_errors()).ValidationError("Unexpected IResolvable",scope);const ret=new CfnAccessLogSubscription(scope,id,propsResult.value);for(const[propKey,propVal]of Object.entries(propsResult.extraProperties))ret.addPropertyOverride(propKey,propVal);return options.parser.handleAttributes(ret,resourceAttributes,id),ret}constructor(scope,id,props){super(scope,id,{type:CfnAccessLogSubscription.CFN_RESOURCE_TYPE_NAME,properties:props});try{jsiiDeprecationWarnings().aws_cdk_lib_aws_vpclattice_CfnAccessLogSubscriptionProps(props)}catch(error){throw process.env.JSII_DEBUG!=="1"&&error.name==="DeprecationError"&&Error.captureStackTrace(error,CfnAccessLogSubscription),error}cdk().requireProperty(props,"destinationArn",this),this.attrArn=cdk().Token.asString(this.getAtt("Arn",cdk().ResolutionTypeHint.STRING)),this.attrId=cdk().Token.asString(this.getAtt("Id",cdk().ResolutionTypeHint.STRING)),this.attrResourceArn=cdk().Token.asString(this.getAtt("ResourceArn",cdk().ResolutionTypeHint.STRING)),this.attrResourceId=cdk().Token.asString(this.getAtt("ResourceId",cdk().ResolutionTypeHint.STRING)),this.destinationArn=props.destinationArn,this.resourceIdentifier=props.resourceIdentifier,this.serviceNetworkLogType=props.serviceNetworkLogType,this.tags=new(cdk()).TagManager(cdk().TagType.STANDARD,"AWS::VpcLattice::AccessLogSubscription",props.tags,{tagPropertyName:"tags"}),this.tagsRaw=props.tags}get cfnProperties(){return{destinationArn:this.destinationArn,resourceIdentifier:this.resourceIdentifier,serviceNetworkLogType:this.serviceNetworkLogType,tags:this.tags.renderTags()}}inspect(inspector){try{jsiiDeprecationWarnings().aws_cdk_lib_TreeInspector(inspector)}catch(error){throw process.env.JSII_DEBUG!=="1"&&error.name==="DeprecationError"&&Error.captureStackTrace(error,this.inspect),error}inspector.addAttribute("aws:cdk:cloudformation:type",CfnAccessLogSubscription.CFN_RESOURCE_TYPE_NAME),inspector.addAttribute("aws:cdk:cloudformation:props",this.cfnProperties)}renderProperties(props){return convertCfnAccessLogSubscriptionPropsToCloudFormation(props)}}exports.CfnAccessLogSubscription=CfnAccessLogSubscription,_a=JSII_RTTI_SYMBOL_1,CfnAccessLogSubscription[_a]={fqn:"aws-cdk-lib.aws_vpclattice.CfnAccessLogSubscription",version:"2.201.0"},CfnAccessLogSubscription.CFN_RESOURCE_TYPE_NAME="AWS::VpcLattice::AccessLogSubscription";function CfnAccessLogSubscriptionPropsValidator(properties){if(!cdk().canInspect(properties))return cdk().VALIDATION_SUCCESS;const errors=new(cdk()).ValidationResults;return properties&&typeof properties=="object"&&!Array.isArray(properties)||errors.collect(new(cdk()).ValidationResult("Expected an object, but received: "+JSON.stringify(properties))),errors.collect(cdk().propertyValidator("destinationArn",cdk().requiredValidator)(properties.destinationArn)),errors.collect(cdk().propertyValidator("destinationArn",cdk().validateString)(properties.destinationArn)),errors.collect(cdk().propertyValidator("resourceIdentifier",cdk().validateString)(properties.resourceIdentifier)),errors.collect(cdk().propertyValidator("serviceNetworkLogType",cdk().validateString)(properties.serviceNetworkLogType)),errors.collect(cdk().propertyValidator("tags",cdk().listValidator(cdk().validateCfnTag))(properties.tags)),errors.wrap('supplied properties not correct for "CfnAccessLogSubscriptionProps"')}function convertCfnAccessLogSubscriptionPropsToCloudFormation(properties){return cdk().canInspect(properties)?(CfnAccessLogSubscriptionPropsValidator(properties).assertSuccess(),{DestinationArn:cdk().stringToCloudFormation(properties.destinationArn),ResourceIdentifier:cdk().stringToCloudFormation(properties.resourceIdentifier),ServiceNetworkLogType:cdk().stringToCloudFormation(properties.serviceNetworkLogType),Tags:cdk().listMapper(cdk().cfnTagToCloudFormation)(properties.tags)}):properties}function CfnAccessLogSubscriptionPropsFromCloudFormation(properties){if(cdk().isResolvableObject(properties))return new(cfn_parse()).FromCloudFormationResult(properties);if(properties=properties??{},!(properties&&typeof properties=="object"&&!Array.isArray(properties)))return new(cfn_parse()).FromCloudFormationResult(properties);const ret=new(cfn_parse()).FromCloudFormationPropertyObject;return ret.addPropertyResult("destinationArn","DestinationArn",properties.DestinationArn!=null?cfn_parse().FromCloudFormation.getString(properties.DestinationArn):void 0),ret.addPropertyResult("resourceIdentifier","ResourceIdentifier",properties.ResourceIdentifier!=null?cfn_parse().FromCloudFormation.getString(properties.ResourceIdentifier):void 0),ret.addPropertyResult("serviceNetworkLogType","ServiceNetworkLogType",properties.ServiceNetworkLogType!=null?cfn_parse().FromCloudFormation.getString(properties.ServiceNetworkLogType):void 0),ret.addPropertyResult("tags","Tags",properties.Tags!=null?cfn_parse().FromCloudFormation.getArray(cfn_parse().FromCloudFormation.getCfnTag)(properties.Tags):void 0),ret.addUnrecognizedPropertiesAsExtra(properties),ret}class CfnAuthPolicy extends cdk().CfnResource{static _fromCloudFormation(scope,id,resourceAttributes,options){resourceAttributes=resourceAttributes||{};const resourceProperties=options.parser.parseValue(resourceAttributes.Properties),propsResult=CfnAuthPolicyPropsFromCloudFormation(resourceProperties);if(cdk().isResolvableObject(propsResult.value))throw new(cdk_errors()).ValidationError("Unexpected IResolvable",scope);const ret=new CfnAuthPolicy(scope,id,propsResult.value);for(const[propKey,propVal]of Object.entries(propsResult.extraProperties))ret.addPropertyOverride(propKey,propVal);return options.parser.handleAttributes(ret,resourceAttributes,id),ret}constructor(scope,id,props){super(scope,id,{type:CfnAuthPolicy.CFN_RESOURCE_TYPE_NAME,properties:props});try{jsiiDeprecationWarnings().aws_cdk_lib_aws_vpclattice_CfnAuthPolicyProps(props)}catch(error){throw process.env.JSII_DEBUG!=="1"&&error.name==="DeprecationError"&&Error.captureStackTrace(error,CfnAuthPolicy),error}cdk().requireProperty(props,"policy",this),cdk().requireProperty(props,"resourceIdentifier",this),this.attrState=cdk().Token.asString(this.getAtt("State",cdk().ResolutionTypeHint.STRING)),this.policy=props.policy,this.resourceIdentifier=props.resourceIdentifier}get cfnProperties(){return{policy:this.policy,resourceIdentifier:this.resourceIdentifier}}inspect(inspector){try{jsiiDeprecationWarnings().aws_cdk_lib_TreeInspector(inspector)}catch(error){throw process.env.JSII_DEBUG!=="1"&&error.name==="DeprecationError"&&Error.captureStackTrace(error,this.inspect),error}inspector.addAttribute("aws:cdk:cloudformation:type",CfnAuthPolicy.CFN_RESOURCE_TYPE_NAME),inspector.addAttribute("aws:cdk:cloudformation:props",this.cfnProperties)}renderProperties(props){return convertCfnAuthPolicyPropsToCloudFormation(props)}}exports.CfnAuthPolicy=CfnAuthPolicy,_b=JSII_RTTI_SYMBOL_1,CfnAuthPolicy[_b]={fqn:"aws-cdk-lib.aws_vpclattice.CfnAuthPolicy",version:"2.201.0"},CfnAuthPolicy.CFN_RESOURCE_TYPE_NAME="AWS::VpcLattice::AuthPolicy";function CfnAuthPolicyPropsValidator(properties){if(!cdk().canInspect(properties))return cdk().VALIDATION_SUCCESS;const errors=new(cdk()).ValidationResults;return properties&&typeof properties=="object"&&!Array.isArray(properties)||errors.collect(new(cdk()).ValidationResult("Expected an object, but received: "+JSON.stringify(properties))),errors.collect(cdk().propertyValidator("policy",cdk().requiredValidator)(properties.policy)),errors.collect(cdk().propertyValidator("policy",cdk().validateObject)(properties.policy)),errors.collect(cdk().propertyValidator("resourceIdentifier",cdk().requiredValidator)(properties.resourceIdentifier)),errors.collect(cdk().propertyValidator("resourceIdentifier",cdk().validateString)(properties.resourceIdentifier)),errors.wrap('supplied properties not correct for "CfnAuthPolicyProps"')}function convertCfnAuthPolicyPropsToCloudFormation(properties){return cdk().canInspect(properties)?(CfnAuthPolicyPropsValidator(properties).assertSuccess(),{Policy:cdk().objectToCloudFormation(properties.policy),ResourceIdentifier:cdk().stringToCloudFormation(properties.resourceIdentifier)}):properties}function CfnAuthPolicyPropsFromCloudFormation(properties){if(cdk().isResolvableObject(properties))return new(cfn_parse()).FromCloudFormationResult(properties);if(properties=properties??{},!(properties&&typeof properties=="object"&&!Array.isArray(properties)))return new(cfn_parse()).FromCloudFormationResult(properties);const ret=new(cfn_parse()).FromCloudFormationPropertyObject;return ret.addPropertyResult("policy","Policy",properties.Policy!=null?cfn_parse().FromCloudFormation.getAny(properties.Policy):void 0),ret.addPropertyResult("resourceIdentifier","ResourceIdentifier",properties.ResourceIdentifier!=null?cfn_parse().FromCloudFormation.getString(properties.ResourceIdentifier):void 0),ret.addUnrecognizedPropertiesAsExtra(properties),ret}class CfnListener extends cdk().CfnResource{static _fromCloudFormation(scope,id,resourceAttributes,options){resourceAttributes=resourceAttributes||{};const resourceProperties=options.parser.parseValue(resourceAttributes.Properties),propsResult=CfnListenerPropsFromCloudFormation(resourceProperties);if(cdk().isResolvableObject(propsResult.value))throw new(cdk_errors()).ValidationError("Unexpected IResolvable",scope);const ret=new CfnListener(scope,id,propsResult.value);for(const[propKey,propVal]of Object.entries(propsResult.extraProperties))ret.addPropertyOverride(propKey,propVal);return options.parser.handleAttributes(ret,resourceAttributes,id),ret}constructor(scope,id,props){super(scope,id,{type:CfnListener.CFN_RESOURCE_TYPE_NAME,properties:props});try{jsiiDeprecationWarnings().aws_cdk_lib_aws_vpclattice_CfnListenerProps(props)}catch(error){throw process.env.JSII_DEBUG!=="1"&&error.name==="DeprecationError"&&Error.captureStackTrace(error,CfnListener),error}cdk().requireProperty(props,"defaultAction",this),cdk().requireProperty(props,"protocol",this),this.attrArn=cdk().Token.asString(this.getAtt("Arn",cdk().ResolutionTypeHint.STRING)),this.attrId=cdk().Token.asString(this.getAtt("Id",cdk().ResolutionTypeHint.STRING)),this.attrServiceArn=cdk().Token.asString(this.getAtt("ServiceArn",cdk().ResolutionTypeHint.STRING)),this.attrServiceId=cdk().Token.asString(this.getAtt("ServiceId",cdk().ResolutionTypeHint.STRING)),this.defaultAction=props.defaultAction,this.name=props.name,this.port=props.port,this.protocol=props.protocol,this.serviceIdentifier=props.serviceIdentifier,this.tags=new(cdk()).TagManager(cdk().TagType.STANDARD,"AWS::VpcLattice::Listener",props.tags,{tagPropertyName:"tags"}),this.tagsRaw=props.tags}get cfnProperties(){return{defaultAction:this.defaultAction,name:this.name,port:this.port,protocol:this.protocol,serviceIdentifier:this.serviceIdentifier,tags:this.tags.renderTags()}}inspect(inspector){try{jsiiDeprecationWarnings().aws_cdk_lib_TreeInspector(inspector)}catch(error){throw process.env.JSII_DEBUG!=="1"&&error.name==="DeprecationError"&&Error.captureStackTrace(error,this.inspect),error}inspector.addAttribute("aws:cdk:cloudformation:type",CfnListener.CFN_RESOURCE_TYPE_NAME),inspector.addAttribute("aws:cdk:cloudformation:props",this.cfnProperties)}renderProperties(props){return convertCfnListenerPropsToCloudFormation(props)}}exports.CfnListener=CfnListener,_c=JSII_RTTI_SYMBOL_1,CfnListener[_c]={fqn:"aws-cdk-lib.aws_vpclattice.CfnListener",version:"2.201.0"},CfnListener.CFN_RESOURCE_TYPE_NAME="AWS::VpcLattice::Listener";function CfnListenerWeightedTargetGroupPropertyValidator(properties){if(!cdk().canInspect(properties))return cdk().VALIDATION_SUCCESS;const errors=new(cdk()).ValidationResults;return properties&&typeof properties=="object"&&!Array.isArray(properties)||errors.collect(new(cdk()).ValidationResult("Expected an object, but received: "+JSON.stringify(properties))),errors.collect(cdk().propertyValidator("targetGroupIdentifier",cdk().requiredValidator)(properties.targetGroupIdentifier)),errors.collect(cdk().propertyValidator("targetGroupIdentifier",cdk().validateString)(properties.targetGroupIdentifier)),errors.collect(cdk().propertyValidator("weight",cdk().validateNumber)(properties.weight)),errors.wrap('supplied properties not correct for "WeightedTargetGroupProperty"')}function convertCfnListenerWeightedTargetGroupPropertyToCloudFormation(properties){return cdk().canInspect(properties)?(CfnListenerWeightedTargetGroupPropertyValidator(properties).assertSuccess(),{TargetGroupIdentifier:cdk().stringToCloudFormation(properties.targetGroupIdentifier),Weight:cdk().numberToCloudFormation(properties.weight)}):properties}function CfnListenerWeightedTargetGroupPropertyFromCloudFormation(properties){if(cdk().isResolvableObject(properties))return new(cfn_parse()).FromCloudFormationResult(properties);if(properties=properties??{},!(properties&&typeof properties=="object"&&!Array.isArray(properties)))return new(cfn_parse()).FromCloudFormationResult(properties);const ret=new(cfn_parse()).FromCloudFormationPropertyObject;return ret.addPropertyResult("targetGroupIdentifier","TargetGroupIdentifier",properties.TargetGroupIdentifier!=null?cfn_parse().FromCloudFormation.getString(properties.TargetGroupIdentifier):void 0),ret.addPropertyResult("weight","Weight",properties.Weight!=null?cfn_parse().FromCloudFormation.getNumber(properties.Weight):void 0),ret.addUnrecognizedPropertiesAsExtra(properties),ret}function CfnListenerForwardPropertyValidator(properties){if(!cdk().canInspect(properties))return cdk().VALIDATION_SUCCESS;const errors=new(cdk()).ValidationResults;return properties&&typeof properties=="object"&&!Array.isArray(properties)||errors.collect(new(cdk()).ValidationResult("Expected an object, but received: "+JSON.stringify(properties))),errors.collect(cdk().propertyValidator("targetGroups",cdk().requiredValidator)(properties.targetGroups)),errors.collect(cdk().propertyValidator("targetGroups",cdk().listValidator(CfnListenerWeightedTargetGroupPropertyValidator))(properties.targetGroups)),errors.wrap('supplied properties not correct for "ForwardProperty"')}function convertCfnListenerForwardPropertyToCloudFormation(properties){return cdk().canInspect(properties)?(CfnListenerForwardPropertyValidator(properties).assertSuccess(),{TargetGroups:cdk().listMapper(convertCfnListenerWeightedTargetGroupPropertyToCloudFormation)(properties.targetGroups)}):properties}function CfnListenerForwardPropertyFromCloudFormation(properties){if(cdk().isResolvableObject(properties))return new(cfn_parse()).FromCloudFormationResult(properties);if(properties=properties??{},!(properties&&typeof properties=="object"&&!Array.isArray(properties)))return new(cfn_parse()).FromCloudFormationResult(properties);const ret=new(cfn_parse()).FromCloudFormationPropertyObject;return ret.addPropertyResult("targetGroups","TargetGroups",properties.TargetGroups!=null?cfn_parse().FromCloudFormation.getArray(CfnListenerWeightedTargetGroupPropertyFromCloudFormation)(properties.TargetGroups):void 0),ret.addUnrecognizedPropertiesAsExtra(properties),ret}function CfnListenerFixedResponsePropertyValidator(properties){if(!cdk().canInspect(properties))return cdk().VALIDATION_SUCCESS;const errors=new(cdk()).ValidationResults;return properties&&typeof properties=="object"&&!Array.isArray(properties)||errors.collect(new(cdk()).ValidationResult("Expected an object, but received: "+JSON.stringify(properties))),errors.collect(cdk().propertyValidator("statusCode",cdk().requiredValidator)(properties.statusCode)),errors.collect(cdk().propertyValidator("statusCode",cdk().validateNumber)(properties.statusCode)),errors.wrap('supplied properties not correct for "FixedResponseProperty"')}function convertCfnListenerFixedResponsePropertyToCloudFormation(properties){return cdk().canInspect(properties)?(CfnListenerFixedResponsePropertyValidator(properties).assertSuccess(),{StatusCode:cdk().numberToCloudFormation(properties.statusCode)}):properties}function CfnListenerFixedResponsePropertyFromCloudFormation(properties){if(cdk().isResolvableObject(properties))return new(cfn_parse()).FromCloudFormationResult(properties);if(properties=properties??{},!(properties&&typeof properties=="object"&&!Array.isArray(properties)))return new(cfn_parse()).FromCloudFormationResult(properties);const ret=new(cfn_parse()).FromCloudFormationPropertyObject;return ret.addPropertyResult("statusCode","StatusCode",properties.StatusCode!=null?cfn_parse().FromCloudFormation.getNumber(properties.StatusCode):void 0),ret.addUnrecognizedPropertiesAsExtra(properties),ret}function CfnListenerDefaultActionPropertyValidator(properties){if(!cdk().canInspect(properties))return cdk().VALIDATION_SUCCESS;const errors=new(cdk()).ValidationResults;return properties&&typeof properties=="object"&&!Array.isArray(properties)||errors.collect(new(cdk()).ValidationResult("Expected an object, but received: "+JSON.stringify(properties))),errors.collect(cdk().propertyValidator("fixedResponse",CfnListenerFixedResponsePropertyValidator)(properties.fixedResponse)),errors.collect(cdk().propertyValidator("forward",CfnListenerForwardPropertyValidator)(properties.forward)),errors.wrap('supplied properties not correct for "DefaultActionProperty"')}function convertCfnListenerDefaultActionPropertyToCloudFormation(properties){return cdk().canInspect(properties)?(CfnListenerDefaultActionPropertyValidator(properties).assertSuccess(),{FixedResponse:convertCfnListenerFixedResponsePropertyToCloudFormation(properties.fixedResponse),Forward:convertCfnListenerForwardPropertyToCloudFormation(properties.forward)}):properties}function CfnListenerDefaultActionPropertyFromCloudFormation(properties){if(cdk().isResolvableObject(properties))return new(cfn_parse()).FromCloudFormationResult(properties);if(properties=properties??{},!(properties&&typeof properties=="object"&&!Array.isArray(properties)))return new(cfn_parse()).FromCloudFormationResult(properties);const ret=new(cfn_parse()).FromCloudFormationPropertyObject;return ret.addPropertyResult("fixedResponse","FixedResponse",properties.FixedResponse!=null?CfnListenerFixedResponsePropertyFromCloudFormation(properties.FixedResponse):void 0),ret.addPropertyResult("forward","Forward",properties.Forward!=null?CfnListenerForwardPropertyFromCloudFormation(properties.Forward):void 0),ret.addUnrecognizedPropertiesAsExtra(properties),ret}function CfnListenerPropsValidator(properties){if(!cdk().canInspect(properties))return cdk().VALIDATION_SUCCESS;const errors=new(cdk()).ValidationResults;return properties&&typeof properties=="object"&&!Array.isArray(properties)||errors.collect(new(cdk()).ValidationResult("Expected an object, but received: "+JSON.stringify(properties))),errors.collect(cdk().propertyValidator("defaultAction",cdk().requiredValidator)(properties.defaultAction)),errors.collect(cdk().propertyValidator("defaultAction",CfnListenerDefaultActionPropertyValidator)(properties.defaultAction)),errors.collect(cdk().propertyValidator("name",cdk().validateString)(properties.name)),errors.collect(cdk().propertyValidator("port",cdk().validateNumber)(properties.port)),errors.collect(cdk().propertyValidator("protocol",cdk().requiredValidator)(properties.protocol)),errors.collect(cdk().propertyValidator("protocol",cdk().validateString)(properties.protocol)),errors.collect(cdk().propertyValidator("serviceIdentifier",cdk().validateString)(properties.serviceIdentifier)),errors.collect(cdk().propertyValidator("tags",cdk().listValidator(cdk().validateCfnTag))(properties.tags)),errors.wrap('supplied properties not correct for "CfnListenerProps"')}function convertCfnListenerPropsToCloudFormation(properties){return cdk().canInspect(properties)?(CfnListenerPropsValidator(properties).assertSuccess(),{DefaultAction:convertCfnListenerDefaultActionPropertyToCloudFormation(properties.defaultAction),Name:cdk().stringToCloudFormation(properties.name),Port:cdk().numberToCloudFormation(properties.port),Protocol:cdk().stringToCloudFormation(properties.protocol),ServiceIdentifier:cdk().stringToCloudFormation(properties.serviceIdentifier),Tags:cdk().listMapper(cdk().cfnTagToCloudFormation)(properties.tags)}):properties}function CfnListenerPropsFromCloudFormation(properties){if(cdk().isResolvableObject(properties))return new(cfn_parse()).FromCloudFormationResult(properties);if(properties=properties??{},!(properties&&typeof properties=="object"&&!Array.isArray(properties)))return new(cfn_parse()).FromCloudFormationResult(properties);const ret=new(cfn_parse()).FromCloudFormationPropertyObject;return ret.addPropertyResult("defaultAction","DefaultAction",properties.DefaultAction!=null?CfnListenerDefaultActionPropertyFromCloudFormation(properties.DefaultAction):void 0),ret.addPropertyResult("name","Name",properties.Name!=null?cfn_parse().FromCloudFormation.getString(properties.Name):void 0),ret.addPropertyResult("port","Port",properties.Port!=null?cfn_parse().FromCloudFormation.getNumber(properties.Port):void 0),ret.addPropertyResult("protocol","Protocol",properties.Protocol!=null?cfn_parse().FromCloudFormation.getString(properties.Protocol):void 0),ret.addPropertyResult("serviceIdentifier","ServiceIdentifier",properties.ServiceIdentifier!=null?cfn_parse().FromCloudFormation.getString(properties.ServiceIdentifier):void 0),ret.addPropertyResult("tags","Tags",properties.Tags!=null?cfn_parse().FromCloudFormation.getArray(cfn_parse().FromCloudFormation.getCfnTag)(properties.Tags):void 0),ret.addUnrecognizedPropertiesAsExtra(properties),ret}class CfnResourcePolicy extends cdk().CfnResource{static _fromCloudFormation(scope,id,resourceAttributes,options){resourceAttributes=resourceAttributes||{};const resourceProperties=options.parser.parseValue(resourceAttributes.Properties),propsResult=CfnResourcePolicyPropsFromCloudFormation(resourceProperties);if(cdk().isResolvableObject(propsResult.value))throw new(cdk_errors()).ValidationError("Unexpected IResolvable",scope);const ret=new CfnResourcePolicy(scope,id,propsResult.value);for(const[propKey,propVal]of Object.entries(propsResult.extraProperties))ret.addPropertyOverride(propKey,propVal);return options.parser.handleAttributes(ret,resourceAttributes,id),ret}constructor(scope,id,props){super(scope,id,{type:CfnResourcePolicy.CFN_RESOURCE_TYPE_NAME,properties:props});try{jsiiDeprecationWarnings().aws_cdk_lib_aws_vpclattice_CfnResourcePolicyProps(props)}catch(error){throw process.env.JSII_DEBUG!=="1"&&error.name==="DeprecationError"&&Error.captureStackTrace(error,CfnResourcePolicy),error}cdk().requireProperty(props,"policy",this),cdk().requireProperty(props,"resourceArn",this),this.policy=props.policy,this.resourceArn=props.resourceArn}get cfnProperties(){return{policy:this.policy,resourceArn:this.resourceArn}}inspect(inspector){try{jsiiDeprecationWarnings().aws_cdk_lib_TreeInspector(inspector)}catch(error){throw process.env.JSII_DEBUG!=="1"&&error.name==="DeprecationError"&&Error.captureStackTrace(error,this.inspect),error}inspector.addAttribute("aws:cdk:cloudformation:type",CfnResourcePolicy.CFN_RESOURCE_TYPE_NAME),inspector.addAttribute("aws:cdk:cloudformation:props",this.cfnProperties)}renderProperties(props){return convertCfnResourcePolicyPropsToCloudFormation(props)}}exports.CfnResourcePolicy=CfnResourcePolicy,_d=JSII_RTTI_SYMBOL_1,CfnResourcePolicy[_d]={fqn:"aws-cdk-lib.aws_vpclattice.CfnResourcePolicy",version:"2.201.0"},CfnResourcePolicy.CFN_RESOURCE_TYPE_NAME="AWS::VpcLattice::ResourcePolicy";function CfnResourcePolicyPropsValidator(properties){if(!cdk().canInspect(properties))return cdk().VALIDATION_SUCCESS;const errors=new(cdk()).ValidationResults;return properties&&typeof properties=="object"&&!Array.isArray(properties)||errors.collect(new(cdk()).ValidationResult("Expected an object, but received: "+JSON.stringify(properties))),errors.collect(cdk().propertyValidator("policy",cdk().requiredValidator)(properties.policy)),errors.collect(cdk().propertyValidator("policy",cdk().validateObject)(properties.policy)),errors.collect(cdk().propertyValidator("resourceArn",cdk().requiredValidator)(properties.resourceArn)),errors.collect(cdk().propertyValidator("resourceArn",cdk().validateString)(properties.resourceArn)),errors.wrap('supplied properties not correct for "CfnResourcePolicyProps"')}function convertCfnResourcePolicyPropsToCloudFormation(properties){return cdk().canInspect(properties)?(CfnResourcePolicyPropsValidator(properties).assertSuccess(),{Policy:cdk().objectToCloudFormation(properties.policy),ResourceArn:cdk().stringToCloudFormation(properties.resourceArn)}):properties}function CfnResourcePolicyPropsFromCloudFormation(properties){if(cdk().isResolvableObject(properties))return new(cfn_parse()).FromCloudFormationResult(properties);if(properties=properties??{},!(properties&&typeof properties=="object"&&!Array.isArray(properties)))return new(cfn_parse()).FromCloudFormationResult(properties);const ret=new(cfn_parse()).FromCloudFormationPropertyObject;return ret.addPropertyResult("policy","Policy",properties.Policy!=null?cfn_parse().FromCloudFormation.getAny(properties.Policy):void 0),ret.addPropertyResult("resourceArn","ResourceArn",properties.ResourceArn!=null?cfn_parse().FromCloudFormation.getString(properties.ResourceArn):void 0),ret.addUnrecognizedPropertiesAsExtra(properties),ret}class CfnRule extends cdk().CfnResource{static _fromCloudFormation(scope,id,resourceAttributes,options){resourceAttributes=resourceAttributes||{};const resourceProperties=options.parser.parseValue(resourceAttributes.Properties),propsResult=CfnRulePropsFromCloudFormation(resourceProperties);if(cdk().isResolvableObject(propsResult.value))throw new(cdk_errors()).ValidationError("Unexpected IResolvable",scope);const ret=new CfnRule(scope,id,propsResult.value);for(const[propKey,propVal]of Object.entries(propsResult.extraProperties))ret.addPropertyOverride(propKey,propVal);return options.parser.handleAttributes(ret,resourceAttributes,id),ret}constructor(scope,id,props){super(scope,id,{type:CfnRule.CFN_RESOURCE_TYPE_NAME,properties:props});try{jsiiDeprecationWarnings().aws_cdk_lib_aws_vpclattice_CfnRuleProps(props)}catch(error){throw process.env.JSII_DEBUG!=="1"&&error.name==="DeprecationError"&&Error.captureStackTrace(error,CfnRule),error}cdk().requireProperty(props,"action",this),cdk().requireProperty(props,"match",this),cdk().requireProperty(props,"priority",this),this.attrArn=cdk().Token.asString(this.getAtt("Arn",cdk().ResolutionTypeHint.STRING)),this.attrId=cdk().Token.asString(this.getAtt("Id",cdk().ResolutionTypeHint.STRING)),this.action=props.action,this.listenerIdentifier=props.listenerIdentifier,this.match=props.match,this.name=props.name,this.priority=props.priority,this.serviceIdentifier=props.serviceIdentifier,this.tags=new(cdk()).TagManager(cdk().TagType.STANDARD,"AWS::VpcLattice::Rule",props.tags,{tagPropertyName:"tags"}),this.tagsRaw=props.tags}get cfnProperties(){return{action:this.action,listenerIdentifier:this.listenerIdentifier,match:this.match,name:this.name,priority:this.priority,serviceIdentifier:this.serviceIdentifier,tags:this.tags.renderTags()}}inspect(inspector){try{jsiiDeprecationWarnings().aws_cdk_lib_TreeInspector(inspector)}catch(error){throw process.env.JSII_DEBUG!=="1"&&error.name==="DeprecationError"&&Error.captureStackTrace(error,this.inspect),error}inspector.addAttribute("aws:cdk:cloudformation:type",CfnRule.CFN_RESOURCE_TYPE_NAME),inspector.addAttribute("aws:cdk:cloudformation:props",this.cfnProperties)}renderProperties(props){return convertCfnRulePropsToCloudFormation(props)}}exports.CfnRule=CfnRule,_e=JSII_RTTI_SYMBOL_1,CfnRule[_e]={fqn:"aws-cdk-lib.aws_vpclattice.CfnRule",version:"2.201.0"},CfnRule.CFN_RESOURCE_TYPE_NAME="AWS::VpcLattice::Rule";function CfnRuleWeightedTargetGroupPropertyValidator(properties){if(!cdk().canInspect(properties))return cdk().VALIDATION_SUCCESS;const errors=new(cdk()).ValidationResults;return properties&&typeof properties=="object"&&!Array.isArray(properties)||errors.collect(new(cdk()).ValidationResult("Expected an object, but received: "+JSON.stringify(properties))),errors.collect(cdk().propertyValidator("targetGroupIdentifier",cdk().requiredValidator)(properties.targetGroupIdentifier)),errors.collect(cdk().propertyValidator("targetGroupIdentifier",cdk().validateString)(properties.targetGroupIdentifier)),errors.collect(cdk().propertyValidator("weight",cdk().validateNumber)(properties.weight)),errors.wrap('supplied properties not correct for "WeightedTargetGroupProperty"')}function convertCfnRuleWeightedTargetGroupPropertyToCloudFormation(properties){return cdk().canInspect(properties)?(CfnRuleWeightedTargetGroupPropertyValidator(properties).assertSuccess(),{TargetGroupIdentifier:cdk().stringToCloudFormation(properties.targetGroupIdentifier),Weight:cdk().numberToCloudFormation(properties.weight)}):properties}function CfnRuleWeightedTargetGroupPropertyFromCloudFormation(properties){if(cdk().isResolvableObject(properties))return new(cfn_parse()).FromCloudFormationResult(properties);if(properties=properties??{},!(properties&&typeof properties=="object"&&!Array.isArray(properties)))return new(cfn_parse()).FromCloudFormationResult(properties);const ret=new(cfn_parse()).FromCloudFormationPropertyObject;return ret.addPropertyResult("targetGroupIdentifier","TargetGroupIdentifier",properties.TargetGroupIdentifier!=null?cfn_parse().FromCloudFormation.getString(properties.TargetGroupIdentifier):void 0),ret.addPropertyResult("weight","Weight",properties.Weight!=null?cfn_parse().FromCloudFormation.getNumber(properties.Weight):void 0),ret.addUnrecognizedPropertiesAsExtra(properties),ret}function CfnRuleForwardPropertyValidator(properties){if(!cdk().canInspect(properties))return cdk().VALIDATION_SUCCESS;const errors=new(cdk()).ValidationResults;return properties&&typeof properties=="object"&&!Array.isArray(properties)||errors.collect(new(cdk()).ValidationResult("Expected an object, but received: "+JSON.stringify(properties))),errors.collect(cdk().propertyValidator("targetGroups",cdk().requiredValidator)(properties.targetGroups)),errors.collect(cdk().propertyValidator("targetGroups",cdk().listValidator(CfnRuleWeightedTargetGroupPropertyValidator))(properties.targetGroups)),errors.wrap('supplied properties not correct for "ForwardProperty"')}function convertCfnRuleForwardPropertyToCloudFormation(properties){return cdk().canInspect(properties)?(CfnRuleForwardPropertyValidator(properties).assertSuccess(),{TargetGroups:cdk().listMapper(convertCfnRuleWeightedTargetGroupPropertyToCloudFormation)(properties.targetGroups)}):properties}function CfnRuleForwardPropertyFromCloudFormation(properties){if(cdk().isResolvableObject(properties))return new(cfn_parse()).FromCloudFormationResult(properties);if(properties=properties??{},!(properties&&typeof properties=="object"&&!Array.isArray(properties)))return new(cfn_parse()).FromCloudFormationResult(properties);const ret=new(cfn_parse()).FromCloudFormationPropertyObject;return ret.addPropertyResult("targetGroups","TargetGroups",properties.TargetGroups!=null?cfn_parse().FromCloudFormation.getArray(CfnRuleWeightedTargetGroupPropertyFromCloudFormation)(properties.TargetGroups):void 0),ret.addUnrecognizedPropertiesAsExtra(properties),ret}function CfnRuleFixedResponsePropertyValidator(properties){if(!cdk().canInspect(properties))return cdk().VALIDATION_SUCCESS;const errors=new(cdk()).ValidationResults;return properties&&typeof properties=="object"&&!Array.isArray(properties)||errors.collect(new(cdk()).ValidationResult("Expected an object, but received: "+JSON.stringify(properties))),errors.collect(cdk().propertyValidator("statusCode",cdk().requiredValidator)(properties.statusCode)),errors.collect(cdk().propertyValidator("statusCode",cdk().validateNumber)(properties.statusCode)),errors.wrap('supplied properties not correct for "FixedResponseProperty"')}function convertCfnRuleFixedResponsePropertyToCloudFormation(properties){return cdk().canInspect(properties)?(CfnRuleFixedResponsePropertyValidator(properties).assertSuccess(),{StatusCode:cdk().numberToCloudFormation(properties.statusCode)}):properties}function CfnRuleFixedResponsePropertyFromCloudFormation(properties){if(cdk().isResolvableObject(properties))return new(cfn_parse()).FromCloudFormationResult(properties);if(properties=properties??{},!(properties&&typeof properties=="object"&&!Array.isArray(properties)))return new(cfn_parse()).FromCloudFormationResult(properties);const ret=new(cfn_parse()).FromCloudFormationPropertyObject;return ret.addPropertyResult("statusCode","StatusCode",properties.StatusCode!=null?cfn_parse().FromCloudFormation.getNumber(properties.StatusCode):void 0),ret.addUnrecognizedPropertiesAsExtra(properties),ret}function CfnRuleActionPropertyValidator(properties){if(!cdk().canInspect(properties))return cdk().VALIDATION_SUCCESS;const errors=new(cdk()).ValidationResults;return properties&&typeof properties=="object"&&!Array.isArray(properties)||errors.collect(new(cdk()).ValidationResult("Expected an object, but received: "+JSON.stringify(properties))),errors.collect(cdk().propertyValidator("fixedResponse",CfnRuleFixedResponsePropertyValidator)(properties.fixedResponse)),errors.collect(cdk().propertyValidator("forward",CfnRuleForwardPropertyValidator)(properties.forward)),errors.wrap('supplied properties not correct for "ActionProperty"')}function convertCfnRuleActionPropertyToCloudFormation(properties){return cdk().canInspect(properties)?(CfnRuleActionPropertyValidator(properties).assertSuccess(),{FixedResponse:convertCfnRuleFixedResponsePropertyToCloudFormation(properties.fixedResponse),Forward:convertCfnRuleForwardPropertyToCloudFormation(properties.forward)}):properties}function CfnRuleActionPropertyFromCloudFormation(properties){if(cdk().isResolvableObject(properties))return new(cfn_parse()).FromCloudFormationResult(properties);if(properties=properties??{},!(properties&&typeof properties=="object"&&!Array.isArray(properties)))return new(cfn_parse()).FromCloudFormationResult(properties);const ret=new(cfn_parse()).FromCloudFormationPropertyObject;return ret.addPropertyResult("fixedResponse","FixedResponse",properties.FixedResponse!=null?CfnRuleFixedResponsePropertyFromCloudFormation(properties.FixedResponse):void 0),ret.addPropertyResult("forward","Forward",properties.Forward!=null?CfnRuleForwardPropertyFromCloudFormation(properties.Forward):void 0),ret.addUnrecognizedPropertiesAsExtra(properties),ret}function CfnRuleHeaderMatchTypePropertyValidator(properties){if(!cdk().canInspect(properties))return cdk().VALIDATION_SUCCESS;const errors=new(cdk()).ValidationResults;return properties&&typeof properties=="object"&&!Array.isArray(properties)||errors.collect(new(cdk()).ValidationResult("Expected an object, but received: "+JSON.stringify(properties))),errors.collect(cdk().propertyValidator("contains",cdk().validateString)(properties.contains)),errors.collect(cdk().propertyValidator("exact",cdk().validateString)(properties.exact)),errors.collect(cdk().propertyValidator("prefix",cdk().validateString)(properties.prefix)),errors.wrap('supplied properties not correct for "HeaderMatchTypeProperty"')}function convertCfnRuleHeaderMatchTypePropertyToCloudFormation(properties){return cdk().canInspect(properties)?(CfnRuleHeaderMatchTypePropertyValidator(properties).assertSuccess(),{Contains:cdk().stringToCloudFormation(properties.contains),Exact:cdk().stringToCloudFormation(properties.exact),Prefix:cdk().stringToCloudFormation(properties.prefix)}):properties}function CfnRuleHeaderMatchTypePropertyFromCloudFormation(properties){if(cdk().isResolvableObject(properties))return new(cfn_parse()).FromCloudFormationResult(properties);if(properties=properties??{},!(properties&&typeof properties=="object"&&!Array.isArray(properties)))return new(cfn_parse()).FromCloudFormationResult(properties);const ret=new(cfn_parse()).FromCloudFormationPropertyObject;return ret.addPropertyResult("contains","Contains",properties.Contains!=null?cfn_parse().FromCloudFormation.getString(properties.Contains):void 0),ret.addPropertyResult("exact","Exact",properties.Exact!=null?cfn_parse().FromCloudFormation.getString(properties.Exact):void 0),ret.addPropertyResult("prefix","Prefix",properties.Prefix!=null?cfn_parse().FromCloudFormation.getString(properties.Prefix):void 0),ret.addUnrecognizedPropertiesAsExtra(properties),ret}function CfnRuleHeaderMatchPropertyValidator(properties){if(!cdk().canInspect(properties))return cdk().VALIDATION_SUCCESS;const errors=new(cdk()).ValidationResults;return properties&&typeof properties=="object"&&!Array.isArray(properties)||errors.collect(new(cdk()).ValidationResult("Expected an object, but received: "+JSON.stringify(properties))),errors.collect(cdk().propertyValidator("caseSensitive",cdk().validateBoolean)(properties.caseSensitive)),errors.collect(cdk().propertyValidator("match",cdk().requiredValidator)(properties.match)),errors.collect(cdk().propertyValidator("match",CfnRuleHeaderMatchTypePropertyValidator)(properties.match)),errors.collect(cdk().propertyValidator("name",cdk().requiredValidator)(properties.name)),errors.collect(cdk().propertyValidator("name",cdk().validateString)(properties.name)),errors.wrap('supplied properties not correct for "HeaderMatchProperty"')}function convertCfnRuleHeaderMatchPropertyToCloudFormation(properties){return cdk().canInspect(properties)?(CfnRuleHeaderMatchPropertyValidator(properties).assertSuccess(),{CaseSensitive:cdk().booleanToCloudFormation(properties.caseSensitive),Match:convertCfnRuleHeaderMatchTypePropertyToCloudFormation(properties.match),Name:cdk().stringToCloudFormation(properties.name)}):properties}function CfnRuleHeaderMatchPropertyFromCloudFormation(properties){if(cdk().isResolvableObject(properties))return new(cfn_parse()).FromCloudFormationResult(properties);if(properties=properties??{},!(properties&&typeof properties=="object"&&!Array.isArray(properties)))return new(cfn_parse()).FromCloudFormationResult(properties);const ret=new(cfn_parse()).FromCloudFormationPropertyObject;return ret.addPropertyResult("caseSensitive","CaseSensitive",properties.CaseSensitive!=null?cfn_parse().FromCloudFormation.getBoolean(properties.CaseSensitive):void 0),ret.addPropertyResult("match","Match",properties.Match!=null?CfnRuleHeaderMatchTypePropertyFromCloudFormation(properties.Match):void 0),ret.addPropertyResult("name","Name",properties.Name!=null?cfn_parse().FromCloudFormation.getString(properties.Name):void 0),ret.addUnrecognizedPropertiesAsExtra(properties),ret}function CfnRulePathMatchTypePropertyValidator(properties){if(!cdk().canInspect(properties))return cdk().VALIDATION_SUCCESS;const errors=new(cdk()).ValidationResults;return properties&&typeof properties=="object"&&!Array.isArray(properties)||errors.collect(new(cdk()).ValidationResult("Expected an object, but received: "+JSON.stringify(properties))),errors.collect(cdk().propertyValidator("exact",cdk().validateString)(properties.exact)),errors.collect(cdk().propertyValidator("prefix",cdk().validateString)(properties.prefix)),errors.wrap('supplied properties not correct for "PathMatchTypeProperty"')}function convertCfnRulePathMatchTypePropertyToCloudFormation(properties){return cdk().canInspect(properties)?(CfnRulePathMatchTypePropertyValidator(properties).assertSuccess(),{Exact:cdk().stringToCloudFormation(properties.exact),Prefix:cdk().stringToCloudFormation(properties.prefix)}):properties}function CfnRulePathMatchTypePropertyFromCloudFormation(properties){if(cdk().isResolvableObject(properties))return new(cfn_parse()).FromCloudFormationResult(properties);if(properties=properties??{},!(properties&&typeof properties=="object"&&!Array.isArray(properties)))return new(cfn_parse()).FromCloudFormationResult(properties);const ret=new(cfn_parse()).FromCloudFormationPropertyObject;return ret.addPropertyResult("exact","Exact",properties.Exact!=null?cfn_parse().FromCloudFormation.getString(properties.Exact):void 0),ret.addPropertyResult("prefix","Prefix",properties.Prefix!=null?cfn_parse().FromCloudFormation.getString(properties.Prefix):void 0),ret.addUnrecognizedPropertiesAsExtra(properties),ret}function CfnRulePathMatchPropertyValidator(properties){if(!cdk().canInspect(properties))return cdk().VALIDATION_SUCCESS;const errors=new(cdk()).ValidationResults;return properties&&typeof properties=="object"&&!Array.isArray(properties)||errors.collect(new(cdk()).ValidationResult("Expected an object, but received: "+JSON.stringify(properties))),errors.collect(cdk().propertyValidator("caseSensitive",cdk().validateBoolean)(properties.caseSensitive)),errors.collect(cdk().propertyValidator("match",cdk().requiredValidator)(properties.match)),errors.collect(cdk().propertyValidator("match",CfnRulePathMatchTypePropertyValidator)(properties.match)),errors.wrap('supplied properties not correct for "PathMatchProperty"')}function convertCfnRulePathMatchPropertyToCloudFormation(properties){return cdk().canInspect(properties)?(CfnRulePathMatchPropertyValidator(properties).assertSuccess(),{CaseSensitive:cdk().booleanToCloudFormation(properties.caseSensitive),Match:convertCfnRulePathMatchTypePropertyToCloudFormation(properties.match)}):properties}function CfnRulePathMatchPropertyFromCloudFormation(properties){if(cdk().isResolvableObject(properties))return new(cfn_parse()).FromCloudFormationResult(properties);if(properties=properties??{},!(properties&&typeof properties=="object"&&!Array.isArray(properties)))return new(cfn_parse()).FromCloudFormationResult(properties);const ret=new(cfn_parse()).FromCloudFormationPropertyObject;return ret.addPropertyResult("caseSensitive","CaseSensitive",properties.CaseSensitive!=null?cfn_parse().FromCloudFormation.getBoolean(properties.CaseSensitive):void 0),ret.addPropertyResult("match","Match",properties.Match!=null?CfnRulePathMatchTypePropertyFromCloudFormation(properties.Match):void 0),ret.addUnrecognizedPropertiesAsExtra(properties),ret}function CfnRuleHttpMatchPropertyValidator(properties){if(!cdk().canInspect(properties))return cdk().VALIDATION_SUCCESS;const errors=new(cdk()).ValidationResults;return properties&&typeof properties=="object"&&!Array.isArray(properties)||errors.collect(new(cdk()).ValidationResult("Expected an object, but received: "+JSON.stringify(properties))),errors.collect(cdk().propertyValidator("headerMatches",cdk().listValidator(CfnRuleHeaderMatchPropertyValidator))(properties.headerMatches)),errors.collect(cdk().propertyValidator("method",cdk().validateString)(properties.method)),errors.collect(cdk().propertyValidator("pathMatch",CfnRulePathMatchPropertyValidator)(properties.pathMatch)),errors.wrap('supplied properties not correct for "HttpMatchProperty"')}function convertCfnRuleHttpMatchPropertyToCloudFormation(properties){return cdk().canInspect(properties)?(CfnRuleHttpMatchPropertyValidator(properties).assertSuccess(),{HeaderMatches:cdk().listMapper(convertCfnRuleHeaderMatchPropertyToCloudFormation)(properties.headerMatches),Method:cdk().stringToCloudFormation(properties.method),PathMatch:convertCfnRulePathMatchPropertyToCloudFormation(properties.pathMatch)}):properties}function CfnRuleHttpMatchPropertyFromCloudFormation(properties){if(cdk().isResolvableObject(properties))return new(cfn_parse()).FromCloudFormationResult(properties);if(properties=properties??{},!(properties&&typeof properties=="object"&&!Array.isArray(properties)))return new(cfn_parse()).FromCloudFormationResult(properties);const ret=new(cfn_parse()).FromCloudFormationPropertyObject;return ret.addPropertyResult("headerMatches","HeaderMatches",properties.HeaderMatches!=null?cfn_parse().FromCloudFormation.getArray(CfnRuleHeaderMatchPropertyFromCloudFormation)(properties.HeaderMatches):void 0),ret.addPropertyResult("method","Method",properties.Method!=null?cfn_parse().FromCloudFormation.getString(properties.Method):void 0),ret.addPropertyResult("pathMatch","PathMatch",properties.PathMatch!=null?CfnRulePathMatchPropertyFromCloudFormation(properties.PathMatch):void 0),ret.addUnrecognizedPropertiesAsExtra(properties),ret}function CfnRuleMatchPropertyValidator(properties){if(!cdk().canInspect(properties))return cdk().VALIDATION_SUCCESS;const errors=new(cdk()).ValidationResults;return properties&&typeof properties=="object"&&!Array.isArray(properties)||errors.collect(new(cdk()).ValidationResult("Expected an object, but received: "+JSON.stringify(properties))),errors.collect(cdk().propertyValidator("httpMatch",cdk().requiredValidator)(properties.httpMatch)),errors.collect(cdk().propertyValidator("httpMatch",CfnRuleHttpMatchPropertyValidator)(properties.httpMatch)),errors.wrap('supplied properties not correct for "MatchProperty"')}function convertCfnRuleMatchPropertyToCloudFormation(properties){return cdk().canInspect(properties)?(CfnRuleMatchPropertyValidator(properties).assertSuccess(),{HttpMatch:convertCfnRuleHttpMatchPropertyToCloudFormation(properties.httpMatch)}):properties}function CfnRuleMatchPropertyFromCloudFormation(properties){if(cdk().isResolvableObject(properties))return new(cfn_parse()).FromCloudFormationResult(properties);if(properties=properties??{},!(properties&&typeof properties=="object"&&!Array.isArray(properties)))return new(cfn_parse()).FromCloudFormationResult(properties);const ret=new(cfn_parse()).FromCloudFormationPropertyObject;return ret.addPropertyResult("httpMatch","HttpMatch",properties.HttpMatch!=null?CfnRuleHttpMatchPropertyFromCloudFormation(properties.HttpMatch):void 0),ret.addUnrecognizedPropertiesAsExtra(properties),ret}function CfnRulePropsValidator(properties){if(!cdk().canInspect(properties))return cdk().VALIDATION_SUCCESS;const errors=new(cdk()).ValidationResults;return properties&&typeof properties=="object"&&!Array.isArray(properties)||errors.collect(new(cdk()).ValidationResult("Expected an object, but received: "+JSON.stringify(properties))),errors.collect(cdk().propertyValidator("action",cdk().requiredValidator)(properties.action)),errors.collect(cdk().propertyValidator("action",CfnRuleActionPropertyValidator)(properties.action)),errors.collect(cdk().propertyValidator("listenerIdentifier",cdk().validateString)(properties.listenerIdentifier)),errors.collect(cdk().propertyValidator("match",cdk().requiredValidator)(properties.match)),errors.collect(cdk().propertyValidator("match",CfnRuleMatchPropertyValidator)(properties.match)),errors.collect(cdk().propertyValidator("name",cdk().validateString)(properties.name)),errors.collect(cdk().propertyValidator("priority",cdk().requiredValidator)(properties.priority)),errors.collect(cdk().propertyValidator("priority",cdk().validateNumber)(properties.priority)),errors.collect(cdk().propertyValidator("serviceIdentifier",cdk().validateString)(properties.serviceIdentifier)),errors.collect(cdk().propertyValidator("tags",cdk().listValidator(cdk().validateCfnTag))(properties.tags)),errors.wrap('supplied properties not correct for "CfnRuleProps"')}function convertCfnRulePropsToCloudFormation(properties){return cdk().canInspect(properties)?(CfnRulePropsValidator(properties).assertSuccess(),{Action:convertCfnRuleActionPropertyToCloudFormation(properties.action),ListenerIdentifier:cdk().stringToCloudFormation(properties.listenerIdentifier),Match:convertCfnRuleMatchPropertyToCloudFormation(properties.match),Name:cdk().stringToCloudFormation(properties.name),Priority:cdk().numberToCloudFormation(properties.priority),ServiceIdentifier:cdk().stringToCloudFormation(properties.serviceIdentifier),Tags:cdk().listMapper(cdk().cfnTagToCloudFormation)(properties.tags)}):properties}function CfnRulePropsFromCloudFormation(properties){if(cdk().isResolvableObject(properties))return new(cfn_parse()).FromCloudFormationResult(properties);if(properties=properties??{},!(properties&&typeof properties=="object"&&!Array.isArray(properties)))return new(cfn_parse()).FromCloudFormationResult(properties);const ret=new(cfn_parse()).FromCloudFormationPropertyObject;return ret.addPropertyResult("action","Action",properties.Action!=null?CfnRuleActionPropertyFromCloudFormation(properties.Action):void 0),ret.addPropertyResult("listenerIdentifier","ListenerIdentifier",properties.ListenerIdentifier!=null?cfn_parse().FromCloudFormation.getString(properties.ListenerIdentifier):void 0),ret.addPropertyResult("match","Match",properties.Match!=null?CfnRuleMatchPropertyFromCloudFormation(properties.Match):void 0),ret.addPropertyResult("name","Name",properties.Name!=null?cfn_parse().FromCloudFormation.getString(properties.Name):void 0),ret.addPropertyResult("priority","Priority",properties.Priority!=null?cfn_parse().FromCloudFormation.getNumber(properties.Priority):void 0),ret.addPropertyResult("serviceIdentifier","ServiceIdentifier",properties.ServiceIdentifier!=null?cfn_parse().FromCloudFormation.getString(properties.ServiceIdentifier):void 0),ret.addPropertyResult("tags","Tags",properties.Tags!=null?cfn_parse().FromCloudFormation.getArray(cfn_parse().FromCloudFormation.getCfnTag)(properties.Tags):void 0),ret.addUnrecognizedPropertiesAsExtra(properties),ret}class CfnService extends cdk().CfnResource{static _fromCloudFormation(scope,id,resourceAttributes,options){resourceAttributes=resourceAttributes||{};const resourceProperties=options.parser.parseValue(resourceAttributes.Properties),propsResult=CfnServicePropsFromCloudFormation(resourceProperties);if(cdk().isResolvableObject(propsResult.value))throw new(cdk_errors()).ValidationError("Unexpected IResolvable",scope);const ret=new CfnService(scope,id,propsResult.value);for(const[propKey,propVal]of Object.entries(propsResult.extraProperties))ret.addPropertyOverride(propKey,propVal);return options.parser.handleAttributes(ret,resourceAttributes,id),ret}constructor(scope,id,props={}){super(scope,id,{type:CfnService.CFN_RESOURCE_TYPE_NAME,properties:props});try{jsiiDeprecationWarnings().aws_cdk_lib_aws_vpclattice_CfnServiceProps(props)}catch(error){throw process.env.JSII_DEBUG!=="1"&&error.name==="DeprecationError"&&Error.captureStackTrace(error,CfnService),error}this.attrArn=cdk().Token.asString(this.getAtt("Arn",cdk().ResolutionTypeHint.STRING)),this.attrCreatedAt=cdk().Token.asString(this.getAtt("CreatedAt",cdk().ResolutionTypeHint.STRING)),this.attrDnsEntryDomainName=cdk().Token.asString(this.getAtt("DnsEntry.DomainName",cdk().ResolutionTypeHint.STRING)),this.attrDnsEntryHostedZoneId=cdk().Token.asString(this.getAtt("DnsEntry.HostedZoneId",cdk().ResolutionTypeHint.STRING)),this.attrId=cdk().Token.asString(this.getAtt("Id",cdk().ResolutionTypeHint.STRING)),this.attrLastUpdatedAt=cdk().Token.asString(this.getAtt("LastUpdatedAt",cdk().ResolutionTypeHint.STRING)),this.attrStatus=cdk().Token.asString(this.getAtt("Status",cdk().ResolutionTypeHint.STRING)),this.authType=props.authType,this.certificateArn=props.certificateArn,this.customDomainName=props.customDomainName,this.dnsEntry=props.dnsEntry,this.name=props.name,this.tags=new(cdk()).TagManager(cdk().TagType.STANDARD,"AWS::VpcLattice::Service",props.tags,{tagPropertyName:"tags"}),this.tagsRaw=props.tags}get cfnProperties(){return{authType:this.authType,certificateArn:this.certificateArn,customDomainName:this.customDomainName,dnsEntry:this.dnsEntry,name:this.name,tags:this.tags.renderTags()}}inspect(inspector){try{jsiiDeprecationWarnings().aws_cdk_lib_TreeInspector(inspector)}catch(error){throw process.env.JSII_DEBUG!=="1"&&error.name==="DeprecationError"&&Error.captureStackTrace(error,this.inspect),error}inspector.addAttribute("aws:cdk:cloudformation:type",CfnService.CFN_RESOURCE_TYPE_NAME),inspector.addAttribute("aws:cdk:cloudformation:props",this.cfnProperties)}renderProperties(props){return convertCfnServicePropsToCloudFormation(props)}}exports.CfnService=CfnService,_f=JSII_RTTI_SYMBOL_1,CfnService[_f]={fqn:"aws-cdk-lib.aws_vpclattice.CfnService",version:"2.201.0"},CfnService.CFN_RESOURCE_TYPE_NAME="AWS::VpcLattice::Service";function CfnServiceDnsEntryPropertyValidator(properties){if(!cdk().canInspect(properties))return cdk().VALIDATION_SUCCESS;const errors=new(cdk()).ValidationResults;return properties&&typeof properties=="object"&&!Array.isArray(properties)||errors.collect(new(cdk()).ValidationResult("Expected an object, but received: "+JSON.stringify(properties))),errors.collect(cdk().propertyValidator("domainName",cdk().validateString)(properties.domainName)),errors.collect(cdk().propertyValidator("hostedZoneId",cdk().validateString)(properties.hostedZoneId)),errors.wrap('supplied properties not correct for "DnsEntryProperty"')}function convertCfnServiceDnsEntryPropertyToCloudFormation(properties){return cdk().canInspect(properties)?(CfnServiceDnsEntryPropertyValidator(properties).assertSuccess(),{DomainName:cdk().stringToCloudFormation(properties.domainName),HostedZoneId:cdk().stringToCloudFormation(properties.hostedZoneId)}):properties}function CfnServiceDnsEntryPropertyFromCloudFormation(properties){if(cdk().isResolvableObject(properties))return new(cfn_parse()).FromCloudFormationResult(properties);if(properties=properties??{},!(properties&&typeof properties=="object"&&!Array.isArray(properties)))return new(cfn_parse()).FromCloudFormationResult(properties);const ret=new(cfn_parse()).FromCloudFormationPropertyObject;return ret.addPropertyResult("domainName","DomainName",properties.DomainName!=null?cfn_parse().FromCloudFormation.getString(properties.DomainName):void 0),ret.addPropertyResult("hostedZoneId","HostedZoneId",properties.HostedZoneId!=null?cfn_parse().FromCloudFormation.getString(properties.HostedZoneId):void 0),ret.addUnrecognizedPropertiesAsExtra(properties),ret}function CfnServicePropsValidator(properties){if(!cdk().canInspect(properties))return cdk().VALIDATION_SUCCESS;const errors=new(cdk()).ValidationResults;return properties&&typeof properties=="object"&&!Array.isArray(properties)||errors.collect(new(cdk()).ValidationResult("Expected an object, but received: "+JSON.stringify(properties))),errors.collect(cdk().propertyValidator("authType",cdk().validateString)(properties.authType)),errors.collect(cdk().propertyValidator("certificateArn",cdk().validateString)(properties.certificateArn)),errors.collect(cdk().propertyValidator("customDomainName",cdk().validateString)(properties.customDomainName)),errors.collect(cdk().propertyValidator("dnsEntry",CfnServiceDnsEntryPropertyValidator)(properties.dnsEntry)),errors.collect(cdk().propertyValidator("name",cdk().validateString)(properties.name)),errors.collect(cdk().propertyValidator("tags",cdk().listValidator(cdk().validateCfnTag))(properties.tags)),errors.wrap('supplied properties not correct for "CfnServiceProps"')}function convertCfnServicePropsToCloudFormation(properties){return cdk().canInspect(properties)?(CfnServicePropsValidator(properties).assertSuccess(),{AuthType:cdk().stringToCloudFormation(properties.authType),CertificateArn:cdk().stringToCloudFormation(properties.certificateArn),CustomDomainName:cdk().stringToCloudFormation(properties.customDomainName),DnsEntry:convertCfnServiceDnsEntryPropertyToCloudFormation(properties.dnsEntry),Name:cdk().stringToCloudFormation(properties.name),Tags:cdk().listMapper(cdk().cfnTagToCloudFormation)(properties.tags)}):properties}function CfnServicePropsFromCloudFormation(properties){if(cdk().isResolvableObject(properties))return new(cfn_parse()).FromCloudFormationResult(properties);if(properties=properties??{},!(properties&&typeof properties=="object"&&!Array.isArray(properties)))return new(cfn_parse()).FromCloudFormationResult(properties);const ret=new(cfn_parse()).FromCloudFormationPropertyObject;return ret.addPropertyResult("authType","AuthType",properties.AuthType!=null?cfn_parse().FromCloudFormation.getString(properties.AuthType):void 0),ret.addPropertyResult("certificateArn","CertificateArn",properties.CertificateArn!=null?cfn_parse().FromCloudFormation.getString(properties.CertificateArn):void 0),ret.addPropertyResult("customDomainName","CustomDomainName",properties.CustomDomainName!=null?cfn_parse().FromCloudFormation.getString(properties.CustomDomainName):void 0),ret.addPropertyResult("dnsEntry","DnsEntry",properties.DnsEntry!=null?CfnServiceDnsEntryPropertyFromCloudFormation(properties.DnsEntry):void 0),ret.addPropertyResult("name","Name",properties.Name!=null?cfn_parse().FromCloudFormation.getString(properties.Name):void 0),ret.addPropertyResult("tags","Tags",properties.Tags!=null?cfn_parse().FromCloudFormation.getArray(cfn_parse().FromCloudFormation.getCfnTag)(properties.Tags):void 0),ret.addUnrecognizedPropertiesAsExtra(properties),ret}class CfnServiceNetwork extends cdk().CfnResource{static _fromCloudFormation(scope,id,resourceAttributes,options){resourceAttributes=resourceAttributes||{};const resourceProperties=options.parser.parseValue(resourceAttributes.Properties),propsResult=CfnServiceNetworkPropsFromCloudFormation(resourceProperties);if(cdk().isResolvableObject(propsResult.value))throw new(cdk_errors()).ValidationError("Unexpected IResolvable",scope);const ret=new CfnServiceNetwork(scope,id,propsResult.value);for(const[propKey,propVal]of Object.entries(propsResult.extraProperties))ret.addPropertyOverride(propKey,propVal);return options.parser.handleAttributes(ret,resourceAttributes,id),ret}constructor(scope,id,props={}){super(scope,id,{type:CfnServiceNetwork.CFN_RESOURCE_TYPE_NAME,properties:props});try{jsiiDeprecationWarnings().aws_cdk_lib_aws_vpclattice_CfnServiceNetworkProps(props)}catch(error){throw process.env.JSII_DEBUG!=="1"&&error.name==="DeprecationError"&&Error.captureStackTrace(error,CfnServiceNetwork),error}this.attrArn=cdk().Token.asString(this.getAtt("Arn",cdk().ResolutionTypeHint.STRING)),this.attrCreatedAt=cdk().Token.asString(this.getAtt("CreatedAt",cdk().ResolutionTypeHint.STRING)),this.attrId=cdk().Token.asString(this.getAtt("Id",cdk().ResolutionTypeHint.STRING)),this.attrLastUpdatedAt=cdk().Token.asString(this.getAtt("LastUpdatedAt",cdk().ResolutionTypeHint.STRING)),this.authType=props.authType,this.name=props.name,this.sharingConfig=props.sharingConfig,this.tags=new(cdk()).TagManager(cdk().TagType.STANDARD,"AWS::VpcLattice::ServiceNetwork",props.tags,{tagPropertyName:"tags"}),this.tagsRaw=props.tags}get cfnProperties(){return{authType:this.authType,name:this.name,sharingConfig:this.sharingConfig,tags:this.tags.renderTags()}}inspect(inspector){try{jsiiDeprecationWarnings().aws_cdk_lib_TreeInspector(inspector)}catch(error){throw process.env.JSII_DEBUG!=="1"&&error.name==="DeprecationError"&&Error.captureStackTrace(error,this.inspect),error}inspector.addAttribute("aws:cdk:cloudformation:type",CfnServiceNetwork.CFN_RESOURCE_TYPE_NAME),inspector.addAttribute("aws:cdk:cloudformation:props",this.cfnProperties)}renderProperties(props){return convertCfnServiceNetworkPropsToCloudFormation(props)}}exports.CfnServiceNetwork=CfnServiceNetwork,_g=JSII_RTTI_SYMBOL_1,CfnServiceNetwork[_g]={fqn:"aws-cdk-lib.aws_vpclattice.CfnServiceNetwork",version:"2.201.0"},CfnServiceNetwork.CFN_RESOURCE_TYPE_NAME="AWS::VpcLattice::ServiceNetwork";function CfnServiceNetworkSharingConfigPropertyValidator(properties){if(!cdk().canInspect(properties))return cdk().VALIDATION_SUCCESS;const errors=new(cdk()).ValidationResults;return properties&&typeof properties=="object"&&!Array.isArray(properties)||errors.collect(new(cdk()).ValidationResult("Expected an object, but received: "+JSON.stringify(properties))),errors.collect(cdk().propertyValidator("enabled",cdk().requiredValidator)(properties.enabled)),errors.collect(cdk().propertyValidator("enabled",cdk().validateBoolean)(properties.enabled)),errors.wrap('supplied properties not correct for "SharingConfigProperty"')}function convertCfnServiceNetworkSharingConfigPropertyToCloudFormation(properties){return cdk().canInspect(properties)?(CfnServiceNetworkSharingConfigPropertyValidator(properties).assertSuccess(),{enabled:cdk().booleanToCloudFormation(properties.enabled)}):properties}function CfnServiceNetworkSharingConfigPropertyFromCloudFormation(properties){if(cdk().isResolvableObject(properties))return new(cfn_parse()).FromCloudFormationResult(properties);if(properties=properties??{},!(properties&&typeof properties=="object"&&!Array.isArray(properties)))return new(cfn_parse()).FromCloudFormationResult(properties);const ret=new(cfn_parse()).FromCloudFormationPropertyObject;return ret.addPropertyResult("enabled","enabled",properties.enabled!=null?cfn_parse().FromCloudFormation.getBoolean(properties.enabled):void 0),ret.addUnrecognizedPropertiesAsExtra(properties),ret}function CfnServiceNetworkPropsValidator(properties){if(!cdk().canInspect(properties))return cdk().VALIDATION_SUCCESS;const errors=new(cdk()).ValidationResults;return properties&&typeof properties=="object"&&!Array.isArray(properties)||errors.collect(new(cdk()).ValidationResult("Expected an object, but received: "+JSON.stringify(properties))),errors.collect(cdk().propertyValidator("authType",cdk().validateString)(properties.authType)),errors.collect(cdk().propertyValidator("name",cdk().validateString)(properties.name)),errors.collect(cdk().propertyValidator("sharingConfig",CfnServiceNetworkSharingConfigPropertyValidator)(properties.sharingConfig)),errors.collect(cdk().propertyValidator("tags",cdk().listValidator(cdk().validateCfnTag))(properties.tags)),errors.wrap('supplied properties not correct for "CfnServiceNetworkProps"')}function convertCfnServiceNetworkPropsToCloudFormation(properties){return cdk().canInspect(properties)?(CfnServiceNetworkPropsValidator(properties).assertSuccess(),{AuthType:cdk().stringToCloudFormation(properties.authType),Name:cdk().stringToCloudFormation(properties.name),SharingConfig:convertCfnServiceNetworkSharingConfigPropertyToCloudFormation(properties.sharingConfig),Tags:cdk().listMapper(cdk().cfnTagToCloudFormation)(properties.tags)}):properties}function CfnServiceNetworkPropsFromCloudFormation(properties){if(cdk().isResolvableObject(properties))return new(cfn_parse()).FromCloudFormationResult(properties);if(properties=properties??{},!(properties&&typeof properties=="object"&&!Array.isArray(properties)))return new(cfn_parse()).FromCloudFormationResult(properties);const ret=new(cfn_parse()).FromCloudFormationPropertyObject;return ret.addPropertyResult("authType","AuthType",properties.AuthType!=null?cfn_parse().FromCloudFormation.getString(properties.AuthType):void 0),ret.addPropertyResult("name","Name",properties.Name!=null?cfn_parse().FromCloudFormation.getString(properties.Name):void 0),ret.addPropertyResult("sharingConfig","SharingConfig",properties.SharingConfig!=null?CfnServiceNetworkSharingConfigPropertyFromCloudFormation(properties.SharingConfig):void 0),ret.addPropertyResult("tags","Tags",properties.Tags!=null?cfn_parse().FromCloudFormation.getArray(cfn_parse().FromCloudFormation.getCfnTag)(properties.Tags):void 0),ret.addUnrecognizedPropertiesAsExtra(properties),ret}class CfnServiceNetworkServiceAssociation extends cdk().CfnResource{static _fromCloudFormation(scope,id,resourceAttributes,options){resourceAttributes=resourceAttributes||{};const resourceProperties=options.parser.parseValue(resourceAttributes.Properties),propsResult=CfnServiceNetworkServiceAssociationPropsFromCloudFormation(resourceProperties);if(cdk().isResolvableObject(propsResult.value))throw new(cdk_errors()).ValidationError("Unexpected IResolvable",scope);const ret=new CfnServiceNetworkServiceAssociation(scope,id,propsResult.value);for(const[propKey,propVal]of Object.entries(propsResult.extraProperties))ret.addPropertyOverride(propKey,propVal);return options.parser.handleAttributes(ret,resourceAttributes,id),ret}constructor(scope,id,props={}){super(scope,id,{type:CfnServiceNetworkServiceAssociation.CFN_RESOURCE_TYPE_NAME,properties:props});try{jsiiDeprecationWarnings().aws_cdk_lib_aws_vpclattice_CfnServiceNetworkServiceAssociationProps(props)}catch(error){throw process.env.JSII_DEBUG!=="1"&&error.name==="DeprecationError"&&Error.captureStackTrace(error,CfnServiceNetworkServiceAssociation),error}this.attrArn=cdk().Token.asString(this.getAtt("Arn",cdk().ResolutionTypeHint.STRING)),this.attrCreatedAt=cdk().Token.asString(this.getAtt("CreatedAt",cdk().ResolutionTypeHint.STRING)),this.attrDnsEntryDomainName=cdk().Token.asString(this.getAtt("DnsEntry.DomainName",cdk().ResolutionTypeHint.STRING)),this.attrDnsEntryHostedZoneId=cdk().Token.asString(this.getAtt("DnsEntry.HostedZoneId",cdk().ResolutionTypeHint.STRING)),this.attrId=cdk().Token.asString(this.getAtt("Id",cdk().ResolutionTypeHint.STRING)),this.attrServiceArn=cdk().Token.asString(this.getAtt("ServiceArn",cdk().ResolutionTypeHint.STRING)),this.attrServiceId=cdk().Token.asString(this.getAtt("ServiceId",cdk().ResolutionTypeHint.STRING)),this.attrServiceName=cdk().Token.asString(this.getAtt("ServiceName",cdk().ResolutionTypeHint.STRING)),this.attrServiceNetworkArn=cdk().Token.asString(this.getAtt("ServiceNetworkArn",cdk().ResolutionTypeHint.STRING)),this.attrServiceNetworkId=cdk().Token.asString(this.getAtt("ServiceNetworkId",cdk().ResolutionTypeHint.STRING)),this.attrServiceNetworkName=cdk().Token.asString(this.getAtt("ServiceNetworkName",cdk().ResolutionTypeHint.STRING)),this.attrStatus=cdk().Token.asString(this.getAtt("Status",cdk().ResolutionTypeHint.STRING)),this.dnsEntry=props.dnsEntry,this.serviceIdentifier=props.serviceIdentifier,this.serviceNetworkIdentifier=props.serviceNetworkIdentifier,this.tags=new(cdk()).TagManager(cdk().TagType.STANDARD,"AWS::VpcLattice::ServiceNetworkServiceAssociation",props.tags,{tagPropertyName:"tags"}),this.tagsRaw=props.tags}get cfnProperties(){return{dnsEntry:this.dnsEntry,serviceIdentifier:this.serviceIdentifier,serviceNetworkIdentifier:this.serviceNetworkIdentifier,tags:this.tags.renderTags()}}inspect(inspector){try{jsiiDeprecationWarnings().aws_cdk_lib_TreeInspector(inspector)}catch(error){throw process.env.JSII_DEBUG!=="1"&&error.name==="DeprecationError"&&Error.captureStackTrace(error,this.inspect),error}inspector.addAttribute("aws:cdk:cloudformation:type",CfnServiceNetworkServiceAssociation.CFN_RESOURCE_TYPE_NAME),inspector.addAttribute("aws:cdk:cloudformation:props",this.cfnProperties)}renderProperties(props){return convertCfnServiceNetworkServiceAssociationPropsToCloudFormation(props)}}exports.CfnServiceNetworkServiceAssociation=CfnServiceNetworkServiceAssociation,_h=JSII_RTTI_SYMBOL_1,CfnServiceNetworkServiceAssociation[_h]={fqn:"aws-cdk-lib.aws_vpclattice.CfnServiceNetworkServiceAssociation",version:"2.201.0"},CfnServiceNetworkServiceAssociation.CFN_RESOURCE_TYPE_NAME="AWS::VpcLattice::ServiceNetworkServiceAssociation";function CfnServiceNetworkServiceAssociationDnsEntryPropertyValidator(properties){if(!cdk().canInspect(properties))return cdk().VALIDATION_SUCCESS;const errors=new(cdk()).ValidationResults;return properties&&typeof properties=="object"&&!Array.isArray(properties)||errors.collect(new(cdk()).ValidationResult("Expected an object, but received: "+JSON.stringify(properties))),errors.collect(cdk().propertyValidator("domainName",cdk().validateString)(properties.domainName)),errors.collect(cdk().propertyValidator("hostedZoneId",cdk().validateString)(properties.hostedZoneId)),errors.wrap('supplied properties not correct for "DnsEntryProperty"')}function convertCfnServiceNetworkServiceAssociationDnsEntryPropertyToCloudFormation(properties){return cdk().canInspect(properties)?(CfnServiceNetworkServiceAssociationDnsEntryPropertyValidator(properties).assertSuccess(),{DomainName:cdk().stringToCloudFormation(properties.domainName),HostedZoneId:cdk().stringToCloudFormation(properties.hostedZoneId)}):properties}function CfnServiceNetworkServiceAssociationDnsEntryPropertyFromCloudFormation(properties){if(cdk().isResolvableObject(properties))return new(cfn_parse()).FromCloudFormationResult(properties);if(properties=properties??{},!(properties&&typeof properties=="object"&&!Array.isArray(properties)))return new(cfn_parse()).FromCloudFormationResult(properties);const ret=new(cfn_parse()).FromCloudFormationPropertyObject;return ret.addPropertyResult("domainName","DomainName",properties.DomainName!=null?cfn_parse().FromCloudFormation.getString(properties.DomainName):void 0),ret.addPropertyResult("hostedZoneId","HostedZoneId",properties.HostedZoneId!=null?cfn_parse().FromCloudFormation.getString(properties.HostedZoneId):void 0),ret.addUnrecognizedPropertiesAsExtra(properties),ret}function CfnServiceNetworkServiceAssociationPropsValidator(properties){if(!cdk().canInspect(properties))return cdk().VALIDATION_SUCCESS;const errors=new(cdk()).ValidationResults;return properties&&typeof properties=="object"&&!Array.isArray(properties)||errors.collect(new(cdk()).ValidationResult("Expected an object, but received: "+JSON.stringify(properties))),errors.collect(cdk().propertyValidator("dnsEntry",CfnServiceNetworkServiceAssociationDnsEntryPropertyValidator)(properties.dnsEntry)),errors.collect(cdk().propertyValidator("serviceIdentifier",cdk().validateString)(properties.serviceIdentifier)),errors.collect(cdk().propertyValidator("serviceNetworkIdentifier",cdk().validateString)(properties.serviceNetworkIdentifier)),errors.collect(cdk().propertyValidator("tags",cdk().listValidator(cdk().validateCfnTag))(properties.tags)),errors.wrap('supplied properties not correct for "CfnServiceNetworkServiceAssociationProps"')}function convertCfnServiceNetworkServiceAssociationPropsToCloudFormation(properties){return cdk().canInspect(properties)?(CfnServiceNetworkServiceAssociationPropsValidator(properties).assertSuccess(),{DnsEntry:convertCfnServiceNetworkServiceAssociationDnsEntryPropertyToCloudFormation(properties.dnsEntry),ServiceIdentifier:cdk().stringToCloudFormation(properties.serviceIdentifier),ServiceNetworkIdentifier:cdk().stringToCloudFormation(properties.serviceNetworkIdentifier),Tags:cdk().listMapper(cdk().cfnTagToCloudFormation)(properties.tags)}):properties}function CfnServiceNetworkServiceAssociationPropsFromCloudFormation(properties){if(cdk().isResolvableObject(properties))return new(cfn_parse()).FromCloudFormationResult(properties);if(properties=properties??{},!(properties&&typeof properties=="object"&&!Array.isArray(properties)))return new(cfn_parse()).FromCloudFormationResult(properties);const ret=new(cfn_parse()).FromCloudFormationPropertyObject;return ret.addPropertyResult("dnsEntry","DnsEntry",properties.DnsEntry!=null?CfnServiceNetworkServiceAssociationDnsEntryPropertyFromCloudFormation(properties.DnsEntry):void 0),ret.addPropertyResult("serviceIdentifier","ServiceIdentifier",properties.ServiceIdentifier!=null?cfn_parse().FromCloudFormation.getString(properties.ServiceIdentifier):void 0),ret.addPropertyResult("serviceNetworkIdentifier","ServiceNetworkIdentifier",properties.ServiceNetworkIdentifier!=null?cfn_parse().FromCloudFormation.getString(properties.ServiceNetworkIdentifier):void 0),ret.addPropertyResult("tags","Tags",properties.Tags!=null?cfn_parse().FromCloudFormation.getArray(cfn_parse().FromCloudFormation.getCfnTag)(properties.Tags):void 0),ret.addUnrecognizedPropertiesAsExtra(properties),ret}class CfnServiceNetworkVpcAssociation extends cdk().CfnResource{static _fromCloudFormation(scope,id,resourceAttributes,options){resourceAttributes=resourceAttributes||{};const resourceProperties=options.parser.parseValue(resourceAttributes.Properties),propsResult=CfnServiceNetworkVpcAssociationPropsFromCloudFormation(resourceProperties);if(cdk().isResolvableObject(propsResult.value))throw new(cdk_errors()).ValidationError("Unexpected IResolvable",scope);const ret=new CfnServiceNetworkVpcAssociation(scope,id,propsResult.value);for(const[propKey,propVal]of Object.entries(propsResult.extraProperties))ret.addPropertyOverride(propKey,propVal);return options.parser.handleAttributes(ret,resourceAttributes,id),ret}constructor(scope,id,props={}){super(scope,id,{type:CfnServiceNetworkVpcAssociation.CFN_RESOURCE_TYPE_NAME,properties:props});try{jsiiDeprecationWarnings().aws_cdk_lib_aws_vpclattice_CfnServiceNetworkVpcAssociationProps(props)}catch(error){throw process.env.JSII_DEBUG!=="1"&&error.name==="DeprecationError"&&Error.captureStackTrace(error,CfnServiceNetworkVpcAssociation),error}this.attrArn=cdk().Token.asString(this.getAtt("Arn",cdk().ResolutionTypeHint.STRING)),this.attrCreatedAt=cdk().Token.asString(this.getAtt("CreatedAt",cdk().ResolutionTypeHint.STRING)),this.attrId=cdk().Token.asString(this.getAtt("Id",cdk().ResolutionTypeHint.STRING)),this.attrServiceNetworkArn=cdk().Token.asString(this.getAtt("ServiceNetworkArn",cdk().ResolutionTypeHint.STRING)),this.attrServiceNetworkId=cdk().Token.asString(this.getAtt("ServiceNetworkId",cdk().ResolutionTypeHint.STRING)),this.attrServiceNetworkName=cdk().Token.asString(this.getAtt("ServiceNetworkName",cdk().ResolutionTypeHint.STRING)),this.attrStatus=cdk().Token.asString(this.getAtt("Status",cdk().ResolutionTypeHint.STRING)),this.attrVpcId=cdk().Token.asString(this.getAtt("VpcId",cdk().ResolutionTypeHint.STRING)),this.securityGroupIds=props.securityGroupIds,this.serviceNetworkIdentifier=props.serviceNetworkIdentifier,this.tags=new(cdk()).TagManager(cdk().TagType.STANDARD,"AWS::VpcLattice::ServiceNetworkVpcAssociation",props.tags,{tagPropertyName:"tags"}),this.tagsRaw=props.tags,this.vpcIdentifier=props.vpcIdentifier}get cfnProperties(){return{securityGroupIds:this.securityGroupIds,serviceNetworkIdentifier:this.serviceNetworkIdentifier,tags:this.tags.renderTags(),vpcIdentifier:this.vpcIdentifier}}inspect(inspector){try{jsiiDeprecationWarnings().aws_cdk_lib_TreeInspector(inspector)}catch(error){throw process.env.JSII_DEBUG!=="1"&&error.name==="DeprecationError"&&Error.captureStackTrace(error,this.inspect),error}inspector.addAttribute("aws:cdk:cloudformation:type",CfnServiceNetworkVpcAssociation.CFN_RESOURCE_TYPE_NAME),inspector.addAttribute("aws:cdk:cloudformation:props",this.cfnProperties)}renderProperties(props){return convertCfnServiceNetworkVpcAssociationPropsToCloudFormation(props)}}exports.CfnServiceNetworkVpcAssociation=CfnServiceNetworkVpcAssociation,_j=JSII_RTTI_SYMBOL_1,CfnServiceNetworkVpcAssociation[_j]={fqn:"aws-cdk-lib.aws_vpclattice.CfnServiceNetworkVpcAssociation",version:"2.201.0"},CfnServiceNetworkVpcAssociation.CFN_RESOURCE_TYPE_NAME="AWS::VpcLattice::ServiceNetworkVpcAssociation";function CfnServiceNetworkVpcAssociationPropsValidator(properties){if(!cdk().canInspect(properties))return cdk().VALIDATION_SUCCESS;const errors=new(cdk()).ValidationResults;return properties&&typeof properties=="object"&&!Array.isArray(properties)||errors.collect(new(cdk()).ValidationResult("Expected an object, but received: "+JSON.stringify(properties))),errors.collect(cdk().propertyValidator("securityGroupIds",cdk().listValidator(cdk().validateString))(properties.securityGroupIds)),errors.collect(cdk().propertyValidator("serviceNetworkIdentifier",cdk().validateString)(properties.serviceNetworkIdentifier)),errors.collect(cdk().propertyValidator("tags",cdk().listValidator(cdk().validateCfnTag))(properties.tags)),errors.collect(cdk().propertyValidator("vpcIdentifier",cdk().validateString)(properties.vpcIdentifier)),errors.wrap('supplied properties not correct for "CfnServiceNetworkVpcAssociationProps"')}function convertCfnServiceNetworkVpcAssociationPropsToCloudFormation(properties){return cdk().canInspect(properties)?(CfnServiceNetworkVpcAssociationPropsValidator(properties).assertSuccess(),{SecurityGroupIds:cdk().listMapper(cdk().stringToCloudFormation)(properties.securityGroupIds),ServiceNetworkIdentifier:cdk().stringToCloudFormation(properties.serviceNetworkIdentifier),Tags:cdk().listMapper(cdk().cfnTagToCloudFormation)(properties.tags),VpcIdentifier:cdk().stringToCloudFormation(properties.vpcIdentifier)}):properties}function CfnServiceNetworkVpcAssociationPropsFromCloudFormation(properties){if(cdk().isResolvableObject(properties))return new(cfn_parse()).FromCloudFormationResult(properties);if(properties=properties??{},!(properties&&typeof properties=="object"&&!Array.isArray(properties)))return new(cfn_parse()).FromCloudFormationResult(properties);const ret=new(cfn_parse()).FromCloudFormationPropertyObject;return ret.addPropertyResult("securityGroupIds","SecurityGroupIds",properties.SecurityGroupIds!=null?cfn_parse().FromCloudFormation.getArray(cfn_parse().FromCloudFormation.getString)(properties.SecurityGroupIds):void 0),ret.addPropertyResult("serviceNetworkIdentifier","ServiceNetworkIdentifier",properties.ServiceNetworkIdentifier!=null?cfn_parse().FromCloudFormation.getString(properties.ServiceNetworkIdentifier):void 0),ret.addPropertyResult("tags","Tags",properties.Tags!=null?cfn_parse().FromCloudFormation.getArray(cfn_parse().FromCloudFormation.getCfnTag)(properties.Tags):void 0),ret.addPropertyResult("vpcIdentifier","VpcIdentifier",properties.VpcIdentifier!=null?cfn_parse().FromCloudFormation.getString(properties.VpcIdentifier):void 0),ret.addUnrecognizedPropertiesAsExtra(properties),ret}class CfnTargetGroup extends cdk().CfnResource{static _fromCloudFormation(scope,id,resourceAttributes,options){resourceAttributes=resourceAttributes||{};const resourceProperties=options.parser.parseValue(resourceAttributes.Properties),propsResult=CfnTargetGroupPropsFromCloudFormation(resourceProperties);if(cdk().isResolvableObject(propsResult.value))throw new(cdk_errors()).ValidationError("Unexpected IResolvable",scope);const ret=new CfnTargetGroup(scope,id,propsResult.value);for(const[propKey,propVal]of Object.entries(propsResult.extraProperties))ret.addPropertyOverride(propKey,propVal);return options.parser.handleAttributes(ret,resourceAttributes,id),ret}constructor(scope,id,props){super(scope,id,{type:CfnTargetGroup.CFN_RESOURCE_TYPE_NAME,properties:props});try{jsiiDeprecationWarnings().aws_cdk_lib_aws_vpclattice_CfnTargetGroupProps(props)}catch(error){throw process.env.JSII_DEBUG!=="1"&&error.name==="DeprecationError"&&Error.captureStackTrace(error,CfnTargetGroup),error}cdk().requireProperty(props,"type",this),this.attrArn=cdk().Token.asString(this.getAtt("Arn",cdk().ResolutionTypeHint.STRING)),this.attrCreatedAt=cdk().Token.asString(this.getAtt("CreatedAt",cdk().ResolutionTypeHint.STRING)),this.attrId=cdk().Token.asString(this.getAtt("Id",cdk().ResolutionTypeHint.STRING)),this.attrLastUpdatedAt=cdk().Token.asString(this.getAtt("LastUpdatedAt",cdk().ResolutionTypeHint.STRING)),this.attrStatus=cdk().Token.asString(this.getAtt("Status",cdk().ResolutionTypeHint.STRING)),this.config=props.config,this.name=props.name,this.tags=new(cdk()).TagManager(cdk().TagType.STANDARD,"AWS::VpcLattice::TargetGroup",props.tags,{tagPropertyName:"tags"}),this.tagsRaw=props.tags,this.targets=props.targets,this.type=props.type}get cfnProperties(){return{config:this.config,name:this.name,tags:this.tags.renderTags(),targets:this.targets,type:this.type}}inspect(inspector){try{jsiiDeprecationWarnings().aws_cdk_lib_TreeInspector(inspector)}catch(error){throw process.env.JSII_DEBUG!=="1"&&error.name==="DeprecationError"&&Error.captureStackTrace(error,this.inspect),error}inspector.addAttribute("aws:cdk:cloudformation:type",CfnTargetGroup.CFN_RESOURCE_TYPE_NAME),inspector.addAttribute("aws:cdk:cloudformation:props",this.cfnProperties)}renderProperties(props){return convertCfnTargetGroupPropsToCloudFormation(props)}}exports.CfnTargetGroup=CfnTargetGroup,_k=JSII_RTTI_SYMBOL_1,CfnTargetGroup[_k]={fqn:"aws-cdk-lib.aws_vpclattice.CfnTargetGroup",version:"2.201.0"},CfnTargetGroup.CFN_RESOURCE_TYPE_NAME="AWS::VpcLattice::TargetGroup";function CfnTargetGroupMatcherPropertyValidator(properties){if(!cdk().canInspect(properties))return cdk().VALIDATION_SUCCESS;const errors=new(cdk()).ValidationResults;return properties&&typeof properties=="object"&&!Array.isArray(properties)||errors.collect(new(cdk()).ValidationResult("Expected an object, but received: "+JSON.stringify(properties))),errors.collect(cdk().propertyValidator("httpCode",cdk().requiredValidator)(properties.httpCode)),errors.collect(cdk().propertyValidator("httpCode",cdk().validateString)(properties.httpCode)),errors.wrap('supplied properties not correct for "MatcherProperty"')}function convertCfnTargetGroupMatcherPropertyToCloudFormation(properties){return cdk().canInspect(properties)?(CfnTargetGroupMatcherPropertyValidator(properties).assertSuccess(),{HttpCode:cdk().stringToCloudFormation(properties.httpCode)}):properties}function CfnTargetGroupMatcherPropertyFromCloudFormation(properties){if(cdk().isResolvableObject(properties))return new(cfn_parse()).FromCloudFormationResult(properties);if(properties=properties??{},!(properties&&typeof properties=="object"&&!Array.isArray(properties)))return new(cfn_parse()).FromCloudFormationResult(properties);const ret=new(cfn_parse()).FromCloudFormationPropertyObject;return ret.addPropertyResult("httpCode","HttpCode",properties.HttpCode!=null?cfn_parse().FromCloudFormation.getString(properties.HttpCode):void 0),ret.addUnrecognizedPropertiesAsExtra(properties),ret}function CfnTargetGroupHealthCheckConfigPropertyValidator(properties){if(!cdk().canInspect(properties))return cdk().VALIDATION_SUCCESS;const errors=new(cdk()).ValidationResults;return properties&&typeof properties=="object"&&!Array.isArray(properties)||errors.collect(new(cdk()).ValidationResult("Expected an object, but received: "+JSON.stringify(properties))),errors.collect(cdk().propertyValidator("enabled",cdk().validateBoolean)(properties.enabled)),errors.collect(cdk().propertyValidator("healthCheckIntervalSeconds",cdk().validateNumber)(properties.healthCheckIntervalSeconds)),errors.collect(cdk().propertyValidator("healthCheckTimeoutSeconds",cdk().validateNumber)(properties.healthCheckTimeoutSeconds)),errors.collect(cdk().propertyValidator("healthyThresholdCount",cdk().validateNumber)(properties.healthyThresholdCount)),errors.collect(cdk().propertyValidator("matcher",CfnTargetGroupMatcherPropertyValidator)(properties.matcher)),errors.collect(cdk().propertyValidator("path",cdk().validateString)(properties.path)),errors.collect(cdk().propertyValidator("port",cdk().validateNumber)(properties.port)),errors.collect(cdk().propertyValidator("protocol",cdk().validateString)(properties.protocol)),errors.collect(cdk().propertyValidator("protocolVersion",cdk().validateString)(properties.protocolVersion)),errors.collect(cdk().propertyValidator("unhealthyThresholdCount",cdk().validateNumber)(properties.unhealthyThresholdCount)),errors.wrap('supplied properties not correct for "HealthCheckConfigProperty"')}function convertCfnTargetGroupHealthCheckConfigPropertyToCloudFormation(properties){return cdk().canInspect(properties)?(CfnTargetGroupHealthCheckConfigPropertyValidator(properties).assertSuccess(),{Enabled:cdk().booleanToCloudFormation(properties.enabled),HealthCheckIntervalSeconds:cdk().numberToCloudFormation(properties.healthCheckIntervalSeconds),HealthCheckTimeoutSeconds:cdk().numberToCloudFormation(properties.healthCheckTimeoutSeconds),HealthyThresholdCount:cdk().numberToCloudFormation(properties.healthyThresholdCount),Matcher:convertCfnTargetGroupMatcherPropertyToCloudFormation(properties.matcher),Path:cdk().stringToCloudFormation(properties.path),Port:cdk().numberToCloudFormation(properties.port),Protocol:cdk().stringToCloudFormation(properties.protocol),ProtocolVersion:cdk().stringToCloudFormation(properties.protocolVersion),UnhealthyThresholdCount:cdk().numberToCloudFormation(properties.unhealthyThresholdCount)}):properties}function CfnTargetGroupHealthCheckConfigPropertyFromCloudFormation(properties){if(cdk().isResolvableObject(properties))return new(cfn_parse()).FromCloudFormationResult(properties);if(properties=properties??{},!(properties&&typeof properties=="object"&&!Array.isArray(properties)))return new(cfn_parse()).FromCloudFormationResult(properties);const ret=new(cfn_parse()).FromCloudFormationPropertyObject;return ret.addPropertyResult("enabled","Enabled",properties.Enabled!=null?cfn_parse().FromCloudFormation.getBoolean(properties.Enabled):void 0),ret.addPropertyResult("healthCheckIntervalSeconds","HealthCheckIntervalSeconds",properties.HealthCheckIntervalSeconds!=null?cfn_parse().FromCloudFormation.getNumber(properties.HealthCheckIntervalSeconds):void 0),ret.addPropertyResult("healthCheckTimeoutSeconds","HealthCheckTimeoutSeconds",properties.HealthCheckTimeoutSeconds!=null?cfn_parse().FromCloudFormation.getNumber(properties.HealthCheckTimeoutSeconds):void 0),ret.addPropertyResult("healthyThresholdCount","HealthyThresholdCount",properties.HealthyThresholdCount!=null?cfn_parse().FromCloudFormation.getNumber(properties.HealthyThresholdCount):void 0),ret.addPropertyResult("matcher","Matcher",properties.Matcher!=null?CfnTargetGroupMatcherPropertyFromCloudFormation(properties.Matcher):void 0),ret.addPropertyResult("path","Path",properties.Path!=null?cfn_parse().FromCloudFormation.getString(properties.Path):void 0),ret.addPropertyResult("port","Port",properties.Port!=null?cfn_parse().FromCloudFormation.getNumber(properties.Port):void 0),ret.addPropertyResult("protocol","Protocol",properties.Protocol!=null?cfn_parse().FromCloudFormation.getString(properties.Protocol):void 0),ret.addPropertyResult("protocolVersion","ProtocolVersion",properties.ProtocolVersion!=null?cfn_parse().FromCloudFormation.getString(properties.ProtocolVersion):void 0),ret.addPropertyResult("unhealthyThresholdCount","UnhealthyThresholdCount",properties.UnhealthyThresholdCount!=null?cfn_parse().FromCloudFormation.getNumber(properties.UnhealthyThresholdCount):void 0),ret.addUnrecognizedPropertiesAsExtra(properties),ret}function CfnTargetGroupTargetGroupConfigPropertyValidator(properties){if(!cdk().canInspect(properties))return cdk().VALIDATION_SUCCESS;const errors=new(cdk()).ValidationResults;return properties&&typeof properties=="object"&&!Array.isArray(properties)||errors.collect(new(cdk()).ValidationResult("Expected an object, but received: "+JSON.stringify(properties))),errors.collect(cdk().propertyValidator("healthCheck",CfnTargetGroupHealthCheckConfigPropertyValidator)(properties.healthCheck)),errors.collect(cdk().propertyValidator("ipAddressType",cdk().validateString)(properties.ipAddressType)),errors.collect(cdk().propertyValidator("lambdaEventStructureVersion",cdk().validateString)(properties.lambdaEventStructureVersion)),errors.collect(cdk().propertyValidator("port",cdk().validateNumber)(properties.port)),errors.collect(cdk().propertyValidator("protocol",cdk().validateString)(properties.protocol)),errors.collect(cdk().propertyValidator("protocolVersion",cdk().validateString)(properties.protocolVersion)),errors.collect(cdk().propertyValidator("vpcIdentifier",cdk().validateString)(properties.vpcIdentifier)),errors.wrap('supplied properties not correct for "TargetGroupConfigProperty"')}function convertCfnTargetGroupTargetGroupConfigPropertyToCloudFormation(properties){return cdk().canInspect(properties)?(CfnTargetGroupTargetGroupConfigPropertyValidator(properties).assertSuccess(),{HealthCheck:convertCfnTargetGroupHealthCheckConfigPropertyToCloudFormation(properties.healthCheck),IpAddressType:cdk().stringToCloudFormation(properties.ipAddressType),LambdaEventStructureVersion:cdk().stringToCloudFormation(properties.lambdaEventStructureVersion),Port:cdk().numberToCloudFormation(properties.port),Protocol:cdk().stringToCloudFormation(properties.protocol),ProtocolVersion:cdk().stringToCloudFormation(properties.protocolVersion),VpcIdentifier:cdk().stringToCloudFormation(properties.vpcIdentifier)}):properties}function CfnTargetGroupTargetGroupConfigPropertyFromCloudFormation(properties){if(cdk().isResolvableObject(properties))return new(cfn_parse()).FromCloudFormationResult(properties);if(properties=properties??{},!(properties&&typeof properties=="object"&&!Array.isArray(properties)))return new(cfn_parse()).FromCloudFormationResult(properties);const ret=new(cfn_parse()).FromCloudFormationPropertyObject;return ret.addPropertyResult("healthCheck","HealthCheck",properties.HealthCheck!=null?CfnTargetGroupHealthCheckConfigPropertyFromCloudFormation(properties.HealthCheck):void 0),ret.addPropertyResult("ipAddressType","IpAddressType",properties.IpAddressType!=null?cfn_parse().FromCloudFormation.getString(properties.IpAddressType):void 0),ret.addPropertyResult("lambdaEventStructureVersion","LambdaEventStructureVersion",properties.LambdaEventStructureVersion!=null?cfn_parse().FromCloudFormation.getString(properties.LambdaEventStructureVersion):void 0),ret.addPropertyResult("port","Port",properties.Port!=null?cfn_parse().FromCloudFormation.getNumber(properties.Port):void 0),ret.addPropertyResult("protocol","Protocol",properties.Protocol!=null?cfn_parse().FromCloudFormation.getString(properties.Protocol):void 0),ret.addPropertyResult("protocolVersion","ProtocolVersion",properties.ProtocolVersion!=null?cfn_parse().FromCloudFormation.getString(properties.ProtocolVersion):void 0),ret.addPropertyResult("vpcIdentifier","VpcIdentifier",properties.VpcIdentifier!=null?cfn_parse().FromCloudFormation.getString(properties.VpcIdentifier):void 0),ret.addUnrecognizedPropertiesAsExtra(properties),ret}function CfnTargetGroupTargetPropertyValidator(properties){if(!cdk().canInspect(properties))return cdk().VALIDATION_SUCCESS;const errors=new(cdk()).ValidationResults;return properties&&typeof properties=="object"&&!Array.isArray(properties)||errors.collect(new(cdk()).ValidationResult("Expected an object, but received: "+JSON.stringify(properties))),errors.collect(cdk().propertyValidator("id",cdk().requiredValidator)(properties.id)),errors.collect(cdk().propertyValidator("id",cdk().validateString)(properties.id)),errors.collect(cdk().propertyValidator("port",cdk().validateNumber)(properties.port)),errors.wrap('supplied properties not correct for "TargetProperty"')}function convertCfnTargetGroupTargetPropertyToCloudFormation(properties){return cdk().canInspect(properties)?(CfnTargetGroupTargetPropertyValidator(properties).assertSuccess(),{Id:cdk().stringToCloudFormation(properties.id),Port:cdk().numberToCloudFormation(properties.port)}):properties}function CfnTargetGroupTargetPropertyFromCloudFormation(properties){if(cdk().isResolvableObject(properties))return new(cfn_parse()).FromCloudFormationResult(properties);if(properties=properties??{},!(properties&&typeof properties=="object"&&!Array.isArray(properties)))return new(cfn_parse()).FromCloudFormationResult(properties);const ret=new(cfn_parse()).FromCloudFormationPropertyObject;return ret.addPropertyResult("id","Id",properties.Id!=null?cfn_parse().FromCloudFormation.getString(properties.Id):void 0),ret.addPropertyResult("port","Port",properties.Port!=null?cfn_parse().FromCloudFormation.getNumber(properties.Port):void 0),ret.addUnrecognizedPropertiesAsExtra(properties),ret}function CfnTargetGroupPropsValidator(properties){if(!cdk().canInspect(properties))return cdk().VALIDATION_SUCCESS;const errors=new(cdk()).ValidationResults;return properties&&typeof properties=="object"&&!Array.isArray(properties)||errors.collect(new(cdk()).ValidationResult("Expected an object, but received: "+JSON.stringify(properties))),errors.collect(cdk().propertyValidator("config",CfnTargetGroupTargetGroupConfigPropertyValidator)(properties.config)),errors.collect(cdk().propertyValidator("name",cdk().validateString)(properties.name)),errors.collect(cdk().propertyValidator("tags",cdk().listValidator(cdk().validateCfnTag))(properties.tags)),errors.collect(cdk().propertyValidator("targets",cdk().listValidator(CfnTargetGroupTargetPropertyValidator))(properties.targets)),errors.collect(cdk().propertyValidator("type",cdk().requiredValidator)(properties.type)),errors.collect(cdk().propertyValidator("type",cdk().validateString)(properties.type)),errors.wrap('supplied properties not correct for "CfnTargetGroupProps"')}function convertCfnTargetGroupPropsToCloudFormation(properties){return cdk().canInspect(properties)?(CfnTargetGroupPropsValidator(properties).assertSuccess(),{Config:convertCfnTargetGroupTargetGroupConfigPropertyToCloudFormation(properties.config),Name:cdk().stringToCloudFormation(properties.name),Tags:cdk().listMapper(cdk().cfnTagToCloudFormation)(properties.tags),Targets:cdk().listMapper(convertCfnTargetGroupTargetPropertyToCloudFormation)(properties.targets),Type:cdk().stringToCloudFormation(properties.type)}):properties}function CfnTargetGroupPropsFromCloudFormation(properties){if(cdk().isResolvableObject(properties))return new(cfn_parse()).FromCloudFormationResult(properties);if(properties=properties??{},!(properties&&typeof properties=="object"&&!Array.isArray(properties)))return new(cfn_parse()).FromCloudFormationResult(properties);const ret=new(cfn_parse()).FromCloudFormationPropertyObject;return ret.addPropertyResult("config","Config",properties.Config!=null?CfnTargetGroupTargetGroupConfigPropertyFromCloudFormation(properties.Config):void 0),ret.addPropertyResult("name","Name",properties.Name!=null?cfn_parse().FromCloudFormation.getString(properties.Name):void 0),ret.addPropertyResult("tags","Tags",properties.Tags!=null?cfn_parse().FromCloudFormation.getArray(cfn_parse().FromCloudFormation.getCfnTag)(properties.Tags):void 0),ret.addPropertyResult("targets","Targets",properties.Targets!=null?cfn_parse().FromCloudFormation.getArray(CfnTargetGroupTargetPropertyFromCloudFormation)(properties.Targets):void 0),ret.addPropertyResult("type","Type",properties.Type!=null?cfn_parse().FromCloudFormation.getString(properties.Type):void 0),ret.addUnrecognizedPropertiesAsExtra(properties),ret}class CfnResourceConfiguration extends cdk().CfnResource{static _fromCloudFormation(scope,id,resourceAttributes,options){resourceAttributes=resourceAttributes||{};const resourceProperties=options.parser.parseValue(resourceAttributes.Properties),propsResult=CfnResourceConfigurationPropsFromCloudFormation(resourceProperties);if(cdk().isResolvableObject(propsResult.value))throw new(cdk_errors()).ValidationError("Unexpected IResolvable",scope);const ret=new CfnResourceConfiguration(scope,id,propsResult.value);for(const[propKey,propVal]of Object.entries(propsResult.extraProperties))ret.addPropertyOverride(propKey,propVal);return options.parser.handleAttributes(ret,resourceAttributes,id),ret}constructor(scope,id,props){super(scope,id,{type:CfnResourceConfiguration.CFN_RESOURCE_TYPE_NAME,properties:props});try{jsiiDeprecationWarnings().aws_cdk_lib_aws_vpclattice_CfnResourceConfigurationProps(props)}catch(error){throw process.env.JSII_DEBUG!=="1"&&error.name==="DeprecationError"&&Error.captureStackTrace(error,CfnResourceConfiguration),error}cdk().requireProperty(props,"name",this),cdk().requireProperty(props,"resourceConfigurationType",this),this.attrArn=cdk().Token.asString(this.getAtt("Arn",cdk().ResolutionTypeHint.STRING)),this.attrId=cdk().Token.asString(this.getAtt("Id",cdk().ResolutionTypeHint.STRING)),this.allowAssociationToSharableServiceNetwork=props.allowAssociationToSharableServiceNetwork,this.cdkTagManager=new(cdk()).TagManager(cdk().TagType.STANDARD,"AWS::VpcLattice::ResourceConfiguration",void 0,{tagPropertyName:"tags"}),this.name=props.name,this.portRanges=props.portRanges,this.protocolType=props.protocolType,this.resourceConfigurationAuthType=props.resourceConfigurationAuthType,this.resourceConfigurationDefinition=props.resourceConfigurationDefinition,this.resourceConfigurationGroupId=props.resourceConfigurationGroupId,this.resourceConfigurationType=props.resourceConfigurationType,this.resourceGatewayId=props.resourceGatewayId,this.tags=props.tags}get cfnProperties(){return{allowAssociationToSharableServiceNetwork:this.allowAssociationToSharableServiceNetwork,tags:this.cdkTagManager.renderTags(this.tags),name:this.name,portRanges:this.portRanges,protocolType:this.protocolType,resourceConfigurationAuthType:this.resourceConfigurationAuthType,resourceConfigurationDefinition:this.resourceConfigurationDefinition,resourceConfigurationGroupId:this.resourceConfigurationGroupId,resourceConfigurationType:this.resourceConfigurationType,resourceGatewayId:this.resourceGatewayId}}inspect(inspector){try{jsiiDeprecationWarnings().aws_cdk_lib_TreeInspector(inspector)}catch(error){throw process.env.JSII_DEBUG!=="1"&&error.name==="DeprecationError"&&Error.captureStackTrace(error,this.inspect),error}inspector.addAttribute("aws:cdk:cloudformation:type",CfnResourceConfiguration.CFN_RESOURCE_TYPE_NAME),inspector.addAttribute("aws:cdk:cloudformation:props",this.cfnProperties)}renderProperties(props){return convertCfnResourceConfigurationPropsToCloudFormation(props)}}exports.CfnResourceConfiguration=CfnResourceConfiguration,_l=JSII_RTTI_SYMBOL_1,CfnResourceConfiguration[_l]={fqn:"aws-cdk-lib.aws_vpclattice.CfnResourceConfiguration",version:"2.201.0"},CfnResourceConfiguration.CFN_RESOURCE_TYPE_NAME="AWS::VpcLattice::ResourceConfiguration";function CfnResourceConfigurationDnsResourcePropertyValidator(properties){if(!cdk().canInspect(properties))return cdk().VALIDATION_SUCCESS;const errors=new(cdk()).ValidationResults;return properties&&typeof properties=="object"&&!Array.isArray(properties)||errors.collect(new(cdk()).ValidationResult("Expected an object, but received: "+JSON.stringify(properties))),errors.collect(cdk().propertyValidator("domainName",cdk().requiredValidator)(properties.domainName)),errors.collect(cdk().propertyValidator("domainName",cdk().validateString)(properties.domainName)),errors.collect(cdk().propertyValidator("ipAddressType",cdk().requiredValidator)(properties.ipAddressType)),errors.collect(cdk().propertyValidator("ipAddressType",cdk().validateString)(properties.ipAddressType)),errors.wrap('supplied properties not correct for "DnsResourceProperty"')}function convertCfnResourceConfigurationDnsResourcePropertyToCloudFormation(properties){return cdk().canInspect(properties)?(CfnResourceConfigurationDnsResourcePropertyValidator(properties).assertSuccess(),{DomainName:cdk().stringToCloudFormation(properties.domainName),IpAddressType:cdk().stringToCloudFormation(properties.ipAddressType)}):properties}function CfnResourceConfigurationDnsResourcePropertyFromCloudFormation(properties){if(cdk().isResolvableObject(properties))return new(cfn_parse()).FromCloudFormationResult(properties);if(properties=properties??{},!(properties&&typeof properties=="object"&&!Array.isArray(properties)))return new(cfn_parse()).FromCloudFormationResult(properties);const ret=new(cfn_parse()).FromCloudFormationPropertyObject;return ret.addPropertyResult("domainName","DomainName",properties.DomainName!=null?cfn_parse().FromCloudFormation.getString(properties.DomainName):void 0),ret.addPropertyResult("ipAddressType","IpAddressType",properties.IpAddressType!=null?cfn_parse().FromCloudFormation.getString(properties.IpAddressType):void 0),ret.addUnrecognizedPropertiesAsExtra(properties),ret}function CfnResourceConfigurationResourceConfigurationDefinitionPropertyValidator(properties){if(!cdk().canInspect(properties))return cdk().VALIDATION_SUCCESS;const errors=new(cdk()).ValidationResults;return properties&&typeof properties=="object"&&!Array.isArray(properties)||errors.collect(new(cdk()).ValidationResult("Expected an object, but received: "+JSON.stringify(properties))),errors.collect(cdk().propertyValidator("arnResource",cdk().validateString)(properties.arnResource)),errors.collect(cdk().propertyValidator("dnsResource",CfnResourceConfigurationDnsResourcePropertyValidator)(properties.dnsResource)),errors.collect(cdk().propertyValidator("ipResource",cdk().validateString)(properties.ipResource)),errors.wrap('supplied properties not correct for "ResourceConfigurationDefinitionProperty"')}function convertCfnResourceConfigurationResourceConfigurationDefinitionPropertyToCloudFormation(properties){return cdk().canInspect(properties)?(CfnResourceConfigurationResourceConfigurationDefinitionPropertyValidator(properties).assertSuccess(),{ArnResource:cdk().stringToCloudFormation(properties.arnResource),DnsResource:convertCfnResourceConfigurationDnsResourcePropertyToCloudFormation(properties.dnsResource),IpResource:cdk().stringToCloudFormation(properties.ipResource)}):properties}function CfnResourceConfigurationResourceConfigurationDefinitionPropertyFromCloudFormation(properties){if(cdk().isResolvableObject(properties))return new(cfn_parse()).FromCloudFormationResult(properties);if(properties=properties??{},!(properties&&typeof properties=="object"&&!Array.isArray(properties)))return new(cfn_parse()).FromCloudFormationResult(properties);const ret=new(cfn_parse()).FromCloudFormationPropertyObject;return ret.addPropertyResult("arnResource","ArnResource",properties.ArnResource!=null?cfn_parse().FromCloudFormation.getString(properties.ArnResource):void 0),ret.addPropertyResult("dnsResource","DnsResource",properties.DnsResource!=null?CfnResourceConfigurationDnsResourcePropertyFromCloudFormation(properties.DnsResource):void 0),ret.addPropertyResult("ipResource","IpResource",properties.IpResource!=null?cfn_parse().FromCloudFormation.getString(properties.IpResource):void 0),ret.addUnrecognizedPropertiesAsExtra(properties),ret}function CfnResourceConfigurationPropsValidator(properties){if(!cdk().canInspect(properties))return cdk().VALIDATION_SUCCESS;const errors=new(cdk()).ValidationResults;return properties&&typeof properties=="object"&&!Array.isArray(properties)||errors.collect(new(cdk()).ValidationResult("Expected an object, but received: "+JSON.stringify(properties))),errors.collect(cdk().propertyValidator("allowAssociationToSharableServiceNetwork",cdk().validateBoolean)(properties.allowAssociationToSharableServiceNetwork)),errors.collect(cdk().propertyValidator("name",cdk().requiredValidator)(properties.name)),errors.collect(cdk().propertyValidator("name",cdk().validateString)(properties.name)),errors.collect(cdk().propertyValidator("portRanges",cdk().listValidator(cdk().validateString))(properties.portRanges)),errors.collect(cdk().propertyValidator("protocolType",cdk().validateString)(properties.protocolType)),errors.collect(cdk().propertyValidator("resourceConfigurationAuthType",cdk().validateString)(properties.resourceConfigurationAuthType)),errors.collect(cdk().propertyValidator("resourceConfigurationDefinition",CfnResourceConfigurationResourceConfigurationDefinitionPropertyValidator)(properties.resourceConfigurationDefinition)),errors.collect(cdk().propertyValidator("resourceConfigurationGroupId",cdk().validateString)(properties.resourceConfigurationGroupId)),errors.collect(cdk().propertyValidator("resourceConfigurationType",cdk().requiredValidator)(properties.resourceConfigurationType)),errors.collect(cdk().propertyValidator("resourceConfigurationType",cdk().validateString)(properties.resourceConfigurationType)),errors.collect(cdk().propertyValidator("resourceGatewayId",cdk().validateString)(properties.resourceGatewayId)),errors.collect(cdk().propertyValidator("tags",cdk().listValidator(cdk().validateCfnTag))(properties.tags)),errors.wrap('supplied properties not correct for "CfnResourceConfigurationProps"')}function convertCfnResourceConfigurationPropsToCloudFormation(properties){return cdk().canInspect(properties)?(CfnResourceConfigurationPropsValidator(properties).assertSuccess(),{AllowAssociationToSharableServiceNetwork:cdk().booleanToCloudFormation(properties.allowAssociationToSharableServiceNetwork),Name:cdk().stringToCloudFormation(properties.name),PortRanges:cdk().listMapper(cdk().stringToCloudFormation)(properties.portRanges),ProtocolType:cdk().stringToCloudFormation(properties.protocolType),ResourceConfigurationAuthType:cdk().stringToCloudFormation(properties.resourceConfigurationAuthType),ResourceConfigurationDefinition:convertCfnResourceConfigurationResourceConfigurationDefinitionPropertyToCloudFormation(properties.resourceConfigurationDefinition),ResourceConfigurationGroupId:cdk().stringToCloudFormation(properties.resourceConfigurationGroupId),ResourceConfigurationType:cdk().stringToCloudFormation(properties.resourceConfigurationType),ResourceGatewayId:cdk().stringToCloudFormation(properties.resourceGatewayId),Tags:cdk().listMapper(cdk().cfnTagToCloudFormation)(properties.tags)}):properties}function CfnResourceConfigurationPropsFromCloudFormation(properties){if(cdk().isResolvableObject(properties))return new(cfn_parse()).FromCloudFormationResult(properties);if(properties=properties??{},!(properties&&typeof properties=="object"&&!Array.isArray(properties)))return new(cfn_parse()).FromCloudFormationResult(properties);const ret=new(cfn_parse()).FromCloudFormationPropertyObject;return ret.addPropertyResult("allowAssociationToSharableServiceNetwork","AllowAssociationToSharableServiceNetwork",properties.AllowAssociationToSharableServiceNetwork!=null?cfn_parse().FromCloudFormation.getBoolean(properties.AllowAssociationToSharableServiceNetwork):void 0),ret.addPropertyResult("name","Name",properties.Name!=null?cfn_parse().FromCloudFormation.getString(properties.Name):void 0),ret.addPropertyResult("portRanges","PortRanges",properties.PortRanges!=null?cfn_parse().FromCloudFormation.getArray(cfn_parse().FromCloudFormation.getString)(properties.PortRanges):void 0),ret.addPropertyResult("protocolType","ProtocolType",properties.ProtocolType!=null?cfn_parse().FromCloudFormation.getString(properties.ProtocolType):void 0),ret.addPropertyResult("resourceConfigurationAuthType","ResourceConfigurationAuthType",properties.ResourceConfigurationAuthType!=null?cfn_parse().FromCloudFormation.getString(properties.ResourceConfigurationAuthType):void 0),ret.addPropertyResult("resourceConfigurationDefinition","ResourceConfigurationDefinition",properties.ResourceConfigurationDefinition!=null?CfnResourceConfigurationResourceConfigurationDefinitionPropertyFromCloudFormation(properties.ResourceConfigurationDefinition):void 0),ret.addPropertyResult("resourceConfigurationGroupId","ResourceConfigurationGroupId",properties.ResourceConfigurationGroupId!=null?cfn_parse().FromCloudFormation.getString(properties.ResourceConfigurationGroupId):void 0),ret.addPropertyResult("resourceConfigurationType","ResourceConfigurationType",properties.ResourceConfigurationType!=null?cfn_parse().FromCloudFormation.getString(properties.ResourceConfigurationType):void 0),ret.addPropertyResult("resourceGatewayId","ResourceGatewayId",properties.ResourceGatewayId!=null?cfn_parse().FromCloudFormation.getString(properties.ResourceGatewayId):void 0),ret.addPropertyResult("tags","Tags",properties.Tags!=null?cfn_parse().FromCloudFormation.getArray(cfn_parse().FromCloudFormation.getCfnTag)(properties.Tags):void 0),ret.addUnrecognizedPropertiesAsExtra(properties),ret}class CfnResourceGateway extends cdk().CfnResource{static _fromCloudFormation(scope,id,resourceAttributes,options){resourceAttributes=resourceAttributes||{};const resourceProperties=options.parser.parseValue(resourceAttributes.Properties),propsResult=CfnResourceGatewayPropsFromCloudFormation(resourceProperties);if(cdk().isResolvableObject(propsResult.value))throw new(cdk_errors()).ValidationError("Unexpected IResolvable",scope);const ret=new CfnResourceGateway(scope,id,propsResult.value);for(const[propKey,propVal]of Object.entries(propsResult.extraProperties))ret.addPropertyOverride(propKey,propVal);return options.parser.handleAttributes(ret,resourceAttributes,id),ret}constructor(scope,id,props){super(scope,id,{type:CfnResourceGateway.CFN_RESOURCE_TYPE_NAME,properties:props});try{jsiiDeprecationWarnings().aws_cdk_lib_aws_vpclattice_CfnResourceGatewayProps(props)}catch(error){throw process.env.JSII_DEBUG!=="1"&&error.name==="DeprecationError"&&Error.captureStackTrace(error,CfnResourceGateway),error}cdk().requireProperty(props,"name",this),cdk().requireProperty(props,"subnetIds",this),cdk().requireProperty(props,"vpcIdentifier",this),this.attrArn=cdk().Token.asString(this.getAtt("Arn",cdk().ResolutionTypeHint.STRING)),this.attrId=cdk().Token.asString(this.getAtt("Id",cdk().ResolutionTypeHint.STRING)),this.cdkTagManager=new(cdk()).TagManager(cdk().TagType.STANDARD,"AWS::VpcLattice::ResourceGateway",void 0,{tagPropertyName:"tags"}),this.ipAddressType=props.ipAddressType,this.name=props.name,this.securityGroupIds=props.securityGroupIds,this.subnetIds=props.subnetIds,this.tags=props.tags,this.vpcIdentifier=props.vpcIdentifier}get cfnProperties(){return{tags:this.cdkTagManager.renderTags(this.tags),ipAddressType:this.ipAddressType,name:this.name,securityGroupIds:this.securityGroupIds,subnetIds:this.subnetIds,vpcIdentifier:this.vpcIdentifier}}inspect(inspector){try{jsiiDeprecationWarnings().aws_cdk_lib_TreeInspector(inspector)}catch(error){throw process.env.JSII_DEBUG!=="1"&&error.name==="DeprecationError"&&Error.captureStackTrace(error,this.inspect),error}inspector.addAttribute("aws:cdk:cloudformation:type",CfnResourceGateway.CFN_RESOURCE_TYPE_NAME),inspector.addAttribute("aws:cdk:cloudformation:props",this.cfnProperties)}renderProperties(props){return convertCfnResourceGatewayPropsToCloudFormation(props)}}exports.CfnResourceGateway=CfnResourceGateway,_m=JSII_RTTI_SYMBOL_1,CfnResourceGateway[_m]={fqn:"aws-cdk-lib.aws_vpclattice.CfnResourceGateway",version:"2.201.0"},CfnResourceGateway.CFN_RESOURCE_TYPE_NAME="AWS::VpcLattice::ResourceGateway";function CfnResourceGatewayPropsValidator(properties){if(!cdk().canInspect(properties))return cdk().VALIDATION_SUCCESS;const errors=new(cdk()).ValidationResults;return properties&&typeof properties=="object"&&!Array.isArray(properties)||errors.collect(new(cdk()).ValidationResult("Expected an object, but received: "+JSON.stringify(properties))),errors.collect(cdk().propertyValidator("ipAddressType",cdk().validateString)(properties.ipAddressType)),errors.collect(cdk().propertyValidator("name",cdk().requiredValidator)(properties.name)),errors.collect(cdk().propertyValidator("name",cdk().validateString)(properties.name)),errors.collect(cdk().propertyValidator("securityGroupIds",cdk().listValidator(cdk().validateString))(properties.securityGroupIds)),errors.collect(cdk().propertyValidator("subnetIds",cdk().requiredValidator)(properties.subnetIds)),errors.collect(cdk().propertyValidator("subnetIds",cdk().listValidator(cdk().validateString))(properties.subnetIds)),errors.collect(cdk().propertyValidator("tags",cdk().listValidator(cdk().validateCfnTag))(properties.tags)),errors.collect(cdk().propertyValidator("vpcIdentifier",cdk().requiredValidator)(properties.vpcIdentifier)),errors.collect(cdk().propertyValidator("vpcIdentifier",cdk().validateString)(properties.vpcIdentifier)),errors.wrap('supplied properties not correct for "CfnResourceGatewayProps"')}function convertCfnResourceGatewayPropsToCloudFormation(properties){return cdk().canInspect(properties)?(CfnResourceGatewayPropsValidator(properties).assertSuccess(),{IpAddressType:cdk().stringToCloudFormation(properties.ipAddressType),Name:cdk().stringToCloudFormation(properties.name),SecurityGroupIds:cdk().listMapper(cdk().stringToCloudFormation)(properties.securityGroupIds),SubnetIds:cdk().listMapper(cdk().stringToCloudFormation)(properties.subnetIds),Tags:cdk().listMapper(cdk().cfnTagToCloudFormation)(properties.tags),VpcIdentifier:cdk().stringToCloudFormation(properties.vpcIdentifier)}):properties}function CfnResourceGatewayPropsFromCloudFormation(properties){if(cdk().isResolvableObject(properties))return new(cfn_parse()).FromCloudFormationResult(properties);if(properties=properties??{},!(properties&&typeof properties=="object"&&!Array.isArray(properties)))return new(cfn_parse()).FromCloudFormationResult(properties);const ret=new(cfn_parse()).FromCloudFormationPropertyObject;return ret.addPropertyResult("ipAddressType","IpAddressType",properties.IpAddressType!=null?cfn_parse().FromCloudFormation.getString(properties.IpAddressType):void 0),ret.addPropertyResult("name","Name",properties.Name!=null?cfn_parse().FromCloudFormation.getString(properties.Name):void 0),ret.addPropertyResult("securityGroupIds","SecurityGroupIds",properties.SecurityGroupIds!=null?cfn_parse().FromCloudFormation.getArray(cfn_parse().FromCloudFormation.getString)(properties.SecurityGroupIds):void 0),ret.addPropertyResult("subnetIds","SubnetIds",properties.SubnetIds!=null?cfn_parse().FromCloudFormation.getArray(cfn_parse().FromCloudFormation.getString)(properties.SubnetIds):void 0),ret.addPropertyResult("tags","Tags",properties.Tags!=null?cfn_parse().FromCloudFormation.getArray(cfn_parse().FromCloudFormation.getCfnTag)(properties.Tags):void 0),ret.addPropertyResult("vpcIdentifier","VpcIdentifier",properties.VpcIdentifier!=null?cfn_parse().FromCloudFormation.getString(properties.VpcIdentifier):void 0),ret.addUnrecognizedPropertiesAsExtra(properties),ret}class CfnServiceNetworkResourceAssociation extends cdk().CfnResource{static _fromCloudFormation(scope,id,resourceAttributes,options){resourceAttributes=resourceAttributes||{};const resourceProperties=options.parser.parseValue(resourceAttributes.Properties),propsResult=CfnServiceNetworkResourceAssociationPropsFromCloudFormation(resourceProperties);if(cdk().isResolvableObject(propsResult.value))throw new(cdk_errors()).ValidationError("Unexpected IResolvable",scope);const ret=new CfnServiceNetworkResourceAssociation(scope,id,propsResult.value);for(const[propKey,propVal]of Object.entries(propsResult.extraProperties))ret.addPropertyOverride(propKey,propVal);return options.parser.handleAttributes(ret,resourceAttributes,id),ret}constructor(scope,id,props={}){super(scope,id,{type:CfnServiceNetworkResourceAssociation.CFN_RESOURCE_TYPE_NAME,properties:props});try{jsiiDeprecationWarnings().aws_cdk_lib_aws_vpclattice_CfnServiceNetworkResourceAssociationProps(props)}catch(error){throw process.env.JSII_DEBUG!=="1"&&error.name==="DeprecationError"&&Error.captureStackTrace(error,CfnServiceNetworkResourceAssociation),error}this.attrArn=cdk().Token.asString(this.getAtt("Arn",cdk().ResolutionTypeHint.STRING)),this.attrId=cdk().Token.asString(this.getAtt("Id",cdk().ResolutionTypeHint.STRING)),this.cdkTagManager=new(cdk()).TagManager(cdk().TagType.STANDARD,"AWS::VpcLattice::ServiceNetworkResourceAssociation",void 0,{tagPropertyName:"tags"}),this.resourceConfigurationId=props.resourceConfigurationId,this.serviceNetworkId=props.serviceNetworkId,this.tags=props.tags}get cfnProperties(){return{tags:this.cdkTagManager.renderTags(this.tags),resourceConfigurationId:this.resourceConfigurationId,serviceNetworkId:this.serviceNetworkId}}inspect(inspector){try{jsiiDeprecationWarnings().aws_cdk_lib_TreeInspector(inspector)}catch(error){throw process.env.JSII_DEBUG!=="1"&&error.name==="DeprecationError"&&Error.captureStackTrace(error,this.inspect),error}inspector.addAttribute("aws:cdk:cloudformation:type",CfnServiceNetworkResourceAssociation.CFN_RESOURCE_TYPE_NAME),inspector.addAttribute("aws:cdk:cloudformation:props",this.cfnProperties)}renderProperties(props){return convertCfnServiceNetworkResourceAssociationPropsToCloudFormation(props)}}exports.CfnServiceNetworkResourceAssociation=CfnServiceNetworkResourceAssociation,_o=JSII_RTTI_SYMBOL_1,CfnServiceNetworkResourceAssociation[_o]={fqn:"aws-cdk-lib.aws_vpclattice.CfnServiceNetworkResourceAssociation",version:"2.201.0"},CfnServiceNetworkResourceAssociation.CFN_RESOURCE_TYPE_NAME="AWS::VpcLattice::ServiceNetworkResourceAssociation";function CfnServiceNetworkResourceAssociationPropsValidator(properties){if(!cdk().canInspect(properties))return cdk().VALIDATION_SUCCESS;const errors=new(cdk()).ValidationResults;return properties&&typeof properties=="object"&&!Array.isArray(properties)||errors.collect(new(cdk()).ValidationResult("Expected an object, but received: "+JSON.stringify(properties))),errors.collect(cdk().propertyValidator("resourceConfigurationId",cdk().validateString)(properties.resourceConfigurationId)),errors.collect(cdk().propertyValidator("serviceNetworkId",cdk().validateString)(properties.serviceNetworkId)),errors.collect(cdk().propertyValidator("tags",cdk().listValidator(cdk().validateCfnTag))(properties.tags)),errors.wrap('supplied properties not correct for "CfnServiceNetworkResourceAssociationProps"')}function convertCfnServiceNetworkResourceAssociationPropsToCloudFormation(properties){return cdk().canInspect(properties)?(CfnServiceNetworkResourceAssociationPropsValidator(properties).assertSuccess(),{ResourceConfigurationId:cdk().stringToCloudFormation(properties.resourceConfigurationId),ServiceNetworkId:cdk().stringToCloudFormation(properties.serviceNetworkId),Tags:cdk().listMapper(cdk().cfnTagToCloudFormation)(properties.tags)}):properties}function CfnServiceNetworkResourceAssociationPropsFromCloudFormation(properties){if(cdk().isResolvableObject(properties))return new(cfn_parse()).FromCloudFormationResult(properties);if(properties=properties??{},!(properties&&typeof properties=="object"&&!Array.isArray(properties)))return new(cfn_parse()).FromCloudFormationResult(properties);const ret=new(cfn_parse()).FromCloudFormationPropertyObject;return ret.addPropertyResult("resourceConfigurationId","ResourceConfigurationId",properties.ResourceConfigurationId!=null?cfn_parse().FromCloudFormation.getString(properties.ResourceConfigurationId):void 0),ret.addPropertyResult("serviceNetworkId","ServiceNetworkId",properties.ServiceNetworkId!=null?cfn_parse().FromCloudFormation.getString(properties.ServiceNetworkId):void 0),ret.addPropertyResult("tags","Tags",properties.Tags!=null?cfn_parse().FromCloudFormation.getArray(cfn_parse().FromCloudFormation.getCfnTag)(properties.Tags):void 0),ret.addUnrecognizedPropertiesAsExtra(properties),ret}
