import * as cdk from "../../core";
import * as constructs from "constructs";
import * as cfn_parse from "../../core/lib/helpers-internal";
/**
 * An SAP application registered with AWS Systems Manager for SAP.
 *
 * @cloudformationResource AWS::SystemsManagerSAP::Application
 * @stability external
 * @see http://docs.aws.amazon.com/AWSCloudFormation/latest/UserGuide/aws-resource-systemsmanagersap-application.html
 */
export declare class CfnApplication extends cdk.CfnResource implements cdk.IInspectable, cdk.ITaggable {
    /**
     * The CloudFormation resource type name for this resource class.
     */
    static readonly CFN_RESOURCE_TYPE_NAME: string;
    /**
     * Build a CfnApplication from CloudFormation properties
     *
     * A factory method that creates a new instance of this class from an object
     * containing the CloudFormation properties of this resource.
     * Used in the @aws-cdk/cloudformation-include module.
     *
     * @internal
     */
    static _fromCloudFormation(scope: constructs.Construct, id: string, resourceAttributes: any, options: cfn_parse.FromCloudFormationOptions): CfnApplication;
    /**
     * The Amazon Resource Name of the SAP application.
     *
     * @cloudformationAttribute Arn
     */
    readonly attrArn: string;
    /**
     * The ID of the application.
     */
    applicationId: string;
    /**
     * The type of the application.
     */
    applicationType: string;
    /**
     * This is an optional parameter for component details to which the SAP ABAP application is attached, such as Web Dispatcher.
     */
    componentsInfo?: Array<CfnApplication.ComponentInfoProperty | cdk.IResolvable> | cdk.IResolvable;
    /**
     * The credentials of the SAP application.
     */
    credentials?: Array<CfnApplication.CredentialProperty | cdk.IResolvable> | cdk.IResolvable;
    /**
     * The Amazon Resource Name (ARN) of the database.
     */
    databaseArn?: string;
    /**
     * The Amazon EC2 instances on which your SAP application is running.
     */
    instances?: Array<string>;
    /**
     * The SAP instance number of the application.
     */
    sapInstanceNumber?: string;
    /**
     * The System ID of the application.
     */
    sid?: string;
    /**
     * Tag Manager which manages the tags for this resource
     */
    readonly tags: cdk.TagManager;
    /**
     * The tags on the application.
     */
    tagsRaw?: Array<cdk.CfnTag>;
    /**
     * @param scope Scope in which this resource is defined
     * @param id Construct identifier for this resource (unique in its scope)
     * @param props Resource properties
     */
    constructor(scope: constructs.Construct, id: string, props: CfnApplicationProps);
    protected get cfnProperties(): Record<string, any>;
    /**
     * Examines the CloudFormation resource and discloses attributes
     *
     * @param inspector tree inspector to collect and process attributes
     */
    inspect(inspector: cdk.TreeInspector): void;
    protected renderProperties(props: Record<string, any>): Record<string, any>;
}
export declare namespace CfnApplication {
    /**
     * The credentials of your SAP application.
     *
     * @struct
     * @stability external
     * @see http://docs.aws.amazon.com/AWSCloudFormation/latest/UserGuide/aws-properties-systemsmanagersap-application-credential.html
     */
    interface CredentialProperty {
        /**
         * The type of the application credentials.
         *
         * @see http://docs.aws.amazon.com/AWSCloudFormation/latest/UserGuide/aws-properties-systemsmanagersap-application-credential.html#cfn-systemsmanagersap-application-credential-credentialtype
         */
        readonly credentialType?: string;
        /**
         * The name of the SAP HANA database.
         *
         * @see http://docs.aws.amazon.com/AWSCloudFormation/latest/UserGuide/aws-properties-systemsmanagersap-application-credential.html#cfn-systemsmanagersap-application-credential-databasename
         */
        readonly databaseName?: string;
        /**
         * The secret ID created in AWS Secrets Manager to store the credentials of the SAP application.
         *
         * @see http://docs.aws.amazon.com/AWSCloudFormation/latest/UserGuide/aws-properties-systemsmanagersap-application-credential.html#cfn-systemsmanagersap-application-credential-secretid
         */
        readonly secretId?: string;
    }
    /**
     * This is information about the component of your SAP application, such as Web Dispatcher.
     *
     * @struct
     * @stability external
     * @see http://docs.aws.amazon.com/AWSCloudFormation/latest/UserGuide/aws-properties-systemsmanagersap-application-componentinfo.html
     */
    interface ComponentInfoProperty {
        /**
         * This string is the type of the component.
         *
         * Accepted value is `WD` .
         *
         * @see http://docs.aws.amazon.com/AWSCloudFormation/latest/UserGuide/aws-properties-systemsmanagersap-application-componentinfo.html#cfn-systemsmanagersap-application-componentinfo-componenttype
         */
        readonly componentType?: string;
        /**
         * This is the Amazon EC2 instance on which your SAP component is running.
         *
         * Accepted values are alphanumeric.
         *
         * @see http://docs.aws.amazon.com/AWSCloudFormation/latest/UserGuide/aws-properties-systemsmanagersap-application-componentinfo.html#cfn-systemsmanagersap-application-componentinfo-ec2instanceid
         */
        readonly ec2InstanceId?: string;
        /**
         * This string is the SAP System ID of the component.
         *
         * Accepted values are alphanumeric.
         *
         * @see http://docs.aws.amazon.com/AWSCloudFormation/latest/UserGuide/aws-properties-systemsmanagersap-application-componentinfo.html#cfn-systemsmanagersap-application-componentinfo-sid
         */
        readonly sid?: string;
    }
}
/**
 * Properties for defining a `CfnApplication`
 *
 * @struct
 * @stability external
 * @see http://docs.aws.amazon.com/AWSCloudFormation/latest/UserGuide/aws-resource-systemsmanagersap-application.html
 */
export interface CfnApplicationProps {
    /**
     * The ID of the application.
     *
     * @see http://docs.aws.amazon.com/AWSCloudFormation/latest/UserGuide/aws-resource-systemsmanagersap-application.html#cfn-systemsmanagersap-application-applicationid
     */
    readonly applicationId: string;
    /**
     * The type of the application.
     *
     * @see http://docs.aws.amazon.com/AWSCloudFormation/latest/UserGuide/aws-resource-systemsmanagersap-application.html#cfn-systemsmanagersap-application-applicationtype
     */
    readonly applicationType: string;
    /**
     * This is an optional parameter for component details to which the SAP ABAP application is attached, such as Web Dispatcher.
     *
     * @see http://docs.aws.amazon.com/AWSCloudFormation/latest/UserGuide/aws-resource-systemsmanagersap-application.html#cfn-systemsmanagersap-application-componentsinfo
     */
    readonly componentsInfo?: Array<CfnApplication.ComponentInfoProperty | cdk.IResolvable> | cdk.IResolvable;
    /**
     * The credentials of the SAP application.
     *
     * @see http://docs.aws.amazon.com/AWSCloudFormation/latest/UserGuide/aws-resource-systemsmanagersap-application.html#cfn-systemsmanagersap-application-credentials
     */
    readonly credentials?: Array<CfnApplication.CredentialProperty | cdk.IResolvable> | cdk.IResolvable;
    /**
     * The Amazon Resource Name (ARN) of the database.
     *
     * @see http://docs.aws.amazon.com/AWSCloudFormation/latest/UserGuide/aws-resource-systemsmanagersap-application.html#cfn-systemsmanagersap-application-databasearn
     */
    readonly databaseArn?: string;
    /**
     * The Amazon EC2 instances on which your SAP application is running.
     *
     * @see http://docs.aws.amazon.com/AWSCloudFormation/latest/UserGuide/aws-resource-systemsmanagersap-application.html#cfn-systemsmanagersap-application-instances
     */
    readonly instances?: Array<string>;
    /**
     * The SAP instance number of the application.
     *
     * @see http://docs.aws.amazon.com/AWSCloudFormation/latest/UserGuide/aws-resource-systemsmanagersap-application.html#cfn-systemsmanagersap-application-sapinstancenumber
     */
    readonly sapInstanceNumber?: string;
    /**
     * The System ID of the application.
     *
     * @see http://docs.aws.amazon.com/AWSCloudFormation/latest/UserGuide/aws-resource-systemsmanagersap-application.html#cfn-systemsmanagersap-application-sid
     */
    readonly sid?: string;
    /**
     * The tags on the application.
     *
     * @see http://docs.aws.amazon.com/AWSCloudFormation/latest/UserGuide/aws-resource-systemsmanagersap-application.html#cfn-systemsmanagersap-application-tags
     */
    readonly tags?: Array<cdk.CfnTag>;
}
