import { Construct } from 'constructs';
import '../../assets';
import * as iam from '../../aws-iam';
import * as kms from '../../aws-kms';
import * as s3 from '../../aws-s3';
import * as cdk from '../../core';
export interface AssetOptions extends cdk.FileCopyOptions, cdk.AssetOptions {
    /**
     * A list of principals that should be able to read this asset from S3.
     * You can use `asset.grantRead(principal)` to grant read permissions later.
     *
     * @default - No principals that can read file asset.
     */
    readonly readers?: iam.IGrantable[];
    /**
     * Whether or not the asset needs to exist beyond deployment time; i.e.
     * are copied over to a different location and not needed afterwards.
     * Setting this property to true has an impact on the lifecycle of the asset,
     * because we will assume that it is safe to delete after the CloudFormation
     * deployment succeeds.
     *
     * For example, Lambda Function assets are copied over to Lambda during
     * deployment. Therefore, it is not necessary to store the asset in S3, so
     * we consider those deployTime assets.
     *
     * @default false
     */
    readonly deployTime?: boolean;
    /**
     * The ARN of the KMS key used to encrypt the handler code.
     * @default - the default server-side encryption with Amazon S3 managed keys(SSE-S3) key will be used.
     */
    readonly sourceKMSKey?: kms.IKey;
    /**
     * A display name for this asset
     *
     * If supplied, the display name will be used in locations where the asset
     * identifier is printed, like in the CLI progress information. If the same
     * asset is added multiple times, the display name of the first occurrence is
     * used.
     *
     * The default is the construct path of the Asset construct, with respect to
     * the enclosing stack. If the asset is produced by a construct helper
     * function (such as `lambda.Code.fromAsset()`), this will look like
     * `MyFunction/Code`.
     *
     * We use the stack-relative construct path so that in the common case where
     * you have multiple stacks with the same asset, we won't show something like
     * `/MyBetaStack/MyFunction/Code` when you are actually deploying to
     * production.
     *
     * @default - Stack-relative construct path
     */
    readonly displayName?: string;
}
export interface AssetProps extends AssetOptions {
    /**
     * The disk location of the asset.
     *
     * The path should refer to one of the following:
     * - A regular file or a .zip file, in which case the file will be uploaded as-is to S3.
     * - A directory, in which case it will be archived into a .zip file and uploaded to S3.
     */
    readonly path: string;
}
/**
 * An asset represents a local file or directory, which is automatically uploaded to S3
 * and then can be referenced within a CDK application.
 */
export declare class Asset extends Construct implements cdk.IAsset {
    /**
     * Attribute that represents the name of the bucket this asset exists in.
     */
    readonly s3BucketName: string;
    /**
     * Attribute which represents the S3 object key of this asset.
     */
    readonly s3ObjectKey: string;
    /**
     * Attribute which represents the S3 HTTP URL of this asset.
     * For example, `https://s3.us-west-1.amazonaws.com/bucket/key`
     */
    readonly httpUrl: string;
    /**
     * Attribute which represents the S3 URL of this asset.
     * For example, `s3://bucket/key`
     */
    readonly s3ObjectUrl: string;
    /**
     * The path to the asset, relative to the current Cloud Assembly
     *
     * If asset staging is disabled, this will just be the original path.
     * If asset staging is enabled it will be the staged path.
     */
    readonly assetPath: string;
    /**
     * The S3 bucket in which this asset resides.
     */
    readonly bucket: s3.IBucket;
    /**
     * Indicates if this asset is a single file. Allows constructs to ensure that the
     * correct file type was used.
     */
    readonly isFile: boolean;
    /**
     * Indicates if this asset is a zip archive. Allows constructs to ensure that the
     * correct file type was used.
     */
    readonly isZipArchive: boolean;
    readonly assetHash: string;
    /**
     * Indicates if this asset got bundled before staged, or not.
     */
    private readonly isBundled;
    constructor(scope: Construct, id: string, props: AssetProps);
    /**
     * Adds CloudFormation template metadata to the specified resource with
     * information that indicates which resource property is mapped to this local
     * asset. This can be used by tools such as SAM CLI to provide local
     * experience such as local invocation and debugging of Lambda functions.
     *
     * Asset metadata will only be included if the stack is synthesized with the
     * "aws:cdk:enable-asset-metadata" context key defined, which is the default
     * behavior when synthesizing via the CDK Toolkit.
     *
     * @see https://github.com/aws/aws-cdk/issues/1432
     *
     * @param resource The CloudFormation resource which is using this asset [disable-awslint:ref-via-interface]
     * @param resourceProperty The property name where this asset is referenced
     * (e.g. "Code" for AWS::Lambda::Function)
     */
    addResourceMetadata(resource: cdk.CfnResource, resourceProperty: string): void;
    /**
     * Grants read permissions to the principal on the assets bucket.
     */
    grantRead(grantee: iam.IGrantable): void;
}
