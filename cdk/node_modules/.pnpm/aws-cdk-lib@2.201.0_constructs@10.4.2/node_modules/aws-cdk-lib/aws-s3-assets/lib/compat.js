"use strict";Object.defineProperty(exports,"__esModule",{value:!0}),exports.toSymlinkFollow=toSymlinkFollow;var assets_1=()=>{var tmp=require("../../assets");return assets_1=()=>tmp,tmp},core_1=()=>{var tmp=require("../../core");return core_1=()=>tmp,tmp},errors_1=()=>{var tmp=require("../../core/lib/errors");return errors_1=()=>tmp,tmp};function toSymlinkFollow(follow){if(follow)switch(follow){case assets_1().FollowMode.NEVER:return core_1().SymlinkFollowMode.NEVER;case assets_1().FollowMode.ALWAYS:return core_1().SymlinkFollowMode.ALWAYS;case assets_1().FollowMode.BLOCK_EXTERNAL:return core_1().SymlinkFollowMode.BLOCK_EXTERNAL;case assets_1().FollowMode.EXTERNAL:return core_1().SymlinkFollowMode.EXTERNAL;default:throw new(errors_1()).UnscopedValidationError(`unknown follow mode: ${follow}`)}}
