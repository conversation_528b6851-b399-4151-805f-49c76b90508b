"use strict";var _a,_b,_c;Object.defineProperty(exports,"__esModule",{value:!0}),exports.CfnTheme=exports.CfnForm=exports.CfnComponent=void 0;var jsiiDeprecationWarnings=()=>{var tmp=require("../../.warnings.jsii.js");return jsiiDeprecationWarnings=()=>tmp,tmp};const JSII_RTTI_SYMBOL_1=Symbol.for("jsii.rtti");var cdk=()=>{var tmp=require("../../core");return cdk=()=>tmp,tmp},cfn_parse=()=>{var tmp=require("../../core/lib/helpers-internal");return cfn_parse=()=>tmp,tmp},cdk_errors=()=>{var tmp=require("../../core/lib/errors");return cdk_errors=()=>tmp,tmp};class CfnComponent extends cdk().CfnResource{static _fromCloudFormation(scope,id,resourceAttributes,options){resourceAttributes=resourceAttributes||{};const resourceProperties=options.parser.parseValue(resourceAttributes.Properties),propsResult=CfnComponentPropsFromCloudFormation(resourceProperties);if(cdk().isResolvableObject(propsResult.value))throw new(cdk_errors()).ValidationError("Unexpected IResolvable",scope);const ret=new CfnComponent(scope,id,propsResult.value);for(const[propKey,propVal]of Object.entries(propsResult.extraProperties))ret.addPropertyOverride(propKey,propVal);return options.parser.handleAttributes(ret,resourceAttributes,id),ret}constructor(scope,id,props={}){super(scope,id,{type:CfnComponent.CFN_RESOURCE_TYPE_NAME,properties:props});try{jsiiDeprecationWarnings().aws_cdk_lib_aws_amplifyuibuilder_CfnComponentProps(props)}catch(error){throw process.env.JSII_DEBUG!=="1"&&error.name==="DeprecationError"&&Error.captureStackTrace(error,CfnComponent),error}this.attrCreatedAt=cdk().Token.asString(this.getAtt("CreatedAt",cdk().ResolutionTypeHint.STRING)),this.attrId=cdk().Token.asString(this.getAtt("Id",cdk().ResolutionTypeHint.STRING)),this.attrModifiedAt=cdk().Token.asString(this.getAtt("ModifiedAt",cdk().ResolutionTypeHint.STRING)),this.appId=props.appId,this.bindingProperties=props.bindingProperties,this.children=props.children,this.collectionProperties=props.collectionProperties,this.componentType=props.componentType,this.environmentName=props.environmentName,this.events=props.events,this.name=props.name,this.overrides=props.overrides,this.properties=props.properties,this.schemaVersion=props.schemaVersion,this.sourceId=props.sourceId,this.tags=new(cdk()).TagManager(cdk().TagType.MAP,"AWS::AmplifyUIBuilder::Component",props.tags,{tagPropertyName:"tags"}),this.tagsRaw=props.tags,this.variants=props.variants}get cfnProperties(){return{appId:this.appId,bindingProperties:this.bindingProperties,children:this.children,collectionProperties:this.collectionProperties,componentType:this.componentType,environmentName:this.environmentName,events:this.events,name:this.name,overrides:this.overrides,properties:this.properties,schemaVersion:this.schemaVersion,sourceId:this.sourceId,tags:this.tags.renderTags(),variants:this.variants}}inspect(inspector){try{jsiiDeprecationWarnings().aws_cdk_lib_TreeInspector(inspector)}catch(error){throw process.env.JSII_DEBUG!=="1"&&error.name==="DeprecationError"&&Error.captureStackTrace(error,this.inspect),error}inspector.addAttribute("aws:cdk:cloudformation:type",CfnComponent.CFN_RESOURCE_TYPE_NAME),inspector.addAttribute("aws:cdk:cloudformation:props",this.cfnProperties)}renderProperties(props){return convertCfnComponentPropsToCloudFormation(props)}}exports.CfnComponent=CfnComponent,_a=JSII_RTTI_SYMBOL_1,CfnComponent[_a]={fqn:"aws-cdk-lib.aws_amplifyuibuilder.CfnComponent",version:"2.201.0"},CfnComponent.CFN_RESOURCE_TYPE_NAME="AWS::AmplifyUIBuilder::Component";function CfnComponentPredicatePropertyValidator(properties){if(!cdk().canInspect(properties))return cdk().VALIDATION_SUCCESS;const errors=new(cdk()).ValidationResults;return properties&&typeof properties=="object"&&!Array.isArray(properties)||errors.collect(new(cdk()).ValidationResult("Expected an object, but received: "+JSON.stringify(properties))),errors.collect(cdk().propertyValidator("and",cdk().listValidator(CfnComponentPredicatePropertyValidator))(properties.and)),errors.collect(cdk().propertyValidator("field",cdk().validateString)(properties.field)),errors.collect(cdk().propertyValidator("operand",cdk().validateString)(properties.operand)),errors.collect(cdk().propertyValidator("operandType",cdk().validateString)(properties.operandType)),errors.collect(cdk().propertyValidator("operator",cdk().validateString)(properties.operator)),errors.collect(cdk().propertyValidator("or",cdk().listValidator(CfnComponentPredicatePropertyValidator))(properties.or)),errors.wrap('supplied properties not correct for "PredicateProperty"')}function convertCfnComponentPredicatePropertyToCloudFormation(properties){return cdk().canInspect(properties)?(CfnComponentPredicatePropertyValidator(properties).assertSuccess(),{And:cdk().listMapper(convertCfnComponentPredicatePropertyToCloudFormation)(properties.and),Field:cdk().stringToCloudFormation(properties.field),Operand:cdk().stringToCloudFormation(properties.operand),OperandType:cdk().stringToCloudFormation(properties.operandType),Operator:cdk().stringToCloudFormation(properties.operator),Or:cdk().listMapper(convertCfnComponentPredicatePropertyToCloudFormation)(properties.or)}):properties}function CfnComponentPredicatePropertyFromCloudFormation(properties){if(cdk().isResolvableObject(properties))return new(cfn_parse()).FromCloudFormationResult(properties);if(properties=properties??{},!(properties&&typeof properties=="object"&&!Array.isArray(properties)))return new(cfn_parse()).FromCloudFormationResult(properties);const ret=new(cfn_parse()).FromCloudFormationPropertyObject;return ret.addPropertyResult("and","And",properties.And!=null?cfn_parse().FromCloudFormation.getArray(CfnComponentPredicatePropertyFromCloudFormation)(properties.And):void 0),ret.addPropertyResult("field","Field",properties.Field!=null?cfn_parse().FromCloudFormation.getString(properties.Field):void 0),ret.addPropertyResult("operand","Operand",properties.Operand!=null?cfn_parse().FromCloudFormation.getString(properties.Operand):void 0),ret.addPropertyResult("operandType","OperandType",properties.OperandType!=null?cfn_parse().FromCloudFormation.getString(properties.OperandType):void 0),ret.addPropertyResult("operator","Operator",properties.Operator!=null?cfn_parse().FromCloudFormation.getString(properties.Operator):void 0),ret.addPropertyResult("or","Or",properties.Or!=null?cfn_parse().FromCloudFormation.getArray(CfnComponentPredicatePropertyFromCloudFormation)(properties.Or):void 0),ret.addUnrecognizedPropertiesAsExtra(properties),ret}function CfnComponentComponentBindingPropertiesValuePropertiesPropertyValidator(properties){if(!cdk().canInspect(properties))return cdk().VALIDATION_SUCCESS;const errors=new(cdk()).ValidationResults;return properties&&typeof properties=="object"&&!Array.isArray(properties)||errors.collect(new(cdk()).ValidationResult("Expected an object, but received: "+JSON.stringify(properties))),errors.collect(cdk().propertyValidator("bucket",cdk().validateString)(properties.bucket)),errors.collect(cdk().propertyValidator("defaultValue",cdk().validateString)(properties.defaultValue)),errors.collect(cdk().propertyValidator("field",cdk().validateString)(properties.field)),errors.collect(cdk().propertyValidator("key",cdk().validateString)(properties.key)),errors.collect(cdk().propertyValidator("model",cdk().validateString)(properties.model)),errors.collect(cdk().propertyValidator("predicates",cdk().listValidator(CfnComponentPredicatePropertyValidator))(properties.predicates)),errors.collect(cdk().propertyValidator("slotName",cdk().validateString)(properties.slotName)),errors.collect(cdk().propertyValidator("userAttribute",cdk().validateString)(properties.userAttribute)),errors.wrap('supplied properties not correct for "ComponentBindingPropertiesValuePropertiesProperty"')}function convertCfnComponentComponentBindingPropertiesValuePropertiesPropertyToCloudFormation(properties){return cdk().canInspect(properties)?(CfnComponentComponentBindingPropertiesValuePropertiesPropertyValidator(properties).assertSuccess(),{Bucket:cdk().stringToCloudFormation(properties.bucket),DefaultValue:cdk().stringToCloudFormation(properties.defaultValue),Field:cdk().stringToCloudFormation(properties.field),Key:cdk().stringToCloudFormation(properties.key),Model:cdk().stringToCloudFormation(properties.model),Predicates:cdk().listMapper(convertCfnComponentPredicatePropertyToCloudFormation)(properties.predicates),SlotName:cdk().stringToCloudFormation(properties.slotName),UserAttribute:cdk().stringToCloudFormation(properties.userAttribute)}):properties}function CfnComponentComponentBindingPropertiesValuePropertiesPropertyFromCloudFormation(properties){if(cdk().isResolvableObject(properties))return new(cfn_parse()).FromCloudFormationResult(properties);if(properties=properties??{},!(properties&&typeof properties=="object"&&!Array.isArray(properties)))return new(cfn_parse()).FromCloudFormationResult(properties);const ret=new(cfn_parse()).FromCloudFormationPropertyObject;return ret.addPropertyResult("bucket","Bucket",properties.Bucket!=null?cfn_parse().FromCloudFormation.getString(properties.Bucket):void 0),ret.addPropertyResult("defaultValue","DefaultValue",properties.DefaultValue!=null?cfn_parse().FromCloudFormation.getString(properties.DefaultValue):void 0),ret.addPropertyResult("field","Field",properties.Field!=null?cfn_parse().FromCloudFormation.getString(properties.Field):void 0),ret.addPropertyResult("key","Key",properties.Key!=null?cfn_parse().FromCloudFormation.getString(properties.Key):void 0),ret.addPropertyResult("model","Model",properties.Model!=null?cfn_parse().FromCloudFormation.getString(properties.Model):void 0),ret.addPropertyResult("predicates","Predicates",properties.Predicates!=null?cfn_parse().FromCloudFormation.getArray(CfnComponentPredicatePropertyFromCloudFormation)(properties.Predicates):void 0),ret.addPropertyResult("slotName","SlotName",properties.SlotName!=null?cfn_parse().FromCloudFormation.getString(properties.SlotName):void 0),ret.addPropertyResult("userAttribute","UserAttribute",properties.UserAttribute!=null?cfn_parse().FromCloudFormation.getString(properties.UserAttribute):void 0),ret.addUnrecognizedPropertiesAsExtra(properties),ret}function CfnComponentComponentBindingPropertiesValuePropertyValidator(properties){if(!cdk().canInspect(properties))return cdk().VALIDATION_SUCCESS;const errors=new(cdk()).ValidationResults;return properties&&typeof properties=="object"&&!Array.isArray(properties)||errors.collect(new(cdk()).ValidationResult("Expected an object, but received: "+JSON.stringify(properties))),errors.collect(cdk().propertyValidator("bindingProperties",CfnComponentComponentBindingPropertiesValuePropertiesPropertyValidator)(properties.bindingProperties)),errors.collect(cdk().propertyValidator("defaultValue",cdk().validateString)(properties.defaultValue)),errors.collect(cdk().propertyValidator("type",cdk().validateString)(properties.type)),errors.wrap('supplied properties not correct for "ComponentBindingPropertiesValueProperty"')}function convertCfnComponentComponentBindingPropertiesValuePropertyToCloudFormation(properties){return cdk().canInspect(properties)?(CfnComponentComponentBindingPropertiesValuePropertyValidator(properties).assertSuccess(),{BindingProperties:convertCfnComponentComponentBindingPropertiesValuePropertiesPropertyToCloudFormation(properties.bindingProperties),DefaultValue:cdk().stringToCloudFormation(properties.defaultValue),Type:cdk().stringToCloudFormation(properties.type)}):properties}function CfnComponentComponentBindingPropertiesValuePropertyFromCloudFormation(properties){if(cdk().isResolvableObject(properties))return new(cfn_parse()).FromCloudFormationResult(properties);if(properties=properties??{},!(properties&&typeof properties=="object"&&!Array.isArray(properties)))return new(cfn_parse()).FromCloudFormationResult(properties);const ret=new(cfn_parse()).FromCloudFormationPropertyObject;return ret.addPropertyResult("bindingProperties","BindingProperties",properties.BindingProperties!=null?CfnComponentComponentBindingPropertiesValuePropertiesPropertyFromCloudFormation(properties.BindingProperties):void 0),ret.addPropertyResult("defaultValue","DefaultValue",properties.DefaultValue!=null?cfn_parse().FromCloudFormation.getString(properties.DefaultValue):void 0),ret.addPropertyResult("type","Type",properties.Type!=null?cfn_parse().FromCloudFormation.getString(properties.Type):void 0),ret.addUnrecognizedPropertiesAsExtra(properties),ret}function CfnComponentComponentConditionPropertyPropertyValidator(properties){if(!cdk().canInspect(properties))return cdk().VALIDATION_SUCCESS;const errors=new(cdk()).ValidationResults;return properties&&typeof properties=="object"&&!Array.isArray(properties)||errors.collect(new(cdk()).ValidationResult("Expected an object, but received: "+JSON.stringify(properties))),errors.collect(cdk().propertyValidator("else",CfnComponentComponentPropertyPropertyValidator)(properties.else)),errors.collect(cdk().propertyValidator("field",cdk().validateString)(properties.field)),errors.collect(cdk().propertyValidator("operand",cdk().validateString)(properties.operand)),errors.collect(cdk().propertyValidator("operandType",cdk().validateString)(properties.operandType)),errors.collect(cdk().propertyValidator("operator",cdk().validateString)(properties.operator)),errors.collect(cdk().propertyValidator("property",cdk().validateString)(properties.property)),errors.collect(cdk().propertyValidator("then",CfnComponentComponentPropertyPropertyValidator)(properties.then)),errors.wrap('supplied properties not correct for "ComponentConditionPropertyProperty"')}function convertCfnComponentComponentConditionPropertyPropertyToCloudFormation(properties){return cdk().canInspect(properties)?(CfnComponentComponentConditionPropertyPropertyValidator(properties).assertSuccess(),{Else:convertCfnComponentComponentPropertyPropertyToCloudFormation(properties.else),Field:cdk().stringToCloudFormation(properties.field),Operand:cdk().stringToCloudFormation(properties.operand),OperandType:cdk().stringToCloudFormation(properties.operandType),Operator:cdk().stringToCloudFormation(properties.operator),Property:cdk().stringToCloudFormation(properties.property),Then:convertCfnComponentComponentPropertyPropertyToCloudFormation(properties.then)}):properties}function CfnComponentComponentConditionPropertyPropertyFromCloudFormation(properties){if(cdk().isResolvableObject(properties))return new(cfn_parse()).FromCloudFormationResult(properties);if(properties=properties??{},!(properties&&typeof properties=="object"&&!Array.isArray(properties)))return new(cfn_parse()).FromCloudFormationResult(properties);const ret=new(cfn_parse()).FromCloudFormationPropertyObject;return ret.addPropertyResult("else","Else",properties.Else!=null?CfnComponentComponentPropertyPropertyFromCloudFormation(properties.Else):void 0),ret.addPropertyResult("field","Field",properties.Field!=null?cfn_parse().FromCloudFormation.getString(properties.Field):void 0),ret.addPropertyResult("operand","Operand",properties.Operand!=null?cfn_parse().FromCloudFormation.getString(properties.Operand):void 0),ret.addPropertyResult("operandType","OperandType",properties.OperandType!=null?cfn_parse().FromCloudFormation.getString(properties.OperandType):void 0),ret.addPropertyResult("operator","Operator",properties.Operator!=null?cfn_parse().FromCloudFormation.getString(properties.Operator):void 0),ret.addPropertyResult("property","Property",properties.Property!=null?cfn_parse().FromCloudFormation.getString(properties.Property):void 0),ret.addPropertyResult("then","Then",properties.Then!=null?CfnComponentComponentPropertyPropertyFromCloudFormation(properties.Then):void 0),ret.addUnrecognizedPropertiesAsExtra(properties),ret}function CfnComponentComponentPropertyBindingPropertiesPropertyValidator(properties){if(!cdk().canInspect(properties))return cdk().VALIDATION_SUCCESS;const errors=new(cdk()).ValidationResults;return properties&&typeof properties=="object"&&!Array.isArray(properties)||errors.collect(new(cdk()).ValidationResult("Expected an object, but received: "+JSON.stringify(properties))),errors.collect(cdk().propertyValidator("field",cdk().validateString)(properties.field)),errors.collect(cdk().propertyValidator("property",cdk().requiredValidator)(properties.property)),errors.collect(cdk().propertyValidator("property",cdk().validateString)(properties.property)),errors.wrap('supplied properties not correct for "ComponentPropertyBindingPropertiesProperty"')}function convertCfnComponentComponentPropertyBindingPropertiesPropertyToCloudFormation(properties){return cdk().canInspect(properties)?(CfnComponentComponentPropertyBindingPropertiesPropertyValidator(properties).assertSuccess(),{Field:cdk().stringToCloudFormation(properties.field),Property:cdk().stringToCloudFormation(properties.property)}):properties}function CfnComponentComponentPropertyBindingPropertiesPropertyFromCloudFormation(properties){if(cdk().isResolvableObject(properties))return new(cfn_parse()).FromCloudFormationResult(properties);if(properties=properties??{},!(properties&&typeof properties=="object"&&!Array.isArray(properties)))return new(cfn_parse()).FromCloudFormationResult(properties);const ret=new(cfn_parse()).FromCloudFormationPropertyObject;return ret.addPropertyResult("field","Field",properties.Field!=null?cfn_parse().FromCloudFormation.getString(properties.Field):void 0),ret.addPropertyResult("property","Property",properties.Property!=null?cfn_parse().FromCloudFormation.getString(properties.Property):void 0),ret.addUnrecognizedPropertiesAsExtra(properties),ret}function CfnComponentFormBindingElementPropertyValidator(properties){if(!cdk().canInspect(properties))return cdk().VALIDATION_SUCCESS;const errors=new(cdk()).ValidationResults;return properties&&typeof properties=="object"&&!Array.isArray(properties)||errors.collect(new(cdk()).ValidationResult("Expected an object, but received: "+JSON.stringify(properties))),errors.collect(cdk().propertyValidator("element",cdk().requiredValidator)(properties.element)),errors.collect(cdk().propertyValidator("element",cdk().validateString)(properties.element)),errors.collect(cdk().propertyValidator("property",cdk().requiredValidator)(properties.property)),errors.collect(cdk().propertyValidator("property",cdk().validateString)(properties.property)),errors.wrap('supplied properties not correct for "FormBindingElementProperty"')}function convertCfnComponentFormBindingElementPropertyToCloudFormation(properties){return cdk().canInspect(properties)?(CfnComponentFormBindingElementPropertyValidator(properties).assertSuccess(),{Element:cdk().stringToCloudFormation(properties.element),Property:cdk().stringToCloudFormation(properties.property)}):properties}function CfnComponentFormBindingElementPropertyFromCloudFormation(properties){if(cdk().isResolvableObject(properties))return new(cfn_parse()).FromCloudFormationResult(properties);if(properties=properties??{},!(properties&&typeof properties=="object"&&!Array.isArray(properties)))return new(cfn_parse()).FromCloudFormationResult(properties);const ret=new(cfn_parse()).FromCloudFormationPropertyObject;return ret.addPropertyResult("element","Element",properties.Element!=null?cfn_parse().FromCloudFormation.getString(properties.Element):void 0),ret.addPropertyResult("property","Property",properties.Property!=null?cfn_parse().FromCloudFormation.getString(properties.Property):void 0),ret.addUnrecognizedPropertiesAsExtra(properties),ret}function CfnComponentComponentPropertyPropertyValidator(properties){if(!cdk().canInspect(properties))return cdk().VALIDATION_SUCCESS;const errors=new(cdk()).ValidationResults;return properties&&typeof properties=="object"&&!Array.isArray(properties)||errors.collect(new(cdk()).ValidationResult("Expected an object, but received: "+JSON.stringify(properties))),errors.collect(cdk().propertyValidator("bindingProperties",CfnComponentComponentPropertyBindingPropertiesPropertyValidator)(properties.bindingProperties)),errors.collect(cdk().propertyValidator("bindings",cdk().hashValidator(CfnComponentFormBindingElementPropertyValidator))(properties.bindings)),errors.collect(cdk().propertyValidator("collectionBindingProperties",CfnComponentComponentPropertyBindingPropertiesPropertyValidator)(properties.collectionBindingProperties)),errors.collect(cdk().propertyValidator("componentName",cdk().validateString)(properties.componentName)),errors.collect(cdk().propertyValidator("concat",cdk().listValidator(CfnComponentComponentPropertyPropertyValidator))(properties.concat)),errors.collect(cdk().propertyValidator("condition",CfnComponentComponentConditionPropertyPropertyValidator)(properties.condition)),errors.collect(cdk().propertyValidator("configured",cdk().validateBoolean)(properties.configured)),errors.collect(cdk().propertyValidator("defaultValue",cdk().validateString)(properties.defaultValue)),errors.collect(cdk().propertyValidator("event",cdk().validateString)(properties.event)),errors.collect(cdk().propertyValidator("importedValue",cdk().validateString)(properties.importedValue)),errors.collect(cdk().propertyValidator("model",cdk().validateString)(properties.model)),errors.collect(cdk().propertyValidator("property",cdk().validateString)(properties.property)),errors.collect(cdk().propertyValidator("type",cdk().validateString)(properties.type)),errors.collect(cdk().propertyValidator("userAttribute",cdk().validateString)(properties.userAttribute)),errors.collect(cdk().propertyValidator("value",cdk().validateString)(properties.value)),errors.wrap('supplied properties not correct for "ComponentPropertyProperty"')}function convertCfnComponentComponentPropertyPropertyToCloudFormation(properties){return cdk().canInspect(properties)?(CfnComponentComponentPropertyPropertyValidator(properties).assertSuccess(),{BindingProperties:convertCfnComponentComponentPropertyBindingPropertiesPropertyToCloudFormation(properties.bindingProperties),Bindings:cdk().hashMapper(convertCfnComponentFormBindingElementPropertyToCloudFormation)(properties.bindings),CollectionBindingProperties:convertCfnComponentComponentPropertyBindingPropertiesPropertyToCloudFormation(properties.collectionBindingProperties),ComponentName:cdk().stringToCloudFormation(properties.componentName),Concat:cdk().listMapper(convertCfnComponentComponentPropertyPropertyToCloudFormation)(properties.concat),Condition:convertCfnComponentComponentConditionPropertyPropertyToCloudFormation(properties.condition),Configured:cdk().booleanToCloudFormation(properties.configured),DefaultValue:cdk().stringToCloudFormation(properties.defaultValue),Event:cdk().stringToCloudFormation(properties.event),ImportedValue:cdk().stringToCloudFormation(properties.importedValue),Model:cdk().stringToCloudFormation(properties.model),Property:cdk().stringToCloudFormation(properties.property),Type:cdk().stringToCloudFormation(properties.type),UserAttribute:cdk().stringToCloudFormation(properties.userAttribute),Value:cdk().stringToCloudFormation(properties.value)}):properties}function CfnComponentComponentPropertyPropertyFromCloudFormation(properties){if(cdk().isResolvableObject(properties))return new(cfn_parse()).FromCloudFormationResult(properties);if(properties=properties??{},!(properties&&typeof properties=="object"&&!Array.isArray(properties)))return new(cfn_parse()).FromCloudFormationResult(properties);const ret=new(cfn_parse()).FromCloudFormationPropertyObject;return ret.addPropertyResult("bindingProperties","BindingProperties",properties.BindingProperties!=null?CfnComponentComponentPropertyBindingPropertiesPropertyFromCloudFormation(properties.BindingProperties):void 0),ret.addPropertyResult("bindings","Bindings",properties.Bindings!=null?cfn_parse().FromCloudFormation.getMap(CfnComponentFormBindingElementPropertyFromCloudFormation)(properties.Bindings):void 0),ret.addPropertyResult("collectionBindingProperties","CollectionBindingProperties",properties.CollectionBindingProperties!=null?CfnComponentComponentPropertyBindingPropertiesPropertyFromCloudFormation(properties.CollectionBindingProperties):void 0),ret.addPropertyResult("componentName","ComponentName",properties.ComponentName!=null?cfn_parse().FromCloudFormation.getString(properties.ComponentName):void 0),ret.addPropertyResult("concat","Concat",properties.Concat!=null?cfn_parse().FromCloudFormation.getArray(CfnComponentComponentPropertyPropertyFromCloudFormation)(properties.Concat):void 0),ret.addPropertyResult("condition","Condition",properties.Condition!=null?CfnComponentComponentConditionPropertyPropertyFromCloudFormation(properties.Condition):void 0),ret.addPropertyResult("configured","Configured",properties.Configured!=null?cfn_parse().FromCloudFormation.getBoolean(properties.Configured):void 0),ret.addPropertyResult("defaultValue","DefaultValue",properties.DefaultValue!=null?cfn_parse().FromCloudFormation.getString(properties.DefaultValue):void 0),ret.addPropertyResult("event","Event",properties.Event!=null?cfn_parse().FromCloudFormation.getString(properties.Event):void 0),ret.addPropertyResult("importedValue","ImportedValue",properties.ImportedValue!=null?cfn_parse().FromCloudFormation.getString(properties.ImportedValue):void 0),ret.addPropertyResult("model","Model",properties.Model!=null?cfn_parse().FromCloudFormation.getString(properties.Model):void 0),ret.addPropertyResult("property","Property",properties.Property!=null?cfn_parse().FromCloudFormation.getString(properties.Property):void 0),ret.addPropertyResult("type","Type",properties.Type!=null?cfn_parse().FromCloudFormation.getString(properties.Type):void 0),ret.addPropertyResult("userAttribute","UserAttribute",properties.UserAttribute!=null?cfn_parse().FromCloudFormation.getString(properties.UserAttribute):void 0),ret.addPropertyResult("value","Value",properties.Value!=null?cfn_parse().FromCloudFormation.getString(properties.Value):void 0),ret.addUnrecognizedPropertiesAsExtra(properties),ret}function CfnComponentSortPropertyPropertyValidator(properties){if(!cdk().canInspect(properties))return cdk().VALIDATION_SUCCESS;const errors=new(cdk()).ValidationResults;return properties&&typeof properties=="object"&&!Array.isArray(properties)||errors.collect(new(cdk()).ValidationResult("Expected an object, but received: "+JSON.stringify(properties))),errors.collect(cdk().propertyValidator("direction",cdk().requiredValidator)(properties.direction)),errors.collect(cdk().propertyValidator("direction",cdk().validateString)(properties.direction)),errors.collect(cdk().propertyValidator("field",cdk().requiredValidator)(properties.field)),errors.collect(cdk().propertyValidator("field",cdk().validateString)(properties.field)),errors.wrap('supplied properties not correct for "SortPropertyProperty"')}function convertCfnComponentSortPropertyPropertyToCloudFormation(properties){return cdk().canInspect(properties)?(CfnComponentSortPropertyPropertyValidator(properties).assertSuccess(),{Direction:cdk().stringToCloudFormation(properties.direction),Field:cdk().stringToCloudFormation(properties.field)}):properties}function CfnComponentSortPropertyPropertyFromCloudFormation(properties){if(cdk().isResolvableObject(properties))return new(cfn_parse()).FromCloudFormationResult(properties);if(properties=properties??{},!(properties&&typeof properties=="object"&&!Array.isArray(properties)))return new(cfn_parse()).FromCloudFormationResult(properties);const ret=new(cfn_parse()).FromCloudFormationPropertyObject;return ret.addPropertyResult("direction","Direction",properties.Direction!=null?cfn_parse().FromCloudFormation.getString(properties.Direction):void 0),ret.addPropertyResult("field","Field",properties.Field!=null?cfn_parse().FromCloudFormation.getString(properties.Field):void 0),ret.addUnrecognizedPropertiesAsExtra(properties),ret}function CfnComponentComponentDataConfigurationPropertyValidator(properties){if(!cdk().canInspect(properties))return cdk().VALIDATION_SUCCESS;const errors=new(cdk()).ValidationResults;return properties&&typeof properties=="object"&&!Array.isArray(properties)||errors.collect(new(cdk()).ValidationResult("Expected an object, but received: "+JSON.stringify(properties))),errors.collect(cdk().propertyValidator("identifiers",cdk().listValidator(cdk().validateString))(properties.identifiers)),errors.collect(cdk().propertyValidator("model",cdk().requiredValidator)(properties.model)),errors.collect(cdk().propertyValidator("model",cdk().validateString)(properties.model)),errors.collect(cdk().propertyValidator("predicate",CfnComponentPredicatePropertyValidator)(properties.predicate)),errors.collect(cdk().propertyValidator("sort",cdk().listValidator(CfnComponentSortPropertyPropertyValidator))(properties.sort)),errors.wrap('supplied properties not correct for "ComponentDataConfigurationProperty"')}function convertCfnComponentComponentDataConfigurationPropertyToCloudFormation(properties){return cdk().canInspect(properties)?(CfnComponentComponentDataConfigurationPropertyValidator(properties).assertSuccess(),{Identifiers:cdk().listMapper(cdk().stringToCloudFormation)(properties.identifiers),Model:cdk().stringToCloudFormation(properties.model),Predicate:convertCfnComponentPredicatePropertyToCloudFormation(properties.predicate),Sort:cdk().listMapper(convertCfnComponentSortPropertyPropertyToCloudFormation)(properties.sort)}):properties}function CfnComponentComponentDataConfigurationPropertyFromCloudFormation(properties){if(cdk().isResolvableObject(properties))return new(cfn_parse()).FromCloudFormationResult(properties);if(properties=properties??{},!(properties&&typeof properties=="object"&&!Array.isArray(properties)))return new(cfn_parse()).FromCloudFormationResult(properties);const ret=new(cfn_parse()).FromCloudFormationPropertyObject;return ret.addPropertyResult("identifiers","Identifiers",properties.Identifiers!=null?cfn_parse().FromCloudFormation.getArray(cfn_parse().FromCloudFormation.getString)(properties.Identifiers):void 0),ret.addPropertyResult("model","Model",properties.Model!=null?cfn_parse().FromCloudFormation.getString(properties.Model):void 0),ret.addPropertyResult("predicate","Predicate",properties.Predicate!=null?CfnComponentPredicatePropertyFromCloudFormation(properties.Predicate):void 0),ret.addPropertyResult("sort","Sort",properties.Sort!=null?cfn_parse().FromCloudFormation.getArray(CfnComponentSortPropertyPropertyFromCloudFormation)(properties.Sort):void 0),ret.addUnrecognizedPropertiesAsExtra(properties),ret}function CfnComponentComponentVariantPropertyValidator(properties){if(!cdk().canInspect(properties))return cdk().VALIDATION_SUCCESS;const errors=new(cdk()).ValidationResults;return properties&&typeof properties=="object"&&!Array.isArray(properties)||errors.collect(new(cdk()).ValidationResult("Expected an object, but received: "+JSON.stringify(properties))),errors.collect(cdk().propertyValidator("overrides",cdk().validateObject)(properties.overrides)),errors.collect(cdk().propertyValidator("variantValues",cdk().hashValidator(cdk().validateString))(properties.variantValues)),errors.wrap('supplied properties not correct for "ComponentVariantProperty"')}function convertCfnComponentComponentVariantPropertyToCloudFormation(properties){return cdk().canInspect(properties)?(CfnComponentComponentVariantPropertyValidator(properties).assertSuccess(),{Overrides:cdk().objectToCloudFormation(properties.overrides),VariantValues:cdk().hashMapper(cdk().stringToCloudFormation)(properties.variantValues)}):properties}function CfnComponentComponentVariantPropertyFromCloudFormation(properties){if(cdk().isResolvableObject(properties))return new(cfn_parse()).FromCloudFormationResult(properties);if(properties=properties??{},!(properties&&typeof properties=="object"&&!Array.isArray(properties)))return new(cfn_parse()).FromCloudFormationResult(properties);const ret=new(cfn_parse()).FromCloudFormationPropertyObject;return ret.addPropertyResult("overrides","Overrides",properties.Overrides!=null?cfn_parse().FromCloudFormation.getAny(properties.Overrides):void 0),ret.addPropertyResult("variantValues","VariantValues",properties.VariantValues!=null?cfn_parse().FromCloudFormation.getMap(cfn_parse().FromCloudFormation.getString)(properties.VariantValues):void 0),ret.addUnrecognizedPropertiesAsExtra(properties),ret}function CfnComponentMutationActionSetStateParameterPropertyValidator(properties){if(!cdk().canInspect(properties))return cdk().VALIDATION_SUCCESS;const errors=new(cdk()).ValidationResults;return properties&&typeof properties=="object"&&!Array.isArray(properties)||errors.collect(new(cdk()).ValidationResult("Expected an object, but received: "+JSON.stringify(properties))),errors.collect(cdk().propertyValidator("componentName",cdk().requiredValidator)(properties.componentName)),errors.collect(cdk().propertyValidator("componentName",cdk().validateString)(properties.componentName)),errors.collect(cdk().propertyValidator("property",cdk().requiredValidator)(properties.property)),errors.collect(cdk().propertyValidator("property",cdk().validateString)(properties.property)),errors.collect(cdk().propertyValidator("set",cdk().requiredValidator)(properties.set)),errors.collect(cdk().propertyValidator("set",CfnComponentComponentPropertyPropertyValidator)(properties.set)),errors.wrap('supplied properties not correct for "MutationActionSetStateParameterProperty"')}function convertCfnComponentMutationActionSetStateParameterPropertyToCloudFormation(properties){return cdk().canInspect(properties)?(CfnComponentMutationActionSetStateParameterPropertyValidator(properties).assertSuccess(),{ComponentName:cdk().stringToCloudFormation(properties.componentName),Property:cdk().stringToCloudFormation(properties.property),Set:convertCfnComponentComponentPropertyPropertyToCloudFormation(properties.set)}):properties}function CfnComponentMutationActionSetStateParameterPropertyFromCloudFormation(properties){if(cdk().isResolvableObject(properties))return new(cfn_parse()).FromCloudFormationResult(properties);if(properties=properties??{},!(properties&&typeof properties=="object"&&!Array.isArray(properties)))return new(cfn_parse()).FromCloudFormationResult(properties);const ret=new(cfn_parse()).FromCloudFormationPropertyObject;return ret.addPropertyResult("componentName","ComponentName",properties.ComponentName!=null?cfn_parse().FromCloudFormation.getString(properties.ComponentName):void 0),ret.addPropertyResult("property","Property",properties.Property!=null?cfn_parse().FromCloudFormation.getString(properties.Property):void 0),ret.addPropertyResult("set","Set",properties.Set!=null?CfnComponentComponentPropertyPropertyFromCloudFormation(properties.Set):void 0),ret.addUnrecognizedPropertiesAsExtra(properties),ret}function CfnComponentActionParametersPropertyValidator(properties){if(!cdk().canInspect(properties))return cdk().VALIDATION_SUCCESS;const errors=new(cdk()).ValidationResults;return properties&&typeof properties=="object"&&!Array.isArray(properties)||errors.collect(new(cdk()).ValidationResult("Expected an object, but received: "+JSON.stringify(properties))),errors.collect(cdk().propertyValidator("anchor",CfnComponentComponentPropertyPropertyValidator)(properties.anchor)),errors.collect(cdk().propertyValidator("fields",cdk().hashValidator(CfnComponentComponentPropertyPropertyValidator))(properties.fields)),errors.collect(cdk().propertyValidator("global",CfnComponentComponentPropertyPropertyValidator)(properties.global)),errors.collect(cdk().propertyValidator("id",CfnComponentComponentPropertyPropertyValidator)(properties.id)),errors.collect(cdk().propertyValidator("model",cdk().validateString)(properties.model)),errors.collect(cdk().propertyValidator("state",CfnComponentMutationActionSetStateParameterPropertyValidator)(properties.state)),errors.collect(cdk().propertyValidator("target",CfnComponentComponentPropertyPropertyValidator)(properties.target)),errors.collect(cdk().propertyValidator("type",CfnComponentComponentPropertyPropertyValidator)(properties.type)),errors.collect(cdk().propertyValidator("url",CfnComponentComponentPropertyPropertyValidator)(properties.url)),errors.wrap('supplied properties not correct for "ActionParametersProperty"')}function convertCfnComponentActionParametersPropertyToCloudFormation(properties){return cdk().canInspect(properties)?(CfnComponentActionParametersPropertyValidator(properties).assertSuccess(),{Anchor:convertCfnComponentComponentPropertyPropertyToCloudFormation(properties.anchor),Fields:cdk().hashMapper(convertCfnComponentComponentPropertyPropertyToCloudFormation)(properties.fields),Global:convertCfnComponentComponentPropertyPropertyToCloudFormation(properties.global),Id:convertCfnComponentComponentPropertyPropertyToCloudFormation(properties.id),Model:cdk().stringToCloudFormation(properties.model),State:convertCfnComponentMutationActionSetStateParameterPropertyToCloudFormation(properties.state),Target:convertCfnComponentComponentPropertyPropertyToCloudFormation(properties.target),Type:convertCfnComponentComponentPropertyPropertyToCloudFormation(properties.type),Url:convertCfnComponentComponentPropertyPropertyToCloudFormation(properties.url)}):properties}function CfnComponentActionParametersPropertyFromCloudFormation(properties){if(cdk().isResolvableObject(properties))return new(cfn_parse()).FromCloudFormationResult(properties);if(properties=properties??{},!(properties&&typeof properties=="object"&&!Array.isArray(properties)))return new(cfn_parse()).FromCloudFormationResult(properties);const ret=new(cfn_parse()).FromCloudFormationPropertyObject;return ret.addPropertyResult("anchor","Anchor",properties.Anchor!=null?CfnComponentComponentPropertyPropertyFromCloudFormation(properties.Anchor):void 0),ret.addPropertyResult("fields","Fields",properties.Fields!=null?cfn_parse().FromCloudFormation.getMap(CfnComponentComponentPropertyPropertyFromCloudFormation)(properties.Fields):void 0),ret.addPropertyResult("global","Global",properties.Global!=null?CfnComponentComponentPropertyPropertyFromCloudFormation(properties.Global):void 0),ret.addPropertyResult("id","Id",properties.Id!=null?CfnComponentComponentPropertyPropertyFromCloudFormation(properties.Id):void 0),ret.addPropertyResult("model","Model",properties.Model!=null?cfn_parse().FromCloudFormation.getString(properties.Model):void 0),ret.addPropertyResult("state","State",properties.State!=null?CfnComponentMutationActionSetStateParameterPropertyFromCloudFormation(properties.State):void 0),ret.addPropertyResult("target","Target",properties.Target!=null?CfnComponentComponentPropertyPropertyFromCloudFormation(properties.Target):void 0),ret.addPropertyResult("type","Type",properties.Type!=null?CfnComponentComponentPropertyPropertyFromCloudFormation(properties.Type):void 0),ret.addPropertyResult("url","Url",properties.Url!=null?CfnComponentComponentPropertyPropertyFromCloudFormation(properties.Url):void 0),ret.addUnrecognizedPropertiesAsExtra(properties),ret}function CfnComponentComponentEventPropertyValidator(properties){if(!cdk().canInspect(properties))return cdk().VALIDATION_SUCCESS;const errors=new(cdk()).ValidationResults;return properties&&typeof properties=="object"&&!Array.isArray(properties)||errors.collect(new(cdk()).ValidationResult("Expected an object, but received: "+JSON.stringify(properties))),errors.collect(cdk().propertyValidator("action",cdk().validateString)(properties.action)),errors.collect(cdk().propertyValidator("bindingEvent",cdk().validateString)(properties.bindingEvent)),errors.collect(cdk().propertyValidator("parameters",CfnComponentActionParametersPropertyValidator)(properties.parameters)),errors.wrap('supplied properties not correct for "ComponentEventProperty"')}function convertCfnComponentComponentEventPropertyToCloudFormation(properties){return cdk().canInspect(properties)?(CfnComponentComponentEventPropertyValidator(properties).assertSuccess(),{Action:cdk().stringToCloudFormation(properties.action),BindingEvent:cdk().stringToCloudFormation(properties.bindingEvent),Parameters:convertCfnComponentActionParametersPropertyToCloudFormation(properties.parameters)}):properties}function CfnComponentComponentEventPropertyFromCloudFormation(properties){if(cdk().isResolvableObject(properties))return new(cfn_parse()).FromCloudFormationResult(properties);if(properties=properties??{},!(properties&&typeof properties=="object"&&!Array.isArray(properties)))return new(cfn_parse()).FromCloudFormationResult(properties);const ret=new(cfn_parse()).FromCloudFormationPropertyObject;return ret.addPropertyResult("action","Action",properties.Action!=null?cfn_parse().FromCloudFormation.getString(properties.Action):void 0),ret.addPropertyResult("bindingEvent","BindingEvent",properties.BindingEvent!=null?cfn_parse().FromCloudFormation.getString(properties.BindingEvent):void 0),ret.addPropertyResult("parameters","Parameters",properties.Parameters!=null?CfnComponentActionParametersPropertyFromCloudFormation(properties.Parameters):void 0),ret.addUnrecognizedPropertiesAsExtra(properties),ret}function CfnComponentComponentChildPropertyValidator(properties){if(!cdk().canInspect(properties))return cdk().VALIDATION_SUCCESS;const errors=new(cdk()).ValidationResults;return properties&&typeof properties=="object"&&!Array.isArray(properties)||errors.collect(new(cdk()).ValidationResult("Expected an object, but received: "+JSON.stringify(properties))),errors.collect(cdk().propertyValidator("children",cdk().listValidator(CfnComponentComponentChildPropertyValidator))(properties.children)),errors.collect(cdk().propertyValidator("componentType",cdk().requiredValidator)(properties.componentType)),errors.collect(cdk().propertyValidator("componentType",cdk().validateString)(properties.componentType)),errors.collect(cdk().propertyValidator("events",cdk().hashValidator(CfnComponentComponentEventPropertyValidator))(properties.events)),errors.collect(cdk().propertyValidator("name",cdk().requiredValidator)(properties.name)),errors.collect(cdk().propertyValidator("name",cdk().validateString)(properties.name)),errors.collect(cdk().propertyValidator("properties",cdk().requiredValidator)(properties.properties)),errors.collect(cdk().propertyValidator("properties",cdk().hashValidator(CfnComponentComponentPropertyPropertyValidator))(properties.properties)),errors.collect(cdk().propertyValidator("sourceId",cdk().validateString)(properties.sourceId)),errors.wrap('supplied properties not correct for "ComponentChildProperty"')}function convertCfnComponentComponentChildPropertyToCloudFormation(properties){return cdk().canInspect(properties)?(CfnComponentComponentChildPropertyValidator(properties).assertSuccess(),{Children:cdk().listMapper(convertCfnComponentComponentChildPropertyToCloudFormation)(properties.children),ComponentType:cdk().stringToCloudFormation(properties.componentType),Events:cdk().hashMapper(convertCfnComponentComponentEventPropertyToCloudFormation)(properties.events),Name:cdk().stringToCloudFormation(properties.name),Properties:cdk().hashMapper(convertCfnComponentComponentPropertyPropertyToCloudFormation)(properties.properties),SourceId:cdk().stringToCloudFormation(properties.sourceId)}):properties}function CfnComponentComponentChildPropertyFromCloudFormation(properties){if(cdk().isResolvableObject(properties))return new(cfn_parse()).FromCloudFormationResult(properties);if(properties=properties??{},!(properties&&typeof properties=="object"&&!Array.isArray(properties)))return new(cfn_parse()).FromCloudFormationResult(properties);const ret=new(cfn_parse()).FromCloudFormationPropertyObject;return ret.addPropertyResult("children","Children",properties.Children!=null?cfn_parse().FromCloudFormation.getArray(CfnComponentComponentChildPropertyFromCloudFormation)(properties.Children):void 0),ret.addPropertyResult("componentType","ComponentType",properties.ComponentType!=null?cfn_parse().FromCloudFormation.getString(properties.ComponentType):void 0),ret.addPropertyResult("events","Events",properties.Events!=null?cfn_parse().FromCloudFormation.getMap(CfnComponentComponentEventPropertyFromCloudFormation)(properties.Events):void 0),ret.addPropertyResult("name","Name",properties.Name!=null?cfn_parse().FromCloudFormation.getString(properties.Name):void 0),ret.addPropertyResult("properties","Properties",properties.Properties!=null?cfn_parse().FromCloudFormation.getMap(CfnComponentComponentPropertyPropertyFromCloudFormation)(properties.Properties):void 0),ret.addPropertyResult("sourceId","SourceId",properties.SourceId!=null?cfn_parse().FromCloudFormation.getString(properties.SourceId):void 0),ret.addUnrecognizedPropertiesAsExtra(properties),ret}function CfnComponentPropsValidator(properties){if(!cdk().canInspect(properties))return cdk().VALIDATION_SUCCESS;const errors=new(cdk()).ValidationResults;return properties&&typeof properties=="object"&&!Array.isArray(properties)||errors.collect(new(cdk()).ValidationResult("Expected an object, but received: "+JSON.stringify(properties))),errors.collect(cdk().propertyValidator("appId",cdk().validateString)(properties.appId)),errors.collect(cdk().propertyValidator("bindingProperties",cdk().hashValidator(CfnComponentComponentBindingPropertiesValuePropertyValidator))(properties.bindingProperties)),errors.collect(cdk().propertyValidator("children",cdk().listValidator(CfnComponentComponentChildPropertyValidator))(properties.children)),errors.collect(cdk().propertyValidator("collectionProperties",cdk().hashValidator(CfnComponentComponentDataConfigurationPropertyValidator))(properties.collectionProperties)),errors.collect(cdk().propertyValidator("componentType",cdk().validateString)(properties.componentType)),errors.collect(cdk().propertyValidator("environmentName",cdk().validateString)(properties.environmentName)),errors.collect(cdk().propertyValidator("events",cdk().hashValidator(CfnComponentComponentEventPropertyValidator))(properties.events)),errors.collect(cdk().propertyValidator("name",cdk().validateString)(properties.name)),errors.collect(cdk().propertyValidator("overrides",cdk().validateObject)(properties.overrides)),errors.collect(cdk().propertyValidator("properties",cdk().hashValidator(CfnComponentComponentPropertyPropertyValidator))(properties.properties)),errors.collect(cdk().propertyValidator("schemaVersion",cdk().validateString)(properties.schemaVersion)),errors.collect(cdk().propertyValidator("sourceId",cdk().validateString)(properties.sourceId)),errors.collect(cdk().propertyValidator("tags",cdk().hashValidator(cdk().validateString))(properties.tags)),errors.collect(cdk().propertyValidator("variants",cdk().listValidator(CfnComponentComponentVariantPropertyValidator))(properties.variants)),errors.wrap('supplied properties not correct for "CfnComponentProps"')}function convertCfnComponentPropsToCloudFormation(properties){return cdk().canInspect(properties)?(CfnComponentPropsValidator(properties).assertSuccess(),{AppId:cdk().stringToCloudFormation(properties.appId),BindingProperties:cdk().hashMapper(convertCfnComponentComponentBindingPropertiesValuePropertyToCloudFormation)(properties.bindingProperties),Children:cdk().listMapper(convertCfnComponentComponentChildPropertyToCloudFormation)(properties.children),CollectionProperties:cdk().hashMapper(convertCfnComponentComponentDataConfigurationPropertyToCloudFormation)(properties.collectionProperties),ComponentType:cdk().stringToCloudFormation(properties.componentType),EnvironmentName:cdk().stringToCloudFormation(properties.environmentName),Events:cdk().hashMapper(convertCfnComponentComponentEventPropertyToCloudFormation)(properties.events),Name:cdk().stringToCloudFormation(properties.name),Overrides:cdk().objectToCloudFormation(properties.overrides),Properties:cdk().hashMapper(convertCfnComponentComponentPropertyPropertyToCloudFormation)(properties.properties),SchemaVersion:cdk().stringToCloudFormation(properties.schemaVersion),SourceId:cdk().stringToCloudFormation(properties.sourceId),Tags:cdk().hashMapper(cdk().stringToCloudFormation)(properties.tags),Variants:cdk().listMapper(convertCfnComponentComponentVariantPropertyToCloudFormation)(properties.variants)}):properties}function CfnComponentPropsFromCloudFormation(properties){if(cdk().isResolvableObject(properties))return new(cfn_parse()).FromCloudFormationResult(properties);if(properties=properties??{},!(properties&&typeof properties=="object"&&!Array.isArray(properties)))return new(cfn_parse()).FromCloudFormationResult(properties);const ret=new(cfn_parse()).FromCloudFormationPropertyObject;return ret.addPropertyResult("appId","AppId",properties.AppId!=null?cfn_parse().FromCloudFormation.getString(properties.AppId):void 0),ret.addPropertyResult("bindingProperties","BindingProperties",properties.BindingProperties!=null?cfn_parse().FromCloudFormation.getMap(CfnComponentComponentBindingPropertiesValuePropertyFromCloudFormation)(properties.BindingProperties):void 0),ret.addPropertyResult("children","Children",properties.Children!=null?cfn_parse().FromCloudFormation.getArray(CfnComponentComponentChildPropertyFromCloudFormation)(properties.Children):void 0),ret.addPropertyResult("collectionProperties","CollectionProperties",properties.CollectionProperties!=null?cfn_parse().FromCloudFormation.getMap(CfnComponentComponentDataConfigurationPropertyFromCloudFormation)(properties.CollectionProperties):void 0),ret.addPropertyResult("componentType","ComponentType",properties.ComponentType!=null?cfn_parse().FromCloudFormation.getString(properties.ComponentType):void 0),ret.addPropertyResult("environmentName","EnvironmentName",properties.EnvironmentName!=null?cfn_parse().FromCloudFormation.getString(properties.EnvironmentName):void 0),ret.addPropertyResult("events","Events",properties.Events!=null?cfn_parse().FromCloudFormation.getMap(CfnComponentComponentEventPropertyFromCloudFormation)(properties.Events):void 0),ret.addPropertyResult("name","Name",properties.Name!=null?cfn_parse().FromCloudFormation.getString(properties.Name):void 0),ret.addPropertyResult("overrides","Overrides",properties.Overrides!=null?cfn_parse().FromCloudFormation.getAny(properties.Overrides):void 0),ret.addPropertyResult("properties","Properties",properties.Properties!=null?cfn_parse().FromCloudFormation.getMap(CfnComponentComponentPropertyPropertyFromCloudFormation)(properties.Properties):void 0),ret.addPropertyResult("schemaVersion","SchemaVersion",properties.SchemaVersion!=null?cfn_parse().FromCloudFormation.getString(properties.SchemaVersion):void 0),ret.addPropertyResult("sourceId","SourceId",properties.SourceId!=null?cfn_parse().FromCloudFormation.getString(properties.SourceId):void 0),ret.addPropertyResult("tags","Tags",properties.Tags!=null?cfn_parse().FromCloudFormation.getMap(cfn_parse().FromCloudFormation.getString)(properties.Tags):void 0),ret.addPropertyResult("variants","Variants",properties.Variants!=null?cfn_parse().FromCloudFormation.getArray(CfnComponentComponentVariantPropertyFromCloudFormation)(properties.Variants):void 0),ret.addUnrecognizedPropertiesAsExtra(properties),ret}class CfnForm extends cdk().CfnResource{static _fromCloudFormation(scope,id,resourceAttributes,options){resourceAttributes=resourceAttributes||{};const resourceProperties=options.parser.parseValue(resourceAttributes.Properties),propsResult=CfnFormPropsFromCloudFormation(resourceProperties);if(cdk().isResolvableObject(propsResult.value))throw new(cdk_errors()).ValidationError("Unexpected IResolvable",scope);const ret=new CfnForm(scope,id,propsResult.value);for(const[propKey,propVal]of Object.entries(propsResult.extraProperties))ret.addPropertyOverride(propKey,propVal);return options.parser.handleAttributes(ret,resourceAttributes,id),ret}constructor(scope,id,props={}){super(scope,id,{type:CfnForm.CFN_RESOURCE_TYPE_NAME,properties:props});try{jsiiDeprecationWarnings().aws_cdk_lib_aws_amplifyuibuilder_CfnFormProps(props)}catch(error){throw process.env.JSII_DEBUG!=="1"&&error.name==="DeprecationError"&&Error.captureStackTrace(error,CfnForm),error}this.attrId=cdk().Token.asString(this.getAtt("Id",cdk().ResolutionTypeHint.STRING)),this.appId=props.appId,this.cta=props.cta,this.dataType=props.dataType,this.environmentName=props.environmentName,this.fields=props.fields,this.formActionType=props.formActionType,this.labelDecorator=props.labelDecorator,this.name=props.name,this.schemaVersion=props.schemaVersion,this.sectionalElements=props.sectionalElements,this.style=props.style,this.tags=new(cdk()).TagManager(cdk().TagType.MAP,"AWS::AmplifyUIBuilder::Form",props.tags,{tagPropertyName:"tags"}),this.tagsRaw=props.tags}get cfnProperties(){return{appId:this.appId,cta:this.cta,dataType:this.dataType,environmentName:this.environmentName,fields:this.fields,formActionType:this.formActionType,labelDecorator:this.labelDecorator,name:this.name,schemaVersion:this.schemaVersion,sectionalElements:this.sectionalElements,style:this.style,tags:this.tags.renderTags()}}inspect(inspector){try{jsiiDeprecationWarnings().aws_cdk_lib_TreeInspector(inspector)}catch(error){throw process.env.JSII_DEBUG!=="1"&&error.name==="DeprecationError"&&Error.captureStackTrace(error,this.inspect),error}inspector.addAttribute("aws:cdk:cloudformation:type",CfnForm.CFN_RESOURCE_TYPE_NAME),inspector.addAttribute("aws:cdk:cloudformation:props",this.cfnProperties)}renderProperties(props){return convertCfnFormPropsToCloudFormation(props)}}exports.CfnForm=CfnForm,_b=JSII_RTTI_SYMBOL_1,CfnForm[_b]={fqn:"aws-cdk-lib.aws_amplifyuibuilder.CfnForm",version:"2.201.0"},CfnForm.CFN_RESOURCE_TYPE_NAME="AWS::AmplifyUIBuilder::Form";function CfnFormFieldPositionPropertyValidator(properties){if(!cdk().canInspect(properties))return cdk().VALIDATION_SUCCESS;const errors=new(cdk()).ValidationResults;return properties&&typeof properties=="object"&&!Array.isArray(properties)||errors.collect(new(cdk()).ValidationResult("Expected an object, but received: "+JSON.stringify(properties))),errors.collect(cdk().propertyValidator("below",cdk().validateString)(properties.below)),errors.collect(cdk().propertyValidator("fixed",cdk().validateString)(properties.fixed)),errors.collect(cdk().propertyValidator("rightOf",cdk().validateString)(properties.rightOf)),errors.wrap('supplied properties not correct for "FieldPositionProperty"')}function convertCfnFormFieldPositionPropertyToCloudFormation(properties){return cdk().canInspect(properties)?(CfnFormFieldPositionPropertyValidator(properties).assertSuccess(),{Below:cdk().stringToCloudFormation(properties.below),Fixed:cdk().stringToCloudFormation(properties.fixed),RightOf:cdk().stringToCloudFormation(properties.rightOf)}):properties}function CfnFormFieldPositionPropertyFromCloudFormation(properties){if(cdk().isResolvableObject(properties))return new(cfn_parse()).FromCloudFormationResult(properties);if(properties=properties??{},!(properties&&typeof properties=="object"&&!Array.isArray(properties)))return new(cfn_parse()).FromCloudFormationResult(properties);const ret=new(cfn_parse()).FromCloudFormationPropertyObject;return ret.addPropertyResult("below","Below",properties.Below!=null?cfn_parse().FromCloudFormation.getString(properties.Below):void 0),ret.addPropertyResult("fixed","Fixed",properties.Fixed!=null?cfn_parse().FromCloudFormation.getString(properties.Fixed):void 0),ret.addPropertyResult("rightOf","RightOf",properties.RightOf!=null?cfn_parse().FromCloudFormation.getString(properties.RightOf):void 0),ret.addUnrecognizedPropertiesAsExtra(properties),ret}function CfnFormFormButtonPropertyValidator(properties){if(!cdk().canInspect(properties))return cdk().VALIDATION_SUCCESS;const errors=new(cdk()).ValidationResults;return properties&&typeof properties=="object"&&!Array.isArray(properties)||errors.collect(new(cdk()).ValidationResult("Expected an object, but received: "+JSON.stringify(properties))),errors.collect(cdk().propertyValidator("children",cdk().validateString)(properties.children)),errors.collect(cdk().propertyValidator("excluded",cdk().validateBoolean)(properties.excluded)),errors.collect(cdk().propertyValidator("position",CfnFormFieldPositionPropertyValidator)(properties.position)),errors.wrap('supplied properties not correct for "FormButtonProperty"')}function convertCfnFormFormButtonPropertyToCloudFormation(properties){return cdk().canInspect(properties)?(CfnFormFormButtonPropertyValidator(properties).assertSuccess(),{Children:cdk().stringToCloudFormation(properties.children),Excluded:cdk().booleanToCloudFormation(properties.excluded),Position:convertCfnFormFieldPositionPropertyToCloudFormation(properties.position)}):properties}function CfnFormFormButtonPropertyFromCloudFormation(properties){if(cdk().isResolvableObject(properties))return new(cfn_parse()).FromCloudFormationResult(properties);if(properties=properties??{},!(properties&&typeof properties=="object"&&!Array.isArray(properties)))return new(cfn_parse()).FromCloudFormationResult(properties);const ret=new(cfn_parse()).FromCloudFormationPropertyObject;return ret.addPropertyResult("children","Children",properties.Children!=null?cfn_parse().FromCloudFormation.getString(properties.Children):void 0),ret.addPropertyResult("excluded","Excluded",properties.Excluded!=null?cfn_parse().FromCloudFormation.getBoolean(properties.Excluded):void 0),ret.addPropertyResult("position","Position",properties.Position!=null?CfnFormFieldPositionPropertyFromCloudFormation(properties.Position):void 0),ret.addUnrecognizedPropertiesAsExtra(properties),ret}function CfnFormFormCTAPropertyValidator(properties){if(!cdk().canInspect(properties))return cdk().VALIDATION_SUCCESS;const errors=new(cdk()).ValidationResults;return properties&&typeof properties=="object"&&!Array.isArray(properties)||errors.collect(new(cdk()).ValidationResult("Expected an object, but received: "+JSON.stringify(properties))),errors.collect(cdk().propertyValidator("cancel",CfnFormFormButtonPropertyValidator)(properties.cancel)),errors.collect(cdk().propertyValidator("clear",CfnFormFormButtonPropertyValidator)(properties.clear)),errors.collect(cdk().propertyValidator("position",cdk().validateString)(properties.position)),errors.collect(cdk().propertyValidator("submit",CfnFormFormButtonPropertyValidator)(properties.submit)),errors.wrap('supplied properties not correct for "FormCTAProperty"')}function convertCfnFormFormCTAPropertyToCloudFormation(properties){return cdk().canInspect(properties)?(CfnFormFormCTAPropertyValidator(properties).assertSuccess(),{Cancel:convertCfnFormFormButtonPropertyToCloudFormation(properties.cancel),Clear:convertCfnFormFormButtonPropertyToCloudFormation(properties.clear),Position:cdk().stringToCloudFormation(properties.position),Submit:convertCfnFormFormButtonPropertyToCloudFormation(properties.submit)}):properties}function CfnFormFormCTAPropertyFromCloudFormation(properties){if(cdk().isResolvableObject(properties))return new(cfn_parse()).FromCloudFormationResult(properties);if(properties=properties??{},!(properties&&typeof properties=="object"&&!Array.isArray(properties)))return new(cfn_parse()).FromCloudFormationResult(properties);const ret=new(cfn_parse()).FromCloudFormationPropertyObject;return ret.addPropertyResult("cancel","Cancel",properties.Cancel!=null?CfnFormFormButtonPropertyFromCloudFormation(properties.Cancel):void 0),ret.addPropertyResult("clear","Clear",properties.Clear!=null?CfnFormFormButtonPropertyFromCloudFormation(properties.Clear):void 0),ret.addPropertyResult("position","Position",properties.Position!=null?cfn_parse().FromCloudFormation.getString(properties.Position):void 0),ret.addPropertyResult("submit","Submit",properties.Submit!=null?CfnFormFormButtonPropertyFromCloudFormation(properties.Submit):void 0),ret.addUnrecognizedPropertiesAsExtra(properties),ret}function CfnFormFieldValidationConfigurationPropertyValidator(properties){if(!cdk().canInspect(properties))return cdk().VALIDATION_SUCCESS;const errors=new(cdk()).ValidationResults;return properties&&typeof properties=="object"&&!Array.isArray(properties)||errors.collect(new(cdk()).ValidationResult("Expected an object, but received: "+JSON.stringify(properties))),errors.collect(cdk().propertyValidator("numValues",cdk().listValidator(cdk().validateNumber))(properties.numValues)),errors.collect(cdk().propertyValidator("strValues",cdk().listValidator(cdk().validateString))(properties.strValues)),errors.collect(cdk().propertyValidator("type",cdk().requiredValidator)(properties.type)),errors.collect(cdk().propertyValidator("type",cdk().validateString)(properties.type)),errors.collect(cdk().propertyValidator("validationMessage",cdk().validateString)(properties.validationMessage)),errors.wrap('supplied properties not correct for "FieldValidationConfigurationProperty"')}function convertCfnFormFieldValidationConfigurationPropertyToCloudFormation(properties){return cdk().canInspect(properties)?(CfnFormFieldValidationConfigurationPropertyValidator(properties).assertSuccess(),{NumValues:cdk().listMapper(cdk().numberToCloudFormation)(properties.numValues),StrValues:cdk().listMapper(cdk().stringToCloudFormation)(properties.strValues),Type:cdk().stringToCloudFormation(properties.type),ValidationMessage:cdk().stringToCloudFormation(properties.validationMessage)}):properties}function CfnFormFieldValidationConfigurationPropertyFromCloudFormation(properties){if(cdk().isResolvableObject(properties))return new(cfn_parse()).FromCloudFormationResult(properties);if(properties=properties??{},!(properties&&typeof properties=="object"&&!Array.isArray(properties)))return new(cfn_parse()).FromCloudFormationResult(properties);const ret=new(cfn_parse()).FromCloudFormationPropertyObject;return ret.addPropertyResult("numValues","NumValues",properties.NumValues!=null?cfn_parse().FromCloudFormation.getArray(cfn_parse().FromCloudFormation.getNumber)(properties.NumValues):void 0),ret.addPropertyResult("strValues","StrValues",properties.StrValues!=null?cfn_parse().FromCloudFormation.getArray(cfn_parse().FromCloudFormation.getString)(properties.StrValues):void 0),ret.addPropertyResult("type","Type",properties.Type!=null?cfn_parse().FromCloudFormation.getString(properties.Type):void 0),ret.addPropertyResult("validationMessage","ValidationMessage",properties.ValidationMessage!=null?cfn_parse().FromCloudFormation.getString(properties.ValidationMessage):void 0),ret.addUnrecognizedPropertiesAsExtra(properties),ret}function CfnFormFileUploaderFieldConfigPropertyValidator(properties){if(!cdk().canInspect(properties))return cdk().VALIDATION_SUCCESS;const errors=new(cdk()).ValidationResults;return properties&&typeof properties=="object"&&!Array.isArray(properties)||errors.collect(new(cdk()).ValidationResult("Expected an object, but received: "+JSON.stringify(properties))),errors.collect(cdk().propertyValidator("acceptedFileTypes",cdk().requiredValidator)(properties.acceptedFileTypes)),errors.collect(cdk().propertyValidator("acceptedFileTypes",cdk().listValidator(cdk().validateString))(properties.acceptedFileTypes)),errors.collect(cdk().propertyValidator("accessLevel",cdk().requiredValidator)(properties.accessLevel)),errors.collect(cdk().propertyValidator("accessLevel",cdk().validateString)(properties.accessLevel)),errors.collect(cdk().propertyValidator("isResumable",cdk().validateBoolean)(properties.isResumable)),errors.collect(cdk().propertyValidator("maxFileCount",cdk().validateNumber)(properties.maxFileCount)),errors.collect(cdk().propertyValidator("maxSize",cdk().validateNumber)(properties.maxSize)),errors.collect(cdk().propertyValidator("showThumbnails",cdk().validateBoolean)(properties.showThumbnails)),errors.wrap('supplied properties not correct for "FileUploaderFieldConfigProperty"')}function convertCfnFormFileUploaderFieldConfigPropertyToCloudFormation(properties){return cdk().canInspect(properties)?(CfnFormFileUploaderFieldConfigPropertyValidator(properties).assertSuccess(),{AcceptedFileTypes:cdk().listMapper(cdk().stringToCloudFormation)(properties.acceptedFileTypes),AccessLevel:cdk().stringToCloudFormation(properties.accessLevel),IsResumable:cdk().booleanToCloudFormation(properties.isResumable),MaxFileCount:cdk().numberToCloudFormation(properties.maxFileCount),MaxSize:cdk().numberToCloudFormation(properties.maxSize),ShowThumbnails:cdk().booleanToCloudFormation(properties.showThumbnails)}):properties}function CfnFormFileUploaderFieldConfigPropertyFromCloudFormation(properties){if(cdk().isResolvableObject(properties))return new(cfn_parse()).FromCloudFormationResult(properties);if(properties=properties??{},!(properties&&typeof properties=="object"&&!Array.isArray(properties)))return new(cfn_parse()).FromCloudFormationResult(properties);const ret=new(cfn_parse()).FromCloudFormationPropertyObject;return ret.addPropertyResult("acceptedFileTypes","AcceptedFileTypes",properties.AcceptedFileTypes!=null?cfn_parse().FromCloudFormation.getArray(cfn_parse().FromCloudFormation.getString)(properties.AcceptedFileTypes):void 0),ret.addPropertyResult("accessLevel","AccessLevel",properties.AccessLevel!=null?cfn_parse().FromCloudFormation.getString(properties.AccessLevel):void 0),ret.addPropertyResult("isResumable","IsResumable",properties.IsResumable!=null?cfn_parse().FromCloudFormation.getBoolean(properties.IsResumable):void 0),ret.addPropertyResult("maxFileCount","MaxFileCount",properties.MaxFileCount!=null?cfn_parse().FromCloudFormation.getNumber(properties.MaxFileCount):void 0),ret.addPropertyResult("maxSize","MaxSize",properties.MaxSize!=null?cfn_parse().FromCloudFormation.getNumber(properties.MaxSize):void 0),ret.addPropertyResult("showThumbnails","ShowThumbnails",properties.ShowThumbnails!=null?cfn_parse().FromCloudFormation.getBoolean(properties.ShowThumbnails):void 0),ret.addUnrecognizedPropertiesAsExtra(properties),ret}function CfnFormFormInputValuePropertyBindingPropertiesPropertyValidator(properties){if(!cdk().canInspect(properties))return cdk().VALIDATION_SUCCESS;const errors=new(cdk()).ValidationResults;return properties&&typeof properties=="object"&&!Array.isArray(properties)||errors.collect(new(cdk()).ValidationResult("Expected an object, but received: "+JSON.stringify(properties))),errors.collect(cdk().propertyValidator("field",cdk().validateString)(properties.field)),errors.collect(cdk().propertyValidator("property",cdk().requiredValidator)(properties.property)),errors.collect(cdk().propertyValidator("property",cdk().validateString)(properties.property)),errors.wrap('supplied properties not correct for "FormInputValuePropertyBindingPropertiesProperty"')}function convertCfnFormFormInputValuePropertyBindingPropertiesPropertyToCloudFormation(properties){return cdk().canInspect(properties)?(CfnFormFormInputValuePropertyBindingPropertiesPropertyValidator(properties).assertSuccess(),{Field:cdk().stringToCloudFormation(properties.field),Property:cdk().stringToCloudFormation(properties.property)}):properties}function CfnFormFormInputValuePropertyBindingPropertiesPropertyFromCloudFormation(properties){if(cdk().isResolvableObject(properties))return new(cfn_parse()).FromCloudFormationResult(properties);if(properties=properties??{},!(properties&&typeof properties=="object"&&!Array.isArray(properties)))return new(cfn_parse()).FromCloudFormationResult(properties);const ret=new(cfn_parse()).FromCloudFormationPropertyObject;return ret.addPropertyResult("field","Field",properties.Field!=null?cfn_parse().FromCloudFormation.getString(properties.Field):void 0),ret.addPropertyResult("property","Property",properties.Property!=null?cfn_parse().FromCloudFormation.getString(properties.Property):void 0),ret.addUnrecognizedPropertiesAsExtra(properties),ret}function CfnFormFormInputValuePropertyPropertyValidator(properties){if(!cdk().canInspect(properties))return cdk().VALIDATION_SUCCESS;const errors=new(cdk()).ValidationResults;return properties&&typeof properties=="object"&&!Array.isArray(properties)||errors.collect(new(cdk()).ValidationResult("Expected an object, but received: "+JSON.stringify(properties))),errors.collect(cdk().propertyValidator("bindingProperties",CfnFormFormInputValuePropertyBindingPropertiesPropertyValidator)(properties.bindingProperties)),errors.collect(cdk().propertyValidator("concat",cdk().listValidator(CfnFormFormInputValuePropertyPropertyValidator))(properties.concat)),errors.collect(cdk().propertyValidator("value",cdk().validateString)(properties.value)),errors.wrap('supplied properties not correct for "FormInputValuePropertyProperty"')}function convertCfnFormFormInputValuePropertyPropertyToCloudFormation(properties){return cdk().canInspect(properties)?(CfnFormFormInputValuePropertyPropertyValidator(properties).assertSuccess(),{BindingProperties:convertCfnFormFormInputValuePropertyBindingPropertiesPropertyToCloudFormation(properties.bindingProperties),Concat:cdk().listMapper(convertCfnFormFormInputValuePropertyPropertyToCloudFormation)(properties.concat),Value:cdk().stringToCloudFormation(properties.value)}):properties}function CfnFormFormInputValuePropertyPropertyFromCloudFormation(properties){if(cdk().isResolvableObject(properties))return new(cfn_parse()).FromCloudFormationResult(properties);if(properties=properties??{},!(properties&&typeof properties=="object"&&!Array.isArray(properties)))return new(cfn_parse()).FromCloudFormationResult(properties);const ret=new(cfn_parse()).FromCloudFormationPropertyObject;return ret.addPropertyResult("bindingProperties","BindingProperties",properties.BindingProperties!=null?CfnFormFormInputValuePropertyBindingPropertiesPropertyFromCloudFormation(properties.BindingProperties):void 0),ret.addPropertyResult("concat","Concat",properties.Concat!=null?cfn_parse().FromCloudFormation.getArray(CfnFormFormInputValuePropertyPropertyFromCloudFormation)(properties.Concat):void 0),ret.addPropertyResult("value","Value",properties.Value!=null?cfn_parse().FromCloudFormation.getString(properties.Value):void 0),ret.addUnrecognizedPropertiesAsExtra(properties),ret}function CfnFormValueMappingPropertyValidator(properties){if(!cdk().canInspect(properties))return cdk().VALIDATION_SUCCESS;const errors=new(cdk()).ValidationResults;return properties&&typeof properties=="object"&&!Array.isArray(properties)||errors.collect(new(cdk()).ValidationResult("Expected an object, but received: "+JSON.stringify(properties))),errors.collect(cdk().propertyValidator("displayValue",CfnFormFormInputValuePropertyPropertyValidator)(properties.displayValue)),errors.collect(cdk().propertyValidator("value",cdk().requiredValidator)(properties.value)),errors.collect(cdk().propertyValidator("value",CfnFormFormInputValuePropertyPropertyValidator)(properties.value)),errors.wrap('supplied properties not correct for "ValueMappingProperty"')}function convertCfnFormValueMappingPropertyToCloudFormation(properties){return cdk().canInspect(properties)?(CfnFormValueMappingPropertyValidator(properties).assertSuccess(),{DisplayValue:convertCfnFormFormInputValuePropertyPropertyToCloudFormation(properties.displayValue),Value:convertCfnFormFormInputValuePropertyPropertyToCloudFormation(properties.value)}):properties}function CfnFormValueMappingPropertyFromCloudFormation(properties){if(cdk().isResolvableObject(properties))return new(cfn_parse()).FromCloudFormationResult(properties);if(properties=properties??{},!(properties&&typeof properties=="object"&&!Array.isArray(properties)))return new(cfn_parse()).FromCloudFormationResult(properties);const ret=new(cfn_parse()).FromCloudFormationPropertyObject;return ret.addPropertyResult("displayValue","DisplayValue",properties.DisplayValue!=null?CfnFormFormInputValuePropertyPropertyFromCloudFormation(properties.DisplayValue):void 0),ret.addPropertyResult("value","Value",properties.Value!=null?CfnFormFormInputValuePropertyPropertyFromCloudFormation(properties.Value):void 0),ret.addUnrecognizedPropertiesAsExtra(properties),ret}function CfnFormFormInputBindingPropertiesValuePropertiesPropertyValidator(properties){if(!cdk().canInspect(properties))return cdk().VALIDATION_SUCCESS;const errors=new(cdk()).ValidationResults;return properties&&typeof properties=="object"&&!Array.isArray(properties)||errors.collect(new(cdk()).ValidationResult("Expected an object, but received: "+JSON.stringify(properties))),errors.collect(cdk().propertyValidator("model",cdk().validateString)(properties.model)),errors.wrap('supplied properties not correct for "FormInputBindingPropertiesValuePropertiesProperty"')}function convertCfnFormFormInputBindingPropertiesValuePropertiesPropertyToCloudFormation(properties){return cdk().canInspect(properties)?(CfnFormFormInputBindingPropertiesValuePropertiesPropertyValidator(properties).assertSuccess(),{Model:cdk().stringToCloudFormation(properties.model)}):properties}function CfnFormFormInputBindingPropertiesValuePropertiesPropertyFromCloudFormation(properties){if(cdk().isResolvableObject(properties))return new(cfn_parse()).FromCloudFormationResult(properties);if(properties=properties??{},!(properties&&typeof properties=="object"&&!Array.isArray(properties)))return new(cfn_parse()).FromCloudFormationResult(properties);const ret=new(cfn_parse()).FromCloudFormationPropertyObject;return ret.addPropertyResult("model","Model",properties.Model!=null?cfn_parse().FromCloudFormation.getString(properties.Model):void 0),ret.addUnrecognizedPropertiesAsExtra(properties),ret}function CfnFormFormInputBindingPropertiesValuePropertyValidator(properties){if(!cdk().canInspect(properties))return cdk().VALIDATION_SUCCESS;const errors=new(cdk()).ValidationResults;return properties&&typeof properties=="object"&&!Array.isArray(properties)||errors.collect(new(cdk()).ValidationResult("Expected an object, but received: "+JSON.stringify(properties))),errors.collect(cdk().propertyValidator("bindingProperties",CfnFormFormInputBindingPropertiesValuePropertiesPropertyValidator)(properties.bindingProperties)),errors.collect(cdk().propertyValidator("type",cdk().validateString)(properties.type)),errors.wrap('supplied properties not correct for "FormInputBindingPropertiesValueProperty"')}function convertCfnFormFormInputBindingPropertiesValuePropertyToCloudFormation(properties){return cdk().canInspect(properties)?(CfnFormFormInputBindingPropertiesValuePropertyValidator(properties).assertSuccess(),{BindingProperties:convertCfnFormFormInputBindingPropertiesValuePropertiesPropertyToCloudFormation(properties.bindingProperties),Type:cdk().stringToCloudFormation(properties.type)}):properties}function CfnFormFormInputBindingPropertiesValuePropertyFromCloudFormation(properties){if(cdk().isResolvableObject(properties))return new(cfn_parse()).FromCloudFormationResult(properties);if(properties=properties??{},!(properties&&typeof properties=="object"&&!Array.isArray(properties)))return new(cfn_parse()).FromCloudFormationResult(properties);const ret=new(cfn_parse()).FromCloudFormationPropertyObject;return ret.addPropertyResult("bindingProperties","BindingProperties",properties.BindingProperties!=null?CfnFormFormInputBindingPropertiesValuePropertiesPropertyFromCloudFormation(properties.BindingProperties):void 0),ret.addPropertyResult("type","Type",properties.Type!=null?cfn_parse().FromCloudFormation.getString(properties.Type):void 0),ret.addUnrecognizedPropertiesAsExtra(properties),ret}function CfnFormValueMappingsPropertyValidator(properties){if(!cdk().canInspect(properties))return cdk().VALIDATION_SUCCESS;const errors=new(cdk()).ValidationResults;return properties&&typeof properties=="object"&&!Array.isArray(properties)||errors.collect(new(cdk()).ValidationResult("Expected an object, but received: "+JSON.stringify(properties))),errors.collect(cdk().propertyValidator("bindingProperties",cdk().hashValidator(CfnFormFormInputBindingPropertiesValuePropertyValidator))(properties.bindingProperties)),errors.collect(cdk().propertyValidator("values",cdk().requiredValidator)(properties.values)),errors.collect(cdk().propertyValidator("values",cdk().listValidator(CfnFormValueMappingPropertyValidator))(properties.values)),errors.wrap('supplied properties not correct for "ValueMappingsProperty"')}function convertCfnFormValueMappingsPropertyToCloudFormation(properties){return cdk().canInspect(properties)?(CfnFormValueMappingsPropertyValidator(properties).assertSuccess(),{BindingProperties:cdk().hashMapper(convertCfnFormFormInputBindingPropertiesValuePropertyToCloudFormation)(properties.bindingProperties),Values:cdk().listMapper(convertCfnFormValueMappingPropertyToCloudFormation)(properties.values)}):properties}function CfnFormValueMappingsPropertyFromCloudFormation(properties){if(cdk().isResolvableObject(properties))return new(cfn_parse()).FromCloudFormationResult(properties);if(properties=properties??{},!(properties&&typeof properties=="object"&&!Array.isArray(properties)))return new(cfn_parse()).FromCloudFormationResult(properties);const ret=new(cfn_parse()).FromCloudFormationPropertyObject;return ret.addPropertyResult("bindingProperties","BindingProperties",properties.BindingProperties!=null?cfn_parse().FromCloudFormation.getMap(CfnFormFormInputBindingPropertiesValuePropertyFromCloudFormation)(properties.BindingProperties):void 0),ret.addPropertyResult("values","Values",properties.Values!=null?cfn_parse().FromCloudFormation.getArray(CfnFormValueMappingPropertyFromCloudFormation)(properties.Values):void 0),ret.addUnrecognizedPropertiesAsExtra(properties),ret}function CfnFormFieldInputConfigPropertyValidator(properties){if(!cdk().canInspect(properties))return cdk().VALIDATION_SUCCESS;const errors=new(cdk()).ValidationResults;return properties&&typeof properties=="object"&&!Array.isArray(properties)||errors.collect(new(cdk()).ValidationResult("Expected an object, but received: "+JSON.stringify(properties))),errors.collect(cdk().propertyValidator("defaultChecked",cdk().validateBoolean)(properties.defaultChecked)),errors.collect(cdk().propertyValidator("defaultCountryCode",cdk().validateString)(properties.defaultCountryCode)),errors.collect(cdk().propertyValidator("defaultValue",cdk().validateString)(properties.defaultValue)),errors.collect(cdk().propertyValidator("descriptiveText",cdk().validateString)(properties.descriptiveText)),errors.collect(cdk().propertyValidator("fileUploaderConfig",CfnFormFileUploaderFieldConfigPropertyValidator)(properties.fileUploaderConfig)),errors.collect(cdk().propertyValidator("isArray",cdk().validateBoolean)(properties.isArray)),errors.collect(cdk().propertyValidator("maxValue",cdk().validateNumber)(properties.maxValue)),errors.collect(cdk().propertyValidator("minValue",cdk().validateNumber)(properties.minValue)),errors.collect(cdk().propertyValidator("name",cdk().validateString)(properties.name)),errors.collect(cdk().propertyValidator("placeholder",cdk().validateString)(properties.placeholder)),errors.collect(cdk().propertyValidator("readOnly",cdk().validateBoolean)(properties.readOnly)),errors.collect(cdk().propertyValidator("required",cdk().validateBoolean)(properties.required)),errors.collect(cdk().propertyValidator("step",cdk().validateNumber)(properties.step)),errors.collect(cdk().propertyValidator("type",cdk().requiredValidator)(properties.type)),errors.collect(cdk().propertyValidator("type",cdk().validateString)(properties.type)),errors.collect(cdk().propertyValidator("value",cdk().validateString)(properties.value)),errors.collect(cdk().propertyValidator("valueMappings",CfnFormValueMappingsPropertyValidator)(properties.valueMappings)),errors.wrap('supplied properties not correct for "FieldInputConfigProperty"')}function convertCfnFormFieldInputConfigPropertyToCloudFormation(properties){return cdk().canInspect(properties)?(CfnFormFieldInputConfigPropertyValidator(properties).assertSuccess(),{DefaultChecked:cdk().booleanToCloudFormation(properties.defaultChecked),DefaultCountryCode:cdk().stringToCloudFormation(properties.defaultCountryCode),DefaultValue:cdk().stringToCloudFormation(properties.defaultValue),DescriptiveText:cdk().stringToCloudFormation(properties.descriptiveText),FileUploaderConfig:convertCfnFormFileUploaderFieldConfigPropertyToCloudFormation(properties.fileUploaderConfig),IsArray:cdk().booleanToCloudFormation(properties.isArray),MaxValue:cdk().numberToCloudFormation(properties.maxValue),MinValue:cdk().numberToCloudFormation(properties.minValue),Name:cdk().stringToCloudFormation(properties.name),Placeholder:cdk().stringToCloudFormation(properties.placeholder),ReadOnly:cdk().booleanToCloudFormation(properties.readOnly),Required:cdk().booleanToCloudFormation(properties.required),Step:cdk().numberToCloudFormation(properties.step),Type:cdk().stringToCloudFormation(properties.type),Value:cdk().stringToCloudFormation(properties.value),ValueMappings:convertCfnFormValueMappingsPropertyToCloudFormation(properties.valueMappings)}):properties}function CfnFormFieldInputConfigPropertyFromCloudFormation(properties){if(cdk().isResolvableObject(properties))return new(cfn_parse()).FromCloudFormationResult(properties);if(properties=properties??{},!(properties&&typeof properties=="object"&&!Array.isArray(properties)))return new(cfn_parse()).FromCloudFormationResult(properties);const ret=new(cfn_parse()).FromCloudFormationPropertyObject;return ret.addPropertyResult("defaultChecked","DefaultChecked",properties.DefaultChecked!=null?cfn_parse().FromCloudFormation.getBoolean(properties.DefaultChecked):void 0),ret.addPropertyResult("defaultCountryCode","DefaultCountryCode",properties.DefaultCountryCode!=null?cfn_parse().FromCloudFormation.getString(properties.DefaultCountryCode):void 0),ret.addPropertyResult("defaultValue","DefaultValue",properties.DefaultValue!=null?cfn_parse().FromCloudFormation.getString(properties.DefaultValue):void 0),ret.addPropertyResult("descriptiveText","DescriptiveText",properties.DescriptiveText!=null?cfn_parse().FromCloudFormation.getString(properties.DescriptiveText):void 0),ret.addPropertyResult("fileUploaderConfig","FileUploaderConfig",properties.FileUploaderConfig!=null?CfnFormFileUploaderFieldConfigPropertyFromCloudFormation(properties.FileUploaderConfig):void 0),ret.addPropertyResult("isArray","IsArray",properties.IsArray!=null?cfn_parse().FromCloudFormation.getBoolean(properties.IsArray):void 0),ret.addPropertyResult("maxValue","MaxValue",properties.MaxValue!=null?cfn_parse().FromCloudFormation.getNumber(properties.MaxValue):void 0),ret.addPropertyResult("minValue","MinValue",properties.MinValue!=null?cfn_parse().FromCloudFormation.getNumber(properties.MinValue):void 0),ret.addPropertyResult("name","Name",properties.Name!=null?cfn_parse().FromCloudFormation.getString(properties.Name):void 0),ret.addPropertyResult("placeholder","Placeholder",properties.Placeholder!=null?cfn_parse().FromCloudFormation.getString(properties.Placeholder):void 0),ret.addPropertyResult("readOnly","ReadOnly",properties.ReadOnly!=null?cfn_parse().FromCloudFormation.getBoolean(properties.ReadOnly):void 0),ret.addPropertyResult("required","Required",properties.Required!=null?cfn_parse().FromCloudFormation.getBoolean(properties.Required):void 0),ret.addPropertyResult("step","Step",properties.Step!=null?cfn_parse().FromCloudFormation.getNumber(properties.Step):void 0),ret.addPropertyResult("type","Type",properties.Type!=null?cfn_parse().FromCloudFormation.getString(properties.Type):void 0),ret.addPropertyResult("value","Value",properties.Value!=null?cfn_parse().FromCloudFormation.getString(properties.Value):void 0),ret.addPropertyResult("valueMappings","ValueMappings",properties.ValueMappings!=null?CfnFormValueMappingsPropertyFromCloudFormation(properties.ValueMappings):void 0),ret.addUnrecognizedPropertiesAsExtra(properties),ret}function CfnFormFieldConfigPropertyValidator(properties){if(!cdk().canInspect(properties))return cdk().VALIDATION_SUCCESS;const errors=new(cdk()).ValidationResults;return properties&&typeof properties=="object"&&!Array.isArray(properties)||errors.collect(new(cdk()).ValidationResult("Expected an object, but received: "+JSON.stringify(properties))),errors.collect(cdk().propertyValidator("excluded",cdk().validateBoolean)(properties.excluded)),errors.collect(cdk().propertyValidator("inputType",CfnFormFieldInputConfigPropertyValidator)(properties.inputType)),errors.collect(cdk().propertyValidator("label",cdk().validateString)(properties.label)),errors.collect(cdk().propertyValidator("position",CfnFormFieldPositionPropertyValidator)(properties.position)),errors.collect(cdk().propertyValidator("validations",cdk().listValidator(CfnFormFieldValidationConfigurationPropertyValidator))(properties.validations)),errors.wrap('supplied properties not correct for "FieldConfigProperty"')}function convertCfnFormFieldConfigPropertyToCloudFormation(properties){return cdk().canInspect(properties)?(CfnFormFieldConfigPropertyValidator(properties).assertSuccess(),{Excluded:cdk().booleanToCloudFormation(properties.excluded),InputType:convertCfnFormFieldInputConfigPropertyToCloudFormation(properties.inputType),Label:cdk().stringToCloudFormation(properties.label),Position:convertCfnFormFieldPositionPropertyToCloudFormation(properties.position),Validations:cdk().listMapper(convertCfnFormFieldValidationConfigurationPropertyToCloudFormation)(properties.validations)}):properties}function CfnFormFieldConfigPropertyFromCloudFormation(properties){if(cdk().isResolvableObject(properties))return new(cfn_parse()).FromCloudFormationResult(properties);if(properties=properties??{},!(properties&&typeof properties=="object"&&!Array.isArray(properties)))return new(cfn_parse()).FromCloudFormationResult(properties);const ret=new(cfn_parse()).FromCloudFormationPropertyObject;return ret.addPropertyResult("excluded","Excluded",properties.Excluded!=null?cfn_parse().FromCloudFormation.getBoolean(properties.Excluded):void 0),ret.addPropertyResult("inputType","InputType",properties.InputType!=null?CfnFormFieldInputConfigPropertyFromCloudFormation(properties.InputType):void 0),ret.addPropertyResult("label","Label",properties.Label!=null?cfn_parse().FromCloudFormation.getString(properties.Label):void 0),ret.addPropertyResult("position","Position",properties.Position!=null?CfnFormFieldPositionPropertyFromCloudFormation(properties.Position):void 0),ret.addPropertyResult("validations","Validations",properties.Validations!=null?cfn_parse().FromCloudFormation.getArray(CfnFormFieldValidationConfigurationPropertyFromCloudFormation)(properties.Validations):void 0),ret.addUnrecognizedPropertiesAsExtra(properties),ret}function CfnFormSectionalElementPropertyValidator(properties){if(!cdk().canInspect(properties))return cdk().VALIDATION_SUCCESS;const errors=new(cdk()).ValidationResults;return properties&&typeof properties=="object"&&!Array.isArray(properties)||errors.collect(new(cdk()).ValidationResult("Expected an object, but received: "+JSON.stringify(properties))),errors.collect(cdk().propertyValidator("excluded",cdk().validateBoolean)(properties.excluded)),errors.collect(cdk().propertyValidator("level",cdk().validateNumber)(properties.level)),errors.collect(cdk().propertyValidator("orientation",cdk().validateString)(properties.orientation)),errors.collect(cdk().propertyValidator("position",CfnFormFieldPositionPropertyValidator)(properties.position)),errors.collect(cdk().propertyValidator("text",cdk().validateString)(properties.text)),errors.collect(cdk().propertyValidator("type",cdk().requiredValidator)(properties.type)),errors.collect(cdk().propertyValidator("type",cdk().validateString)(properties.type)),errors.wrap('supplied properties not correct for "SectionalElementProperty"')}function convertCfnFormSectionalElementPropertyToCloudFormation(properties){return cdk().canInspect(properties)?(CfnFormSectionalElementPropertyValidator(properties).assertSuccess(),{Excluded:cdk().booleanToCloudFormation(properties.excluded),Level:cdk().numberToCloudFormation(properties.level),Orientation:cdk().stringToCloudFormation(properties.orientation),Position:convertCfnFormFieldPositionPropertyToCloudFormation(properties.position),Text:cdk().stringToCloudFormation(properties.text),Type:cdk().stringToCloudFormation(properties.type)}):properties}function CfnFormSectionalElementPropertyFromCloudFormation(properties){if(cdk().isResolvableObject(properties))return new(cfn_parse()).FromCloudFormationResult(properties);if(properties=properties??{},!(properties&&typeof properties=="object"&&!Array.isArray(properties)))return new(cfn_parse()).FromCloudFormationResult(properties);const ret=new(cfn_parse()).FromCloudFormationPropertyObject;return ret.addPropertyResult("excluded","Excluded",properties.Excluded!=null?cfn_parse().FromCloudFormation.getBoolean(properties.Excluded):void 0),ret.addPropertyResult("level","Level",properties.Level!=null?cfn_parse().FromCloudFormation.getNumber(properties.Level):void 0),ret.addPropertyResult("orientation","Orientation",properties.Orientation!=null?cfn_parse().FromCloudFormation.getString(properties.Orientation):void 0),ret.addPropertyResult("position","Position",properties.Position!=null?CfnFormFieldPositionPropertyFromCloudFormation(properties.Position):void 0),ret.addPropertyResult("text","Text",properties.Text!=null?cfn_parse().FromCloudFormation.getString(properties.Text):void 0),ret.addPropertyResult("type","Type",properties.Type!=null?cfn_parse().FromCloudFormation.getString(properties.Type):void 0),ret.addUnrecognizedPropertiesAsExtra(properties),ret}function CfnFormFormDataTypeConfigPropertyValidator(properties){if(!cdk().canInspect(properties))return cdk().VALIDATION_SUCCESS;const errors=new(cdk()).ValidationResults;return properties&&typeof properties=="object"&&!Array.isArray(properties)||errors.collect(new(cdk()).ValidationResult("Expected an object, but received: "+JSON.stringify(properties))),errors.collect(cdk().propertyValidator("dataSourceType",cdk().requiredValidator)(properties.dataSourceType)),errors.collect(cdk().propertyValidator("dataSourceType",cdk().validateString)(properties.dataSourceType)),errors.collect(cdk().propertyValidator("dataTypeName",cdk().requiredValidator)(properties.dataTypeName)),errors.collect(cdk().propertyValidator("dataTypeName",cdk().validateString)(properties.dataTypeName)),errors.wrap('supplied properties not correct for "FormDataTypeConfigProperty"')}function convertCfnFormFormDataTypeConfigPropertyToCloudFormation(properties){return cdk().canInspect(properties)?(CfnFormFormDataTypeConfigPropertyValidator(properties).assertSuccess(),{DataSourceType:cdk().stringToCloudFormation(properties.dataSourceType),DataTypeName:cdk().stringToCloudFormation(properties.dataTypeName)}):properties}function CfnFormFormDataTypeConfigPropertyFromCloudFormation(properties){if(cdk().isResolvableObject(properties))return new(cfn_parse()).FromCloudFormationResult(properties);if(properties=properties??{},!(properties&&typeof properties=="object"&&!Array.isArray(properties)))return new(cfn_parse()).FromCloudFormationResult(properties);const ret=new(cfn_parse()).FromCloudFormationPropertyObject;return ret.addPropertyResult("dataSourceType","DataSourceType",properties.DataSourceType!=null?cfn_parse().FromCloudFormation.getString(properties.DataSourceType):void 0),ret.addPropertyResult("dataTypeName","DataTypeName",properties.DataTypeName!=null?cfn_parse().FromCloudFormation.getString(properties.DataTypeName):void 0),ret.addUnrecognizedPropertiesAsExtra(properties),ret}function CfnFormFormStyleConfigPropertyValidator(properties){if(!cdk().canInspect(properties))return cdk().VALIDATION_SUCCESS;const errors=new(cdk()).ValidationResults;return properties&&typeof properties=="object"&&!Array.isArray(properties)||errors.collect(new(cdk()).ValidationResult("Expected an object, but received: "+JSON.stringify(properties))),errors.collect(cdk().propertyValidator("tokenReference",cdk().validateString)(properties.tokenReference)),errors.collect(cdk().propertyValidator("value",cdk().validateString)(properties.value)),errors.wrap('supplied properties not correct for "FormStyleConfigProperty"')}function convertCfnFormFormStyleConfigPropertyToCloudFormation(properties){return cdk().canInspect(properties)?(CfnFormFormStyleConfigPropertyValidator(properties).assertSuccess(),{TokenReference:cdk().stringToCloudFormation(properties.tokenReference),Value:cdk().stringToCloudFormation(properties.value)}):properties}function CfnFormFormStyleConfigPropertyFromCloudFormation(properties){if(cdk().isResolvableObject(properties))return new(cfn_parse()).FromCloudFormationResult(properties);if(properties=properties??{},!(properties&&typeof properties=="object"&&!Array.isArray(properties)))return new(cfn_parse()).FromCloudFormationResult(properties);const ret=new(cfn_parse()).FromCloudFormationPropertyObject;return ret.addPropertyResult("tokenReference","TokenReference",properties.TokenReference!=null?cfn_parse().FromCloudFormation.getString(properties.TokenReference):void 0),ret.addPropertyResult("value","Value",properties.Value!=null?cfn_parse().FromCloudFormation.getString(properties.Value):void 0),ret.addUnrecognizedPropertiesAsExtra(properties),ret}function CfnFormFormStylePropertyValidator(properties){if(!cdk().canInspect(properties))return cdk().VALIDATION_SUCCESS;const errors=new(cdk()).ValidationResults;return properties&&typeof properties=="object"&&!Array.isArray(properties)||errors.collect(new(cdk()).ValidationResult("Expected an object, but received: "+JSON.stringify(properties))),errors.collect(cdk().propertyValidator("horizontalGap",CfnFormFormStyleConfigPropertyValidator)(properties.horizontalGap)),errors.collect(cdk().propertyValidator("outerPadding",CfnFormFormStyleConfigPropertyValidator)(properties.outerPadding)),errors.collect(cdk().propertyValidator("verticalGap",CfnFormFormStyleConfigPropertyValidator)(properties.verticalGap)),errors.wrap('supplied properties not correct for "FormStyleProperty"')}function convertCfnFormFormStylePropertyToCloudFormation(properties){return cdk().canInspect(properties)?(CfnFormFormStylePropertyValidator(properties).assertSuccess(),{HorizontalGap:convertCfnFormFormStyleConfigPropertyToCloudFormation(properties.horizontalGap),OuterPadding:convertCfnFormFormStyleConfigPropertyToCloudFormation(properties.outerPadding),VerticalGap:convertCfnFormFormStyleConfigPropertyToCloudFormation(properties.verticalGap)}):properties}function CfnFormFormStylePropertyFromCloudFormation(properties){if(cdk().isResolvableObject(properties))return new(cfn_parse()).FromCloudFormationResult(properties);if(properties=properties??{},!(properties&&typeof properties=="object"&&!Array.isArray(properties)))return new(cfn_parse()).FromCloudFormationResult(properties);const ret=new(cfn_parse()).FromCloudFormationPropertyObject;return ret.addPropertyResult("horizontalGap","HorizontalGap",properties.HorizontalGap!=null?CfnFormFormStyleConfigPropertyFromCloudFormation(properties.HorizontalGap):void 0),ret.addPropertyResult("outerPadding","OuterPadding",properties.OuterPadding!=null?CfnFormFormStyleConfigPropertyFromCloudFormation(properties.OuterPadding):void 0),ret.addPropertyResult("verticalGap","VerticalGap",properties.VerticalGap!=null?CfnFormFormStyleConfigPropertyFromCloudFormation(properties.VerticalGap):void 0),ret.addUnrecognizedPropertiesAsExtra(properties),ret}function CfnFormPropsValidator(properties){if(!cdk().canInspect(properties))return cdk().VALIDATION_SUCCESS;const errors=new(cdk()).ValidationResults;return properties&&typeof properties=="object"&&!Array.isArray(properties)||errors.collect(new(cdk()).ValidationResult("Expected an object, but received: "+JSON.stringify(properties))),errors.collect(cdk().propertyValidator("appId",cdk().validateString)(properties.appId)),errors.collect(cdk().propertyValidator("cta",CfnFormFormCTAPropertyValidator)(properties.cta)),errors.collect(cdk().propertyValidator("dataType",CfnFormFormDataTypeConfigPropertyValidator)(properties.dataType)),errors.collect(cdk().propertyValidator("environmentName",cdk().validateString)(properties.environmentName)),errors.collect(cdk().propertyValidator("fields",cdk().hashValidator(CfnFormFieldConfigPropertyValidator))(properties.fields)),errors.collect(cdk().propertyValidator("formActionType",cdk().validateString)(properties.formActionType)),errors.collect(cdk().propertyValidator("labelDecorator",cdk().validateString)(properties.labelDecorator)),errors.collect(cdk().propertyValidator("name",cdk().validateString)(properties.name)),errors.collect(cdk().propertyValidator("schemaVersion",cdk().validateString)(properties.schemaVersion)),errors.collect(cdk().propertyValidator("sectionalElements",cdk().hashValidator(CfnFormSectionalElementPropertyValidator))(properties.sectionalElements)),errors.collect(cdk().propertyValidator("style",CfnFormFormStylePropertyValidator)(properties.style)),errors.collect(cdk().propertyValidator("tags",cdk().hashValidator(cdk().validateString))(properties.tags)),errors.wrap('supplied properties not correct for "CfnFormProps"')}function convertCfnFormPropsToCloudFormation(properties){return cdk().canInspect(properties)?(CfnFormPropsValidator(properties).assertSuccess(),{AppId:cdk().stringToCloudFormation(properties.appId),Cta:convertCfnFormFormCTAPropertyToCloudFormation(properties.cta),DataType:convertCfnFormFormDataTypeConfigPropertyToCloudFormation(properties.dataType),EnvironmentName:cdk().stringToCloudFormation(properties.environmentName),Fields:cdk().hashMapper(convertCfnFormFieldConfigPropertyToCloudFormation)(properties.fields),FormActionType:cdk().stringToCloudFormation(properties.formActionType),LabelDecorator:cdk().stringToCloudFormation(properties.labelDecorator),Name:cdk().stringToCloudFormation(properties.name),SchemaVersion:cdk().stringToCloudFormation(properties.schemaVersion),SectionalElements:cdk().hashMapper(convertCfnFormSectionalElementPropertyToCloudFormation)(properties.sectionalElements),Style:convertCfnFormFormStylePropertyToCloudFormation(properties.style),Tags:cdk().hashMapper(cdk().stringToCloudFormation)(properties.tags)}):properties}function CfnFormPropsFromCloudFormation(properties){if(cdk().isResolvableObject(properties))return new(cfn_parse()).FromCloudFormationResult(properties);if(properties=properties??{},!(properties&&typeof properties=="object"&&!Array.isArray(properties)))return new(cfn_parse()).FromCloudFormationResult(properties);const ret=new(cfn_parse()).FromCloudFormationPropertyObject;return ret.addPropertyResult("appId","AppId",properties.AppId!=null?cfn_parse().FromCloudFormation.getString(properties.AppId):void 0),ret.addPropertyResult("cta","Cta",properties.Cta!=null?CfnFormFormCTAPropertyFromCloudFormation(properties.Cta):void 0),ret.addPropertyResult("dataType","DataType",properties.DataType!=null?CfnFormFormDataTypeConfigPropertyFromCloudFormation(properties.DataType):void 0),ret.addPropertyResult("environmentName","EnvironmentName",properties.EnvironmentName!=null?cfn_parse().FromCloudFormation.getString(properties.EnvironmentName):void 0),ret.addPropertyResult("fields","Fields",properties.Fields!=null?cfn_parse().FromCloudFormation.getMap(CfnFormFieldConfigPropertyFromCloudFormation)(properties.Fields):void 0),ret.addPropertyResult("formActionType","FormActionType",properties.FormActionType!=null?cfn_parse().FromCloudFormation.getString(properties.FormActionType):void 0),ret.addPropertyResult("labelDecorator","LabelDecorator",properties.LabelDecorator!=null?cfn_parse().FromCloudFormation.getString(properties.LabelDecorator):void 0),ret.addPropertyResult("name","Name",properties.Name!=null?cfn_parse().FromCloudFormation.getString(properties.Name):void 0),ret.addPropertyResult("schemaVersion","SchemaVersion",properties.SchemaVersion!=null?cfn_parse().FromCloudFormation.getString(properties.SchemaVersion):void 0),ret.addPropertyResult("sectionalElements","SectionalElements",properties.SectionalElements!=null?cfn_parse().FromCloudFormation.getMap(CfnFormSectionalElementPropertyFromCloudFormation)(properties.SectionalElements):void 0),ret.addPropertyResult("style","Style",properties.Style!=null?CfnFormFormStylePropertyFromCloudFormation(properties.Style):void 0),ret.addPropertyResult("tags","Tags",properties.Tags!=null?cfn_parse().FromCloudFormation.getMap(cfn_parse().FromCloudFormation.getString)(properties.Tags):void 0),ret.addUnrecognizedPropertiesAsExtra(properties),ret}class CfnTheme extends cdk().CfnResource{static _fromCloudFormation(scope,id,resourceAttributes,options){resourceAttributes=resourceAttributes||{};const resourceProperties=options.parser.parseValue(resourceAttributes.Properties),propsResult=CfnThemePropsFromCloudFormation(resourceProperties);if(cdk().isResolvableObject(propsResult.value))throw new(cdk_errors()).ValidationError("Unexpected IResolvable",scope);const ret=new CfnTheme(scope,id,propsResult.value);for(const[propKey,propVal]of Object.entries(propsResult.extraProperties))ret.addPropertyOverride(propKey,propVal);return options.parser.handleAttributes(ret,resourceAttributes,id),ret}constructor(scope,id,props={}){super(scope,id,{type:CfnTheme.CFN_RESOURCE_TYPE_NAME,properties:props});try{jsiiDeprecationWarnings().aws_cdk_lib_aws_amplifyuibuilder_CfnThemeProps(props)}catch(error){throw process.env.JSII_DEBUG!=="1"&&error.name==="DeprecationError"&&Error.captureStackTrace(error,CfnTheme),error}this.attrCreatedAt=cdk().Token.asString(this.getAtt("CreatedAt",cdk().ResolutionTypeHint.STRING)),this.attrId=cdk().Token.asString(this.getAtt("Id",cdk().ResolutionTypeHint.STRING)),this.attrModifiedAt=cdk().Token.asString(this.getAtt("ModifiedAt",cdk().ResolutionTypeHint.STRING)),this.appId=props.appId,this.environmentName=props.environmentName,this.name=props.name,this.overrides=props.overrides,this.tags=new(cdk()).TagManager(cdk().TagType.MAP,"AWS::AmplifyUIBuilder::Theme",props.tags,{tagPropertyName:"tags"}),this.tagsRaw=props.tags,this.values=props.values}get cfnProperties(){return{appId:this.appId,environmentName:this.environmentName,name:this.name,overrides:this.overrides,tags:this.tags.renderTags(),values:this.values}}inspect(inspector){try{jsiiDeprecationWarnings().aws_cdk_lib_TreeInspector(inspector)}catch(error){throw process.env.JSII_DEBUG!=="1"&&error.name==="DeprecationError"&&Error.captureStackTrace(error,this.inspect),error}inspector.addAttribute("aws:cdk:cloudformation:type",CfnTheme.CFN_RESOURCE_TYPE_NAME),inspector.addAttribute("aws:cdk:cloudformation:props",this.cfnProperties)}renderProperties(props){return convertCfnThemePropsToCloudFormation(props)}}exports.CfnTheme=CfnTheme,_c=JSII_RTTI_SYMBOL_1,CfnTheme[_c]={fqn:"aws-cdk-lib.aws_amplifyuibuilder.CfnTheme",version:"2.201.0"},CfnTheme.CFN_RESOURCE_TYPE_NAME="AWS::AmplifyUIBuilder::Theme";function CfnThemeThemeValuePropertyValidator(properties){if(!cdk().canInspect(properties))return cdk().VALIDATION_SUCCESS;const errors=new(cdk()).ValidationResults;return properties&&typeof properties=="object"&&!Array.isArray(properties)||errors.collect(new(cdk()).ValidationResult("Expected an object, but received: "+JSON.stringify(properties))),errors.collect(cdk().propertyValidator("children",cdk().listValidator(CfnThemeThemeValuesPropertyValidator))(properties.children)),errors.collect(cdk().propertyValidator("value",cdk().validateString)(properties.value)),errors.wrap('supplied properties not correct for "ThemeValueProperty"')}function convertCfnThemeThemeValuePropertyToCloudFormation(properties){return cdk().canInspect(properties)?(CfnThemeThemeValuePropertyValidator(properties).assertSuccess(),{Children:cdk().listMapper(convertCfnThemeThemeValuesPropertyToCloudFormation)(properties.children),Value:cdk().stringToCloudFormation(properties.value)}):properties}function CfnThemeThemeValuePropertyFromCloudFormation(properties){if(cdk().isResolvableObject(properties))return new(cfn_parse()).FromCloudFormationResult(properties);if(properties=properties??{},!(properties&&typeof properties=="object"&&!Array.isArray(properties)))return new(cfn_parse()).FromCloudFormationResult(properties);const ret=new(cfn_parse()).FromCloudFormationPropertyObject;return ret.addPropertyResult("children","Children",properties.Children!=null?cfn_parse().FromCloudFormation.getArray(CfnThemeThemeValuesPropertyFromCloudFormation)(properties.Children):void 0),ret.addPropertyResult("value","Value",properties.Value!=null?cfn_parse().FromCloudFormation.getString(properties.Value):void 0),ret.addUnrecognizedPropertiesAsExtra(properties),ret}function CfnThemeThemeValuesPropertyValidator(properties){if(!cdk().canInspect(properties))return cdk().VALIDATION_SUCCESS;const errors=new(cdk()).ValidationResults;return properties&&typeof properties=="object"&&!Array.isArray(properties)||errors.collect(new(cdk()).ValidationResult("Expected an object, but received: "+JSON.stringify(properties))),errors.collect(cdk().propertyValidator("key",cdk().validateString)(properties.key)),errors.collect(cdk().propertyValidator("value",CfnThemeThemeValuePropertyValidator)(properties.value)),errors.wrap('supplied properties not correct for "ThemeValuesProperty"')}function convertCfnThemeThemeValuesPropertyToCloudFormation(properties){return cdk().canInspect(properties)?(CfnThemeThemeValuesPropertyValidator(properties).assertSuccess(),{Key:cdk().stringToCloudFormation(properties.key),Value:convertCfnThemeThemeValuePropertyToCloudFormation(properties.value)}):properties}function CfnThemeThemeValuesPropertyFromCloudFormation(properties){if(cdk().isResolvableObject(properties))return new(cfn_parse()).FromCloudFormationResult(properties);if(properties=properties??{},!(properties&&typeof properties=="object"&&!Array.isArray(properties)))return new(cfn_parse()).FromCloudFormationResult(properties);const ret=new(cfn_parse()).FromCloudFormationPropertyObject;return ret.addPropertyResult("key","Key",properties.Key!=null?cfn_parse().FromCloudFormation.getString(properties.Key):void 0),ret.addPropertyResult("value","Value",properties.Value!=null?CfnThemeThemeValuePropertyFromCloudFormation(properties.Value):void 0),ret.addUnrecognizedPropertiesAsExtra(properties),ret}function CfnThemePropsValidator(properties){if(!cdk().canInspect(properties))return cdk().VALIDATION_SUCCESS;const errors=new(cdk()).ValidationResults;return properties&&typeof properties=="object"&&!Array.isArray(properties)||errors.collect(new(cdk()).ValidationResult("Expected an object, but received: "+JSON.stringify(properties))),errors.collect(cdk().propertyValidator("appId",cdk().validateString)(properties.appId)),errors.collect(cdk().propertyValidator("environmentName",cdk().validateString)(properties.environmentName)),errors.collect(cdk().propertyValidator("name",cdk().validateString)(properties.name)),errors.collect(cdk().propertyValidator("overrides",cdk().listValidator(CfnThemeThemeValuesPropertyValidator))(properties.overrides)),errors.collect(cdk().propertyValidator("tags",cdk().hashValidator(cdk().validateString))(properties.tags)),errors.collect(cdk().propertyValidator("values",cdk().listValidator(CfnThemeThemeValuesPropertyValidator))(properties.values)),errors.wrap('supplied properties not correct for "CfnThemeProps"')}function convertCfnThemePropsToCloudFormation(properties){return cdk().canInspect(properties)?(CfnThemePropsValidator(properties).assertSuccess(),{AppId:cdk().stringToCloudFormation(properties.appId),EnvironmentName:cdk().stringToCloudFormation(properties.environmentName),Name:cdk().stringToCloudFormation(properties.name),Overrides:cdk().listMapper(convertCfnThemeThemeValuesPropertyToCloudFormation)(properties.overrides),Tags:cdk().hashMapper(cdk().stringToCloudFormation)(properties.tags),Values:cdk().listMapper(convertCfnThemeThemeValuesPropertyToCloudFormation)(properties.values)}):properties}function CfnThemePropsFromCloudFormation(properties){if(cdk().isResolvableObject(properties))return new(cfn_parse()).FromCloudFormationResult(properties);if(properties=properties??{},!(properties&&typeof properties=="object"&&!Array.isArray(properties)))return new(cfn_parse()).FromCloudFormationResult(properties);const ret=new(cfn_parse()).FromCloudFormationPropertyObject;return ret.addPropertyResult("appId","AppId",properties.AppId!=null?cfn_parse().FromCloudFormation.getString(properties.AppId):void 0),ret.addPropertyResult("environmentName","EnvironmentName",properties.EnvironmentName!=null?cfn_parse().FromCloudFormation.getString(properties.EnvironmentName):void 0),ret.addPropertyResult("name","Name",properties.Name!=null?cfn_parse().FromCloudFormation.getString(properties.Name):void 0),ret.addPropertyResult("overrides","Overrides",properties.Overrides!=null?cfn_parse().FromCloudFormation.getArray(CfnThemeThemeValuesPropertyFromCloudFormation)(properties.Overrides):void 0),ret.addPropertyResult("tags","Tags",properties.Tags!=null?cfn_parse().FromCloudFormation.getMap(cfn_parse().FromCloudFormation.getString)(properties.Tags):void 0),ret.addPropertyResult("values","Values",properties.Values!=null?cfn_parse().FromCloudFormation.getArray(CfnThemeThemeValuesPropertyFromCloudFormation)(properties.Values):void 0),ret.addUnrecognizedPropertiesAsExtra(properties),ret}
