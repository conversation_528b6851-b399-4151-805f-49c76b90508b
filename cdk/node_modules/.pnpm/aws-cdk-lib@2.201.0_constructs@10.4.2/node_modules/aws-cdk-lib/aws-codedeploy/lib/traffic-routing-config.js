"use strict";var _a,_b,_c,_d;Object.defineProperty(exports,"__esModule",{value:!0}),exports.TimeBasedLinearTrafficRouting=exports.TimeBasedCanaryTrafficRouting=exports.AllAtOnceTrafficRouting=exports.TrafficRouting=void 0;var jsiiDeprecationWarnings=()=>{var tmp=require("../../.warnings.jsii.js");return jsiiDeprecationWarnings=()=>tmp,tmp};const JSII_RTTI_SYMBOL_1=Symbol.for("jsii.rtti");class TrafficRouting{static allAtOnce(){return new AllAtOnceTrafficRouting}static timeBasedCanary(props){try{jsiiDeprecationWarnings().aws_cdk_lib_aws_codedeploy_TimeBasedCanaryTrafficRoutingProps(props)}catch(error){throw process.env.JSII_DEBUG!=="1"&&error.name==="DeprecationError"&&Error.captureStackTrace(error,this.timeBasedCanary),error}return new TimeBasedCanaryTrafficRouting(props)}static timeBasedLinear(props){try{jsiiDeprecationWarnings().aws_cdk_lib_aws_codedeploy_TimeBasedLinearTrafficRoutingProps(props)}catch(error){throw process.env.JSII_DEBUG!=="1"&&error.name==="DeprecationError"&&Error.captureStackTrace(error,this.timeBasedLinear),error}return new TimeBasedLinearTrafficRouting(props)}}exports.TrafficRouting=TrafficRouting,_a=JSII_RTTI_SYMBOL_1,TrafficRouting[_a]={fqn:"aws-cdk-lib.aws_codedeploy.TrafficRouting",version:"2.201.0"};class AllAtOnceTrafficRouting extends TrafficRouting{constructor(){super()}bind(_scope){return{type:"AllAtOnce"}}}exports.AllAtOnceTrafficRouting=AllAtOnceTrafficRouting,_b=JSII_RTTI_SYMBOL_1,AllAtOnceTrafficRouting[_b]={fqn:"aws-cdk-lib.aws_codedeploy.AllAtOnceTrafficRouting",version:"2.201.0"};class TimeBasedCanaryTrafficRouting extends TrafficRouting{constructor(props){super();try{jsiiDeprecationWarnings().aws_cdk_lib_aws_codedeploy_TimeBasedCanaryTrafficRoutingProps(props)}catch(error){throw process.env.JSII_DEBUG!=="1"&&error.name==="DeprecationError"&&Error.captureStackTrace(error,TimeBasedCanaryTrafficRouting),error}this.interval=props.interval,this.percentage=props.percentage}bind(_scope){return{type:"TimeBasedCanary",timeBasedCanary:{canaryInterval:this.interval.toMinutes(),canaryPercentage:this.percentage}}}}exports.TimeBasedCanaryTrafficRouting=TimeBasedCanaryTrafficRouting,_c=JSII_RTTI_SYMBOL_1,TimeBasedCanaryTrafficRouting[_c]={fqn:"aws-cdk-lib.aws_codedeploy.TimeBasedCanaryTrafficRouting",version:"2.201.0"};class TimeBasedLinearTrafficRouting extends TrafficRouting{constructor(props){super();try{jsiiDeprecationWarnings().aws_cdk_lib_aws_codedeploy_TimeBasedLinearTrafficRoutingProps(props)}catch(error){throw process.env.JSII_DEBUG!=="1"&&error.name==="DeprecationError"&&Error.captureStackTrace(error,TimeBasedLinearTrafficRouting),error}this.interval=props.interval,this.percentage=props.percentage}bind(_scope){return{type:"TimeBasedLinear",timeBasedLinear:{linearInterval:this.interval.toMinutes(),linearPercentage:this.percentage}}}}exports.TimeBasedLinearTrafficRouting=TimeBasedLinearTrafficRouting,_d=JSII_RTTI_SYMBOL_1,TimeBasedLinearTrafficRouting[_d]={fqn:"aws-cdk-lib.aws_codedeploy.TimeBasedLinearTrafficRouting",version:"2.201.0"};
