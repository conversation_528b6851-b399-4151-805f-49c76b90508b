"use strict";var __decorate=exports&&exports.__decorate||function(decorators,target,key,desc){var c=arguments.length,r=c<3?target:desc===null?desc=Object.getOwnPropertyDescriptor(target,key):desc,d;if(typeof Reflect=="object"&&typeof Reflect.decorate=="function")r=Reflect.decorate(decorators,target,key,desc);else for(var i=decorators.length-1;i>=0;i--)(d=decorators[i])&&(r=(c<3?d(r):c>3?d(target,key,r):d(target,key))||r);return c>3&&r&&Object.defineProperty(target,key,r),r},_a,ServerDeploymentConfig_1;Object.defineProperty(exports,"__esModule",{value:!0}),exports.ServerDeploymentConfig=void 0;var jsiiDeprecationWarnings=()=>{var tmp=require("../../../.warnings.jsii.js");return jsiiDeprecationWarnings=()=>tmp,tmp};const JSII_RTTI_SYMBOL_1=Symbol.for("jsii.rtti");var metadata_resource_1=()=>{var tmp=require("../../../core/lib/metadata-resource");return metadata_resource_1=()=>tmp,tmp},prop_injectable_1=()=>{var tmp=require("../../../core/lib/prop-injectable");return prop_injectable_1=()=>tmp,tmp},base_deployment_config_1=()=>{var tmp=require("../base-deployment-config");return base_deployment_config_1=()=>tmp,tmp},utils_1=()=>{var tmp=require("../private/utils");return utils_1=()=>tmp,tmp};let ServerDeploymentConfig=ServerDeploymentConfig_1=class ServerDeploymentConfig2 extends base_deployment_config_1().BaseDeploymentConfig{static fromServerDeploymentConfigName(scope,id,serverDeploymentConfigName){return this.fromDeploymentConfigName(scope,id,serverDeploymentConfigName)}static deploymentConfig(name){return(0,utils_1().deploymentConfig)(name)}constructor(scope,id,props){super(scope,id,props);try{jsiiDeprecationWarnings().aws_cdk_lib_aws_codedeploy_ServerDeploymentConfigProps(props)}catch(error){throw process.env.JSII_DEBUG!=="1"&&error.name==="DeprecationError"&&Error.captureStackTrace(error,ServerDeploymentConfig2),error}(0,metadata_resource_1().addConstructMetadata)(this,props)}};exports.ServerDeploymentConfig=ServerDeploymentConfig,_a=JSII_RTTI_SYMBOL_1,ServerDeploymentConfig[_a]={fqn:"aws-cdk-lib.aws_codedeploy.ServerDeploymentConfig",version:"2.201.0"},ServerDeploymentConfig.PROPERTY_INJECTION_ID="aws-cdk-lib.aws-codedeploy.ServerDeploymentConfig",ServerDeploymentConfig.ONE_AT_A_TIME=ServerDeploymentConfig_1.deploymentConfig("CodeDeployDefault.OneAtATime"),ServerDeploymentConfig.HALF_AT_A_TIME=ServerDeploymentConfig_1.deploymentConfig("CodeDeployDefault.HalfAtATime"),ServerDeploymentConfig.ALL_AT_ONCE=ServerDeploymentConfig_1.deploymentConfig("CodeDeployDefault.AllAtOnce"),exports.ServerDeploymentConfig=ServerDeploymentConfig=ServerDeploymentConfig_1=__decorate([prop_injectable_1().propertyInjectable],ServerDeploymentConfig);
