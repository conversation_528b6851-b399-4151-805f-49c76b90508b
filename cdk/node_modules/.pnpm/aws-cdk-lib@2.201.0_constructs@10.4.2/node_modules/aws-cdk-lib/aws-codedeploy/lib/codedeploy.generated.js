"use strict";var _a,_b,_c;Object.defineProperty(exports,"__esModule",{value:!0}),exports.CfnDeploymentGroup=exports.CfnDeploymentConfig=exports.CfnApplication=void 0;var jsiiDeprecationWarnings=()=>{var tmp=require("../../.warnings.jsii.js");return jsiiDeprecationWarnings=()=>tmp,tmp};const JSII_RTTI_SYMBOL_1=Symbol.for("jsii.rtti");var cdk=()=>{var tmp=require("../../core");return cdk=()=>tmp,tmp},cfn_parse=()=>{var tmp=require("../../core/lib/helpers-internal");return cfn_parse=()=>tmp,tmp},cdk_errors=()=>{var tmp=require("../../core/lib/errors");return cdk_errors=()=>tmp,tmp};class CfnApplication extends cdk().CfnResource{static _fromCloudFormation(scope,id,resourceAttributes,options){resourceAttributes=resourceAttributes||{};const resourceProperties=options.parser.parseValue(resourceAttributes.Properties),propsResult=CfnApplicationPropsFromCloudFormation(resourceProperties);if(cdk().isResolvableObject(propsResult.value))throw new(cdk_errors()).ValidationError("Unexpected IResolvable",scope);const ret=new CfnApplication(scope,id,propsResult.value);for(const[propKey,propVal]of Object.entries(propsResult.extraProperties))ret.addPropertyOverride(propKey,propVal);return options.parser.handleAttributes(ret,resourceAttributes,id),ret}constructor(scope,id,props={}){super(scope,id,{type:CfnApplication.CFN_RESOURCE_TYPE_NAME,properties:props});try{jsiiDeprecationWarnings().aws_cdk_lib_aws_codedeploy_CfnApplicationProps(props)}catch(error){throw process.env.JSII_DEBUG!=="1"&&error.name==="DeprecationError"&&Error.captureStackTrace(error,CfnApplication),error}this.applicationName=props.applicationName,this.computePlatform=props.computePlatform,this.tags=new(cdk()).TagManager(cdk().TagType.STANDARD,"AWS::CodeDeploy::Application",props.tags,{tagPropertyName:"tags"}),this.tagsRaw=props.tags}get cfnProperties(){return{applicationName:this.applicationName,computePlatform:this.computePlatform,tags:this.tags.renderTags()}}inspect(inspector){try{jsiiDeprecationWarnings().aws_cdk_lib_TreeInspector(inspector)}catch(error){throw process.env.JSII_DEBUG!=="1"&&error.name==="DeprecationError"&&Error.captureStackTrace(error,this.inspect),error}inspector.addAttribute("aws:cdk:cloudformation:type",CfnApplication.CFN_RESOURCE_TYPE_NAME),inspector.addAttribute("aws:cdk:cloudformation:props",this.cfnProperties)}renderProperties(props){return convertCfnApplicationPropsToCloudFormation(props)}}exports.CfnApplication=CfnApplication,_a=JSII_RTTI_SYMBOL_1,CfnApplication[_a]={fqn:"aws-cdk-lib.aws_codedeploy.CfnApplication",version:"2.201.0"},CfnApplication.CFN_RESOURCE_TYPE_NAME="AWS::CodeDeploy::Application";function CfnApplicationPropsValidator(properties){if(!cdk().canInspect(properties))return cdk().VALIDATION_SUCCESS;const errors=new(cdk()).ValidationResults;return properties&&typeof properties=="object"&&!Array.isArray(properties)||errors.collect(new(cdk()).ValidationResult("Expected an object, but received: "+JSON.stringify(properties))),errors.collect(cdk().propertyValidator("applicationName",cdk().validateString)(properties.applicationName)),errors.collect(cdk().propertyValidator("computePlatform",cdk().validateString)(properties.computePlatform)),errors.collect(cdk().propertyValidator("tags",cdk().listValidator(cdk().validateCfnTag))(properties.tags)),errors.wrap('supplied properties not correct for "CfnApplicationProps"')}function convertCfnApplicationPropsToCloudFormation(properties){return cdk().canInspect(properties)?(CfnApplicationPropsValidator(properties).assertSuccess(),{ApplicationName:cdk().stringToCloudFormation(properties.applicationName),ComputePlatform:cdk().stringToCloudFormation(properties.computePlatform),Tags:cdk().listMapper(cdk().cfnTagToCloudFormation)(properties.tags)}):properties}function CfnApplicationPropsFromCloudFormation(properties){if(cdk().isResolvableObject(properties))return new(cfn_parse()).FromCloudFormationResult(properties);if(properties=properties??{},!(properties&&typeof properties=="object"&&!Array.isArray(properties)))return new(cfn_parse()).FromCloudFormationResult(properties);const ret=new(cfn_parse()).FromCloudFormationPropertyObject;return ret.addPropertyResult("applicationName","ApplicationName",properties.ApplicationName!=null?cfn_parse().FromCloudFormation.getString(properties.ApplicationName):void 0),ret.addPropertyResult("computePlatform","ComputePlatform",properties.ComputePlatform!=null?cfn_parse().FromCloudFormation.getString(properties.ComputePlatform):void 0),ret.addPropertyResult("tags","Tags",properties.Tags!=null?cfn_parse().FromCloudFormation.getArray(cfn_parse().FromCloudFormation.getCfnTag)(properties.Tags):void 0),ret.addUnrecognizedPropertiesAsExtra(properties),ret}class CfnDeploymentConfig extends cdk().CfnResource{static _fromCloudFormation(scope,id,resourceAttributes,options){resourceAttributes=resourceAttributes||{};const resourceProperties=options.parser.parseValue(resourceAttributes.Properties),propsResult=CfnDeploymentConfigPropsFromCloudFormation(resourceProperties);if(cdk().isResolvableObject(propsResult.value))throw new(cdk_errors()).ValidationError("Unexpected IResolvable",scope);const ret=new CfnDeploymentConfig(scope,id,propsResult.value);for(const[propKey,propVal]of Object.entries(propsResult.extraProperties))ret.addPropertyOverride(propKey,propVal);return options.parser.handleAttributes(ret,resourceAttributes,id),ret}constructor(scope,id,props={}){super(scope,id,{type:CfnDeploymentConfig.CFN_RESOURCE_TYPE_NAME,properties:props});try{jsiiDeprecationWarnings().aws_cdk_lib_aws_codedeploy_CfnDeploymentConfigProps(props)}catch(error){throw process.env.JSII_DEBUG!=="1"&&error.name==="DeprecationError"&&Error.captureStackTrace(error,CfnDeploymentConfig),error}this.computePlatform=props.computePlatform,this.deploymentConfigName=props.deploymentConfigName,this.minimumHealthyHosts=props.minimumHealthyHosts,this.trafficRoutingConfig=props.trafficRoutingConfig,this.zonalConfig=props.zonalConfig}get cfnProperties(){return{computePlatform:this.computePlatform,deploymentConfigName:this.deploymentConfigName,minimumHealthyHosts:this.minimumHealthyHosts,trafficRoutingConfig:this.trafficRoutingConfig,zonalConfig:this.zonalConfig}}inspect(inspector){try{jsiiDeprecationWarnings().aws_cdk_lib_TreeInspector(inspector)}catch(error){throw process.env.JSII_DEBUG!=="1"&&error.name==="DeprecationError"&&Error.captureStackTrace(error,this.inspect),error}inspector.addAttribute("aws:cdk:cloudformation:type",CfnDeploymentConfig.CFN_RESOURCE_TYPE_NAME),inspector.addAttribute("aws:cdk:cloudformation:props",this.cfnProperties)}renderProperties(props){return convertCfnDeploymentConfigPropsToCloudFormation(props)}}exports.CfnDeploymentConfig=CfnDeploymentConfig,_b=JSII_RTTI_SYMBOL_1,CfnDeploymentConfig[_b]={fqn:"aws-cdk-lib.aws_codedeploy.CfnDeploymentConfig",version:"2.201.0"},CfnDeploymentConfig.CFN_RESOURCE_TYPE_NAME="AWS::CodeDeploy::DeploymentConfig";function CfnDeploymentConfigTimeBasedLinearPropertyValidator(properties){if(!cdk().canInspect(properties))return cdk().VALIDATION_SUCCESS;const errors=new(cdk()).ValidationResults;return properties&&typeof properties=="object"&&!Array.isArray(properties)||errors.collect(new(cdk()).ValidationResult("Expected an object, but received: "+JSON.stringify(properties))),errors.collect(cdk().propertyValidator("linearInterval",cdk().requiredValidator)(properties.linearInterval)),errors.collect(cdk().propertyValidator("linearInterval",cdk().validateNumber)(properties.linearInterval)),errors.collect(cdk().propertyValidator("linearPercentage",cdk().requiredValidator)(properties.linearPercentage)),errors.collect(cdk().propertyValidator("linearPercentage",cdk().validateNumber)(properties.linearPercentage)),errors.wrap('supplied properties not correct for "TimeBasedLinearProperty"')}function convertCfnDeploymentConfigTimeBasedLinearPropertyToCloudFormation(properties){return cdk().canInspect(properties)?(CfnDeploymentConfigTimeBasedLinearPropertyValidator(properties).assertSuccess(),{LinearInterval:cdk().numberToCloudFormation(properties.linearInterval),LinearPercentage:cdk().numberToCloudFormation(properties.linearPercentage)}):properties}function CfnDeploymentConfigTimeBasedLinearPropertyFromCloudFormation(properties){if(cdk().isResolvableObject(properties))return new(cfn_parse()).FromCloudFormationResult(properties);if(properties=properties??{},!(properties&&typeof properties=="object"&&!Array.isArray(properties)))return new(cfn_parse()).FromCloudFormationResult(properties);const ret=new(cfn_parse()).FromCloudFormationPropertyObject;return ret.addPropertyResult("linearInterval","LinearInterval",properties.LinearInterval!=null?cfn_parse().FromCloudFormation.getNumber(properties.LinearInterval):void 0),ret.addPropertyResult("linearPercentage","LinearPercentage",properties.LinearPercentage!=null?cfn_parse().FromCloudFormation.getNumber(properties.LinearPercentage):void 0),ret.addUnrecognizedPropertiesAsExtra(properties),ret}function CfnDeploymentConfigTimeBasedCanaryPropertyValidator(properties){if(!cdk().canInspect(properties))return cdk().VALIDATION_SUCCESS;const errors=new(cdk()).ValidationResults;return properties&&typeof properties=="object"&&!Array.isArray(properties)||errors.collect(new(cdk()).ValidationResult("Expected an object, but received: "+JSON.stringify(properties))),errors.collect(cdk().propertyValidator("canaryInterval",cdk().requiredValidator)(properties.canaryInterval)),errors.collect(cdk().propertyValidator("canaryInterval",cdk().validateNumber)(properties.canaryInterval)),errors.collect(cdk().propertyValidator("canaryPercentage",cdk().requiredValidator)(properties.canaryPercentage)),errors.collect(cdk().propertyValidator("canaryPercentage",cdk().validateNumber)(properties.canaryPercentage)),errors.wrap('supplied properties not correct for "TimeBasedCanaryProperty"')}function convertCfnDeploymentConfigTimeBasedCanaryPropertyToCloudFormation(properties){return cdk().canInspect(properties)?(CfnDeploymentConfigTimeBasedCanaryPropertyValidator(properties).assertSuccess(),{CanaryInterval:cdk().numberToCloudFormation(properties.canaryInterval),CanaryPercentage:cdk().numberToCloudFormation(properties.canaryPercentage)}):properties}function CfnDeploymentConfigTimeBasedCanaryPropertyFromCloudFormation(properties){if(cdk().isResolvableObject(properties))return new(cfn_parse()).FromCloudFormationResult(properties);if(properties=properties??{},!(properties&&typeof properties=="object"&&!Array.isArray(properties)))return new(cfn_parse()).FromCloudFormationResult(properties);const ret=new(cfn_parse()).FromCloudFormationPropertyObject;return ret.addPropertyResult("canaryInterval","CanaryInterval",properties.CanaryInterval!=null?cfn_parse().FromCloudFormation.getNumber(properties.CanaryInterval):void 0),ret.addPropertyResult("canaryPercentage","CanaryPercentage",properties.CanaryPercentage!=null?cfn_parse().FromCloudFormation.getNumber(properties.CanaryPercentage):void 0),ret.addUnrecognizedPropertiesAsExtra(properties),ret}function CfnDeploymentConfigTrafficRoutingConfigPropertyValidator(properties){if(!cdk().canInspect(properties))return cdk().VALIDATION_SUCCESS;const errors=new(cdk()).ValidationResults;return properties&&typeof properties=="object"&&!Array.isArray(properties)||errors.collect(new(cdk()).ValidationResult("Expected an object, but received: "+JSON.stringify(properties))),errors.collect(cdk().propertyValidator("timeBasedCanary",CfnDeploymentConfigTimeBasedCanaryPropertyValidator)(properties.timeBasedCanary)),errors.collect(cdk().propertyValidator("timeBasedLinear",CfnDeploymentConfigTimeBasedLinearPropertyValidator)(properties.timeBasedLinear)),errors.collect(cdk().propertyValidator("type",cdk().requiredValidator)(properties.type)),errors.collect(cdk().propertyValidator("type",cdk().validateString)(properties.type)),errors.wrap('supplied properties not correct for "TrafficRoutingConfigProperty"')}function convertCfnDeploymentConfigTrafficRoutingConfigPropertyToCloudFormation(properties){return cdk().canInspect(properties)?(CfnDeploymentConfigTrafficRoutingConfigPropertyValidator(properties).assertSuccess(),{TimeBasedCanary:convertCfnDeploymentConfigTimeBasedCanaryPropertyToCloudFormation(properties.timeBasedCanary),TimeBasedLinear:convertCfnDeploymentConfigTimeBasedLinearPropertyToCloudFormation(properties.timeBasedLinear),Type:cdk().stringToCloudFormation(properties.type)}):properties}function CfnDeploymentConfigTrafficRoutingConfigPropertyFromCloudFormation(properties){if(cdk().isResolvableObject(properties))return new(cfn_parse()).FromCloudFormationResult(properties);if(properties=properties??{},!(properties&&typeof properties=="object"&&!Array.isArray(properties)))return new(cfn_parse()).FromCloudFormationResult(properties);const ret=new(cfn_parse()).FromCloudFormationPropertyObject;return ret.addPropertyResult("timeBasedCanary","TimeBasedCanary",properties.TimeBasedCanary!=null?CfnDeploymentConfigTimeBasedCanaryPropertyFromCloudFormation(properties.TimeBasedCanary):void 0),ret.addPropertyResult("timeBasedLinear","TimeBasedLinear",properties.TimeBasedLinear!=null?CfnDeploymentConfigTimeBasedLinearPropertyFromCloudFormation(properties.TimeBasedLinear):void 0),ret.addPropertyResult("type","Type",properties.Type!=null?cfn_parse().FromCloudFormation.getString(properties.Type):void 0),ret.addUnrecognizedPropertiesAsExtra(properties),ret}function CfnDeploymentConfigMinimumHealthyHostsPropertyValidator(properties){if(!cdk().canInspect(properties))return cdk().VALIDATION_SUCCESS;const errors=new(cdk()).ValidationResults;return properties&&typeof properties=="object"&&!Array.isArray(properties)||errors.collect(new(cdk()).ValidationResult("Expected an object, but received: "+JSON.stringify(properties))),errors.collect(cdk().propertyValidator("type",cdk().requiredValidator)(properties.type)),errors.collect(cdk().propertyValidator("type",cdk().validateString)(properties.type)),errors.collect(cdk().propertyValidator("value",cdk().requiredValidator)(properties.value)),errors.collect(cdk().propertyValidator("value",cdk().validateNumber)(properties.value)),errors.wrap('supplied properties not correct for "MinimumHealthyHostsProperty"')}function convertCfnDeploymentConfigMinimumHealthyHostsPropertyToCloudFormation(properties){return cdk().canInspect(properties)?(CfnDeploymentConfigMinimumHealthyHostsPropertyValidator(properties).assertSuccess(),{Type:cdk().stringToCloudFormation(properties.type),Value:cdk().numberToCloudFormation(properties.value)}):properties}function CfnDeploymentConfigMinimumHealthyHostsPropertyFromCloudFormation(properties){if(cdk().isResolvableObject(properties))return new(cfn_parse()).FromCloudFormationResult(properties);if(properties=properties??{},!(properties&&typeof properties=="object"&&!Array.isArray(properties)))return new(cfn_parse()).FromCloudFormationResult(properties);const ret=new(cfn_parse()).FromCloudFormationPropertyObject;return ret.addPropertyResult("type","Type",properties.Type!=null?cfn_parse().FromCloudFormation.getString(properties.Type):void 0),ret.addPropertyResult("value","Value",properties.Value!=null?cfn_parse().FromCloudFormation.getNumber(properties.Value):void 0),ret.addUnrecognizedPropertiesAsExtra(properties),ret}function CfnDeploymentConfigMinimumHealthyHostsPerZonePropertyValidator(properties){if(!cdk().canInspect(properties))return cdk().VALIDATION_SUCCESS;const errors=new(cdk()).ValidationResults;return properties&&typeof properties=="object"&&!Array.isArray(properties)||errors.collect(new(cdk()).ValidationResult("Expected an object, but received: "+JSON.stringify(properties))),errors.collect(cdk().propertyValidator("type",cdk().requiredValidator)(properties.type)),errors.collect(cdk().propertyValidator("type",cdk().validateString)(properties.type)),errors.collect(cdk().propertyValidator("value",cdk().requiredValidator)(properties.value)),errors.collect(cdk().propertyValidator("value",cdk().validateNumber)(properties.value)),errors.wrap('supplied properties not correct for "MinimumHealthyHostsPerZoneProperty"')}function convertCfnDeploymentConfigMinimumHealthyHostsPerZonePropertyToCloudFormation(properties){return cdk().canInspect(properties)?(CfnDeploymentConfigMinimumHealthyHostsPerZonePropertyValidator(properties).assertSuccess(),{Type:cdk().stringToCloudFormation(properties.type),Value:cdk().numberToCloudFormation(properties.value)}):properties}function CfnDeploymentConfigMinimumHealthyHostsPerZonePropertyFromCloudFormation(properties){if(cdk().isResolvableObject(properties))return new(cfn_parse()).FromCloudFormationResult(properties);if(properties=properties??{},!(properties&&typeof properties=="object"&&!Array.isArray(properties)))return new(cfn_parse()).FromCloudFormationResult(properties);const ret=new(cfn_parse()).FromCloudFormationPropertyObject;return ret.addPropertyResult("type","Type",properties.Type!=null?cfn_parse().FromCloudFormation.getString(properties.Type):void 0),ret.addPropertyResult("value","Value",properties.Value!=null?cfn_parse().FromCloudFormation.getNumber(properties.Value):void 0),ret.addUnrecognizedPropertiesAsExtra(properties),ret}function CfnDeploymentConfigZonalConfigPropertyValidator(properties){if(!cdk().canInspect(properties))return cdk().VALIDATION_SUCCESS;const errors=new(cdk()).ValidationResults;return properties&&typeof properties=="object"&&!Array.isArray(properties)||errors.collect(new(cdk()).ValidationResult("Expected an object, but received: "+JSON.stringify(properties))),errors.collect(cdk().propertyValidator("firstZoneMonitorDurationInSeconds",cdk().validateNumber)(properties.firstZoneMonitorDurationInSeconds)),errors.collect(cdk().propertyValidator("minimumHealthyHostsPerZone",CfnDeploymentConfigMinimumHealthyHostsPerZonePropertyValidator)(properties.minimumHealthyHostsPerZone)),errors.collect(cdk().propertyValidator("monitorDurationInSeconds",cdk().validateNumber)(properties.monitorDurationInSeconds)),errors.wrap('supplied properties not correct for "ZonalConfigProperty"')}function convertCfnDeploymentConfigZonalConfigPropertyToCloudFormation(properties){return cdk().canInspect(properties)?(CfnDeploymentConfigZonalConfigPropertyValidator(properties).assertSuccess(),{FirstZoneMonitorDurationInSeconds:cdk().numberToCloudFormation(properties.firstZoneMonitorDurationInSeconds),MinimumHealthyHostsPerZone:convertCfnDeploymentConfigMinimumHealthyHostsPerZonePropertyToCloudFormation(properties.minimumHealthyHostsPerZone),MonitorDurationInSeconds:cdk().numberToCloudFormation(properties.monitorDurationInSeconds)}):properties}function CfnDeploymentConfigZonalConfigPropertyFromCloudFormation(properties){if(cdk().isResolvableObject(properties))return new(cfn_parse()).FromCloudFormationResult(properties);if(properties=properties??{},!(properties&&typeof properties=="object"&&!Array.isArray(properties)))return new(cfn_parse()).FromCloudFormationResult(properties);const ret=new(cfn_parse()).FromCloudFormationPropertyObject;return ret.addPropertyResult("firstZoneMonitorDurationInSeconds","FirstZoneMonitorDurationInSeconds",properties.FirstZoneMonitorDurationInSeconds!=null?cfn_parse().FromCloudFormation.getNumber(properties.FirstZoneMonitorDurationInSeconds):void 0),ret.addPropertyResult("minimumHealthyHostsPerZone","MinimumHealthyHostsPerZone",properties.MinimumHealthyHostsPerZone!=null?CfnDeploymentConfigMinimumHealthyHostsPerZonePropertyFromCloudFormation(properties.MinimumHealthyHostsPerZone):void 0),ret.addPropertyResult("monitorDurationInSeconds","MonitorDurationInSeconds",properties.MonitorDurationInSeconds!=null?cfn_parse().FromCloudFormation.getNumber(properties.MonitorDurationInSeconds):void 0),ret.addUnrecognizedPropertiesAsExtra(properties),ret}function CfnDeploymentConfigPropsValidator(properties){if(!cdk().canInspect(properties))return cdk().VALIDATION_SUCCESS;const errors=new(cdk()).ValidationResults;return properties&&typeof properties=="object"&&!Array.isArray(properties)||errors.collect(new(cdk()).ValidationResult("Expected an object, but received: "+JSON.stringify(properties))),errors.collect(cdk().propertyValidator("computePlatform",cdk().validateString)(properties.computePlatform)),errors.collect(cdk().propertyValidator("deploymentConfigName",cdk().validateString)(properties.deploymentConfigName)),errors.collect(cdk().propertyValidator("minimumHealthyHosts",CfnDeploymentConfigMinimumHealthyHostsPropertyValidator)(properties.minimumHealthyHosts)),errors.collect(cdk().propertyValidator("trafficRoutingConfig",CfnDeploymentConfigTrafficRoutingConfigPropertyValidator)(properties.trafficRoutingConfig)),errors.collect(cdk().propertyValidator("zonalConfig",CfnDeploymentConfigZonalConfigPropertyValidator)(properties.zonalConfig)),errors.wrap('supplied properties not correct for "CfnDeploymentConfigProps"')}function convertCfnDeploymentConfigPropsToCloudFormation(properties){return cdk().canInspect(properties)?(CfnDeploymentConfigPropsValidator(properties).assertSuccess(),{ComputePlatform:cdk().stringToCloudFormation(properties.computePlatform),DeploymentConfigName:cdk().stringToCloudFormation(properties.deploymentConfigName),MinimumHealthyHosts:convertCfnDeploymentConfigMinimumHealthyHostsPropertyToCloudFormation(properties.minimumHealthyHosts),TrafficRoutingConfig:convertCfnDeploymentConfigTrafficRoutingConfigPropertyToCloudFormation(properties.trafficRoutingConfig),ZonalConfig:convertCfnDeploymentConfigZonalConfigPropertyToCloudFormation(properties.zonalConfig)}):properties}function CfnDeploymentConfigPropsFromCloudFormation(properties){if(cdk().isResolvableObject(properties))return new(cfn_parse()).FromCloudFormationResult(properties);if(properties=properties??{},!(properties&&typeof properties=="object"&&!Array.isArray(properties)))return new(cfn_parse()).FromCloudFormationResult(properties);const ret=new(cfn_parse()).FromCloudFormationPropertyObject;return ret.addPropertyResult("computePlatform","ComputePlatform",properties.ComputePlatform!=null?cfn_parse().FromCloudFormation.getString(properties.ComputePlatform):void 0),ret.addPropertyResult("deploymentConfigName","DeploymentConfigName",properties.DeploymentConfigName!=null?cfn_parse().FromCloudFormation.getString(properties.DeploymentConfigName):void 0),ret.addPropertyResult("minimumHealthyHosts","MinimumHealthyHosts",properties.MinimumHealthyHosts!=null?CfnDeploymentConfigMinimumHealthyHostsPropertyFromCloudFormation(properties.MinimumHealthyHosts):void 0),ret.addPropertyResult("trafficRoutingConfig","TrafficRoutingConfig",properties.TrafficRoutingConfig!=null?CfnDeploymentConfigTrafficRoutingConfigPropertyFromCloudFormation(properties.TrafficRoutingConfig):void 0),ret.addPropertyResult("zonalConfig","ZonalConfig",properties.ZonalConfig!=null?CfnDeploymentConfigZonalConfigPropertyFromCloudFormation(properties.ZonalConfig):void 0),ret.addUnrecognizedPropertiesAsExtra(properties),ret}class CfnDeploymentGroup extends cdk().CfnResource{static _fromCloudFormation(scope,id,resourceAttributes,options){resourceAttributes=resourceAttributes||{};const resourceProperties=options.parser.parseValue(resourceAttributes.Properties),propsResult=CfnDeploymentGroupPropsFromCloudFormation(resourceProperties);if(cdk().isResolvableObject(propsResult.value))throw new(cdk_errors()).ValidationError("Unexpected IResolvable",scope);const ret=new CfnDeploymentGroup(scope,id,propsResult.value);for(const[propKey,propVal]of Object.entries(propsResult.extraProperties))ret.addPropertyOverride(propKey,propVal);return options.parser.handleAttributes(ret,resourceAttributes,id),ret}constructor(scope,id,props){super(scope,id,{type:CfnDeploymentGroup.CFN_RESOURCE_TYPE_NAME,properties:props});try{jsiiDeprecationWarnings().aws_cdk_lib_aws_codedeploy_CfnDeploymentGroupProps(props)}catch(error){throw process.env.JSII_DEBUG!=="1"&&error.name==="DeprecationError"&&Error.captureStackTrace(error,CfnDeploymentGroup),error}cdk().requireProperty(props,"applicationName",this),cdk().requireProperty(props,"serviceRoleArn",this),this.attrId=cdk().Token.asString(this.getAtt("Id",cdk().ResolutionTypeHint.STRING)),this.alarmConfiguration=props.alarmConfiguration,this.applicationName=props.applicationName,this.autoRollbackConfiguration=props.autoRollbackConfiguration,this.autoScalingGroups=props.autoScalingGroups,this.blueGreenDeploymentConfiguration=props.blueGreenDeploymentConfiguration,this.deployment=props.deployment,this.deploymentConfigName=props.deploymentConfigName,this.deploymentGroupName=props.deploymentGroupName,this.deploymentStyle=props.deploymentStyle,this.ec2TagFilters=props.ec2TagFilters,this.ec2TagSet=props.ec2TagSet,this.ecsServices=props.ecsServices,this.loadBalancerInfo=props.loadBalancerInfo,this.onPremisesInstanceTagFilters=props.onPremisesInstanceTagFilters,this.onPremisesTagSet=props.onPremisesTagSet,this.outdatedInstancesStrategy=props.outdatedInstancesStrategy,this.serviceRoleArn=props.serviceRoleArn,this.tags=new(cdk()).TagManager(cdk().TagType.STANDARD,"AWS::CodeDeploy::DeploymentGroup",props.tags,{tagPropertyName:"tags"}),this.tagsRaw=props.tags,this.terminationHookEnabled=props.terminationHookEnabled,this.triggerConfigurations=props.triggerConfigurations}get cfnProperties(){return{alarmConfiguration:this.alarmConfiguration,applicationName:this.applicationName,autoRollbackConfiguration:this.autoRollbackConfiguration,autoScalingGroups:this.autoScalingGroups,blueGreenDeploymentConfiguration:this.blueGreenDeploymentConfiguration,deployment:this.deployment,deploymentConfigName:this.deploymentConfigName,deploymentGroupName:this.deploymentGroupName,deploymentStyle:this.deploymentStyle,ec2TagFilters:this.ec2TagFilters,ec2TagSet:this.ec2TagSet,ecsServices:this.ecsServices,loadBalancerInfo:this.loadBalancerInfo,onPremisesInstanceTagFilters:this.onPremisesInstanceTagFilters,onPremisesTagSet:this.onPremisesTagSet,outdatedInstancesStrategy:this.outdatedInstancesStrategy,serviceRoleArn:this.serviceRoleArn,tags:this.tags.renderTags(),terminationHookEnabled:this.terminationHookEnabled,triggerConfigurations:this.triggerConfigurations}}inspect(inspector){try{jsiiDeprecationWarnings().aws_cdk_lib_TreeInspector(inspector)}catch(error){throw process.env.JSII_DEBUG!=="1"&&error.name==="DeprecationError"&&Error.captureStackTrace(error,this.inspect),error}inspector.addAttribute("aws:cdk:cloudformation:type",CfnDeploymentGroup.CFN_RESOURCE_TYPE_NAME),inspector.addAttribute("aws:cdk:cloudformation:props",this.cfnProperties)}renderProperties(props){return convertCfnDeploymentGroupPropsToCloudFormation(props)}}exports.CfnDeploymentGroup=CfnDeploymentGroup,_c=JSII_RTTI_SYMBOL_1,CfnDeploymentGroup[_c]={fqn:"aws-cdk-lib.aws_codedeploy.CfnDeploymentGroup",version:"2.201.0"},CfnDeploymentGroup.CFN_RESOURCE_TYPE_NAME="AWS::CodeDeploy::DeploymentGroup";function CfnDeploymentGroupAlarmPropertyValidator(properties){if(!cdk().canInspect(properties))return cdk().VALIDATION_SUCCESS;const errors=new(cdk()).ValidationResults;return properties&&typeof properties=="object"&&!Array.isArray(properties)||errors.collect(new(cdk()).ValidationResult("Expected an object, but received: "+JSON.stringify(properties))),errors.collect(cdk().propertyValidator("name",cdk().validateString)(properties.name)),errors.wrap('supplied properties not correct for "AlarmProperty"')}function convertCfnDeploymentGroupAlarmPropertyToCloudFormation(properties){return cdk().canInspect(properties)?(CfnDeploymentGroupAlarmPropertyValidator(properties).assertSuccess(),{Name:cdk().stringToCloudFormation(properties.name)}):properties}function CfnDeploymentGroupAlarmPropertyFromCloudFormation(properties){if(cdk().isResolvableObject(properties))return new(cfn_parse()).FromCloudFormationResult(properties);if(properties=properties??{},!(properties&&typeof properties=="object"&&!Array.isArray(properties)))return new(cfn_parse()).FromCloudFormationResult(properties);const ret=new(cfn_parse()).FromCloudFormationPropertyObject;return ret.addPropertyResult("name","Name",properties.Name!=null?cfn_parse().FromCloudFormation.getString(properties.Name):void 0),ret.addUnrecognizedPropertiesAsExtra(properties),ret}function CfnDeploymentGroupAlarmConfigurationPropertyValidator(properties){if(!cdk().canInspect(properties))return cdk().VALIDATION_SUCCESS;const errors=new(cdk()).ValidationResults;return properties&&typeof properties=="object"&&!Array.isArray(properties)||errors.collect(new(cdk()).ValidationResult("Expected an object, but received: "+JSON.stringify(properties))),errors.collect(cdk().propertyValidator("alarms",cdk().listValidator(CfnDeploymentGroupAlarmPropertyValidator))(properties.alarms)),errors.collect(cdk().propertyValidator("enabled",cdk().validateBoolean)(properties.enabled)),errors.collect(cdk().propertyValidator("ignorePollAlarmFailure",cdk().validateBoolean)(properties.ignorePollAlarmFailure)),errors.wrap('supplied properties not correct for "AlarmConfigurationProperty"')}function convertCfnDeploymentGroupAlarmConfigurationPropertyToCloudFormation(properties){return cdk().canInspect(properties)?(CfnDeploymentGroupAlarmConfigurationPropertyValidator(properties).assertSuccess(),{Alarms:cdk().listMapper(convertCfnDeploymentGroupAlarmPropertyToCloudFormation)(properties.alarms),Enabled:cdk().booleanToCloudFormation(properties.enabled),IgnorePollAlarmFailure:cdk().booleanToCloudFormation(properties.ignorePollAlarmFailure)}):properties}function CfnDeploymentGroupAlarmConfigurationPropertyFromCloudFormation(properties){if(cdk().isResolvableObject(properties))return new(cfn_parse()).FromCloudFormationResult(properties);if(properties=properties??{},!(properties&&typeof properties=="object"&&!Array.isArray(properties)))return new(cfn_parse()).FromCloudFormationResult(properties);const ret=new(cfn_parse()).FromCloudFormationPropertyObject;return ret.addPropertyResult("alarms","Alarms",properties.Alarms!=null?cfn_parse().FromCloudFormation.getArray(CfnDeploymentGroupAlarmPropertyFromCloudFormation)(properties.Alarms):void 0),ret.addPropertyResult("enabled","Enabled",properties.Enabled!=null?cfn_parse().FromCloudFormation.getBoolean(properties.Enabled):void 0),ret.addPropertyResult("ignorePollAlarmFailure","IgnorePollAlarmFailure",properties.IgnorePollAlarmFailure!=null?cfn_parse().FromCloudFormation.getBoolean(properties.IgnorePollAlarmFailure):void 0),ret.addUnrecognizedPropertiesAsExtra(properties),ret}function CfnDeploymentGroupAutoRollbackConfigurationPropertyValidator(properties){if(!cdk().canInspect(properties))return cdk().VALIDATION_SUCCESS;const errors=new(cdk()).ValidationResults;return properties&&typeof properties=="object"&&!Array.isArray(properties)||errors.collect(new(cdk()).ValidationResult("Expected an object, but received: "+JSON.stringify(properties))),errors.collect(cdk().propertyValidator("enabled",cdk().validateBoolean)(properties.enabled)),errors.collect(cdk().propertyValidator("events",cdk().listValidator(cdk().validateString))(properties.events)),errors.wrap('supplied properties not correct for "AutoRollbackConfigurationProperty"')}function convertCfnDeploymentGroupAutoRollbackConfigurationPropertyToCloudFormation(properties){return cdk().canInspect(properties)?(CfnDeploymentGroupAutoRollbackConfigurationPropertyValidator(properties).assertSuccess(),{Enabled:cdk().booleanToCloudFormation(properties.enabled),Events:cdk().listMapper(cdk().stringToCloudFormation)(properties.events)}):properties}function CfnDeploymentGroupAutoRollbackConfigurationPropertyFromCloudFormation(properties){if(cdk().isResolvableObject(properties))return new(cfn_parse()).FromCloudFormationResult(properties);if(properties=properties??{},!(properties&&typeof properties=="object"&&!Array.isArray(properties)))return new(cfn_parse()).FromCloudFormationResult(properties);const ret=new(cfn_parse()).FromCloudFormationPropertyObject;return ret.addPropertyResult("enabled","Enabled",properties.Enabled!=null?cfn_parse().FromCloudFormation.getBoolean(properties.Enabled):void 0),ret.addPropertyResult("events","Events",properties.Events!=null?cfn_parse().FromCloudFormation.getArray(cfn_parse().FromCloudFormation.getString)(properties.Events):void 0),ret.addUnrecognizedPropertiesAsExtra(properties),ret}function CfnDeploymentGroupDeploymentReadyOptionPropertyValidator(properties){if(!cdk().canInspect(properties))return cdk().VALIDATION_SUCCESS;const errors=new(cdk()).ValidationResults;return properties&&typeof properties=="object"&&!Array.isArray(properties)||errors.collect(new(cdk()).ValidationResult("Expected an object, but received: "+JSON.stringify(properties))),errors.collect(cdk().propertyValidator("actionOnTimeout",cdk().validateString)(properties.actionOnTimeout)),errors.collect(cdk().propertyValidator("waitTimeInMinutes",cdk().validateNumber)(properties.waitTimeInMinutes)),errors.wrap('supplied properties not correct for "DeploymentReadyOptionProperty"')}function convertCfnDeploymentGroupDeploymentReadyOptionPropertyToCloudFormation(properties){return cdk().canInspect(properties)?(CfnDeploymentGroupDeploymentReadyOptionPropertyValidator(properties).assertSuccess(),{ActionOnTimeout:cdk().stringToCloudFormation(properties.actionOnTimeout),WaitTimeInMinutes:cdk().numberToCloudFormation(properties.waitTimeInMinutes)}):properties}function CfnDeploymentGroupDeploymentReadyOptionPropertyFromCloudFormation(properties){if(cdk().isResolvableObject(properties))return new(cfn_parse()).FromCloudFormationResult(properties);if(properties=properties??{},!(properties&&typeof properties=="object"&&!Array.isArray(properties)))return new(cfn_parse()).FromCloudFormationResult(properties);const ret=new(cfn_parse()).FromCloudFormationPropertyObject;return ret.addPropertyResult("actionOnTimeout","ActionOnTimeout",properties.ActionOnTimeout!=null?cfn_parse().FromCloudFormation.getString(properties.ActionOnTimeout):void 0),ret.addPropertyResult("waitTimeInMinutes","WaitTimeInMinutes",properties.WaitTimeInMinutes!=null?cfn_parse().FromCloudFormation.getNumber(properties.WaitTimeInMinutes):void 0),ret.addUnrecognizedPropertiesAsExtra(properties),ret}function CfnDeploymentGroupGreenFleetProvisioningOptionPropertyValidator(properties){if(!cdk().canInspect(properties))return cdk().VALIDATION_SUCCESS;const errors=new(cdk()).ValidationResults;return properties&&typeof properties=="object"&&!Array.isArray(properties)||errors.collect(new(cdk()).ValidationResult("Expected an object, but received: "+JSON.stringify(properties))),errors.collect(cdk().propertyValidator("action",cdk().validateString)(properties.action)),errors.wrap('supplied properties not correct for "GreenFleetProvisioningOptionProperty"')}function convertCfnDeploymentGroupGreenFleetProvisioningOptionPropertyToCloudFormation(properties){return cdk().canInspect(properties)?(CfnDeploymentGroupGreenFleetProvisioningOptionPropertyValidator(properties).assertSuccess(),{Action:cdk().stringToCloudFormation(properties.action)}):properties}function CfnDeploymentGroupGreenFleetProvisioningOptionPropertyFromCloudFormation(properties){if(cdk().isResolvableObject(properties))return new(cfn_parse()).FromCloudFormationResult(properties);if(properties=properties??{},!(properties&&typeof properties=="object"&&!Array.isArray(properties)))return new(cfn_parse()).FromCloudFormationResult(properties);const ret=new(cfn_parse()).FromCloudFormationPropertyObject;return ret.addPropertyResult("action","Action",properties.Action!=null?cfn_parse().FromCloudFormation.getString(properties.Action):void 0),ret.addUnrecognizedPropertiesAsExtra(properties),ret}function CfnDeploymentGroupBlueInstanceTerminationOptionPropertyValidator(properties){if(!cdk().canInspect(properties))return cdk().VALIDATION_SUCCESS;const errors=new(cdk()).ValidationResults;return properties&&typeof properties=="object"&&!Array.isArray(properties)||errors.collect(new(cdk()).ValidationResult("Expected an object, but received: "+JSON.stringify(properties))),errors.collect(cdk().propertyValidator("action",cdk().validateString)(properties.action)),errors.collect(cdk().propertyValidator("terminationWaitTimeInMinutes",cdk().validateNumber)(properties.terminationWaitTimeInMinutes)),errors.wrap('supplied properties not correct for "BlueInstanceTerminationOptionProperty"')}function convertCfnDeploymentGroupBlueInstanceTerminationOptionPropertyToCloudFormation(properties){return cdk().canInspect(properties)?(CfnDeploymentGroupBlueInstanceTerminationOptionPropertyValidator(properties).assertSuccess(),{Action:cdk().stringToCloudFormation(properties.action),TerminationWaitTimeInMinutes:cdk().numberToCloudFormation(properties.terminationWaitTimeInMinutes)}):properties}function CfnDeploymentGroupBlueInstanceTerminationOptionPropertyFromCloudFormation(properties){if(cdk().isResolvableObject(properties))return new(cfn_parse()).FromCloudFormationResult(properties);if(properties=properties??{},!(properties&&typeof properties=="object"&&!Array.isArray(properties)))return new(cfn_parse()).FromCloudFormationResult(properties);const ret=new(cfn_parse()).FromCloudFormationPropertyObject;return ret.addPropertyResult("action","Action",properties.Action!=null?cfn_parse().FromCloudFormation.getString(properties.Action):void 0),ret.addPropertyResult("terminationWaitTimeInMinutes","TerminationWaitTimeInMinutes",properties.TerminationWaitTimeInMinutes!=null?cfn_parse().FromCloudFormation.getNumber(properties.TerminationWaitTimeInMinutes):void 0),ret.addUnrecognizedPropertiesAsExtra(properties),ret}function CfnDeploymentGroupBlueGreenDeploymentConfigurationPropertyValidator(properties){if(!cdk().canInspect(properties))return cdk().VALIDATION_SUCCESS;const errors=new(cdk()).ValidationResults;return properties&&typeof properties=="object"&&!Array.isArray(properties)||errors.collect(new(cdk()).ValidationResult("Expected an object, but received: "+JSON.stringify(properties))),errors.collect(cdk().propertyValidator("deploymentReadyOption",CfnDeploymentGroupDeploymentReadyOptionPropertyValidator)(properties.deploymentReadyOption)),errors.collect(cdk().propertyValidator("greenFleetProvisioningOption",CfnDeploymentGroupGreenFleetProvisioningOptionPropertyValidator)(properties.greenFleetProvisioningOption)),errors.collect(cdk().propertyValidator("terminateBlueInstancesOnDeploymentSuccess",CfnDeploymentGroupBlueInstanceTerminationOptionPropertyValidator)(properties.terminateBlueInstancesOnDeploymentSuccess)),errors.wrap('supplied properties not correct for "BlueGreenDeploymentConfigurationProperty"')}function convertCfnDeploymentGroupBlueGreenDeploymentConfigurationPropertyToCloudFormation(properties){return cdk().canInspect(properties)?(CfnDeploymentGroupBlueGreenDeploymentConfigurationPropertyValidator(properties).assertSuccess(),{DeploymentReadyOption:convertCfnDeploymentGroupDeploymentReadyOptionPropertyToCloudFormation(properties.deploymentReadyOption),GreenFleetProvisioningOption:convertCfnDeploymentGroupGreenFleetProvisioningOptionPropertyToCloudFormation(properties.greenFleetProvisioningOption),TerminateBlueInstancesOnDeploymentSuccess:convertCfnDeploymentGroupBlueInstanceTerminationOptionPropertyToCloudFormation(properties.terminateBlueInstancesOnDeploymentSuccess)}):properties}function CfnDeploymentGroupBlueGreenDeploymentConfigurationPropertyFromCloudFormation(properties){if(cdk().isResolvableObject(properties))return new(cfn_parse()).FromCloudFormationResult(properties);if(properties=properties??{},!(properties&&typeof properties=="object"&&!Array.isArray(properties)))return new(cfn_parse()).FromCloudFormationResult(properties);const ret=new(cfn_parse()).FromCloudFormationPropertyObject;return ret.addPropertyResult("deploymentReadyOption","DeploymentReadyOption",properties.DeploymentReadyOption!=null?CfnDeploymentGroupDeploymentReadyOptionPropertyFromCloudFormation(properties.DeploymentReadyOption):void 0),ret.addPropertyResult("greenFleetProvisioningOption","GreenFleetProvisioningOption",properties.GreenFleetProvisioningOption!=null?CfnDeploymentGroupGreenFleetProvisioningOptionPropertyFromCloudFormation(properties.GreenFleetProvisioningOption):void 0),ret.addPropertyResult("terminateBlueInstancesOnDeploymentSuccess","TerminateBlueInstancesOnDeploymentSuccess",properties.TerminateBlueInstancesOnDeploymentSuccess!=null?CfnDeploymentGroupBlueInstanceTerminationOptionPropertyFromCloudFormation(properties.TerminateBlueInstancesOnDeploymentSuccess):void 0),ret.addUnrecognizedPropertiesAsExtra(properties),ret}function CfnDeploymentGroupGitHubLocationPropertyValidator(properties){if(!cdk().canInspect(properties))return cdk().VALIDATION_SUCCESS;const errors=new(cdk()).ValidationResults;return properties&&typeof properties=="object"&&!Array.isArray(properties)||errors.collect(new(cdk()).ValidationResult("Expected an object, but received: "+JSON.stringify(properties))),errors.collect(cdk().propertyValidator("commitId",cdk().requiredValidator)(properties.commitId)),errors.collect(cdk().propertyValidator("commitId",cdk().validateString)(properties.commitId)),errors.collect(cdk().propertyValidator("repository",cdk().requiredValidator)(properties.repository)),errors.collect(cdk().propertyValidator("repository",cdk().validateString)(properties.repository)),errors.wrap('supplied properties not correct for "GitHubLocationProperty"')}function convertCfnDeploymentGroupGitHubLocationPropertyToCloudFormation(properties){return cdk().canInspect(properties)?(CfnDeploymentGroupGitHubLocationPropertyValidator(properties).assertSuccess(),{CommitId:cdk().stringToCloudFormation(properties.commitId),Repository:cdk().stringToCloudFormation(properties.repository)}):properties}function CfnDeploymentGroupGitHubLocationPropertyFromCloudFormation(properties){if(cdk().isResolvableObject(properties))return new(cfn_parse()).FromCloudFormationResult(properties);if(properties=properties??{},!(properties&&typeof properties=="object"&&!Array.isArray(properties)))return new(cfn_parse()).FromCloudFormationResult(properties);const ret=new(cfn_parse()).FromCloudFormationPropertyObject;return ret.addPropertyResult("commitId","CommitId",properties.CommitId!=null?cfn_parse().FromCloudFormation.getString(properties.CommitId):void 0),ret.addPropertyResult("repository","Repository",properties.Repository!=null?cfn_parse().FromCloudFormation.getString(properties.Repository):void 0),ret.addUnrecognizedPropertiesAsExtra(properties),ret}function CfnDeploymentGroupS3LocationPropertyValidator(properties){if(!cdk().canInspect(properties))return cdk().VALIDATION_SUCCESS;const errors=new(cdk()).ValidationResults;return properties&&typeof properties=="object"&&!Array.isArray(properties)||errors.collect(new(cdk()).ValidationResult("Expected an object, but received: "+JSON.stringify(properties))),errors.collect(cdk().propertyValidator("bucket",cdk().requiredValidator)(properties.bucket)),errors.collect(cdk().propertyValidator("bucket",cdk().validateString)(properties.bucket)),errors.collect(cdk().propertyValidator("bundleType",cdk().validateString)(properties.bundleType)),errors.collect(cdk().propertyValidator("eTag",cdk().validateString)(properties.eTag)),errors.collect(cdk().propertyValidator("key",cdk().requiredValidator)(properties.key)),errors.collect(cdk().propertyValidator("key",cdk().validateString)(properties.key)),errors.collect(cdk().propertyValidator("version",cdk().validateString)(properties.version)),errors.wrap('supplied properties not correct for "S3LocationProperty"')}function convertCfnDeploymentGroupS3LocationPropertyToCloudFormation(properties){return cdk().canInspect(properties)?(CfnDeploymentGroupS3LocationPropertyValidator(properties).assertSuccess(),{Bucket:cdk().stringToCloudFormation(properties.bucket),BundleType:cdk().stringToCloudFormation(properties.bundleType),ETag:cdk().stringToCloudFormation(properties.eTag),Key:cdk().stringToCloudFormation(properties.key),Version:cdk().stringToCloudFormation(properties.version)}):properties}function CfnDeploymentGroupS3LocationPropertyFromCloudFormation(properties){if(cdk().isResolvableObject(properties))return new(cfn_parse()).FromCloudFormationResult(properties);if(properties=properties??{},!(properties&&typeof properties=="object"&&!Array.isArray(properties)))return new(cfn_parse()).FromCloudFormationResult(properties);const ret=new(cfn_parse()).FromCloudFormationPropertyObject;return ret.addPropertyResult("bucket","Bucket",properties.Bucket!=null?cfn_parse().FromCloudFormation.getString(properties.Bucket):void 0),ret.addPropertyResult("bundleType","BundleType",properties.BundleType!=null?cfn_parse().FromCloudFormation.getString(properties.BundleType):void 0),ret.addPropertyResult("eTag","ETag",properties.ETag!=null?cfn_parse().FromCloudFormation.getString(properties.ETag):void 0),ret.addPropertyResult("key","Key",properties.Key!=null?cfn_parse().FromCloudFormation.getString(properties.Key):void 0),ret.addPropertyResult("version","Version",properties.Version!=null?cfn_parse().FromCloudFormation.getString(properties.Version):void 0),ret.addUnrecognizedPropertiesAsExtra(properties),ret}function CfnDeploymentGroupRevisionLocationPropertyValidator(properties){if(!cdk().canInspect(properties))return cdk().VALIDATION_SUCCESS;const errors=new(cdk()).ValidationResults;return properties&&typeof properties=="object"&&!Array.isArray(properties)||errors.collect(new(cdk()).ValidationResult("Expected an object, but received: "+JSON.stringify(properties))),errors.collect(cdk().propertyValidator("gitHubLocation",CfnDeploymentGroupGitHubLocationPropertyValidator)(properties.gitHubLocation)),errors.collect(cdk().propertyValidator("revisionType",cdk().validateString)(properties.revisionType)),errors.collect(cdk().propertyValidator("s3Location",CfnDeploymentGroupS3LocationPropertyValidator)(properties.s3Location)),errors.wrap('supplied properties not correct for "RevisionLocationProperty"')}function convertCfnDeploymentGroupRevisionLocationPropertyToCloudFormation(properties){return cdk().canInspect(properties)?(CfnDeploymentGroupRevisionLocationPropertyValidator(properties).assertSuccess(),{GitHubLocation:convertCfnDeploymentGroupGitHubLocationPropertyToCloudFormation(properties.gitHubLocation),RevisionType:cdk().stringToCloudFormation(properties.revisionType),S3Location:convertCfnDeploymentGroupS3LocationPropertyToCloudFormation(properties.s3Location)}):properties}function CfnDeploymentGroupRevisionLocationPropertyFromCloudFormation(properties){if(cdk().isResolvableObject(properties))return new(cfn_parse()).FromCloudFormationResult(properties);if(properties=properties??{},!(properties&&typeof properties=="object"&&!Array.isArray(properties)))return new(cfn_parse()).FromCloudFormationResult(properties);const ret=new(cfn_parse()).FromCloudFormationPropertyObject;return ret.addPropertyResult("gitHubLocation","GitHubLocation",properties.GitHubLocation!=null?CfnDeploymentGroupGitHubLocationPropertyFromCloudFormation(properties.GitHubLocation):void 0),ret.addPropertyResult("revisionType","RevisionType",properties.RevisionType!=null?cfn_parse().FromCloudFormation.getString(properties.RevisionType):void 0),ret.addPropertyResult("s3Location","S3Location",properties.S3Location!=null?CfnDeploymentGroupS3LocationPropertyFromCloudFormation(properties.S3Location):void 0),ret.addUnrecognizedPropertiesAsExtra(properties),ret}function CfnDeploymentGroupDeploymentPropertyValidator(properties){if(!cdk().canInspect(properties))return cdk().VALIDATION_SUCCESS;const errors=new(cdk()).ValidationResults;return properties&&typeof properties=="object"&&!Array.isArray(properties)||errors.collect(new(cdk()).ValidationResult("Expected an object, but received: "+JSON.stringify(properties))),errors.collect(cdk().propertyValidator("description",cdk().validateString)(properties.description)),errors.collect(cdk().propertyValidator("ignoreApplicationStopFailures",cdk().validateBoolean)(properties.ignoreApplicationStopFailures)),errors.collect(cdk().propertyValidator("revision",cdk().requiredValidator)(properties.revision)),errors.collect(cdk().propertyValidator("revision",CfnDeploymentGroupRevisionLocationPropertyValidator)(properties.revision)),errors.wrap('supplied properties not correct for "DeploymentProperty"')}function convertCfnDeploymentGroupDeploymentPropertyToCloudFormation(properties){return cdk().canInspect(properties)?(CfnDeploymentGroupDeploymentPropertyValidator(properties).assertSuccess(),{Description:cdk().stringToCloudFormation(properties.description),IgnoreApplicationStopFailures:cdk().booleanToCloudFormation(properties.ignoreApplicationStopFailures),Revision:convertCfnDeploymentGroupRevisionLocationPropertyToCloudFormation(properties.revision)}):properties}function CfnDeploymentGroupDeploymentPropertyFromCloudFormation(properties){if(cdk().isResolvableObject(properties))return new(cfn_parse()).FromCloudFormationResult(properties);if(properties=properties??{},!(properties&&typeof properties=="object"&&!Array.isArray(properties)))return new(cfn_parse()).FromCloudFormationResult(properties);const ret=new(cfn_parse()).FromCloudFormationPropertyObject;return ret.addPropertyResult("description","Description",properties.Description!=null?cfn_parse().FromCloudFormation.getString(properties.Description):void 0),ret.addPropertyResult("ignoreApplicationStopFailures","IgnoreApplicationStopFailures",properties.IgnoreApplicationStopFailures!=null?cfn_parse().FromCloudFormation.getBoolean(properties.IgnoreApplicationStopFailures):void 0),ret.addPropertyResult("revision","Revision",properties.Revision!=null?CfnDeploymentGroupRevisionLocationPropertyFromCloudFormation(properties.Revision):void 0),ret.addUnrecognizedPropertiesAsExtra(properties),ret}function CfnDeploymentGroupDeploymentStylePropertyValidator(properties){if(!cdk().canInspect(properties))return cdk().VALIDATION_SUCCESS;const errors=new(cdk()).ValidationResults;return properties&&typeof properties=="object"&&!Array.isArray(properties)||errors.collect(new(cdk()).ValidationResult("Expected an object, but received: "+JSON.stringify(properties))),errors.collect(cdk().propertyValidator("deploymentOption",cdk().validateString)(properties.deploymentOption)),errors.collect(cdk().propertyValidator("deploymentType",cdk().validateString)(properties.deploymentType)),errors.wrap('supplied properties not correct for "DeploymentStyleProperty"')}function convertCfnDeploymentGroupDeploymentStylePropertyToCloudFormation(properties){return cdk().canInspect(properties)?(CfnDeploymentGroupDeploymentStylePropertyValidator(properties).assertSuccess(),{DeploymentOption:cdk().stringToCloudFormation(properties.deploymentOption),DeploymentType:cdk().stringToCloudFormation(properties.deploymentType)}):properties}function CfnDeploymentGroupDeploymentStylePropertyFromCloudFormation(properties){if(cdk().isResolvableObject(properties))return new(cfn_parse()).FromCloudFormationResult(properties);if(properties=properties??{},!(properties&&typeof properties=="object"&&!Array.isArray(properties)))return new(cfn_parse()).FromCloudFormationResult(properties);const ret=new(cfn_parse()).FromCloudFormationPropertyObject;return ret.addPropertyResult("deploymentOption","DeploymentOption",properties.DeploymentOption!=null?cfn_parse().FromCloudFormation.getString(properties.DeploymentOption):void 0),ret.addPropertyResult("deploymentType","DeploymentType",properties.DeploymentType!=null?cfn_parse().FromCloudFormation.getString(properties.DeploymentType):void 0),ret.addUnrecognizedPropertiesAsExtra(properties),ret}function CfnDeploymentGroupECSServicePropertyValidator(properties){if(!cdk().canInspect(properties))return cdk().VALIDATION_SUCCESS;const errors=new(cdk()).ValidationResults;return properties&&typeof properties=="object"&&!Array.isArray(properties)||errors.collect(new(cdk()).ValidationResult("Expected an object, but received: "+JSON.stringify(properties))),errors.collect(cdk().propertyValidator("clusterName",cdk().requiredValidator)(properties.clusterName)),errors.collect(cdk().propertyValidator("clusterName",cdk().validateString)(properties.clusterName)),errors.collect(cdk().propertyValidator("serviceName",cdk().requiredValidator)(properties.serviceName)),errors.collect(cdk().propertyValidator("serviceName",cdk().validateString)(properties.serviceName)),errors.wrap('supplied properties not correct for "ECSServiceProperty"')}function convertCfnDeploymentGroupECSServicePropertyToCloudFormation(properties){return cdk().canInspect(properties)?(CfnDeploymentGroupECSServicePropertyValidator(properties).assertSuccess(),{ClusterName:cdk().stringToCloudFormation(properties.clusterName),ServiceName:cdk().stringToCloudFormation(properties.serviceName)}):properties}function CfnDeploymentGroupECSServicePropertyFromCloudFormation(properties){if(cdk().isResolvableObject(properties))return new(cfn_parse()).FromCloudFormationResult(properties);if(properties=properties??{},!(properties&&typeof properties=="object"&&!Array.isArray(properties)))return new(cfn_parse()).FromCloudFormationResult(properties);const ret=new(cfn_parse()).FromCloudFormationPropertyObject;return ret.addPropertyResult("clusterName","ClusterName",properties.ClusterName!=null?cfn_parse().FromCloudFormation.getString(properties.ClusterName):void 0),ret.addPropertyResult("serviceName","ServiceName",properties.ServiceName!=null?cfn_parse().FromCloudFormation.getString(properties.ServiceName):void 0),ret.addUnrecognizedPropertiesAsExtra(properties),ret}function CfnDeploymentGroupEC2TagFilterPropertyValidator(properties){if(!cdk().canInspect(properties))return cdk().VALIDATION_SUCCESS;const errors=new(cdk()).ValidationResults;return properties&&typeof properties=="object"&&!Array.isArray(properties)||errors.collect(new(cdk()).ValidationResult("Expected an object, but received: "+JSON.stringify(properties))),errors.collect(cdk().propertyValidator("key",cdk().validateString)(properties.key)),errors.collect(cdk().propertyValidator("type",cdk().validateString)(properties.type)),errors.collect(cdk().propertyValidator("value",cdk().validateString)(properties.value)),errors.wrap('supplied properties not correct for "EC2TagFilterProperty"')}function convertCfnDeploymentGroupEC2TagFilterPropertyToCloudFormation(properties){return cdk().canInspect(properties)?(CfnDeploymentGroupEC2TagFilterPropertyValidator(properties).assertSuccess(),{Key:cdk().stringToCloudFormation(properties.key),Type:cdk().stringToCloudFormation(properties.type),Value:cdk().stringToCloudFormation(properties.value)}):properties}function CfnDeploymentGroupEC2TagFilterPropertyFromCloudFormation(properties){if(cdk().isResolvableObject(properties))return new(cfn_parse()).FromCloudFormationResult(properties);if(properties=properties??{},!(properties&&typeof properties=="object"&&!Array.isArray(properties)))return new(cfn_parse()).FromCloudFormationResult(properties);const ret=new(cfn_parse()).FromCloudFormationPropertyObject;return ret.addPropertyResult("key","Key",properties.Key!=null?cfn_parse().FromCloudFormation.getString(properties.Key):void 0),ret.addPropertyResult("type","Type",properties.Type!=null?cfn_parse().FromCloudFormation.getString(properties.Type):void 0),ret.addPropertyResult("value","Value",properties.Value!=null?cfn_parse().FromCloudFormation.getString(properties.Value):void 0),ret.addUnrecognizedPropertiesAsExtra(properties),ret}function CfnDeploymentGroupEC2TagSetListObjectPropertyValidator(properties){if(!cdk().canInspect(properties))return cdk().VALIDATION_SUCCESS;const errors=new(cdk()).ValidationResults;return properties&&typeof properties=="object"&&!Array.isArray(properties)||errors.collect(new(cdk()).ValidationResult("Expected an object, but received: "+JSON.stringify(properties))),errors.collect(cdk().propertyValidator("ec2TagGroup",cdk().listValidator(CfnDeploymentGroupEC2TagFilterPropertyValidator))(properties.ec2TagGroup)),errors.wrap('supplied properties not correct for "EC2TagSetListObjectProperty"')}function convertCfnDeploymentGroupEC2TagSetListObjectPropertyToCloudFormation(properties){return cdk().canInspect(properties)?(CfnDeploymentGroupEC2TagSetListObjectPropertyValidator(properties).assertSuccess(),{Ec2TagGroup:cdk().listMapper(convertCfnDeploymentGroupEC2TagFilterPropertyToCloudFormation)(properties.ec2TagGroup)}):properties}function CfnDeploymentGroupEC2TagSetListObjectPropertyFromCloudFormation(properties){if(cdk().isResolvableObject(properties))return new(cfn_parse()).FromCloudFormationResult(properties);if(properties=properties??{},!(properties&&typeof properties=="object"&&!Array.isArray(properties)))return new(cfn_parse()).FromCloudFormationResult(properties);const ret=new(cfn_parse()).FromCloudFormationPropertyObject;return ret.addPropertyResult("ec2TagGroup","Ec2TagGroup",properties.Ec2TagGroup!=null?cfn_parse().FromCloudFormation.getArray(CfnDeploymentGroupEC2TagFilterPropertyFromCloudFormation)(properties.Ec2TagGroup):void 0),ret.addUnrecognizedPropertiesAsExtra(properties),ret}function CfnDeploymentGroupEC2TagSetPropertyValidator(properties){if(!cdk().canInspect(properties))return cdk().VALIDATION_SUCCESS;const errors=new(cdk()).ValidationResults;return properties&&typeof properties=="object"&&!Array.isArray(properties)||errors.collect(new(cdk()).ValidationResult("Expected an object, but received: "+JSON.stringify(properties))),errors.collect(cdk().propertyValidator("ec2TagSetList",cdk().listValidator(CfnDeploymentGroupEC2TagSetListObjectPropertyValidator))(properties.ec2TagSetList)),errors.wrap('supplied properties not correct for "EC2TagSetProperty"')}function convertCfnDeploymentGroupEC2TagSetPropertyToCloudFormation(properties){return cdk().canInspect(properties)?(CfnDeploymentGroupEC2TagSetPropertyValidator(properties).assertSuccess(),{Ec2TagSetList:cdk().listMapper(convertCfnDeploymentGroupEC2TagSetListObjectPropertyToCloudFormation)(properties.ec2TagSetList)}):properties}function CfnDeploymentGroupEC2TagSetPropertyFromCloudFormation(properties){if(cdk().isResolvableObject(properties))return new(cfn_parse()).FromCloudFormationResult(properties);if(properties=properties??{},!(properties&&typeof properties=="object"&&!Array.isArray(properties)))return new(cfn_parse()).FromCloudFormationResult(properties);const ret=new(cfn_parse()).FromCloudFormationPropertyObject;return ret.addPropertyResult("ec2TagSetList","Ec2TagSetList",properties.Ec2TagSetList!=null?cfn_parse().FromCloudFormation.getArray(CfnDeploymentGroupEC2TagSetListObjectPropertyFromCloudFormation)(properties.Ec2TagSetList):void 0),ret.addUnrecognizedPropertiesAsExtra(properties),ret}function CfnDeploymentGroupELBInfoPropertyValidator(properties){if(!cdk().canInspect(properties))return cdk().VALIDATION_SUCCESS;const errors=new(cdk()).ValidationResults;return properties&&typeof properties=="object"&&!Array.isArray(properties)||errors.collect(new(cdk()).ValidationResult("Expected an object, but received: "+JSON.stringify(properties))),errors.collect(cdk().propertyValidator("name",cdk().validateString)(properties.name)),errors.wrap('supplied properties not correct for "ELBInfoProperty"')}function convertCfnDeploymentGroupELBInfoPropertyToCloudFormation(properties){return cdk().canInspect(properties)?(CfnDeploymentGroupELBInfoPropertyValidator(properties).assertSuccess(),{Name:cdk().stringToCloudFormation(properties.name)}):properties}function CfnDeploymentGroupELBInfoPropertyFromCloudFormation(properties){if(cdk().isResolvableObject(properties))return new(cfn_parse()).FromCloudFormationResult(properties);if(properties=properties??{},!(properties&&typeof properties=="object"&&!Array.isArray(properties)))return new(cfn_parse()).FromCloudFormationResult(properties);const ret=new(cfn_parse()).FromCloudFormationPropertyObject;return ret.addPropertyResult("name","Name",properties.Name!=null?cfn_parse().FromCloudFormation.getString(properties.Name):void 0),ret.addUnrecognizedPropertiesAsExtra(properties),ret}function CfnDeploymentGroupTargetGroupInfoPropertyValidator(properties){if(!cdk().canInspect(properties))return cdk().VALIDATION_SUCCESS;const errors=new(cdk()).ValidationResults;return properties&&typeof properties=="object"&&!Array.isArray(properties)||errors.collect(new(cdk()).ValidationResult("Expected an object, but received: "+JSON.stringify(properties))),errors.collect(cdk().propertyValidator("name",cdk().validateString)(properties.name)),errors.wrap('supplied properties not correct for "TargetGroupInfoProperty"')}function convertCfnDeploymentGroupTargetGroupInfoPropertyToCloudFormation(properties){return cdk().canInspect(properties)?(CfnDeploymentGroupTargetGroupInfoPropertyValidator(properties).assertSuccess(),{Name:cdk().stringToCloudFormation(properties.name)}):properties}function CfnDeploymentGroupTargetGroupInfoPropertyFromCloudFormation(properties){if(cdk().isResolvableObject(properties))return new(cfn_parse()).FromCloudFormationResult(properties);if(properties=properties??{},!(properties&&typeof properties=="object"&&!Array.isArray(properties)))return new(cfn_parse()).FromCloudFormationResult(properties);const ret=new(cfn_parse()).FromCloudFormationPropertyObject;return ret.addPropertyResult("name","Name",properties.Name!=null?cfn_parse().FromCloudFormation.getString(properties.Name):void 0),ret.addUnrecognizedPropertiesAsExtra(properties),ret}function CfnDeploymentGroupTrafficRoutePropertyValidator(properties){if(!cdk().canInspect(properties))return cdk().VALIDATION_SUCCESS;const errors=new(cdk()).ValidationResults;return properties&&typeof properties=="object"&&!Array.isArray(properties)||errors.collect(new(cdk()).ValidationResult("Expected an object, but received: "+JSON.stringify(properties))),errors.collect(cdk().propertyValidator("listenerArns",cdk().listValidator(cdk().validateString))(properties.listenerArns)),errors.wrap('supplied properties not correct for "TrafficRouteProperty"')}function convertCfnDeploymentGroupTrafficRoutePropertyToCloudFormation(properties){return cdk().canInspect(properties)?(CfnDeploymentGroupTrafficRoutePropertyValidator(properties).assertSuccess(),{ListenerArns:cdk().listMapper(cdk().stringToCloudFormation)(properties.listenerArns)}):properties}function CfnDeploymentGroupTrafficRoutePropertyFromCloudFormation(properties){if(cdk().isResolvableObject(properties))return new(cfn_parse()).FromCloudFormationResult(properties);if(properties=properties??{},!(properties&&typeof properties=="object"&&!Array.isArray(properties)))return new(cfn_parse()).FromCloudFormationResult(properties);const ret=new(cfn_parse()).FromCloudFormationPropertyObject;return ret.addPropertyResult("listenerArns","ListenerArns",properties.ListenerArns!=null?cfn_parse().FromCloudFormation.getArray(cfn_parse().FromCloudFormation.getString)(properties.ListenerArns):void 0),ret.addUnrecognizedPropertiesAsExtra(properties),ret}function CfnDeploymentGroupTargetGroupPairInfoPropertyValidator(properties){if(!cdk().canInspect(properties))return cdk().VALIDATION_SUCCESS;const errors=new(cdk()).ValidationResults;return properties&&typeof properties=="object"&&!Array.isArray(properties)||errors.collect(new(cdk()).ValidationResult("Expected an object, but received: "+JSON.stringify(properties))),errors.collect(cdk().propertyValidator("prodTrafficRoute",CfnDeploymentGroupTrafficRoutePropertyValidator)(properties.prodTrafficRoute)),errors.collect(cdk().propertyValidator("targetGroups",cdk().listValidator(CfnDeploymentGroupTargetGroupInfoPropertyValidator))(properties.targetGroups)),errors.collect(cdk().propertyValidator("testTrafficRoute",CfnDeploymentGroupTrafficRoutePropertyValidator)(properties.testTrafficRoute)),errors.wrap('supplied properties not correct for "TargetGroupPairInfoProperty"')}function convertCfnDeploymentGroupTargetGroupPairInfoPropertyToCloudFormation(properties){return cdk().canInspect(properties)?(CfnDeploymentGroupTargetGroupPairInfoPropertyValidator(properties).assertSuccess(),{ProdTrafficRoute:convertCfnDeploymentGroupTrafficRoutePropertyToCloudFormation(properties.prodTrafficRoute),TargetGroups:cdk().listMapper(convertCfnDeploymentGroupTargetGroupInfoPropertyToCloudFormation)(properties.targetGroups),TestTrafficRoute:convertCfnDeploymentGroupTrafficRoutePropertyToCloudFormation(properties.testTrafficRoute)}):properties}function CfnDeploymentGroupTargetGroupPairInfoPropertyFromCloudFormation(properties){if(cdk().isResolvableObject(properties))return new(cfn_parse()).FromCloudFormationResult(properties);if(properties=properties??{},!(properties&&typeof properties=="object"&&!Array.isArray(properties)))return new(cfn_parse()).FromCloudFormationResult(properties);const ret=new(cfn_parse()).FromCloudFormationPropertyObject;return ret.addPropertyResult("prodTrafficRoute","ProdTrafficRoute",properties.ProdTrafficRoute!=null?CfnDeploymentGroupTrafficRoutePropertyFromCloudFormation(properties.ProdTrafficRoute):void 0),ret.addPropertyResult("targetGroups","TargetGroups",properties.TargetGroups!=null?cfn_parse().FromCloudFormation.getArray(CfnDeploymentGroupTargetGroupInfoPropertyFromCloudFormation)(properties.TargetGroups):void 0),ret.addPropertyResult("testTrafficRoute","TestTrafficRoute",properties.TestTrafficRoute!=null?CfnDeploymentGroupTrafficRoutePropertyFromCloudFormation(properties.TestTrafficRoute):void 0),ret.addUnrecognizedPropertiesAsExtra(properties),ret}function CfnDeploymentGroupLoadBalancerInfoPropertyValidator(properties){if(!cdk().canInspect(properties))return cdk().VALIDATION_SUCCESS;const errors=new(cdk()).ValidationResults;return properties&&typeof properties=="object"&&!Array.isArray(properties)||errors.collect(new(cdk()).ValidationResult("Expected an object, but received: "+JSON.stringify(properties))),errors.collect(cdk().propertyValidator("elbInfoList",cdk().listValidator(CfnDeploymentGroupELBInfoPropertyValidator))(properties.elbInfoList)),errors.collect(cdk().propertyValidator("targetGroupInfoList",cdk().listValidator(CfnDeploymentGroupTargetGroupInfoPropertyValidator))(properties.targetGroupInfoList)),errors.collect(cdk().propertyValidator("targetGroupPairInfoList",cdk().listValidator(CfnDeploymentGroupTargetGroupPairInfoPropertyValidator))(properties.targetGroupPairInfoList)),errors.wrap('supplied properties not correct for "LoadBalancerInfoProperty"')}function convertCfnDeploymentGroupLoadBalancerInfoPropertyToCloudFormation(properties){return cdk().canInspect(properties)?(CfnDeploymentGroupLoadBalancerInfoPropertyValidator(properties).assertSuccess(),{ElbInfoList:cdk().listMapper(convertCfnDeploymentGroupELBInfoPropertyToCloudFormation)(properties.elbInfoList),TargetGroupInfoList:cdk().listMapper(convertCfnDeploymentGroupTargetGroupInfoPropertyToCloudFormation)(properties.targetGroupInfoList),TargetGroupPairInfoList:cdk().listMapper(convertCfnDeploymentGroupTargetGroupPairInfoPropertyToCloudFormation)(properties.targetGroupPairInfoList)}):properties}function CfnDeploymentGroupLoadBalancerInfoPropertyFromCloudFormation(properties){if(cdk().isResolvableObject(properties))return new(cfn_parse()).FromCloudFormationResult(properties);if(properties=properties??{},!(properties&&typeof properties=="object"&&!Array.isArray(properties)))return new(cfn_parse()).FromCloudFormationResult(properties);const ret=new(cfn_parse()).FromCloudFormationPropertyObject;return ret.addPropertyResult("elbInfoList","ElbInfoList",properties.ElbInfoList!=null?cfn_parse().FromCloudFormation.getArray(CfnDeploymentGroupELBInfoPropertyFromCloudFormation)(properties.ElbInfoList):void 0),ret.addPropertyResult("targetGroupInfoList","TargetGroupInfoList",properties.TargetGroupInfoList!=null?cfn_parse().FromCloudFormation.getArray(CfnDeploymentGroupTargetGroupInfoPropertyFromCloudFormation)(properties.TargetGroupInfoList):void 0),ret.addPropertyResult("targetGroupPairInfoList","TargetGroupPairInfoList",properties.TargetGroupPairInfoList!=null?cfn_parse().FromCloudFormation.getArray(CfnDeploymentGroupTargetGroupPairInfoPropertyFromCloudFormation)(properties.TargetGroupPairInfoList):void 0),ret.addUnrecognizedPropertiesAsExtra(properties),ret}function CfnDeploymentGroupTagFilterPropertyValidator(properties){if(!cdk().canInspect(properties))return cdk().VALIDATION_SUCCESS;const errors=new(cdk()).ValidationResults;return properties&&typeof properties=="object"&&!Array.isArray(properties)||errors.collect(new(cdk()).ValidationResult("Expected an object, but received: "+JSON.stringify(properties))),errors.collect(cdk().propertyValidator("key",cdk().validateString)(properties.key)),errors.collect(cdk().propertyValidator("type",cdk().validateString)(properties.type)),errors.collect(cdk().propertyValidator("value",cdk().validateString)(properties.value)),errors.wrap('supplied properties not correct for "TagFilterProperty"')}function convertCfnDeploymentGroupTagFilterPropertyToCloudFormation(properties){return cdk().canInspect(properties)?(CfnDeploymentGroupTagFilterPropertyValidator(properties).assertSuccess(),{Key:cdk().stringToCloudFormation(properties.key),Type:cdk().stringToCloudFormation(properties.type),Value:cdk().stringToCloudFormation(properties.value)}):properties}function CfnDeploymentGroupTagFilterPropertyFromCloudFormation(properties){if(cdk().isResolvableObject(properties))return new(cfn_parse()).FromCloudFormationResult(properties);if(properties=properties??{},!(properties&&typeof properties=="object"&&!Array.isArray(properties)))return new(cfn_parse()).FromCloudFormationResult(properties);const ret=new(cfn_parse()).FromCloudFormationPropertyObject;return ret.addPropertyResult("key","Key",properties.Key!=null?cfn_parse().FromCloudFormation.getString(properties.Key):void 0),ret.addPropertyResult("type","Type",properties.Type!=null?cfn_parse().FromCloudFormation.getString(properties.Type):void 0),ret.addPropertyResult("value","Value",properties.Value!=null?cfn_parse().FromCloudFormation.getString(properties.Value):void 0),ret.addUnrecognizedPropertiesAsExtra(properties),ret}function CfnDeploymentGroupOnPremisesTagSetListObjectPropertyValidator(properties){if(!cdk().canInspect(properties))return cdk().VALIDATION_SUCCESS;const errors=new(cdk()).ValidationResults;return properties&&typeof properties=="object"&&!Array.isArray(properties)||errors.collect(new(cdk()).ValidationResult("Expected an object, but received: "+JSON.stringify(properties))),errors.collect(cdk().propertyValidator("onPremisesTagGroup",cdk().listValidator(CfnDeploymentGroupTagFilterPropertyValidator))(properties.onPremisesTagGroup)),errors.wrap('supplied properties not correct for "OnPremisesTagSetListObjectProperty"')}function convertCfnDeploymentGroupOnPremisesTagSetListObjectPropertyToCloudFormation(properties){return cdk().canInspect(properties)?(CfnDeploymentGroupOnPremisesTagSetListObjectPropertyValidator(properties).assertSuccess(),{OnPremisesTagGroup:cdk().listMapper(convertCfnDeploymentGroupTagFilterPropertyToCloudFormation)(properties.onPremisesTagGroup)}):properties}function CfnDeploymentGroupOnPremisesTagSetListObjectPropertyFromCloudFormation(properties){if(cdk().isResolvableObject(properties))return new(cfn_parse()).FromCloudFormationResult(properties);if(properties=properties??{},!(properties&&typeof properties=="object"&&!Array.isArray(properties)))return new(cfn_parse()).FromCloudFormationResult(properties);const ret=new(cfn_parse()).FromCloudFormationPropertyObject;return ret.addPropertyResult("onPremisesTagGroup","OnPremisesTagGroup",properties.OnPremisesTagGroup!=null?cfn_parse().FromCloudFormation.getArray(CfnDeploymentGroupTagFilterPropertyFromCloudFormation)(properties.OnPremisesTagGroup):void 0),ret.addUnrecognizedPropertiesAsExtra(properties),ret}function CfnDeploymentGroupOnPremisesTagSetPropertyValidator(properties){if(!cdk().canInspect(properties))return cdk().VALIDATION_SUCCESS;const errors=new(cdk()).ValidationResults;return properties&&typeof properties=="object"&&!Array.isArray(properties)||errors.collect(new(cdk()).ValidationResult("Expected an object, but received: "+JSON.stringify(properties))),errors.collect(cdk().propertyValidator("onPremisesTagSetList",cdk().listValidator(CfnDeploymentGroupOnPremisesTagSetListObjectPropertyValidator))(properties.onPremisesTagSetList)),errors.wrap('supplied properties not correct for "OnPremisesTagSetProperty"')}function convertCfnDeploymentGroupOnPremisesTagSetPropertyToCloudFormation(properties){return cdk().canInspect(properties)?(CfnDeploymentGroupOnPremisesTagSetPropertyValidator(properties).assertSuccess(),{OnPremisesTagSetList:cdk().listMapper(convertCfnDeploymentGroupOnPremisesTagSetListObjectPropertyToCloudFormation)(properties.onPremisesTagSetList)}):properties}function CfnDeploymentGroupOnPremisesTagSetPropertyFromCloudFormation(properties){if(cdk().isResolvableObject(properties))return new(cfn_parse()).FromCloudFormationResult(properties);if(properties=properties??{},!(properties&&typeof properties=="object"&&!Array.isArray(properties)))return new(cfn_parse()).FromCloudFormationResult(properties);const ret=new(cfn_parse()).FromCloudFormationPropertyObject;return ret.addPropertyResult("onPremisesTagSetList","OnPremisesTagSetList",properties.OnPremisesTagSetList!=null?cfn_parse().FromCloudFormation.getArray(CfnDeploymentGroupOnPremisesTagSetListObjectPropertyFromCloudFormation)(properties.OnPremisesTagSetList):void 0),ret.addUnrecognizedPropertiesAsExtra(properties),ret}function CfnDeploymentGroupTriggerConfigPropertyValidator(properties){if(!cdk().canInspect(properties))return cdk().VALIDATION_SUCCESS;const errors=new(cdk()).ValidationResults;return properties&&typeof properties=="object"&&!Array.isArray(properties)||errors.collect(new(cdk()).ValidationResult("Expected an object, but received: "+JSON.stringify(properties))),errors.collect(cdk().propertyValidator("triggerEvents",cdk().listValidator(cdk().validateString))(properties.triggerEvents)),errors.collect(cdk().propertyValidator("triggerName",cdk().validateString)(properties.triggerName)),errors.collect(cdk().propertyValidator("triggerTargetArn",cdk().validateString)(properties.triggerTargetArn)),errors.wrap('supplied properties not correct for "TriggerConfigProperty"')}function convertCfnDeploymentGroupTriggerConfigPropertyToCloudFormation(properties){return cdk().canInspect(properties)?(CfnDeploymentGroupTriggerConfigPropertyValidator(properties).assertSuccess(),{TriggerEvents:cdk().listMapper(cdk().stringToCloudFormation)(properties.triggerEvents),TriggerName:cdk().stringToCloudFormation(properties.triggerName),TriggerTargetArn:cdk().stringToCloudFormation(properties.triggerTargetArn)}):properties}function CfnDeploymentGroupTriggerConfigPropertyFromCloudFormation(properties){if(cdk().isResolvableObject(properties))return new(cfn_parse()).FromCloudFormationResult(properties);if(properties=properties??{},!(properties&&typeof properties=="object"&&!Array.isArray(properties)))return new(cfn_parse()).FromCloudFormationResult(properties);const ret=new(cfn_parse()).FromCloudFormationPropertyObject;return ret.addPropertyResult("triggerEvents","TriggerEvents",properties.TriggerEvents!=null?cfn_parse().FromCloudFormation.getArray(cfn_parse().FromCloudFormation.getString)(properties.TriggerEvents):void 0),ret.addPropertyResult("triggerName","TriggerName",properties.TriggerName!=null?cfn_parse().FromCloudFormation.getString(properties.TriggerName):void 0),ret.addPropertyResult("triggerTargetArn","TriggerTargetArn",properties.TriggerTargetArn!=null?cfn_parse().FromCloudFormation.getString(properties.TriggerTargetArn):void 0),ret.addUnrecognizedPropertiesAsExtra(properties),ret}function CfnDeploymentGroupPropsValidator(properties){if(!cdk().canInspect(properties))return cdk().VALIDATION_SUCCESS;const errors=new(cdk()).ValidationResults;return properties&&typeof properties=="object"&&!Array.isArray(properties)||errors.collect(new(cdk()).ValidationResult("Expected an object, but received: "+JSON.stringify(properties))),errors.collect(cdk().propertyValidator("alarmConfiguration",CfnDeploymentGroupAlarmConfigurationPropertyValidator)(properties.alarmConfiguration)),errors.collect(cdk().propertyValidator("applicationName",cdk().requiredValidator)(properties.applicationName)),errors.collect(cdk().propertyValidator("applicationName",cdk().validateString)(properties.applicationName)),errors.collect(cdk().propertyValidator("autoRollbackConfiguration",CfnDeploymentGroupAutoRollbackConfigurationPropertyValidator)(properties.autoRollbackConfiguration)),errors.collect(cdk().propertyValidator("autoScalingGroups",cdk().listValidator(cdk().validateString))(properties.autoScalingGroups)),errors.collect(cdk().propertyValidator("blueGreenDeploymentConfiguration",CfnDeploymentGroupBlueGreenDeploymentConfigurationPropertyValidator)(properties.blueGreenDeploymentConfiguration)),errors.collect(cdk().propertyValidator("deployment",CfnDeploymentGroupDeploymentPropertyValidator)(properties.deployment)),errors.collect(cdk().propertyValidator("deploymentConfigName",cdk().validateString)(properties.deploymentConfigName)),errors.collect(cdk().propertyValidator("deploymentGroupName",cdk().validateString)(properties.deploymentGroupName)),errors.collect(cdk().propertyValidator("deploymentStyle",CfnDeploymentGroupDeploymentStylePropertyValidator)(properties.deploymentStyle)),errors.collect(cdk().propertyValidator("ecsServices",cdk().listValidator(CfnDeploymentGroupECSServicePropertyValidator))(properties.ecsServices)),errors.collect(cdk().propertyValidator("ec2TagFilters",cdk().listValidator(CfnDeploymentGroupEC2TagFilterPropertyValidator))(properties.ec2TagFilters)),errors.collect(cdk().propertyValidator("ec2TagSet",CfnDeploymentGroupEC2TagSetPropertyValidator)(properties.ec2TagSet)),errors.collect(cdk().propertyValidator("loadBalancerInfo",CfnDeploymentGroupLoadBalancerInfoPropertyValidator)(properties.loadBalancerInfo)),errors.collect(cdk().propertyValidator("onPremisesInstanceTagFilters",cdk().listValidator(CfnDeploymentGroupTagFilterPropertyValidator))(properties.onPremisesInstanceTagFilters)),errors.collect(cdk().propertyValidator("onPremisesTagSet",CfnDeploymentGroupOnPremisesTagSetPropertyValidator)(properties.onPremisesTagSet)),errors.collect(cdk().propertyValidator("outdatedInstancesStrategy",cdk().validateString)(properties.outdatedInstancesStrategy)),errors.collect(cdk().propertyValidator("serviceRoleArn",cdk().requiredValidator)(properties.serviceRoleArn)),errors.collect(cdk().propertyValidator("serviceRoleArn",cdk().validateString)(properties.serviceRoleArn)),errors.collect(cdk().propertyValidator("tags",cdk().listValidator(cdk().validateCfnTag))(properties.tags)),errors.collect(cdk().propertyValidator("terminationHookEnabled",cdk().validateBoolean)(properties.terminationHookEnabled)),errors.collect(cdk().propertyValidator("triggerConfigurations",cdk().listValidator(CfnDeploymentGroupTriggerConfigPropertyValidator))(properties.triggerConfigurations)),errors.wrap('supplied properties not correct for "CfnDeploymentGroupProps"')}function convertCfnDeploymentGroupPropsToCloudFormation(properties){return cdk().canInspect(properties)?(CfnDeploymentGroupPropsValidator(properties).assertSuccess(),{AlarmConfiguration:convertCfnDeploymentGroupAlarmConfigurationPropertyToCloudFormation(properties.alarmConfiguration),ApplicationName:cdk().stringToCloudFormation(properties.applicationName),AutoRollbackConfiguration:convertCfnDeploymentGroupAutoRollbackConfigurationPropertyToCloudFormation(properties.autoRollbackConfiguration),AutoScalingGroups:cdk().listMapper(cdk().stringToCloudFormation)(properties.autoScalingGroups),BlueGreenDeploymentConfiguration:convertCfnDeploymentGroupBlueGreenDeploymentConfigurationPropertyToCloudFormation(properties.blueGreenDeploymentConfiguration),Deployment:convertCfnDeploymentGroupDeploymentPropertyToCloudFormation(properties.deployment),DeploymentConfigName:cdk().stringToCloudFormation(properties.deploymentConfigName),DeploymentGroupName:cdk().stringToCloudFormation(properties.deploymentGroupName),DeploymentStyle:convertCfnDeploymentGroupDeploymentStylePropertyToCloudFormation(properties.deploymentStyle),ECSServices:cdk().listMapper(convertCfnDeploymentGroupECSServicePropertyToCloudFormation)(properties.ecsServices),Ec2TagFilters:cdk().listMapper(convertCfnDeploymentGroupEC2TagFilterPropertyToCloudFormation)(properties.ec2TagFilters),Ec2TagSet:convertCfnDeploymentGroupEC2TagSetPropertyToCloudFormation(properties.ec2TagSet),LoadBalancerInfo:convertCfnDeploymentGroupLoadBalancerInfoPropertyToCloudFormation(properties.loadBalancerInfo),OnPremisesInstanceTagFilters:cdk().listMapper(convertCfnDeploymentGroupTagFilterPropertyToCloudFormation)(properties.onPremisesInstanceTagFilters),OnPremisesTagSet:convertCfnDeploymentGroupOnPremisesTagSetPropertyToCloudFormation(properties.onPremisesTagSet),OutdatedInstancesStrategy:cdk().stringToCloudFormation(properties.outdatedInstancesStrategy),ServiceRoleArn:cdk().stringToCloudFormation(properties.serviceRoleArn),Tags:cdk().listMapper(cdk().cfnTagToCloudFormation)(properties.tags),TerminationHookEnabled:cdk().booleanToCloudFormation(properties.terminationHookEnabled),TriggerConfigurations:cdk().listMapper(convertCfnDeploymentGroupTriggerConfigPropertyToCloudFormation)(properties.triggerConfigurations)}):properties}function CfnDeploymentGroupPropsFromCloudFormation(properties){if(cdk().isResolvableObject(properties))return new(cfn_parse()).FromCloudFormationResult(properties);if(properties=properties??{},!(properties&&typeof properties=="object"&&!Array.isArray(properties)))return new(cfn_parse()).FromCloudFormationResult(properties);const ret=new(cfn_parse()).FromCloudFormationPropertyObject;return ret.addPropertyResult("alarmConfiguration","AlarmConfiguration",properties.AlarmConfiguration!=null?CfnDeploymentGroupAlarmConfigurationPropertyFromCloudFormation(properties.AlarmConfiguration):void 0),ret.addPropertyResult("applicationName","ApplicationName",properties.ApplicationName!=null?cfn_parse().FromCloudFormation.getString(properties.ApplicationName):void 0),ret.addPropertyResult("autoRollbackConfiguration","AutoRollbackConfiguration",properties.AutoRollbackConfiguration!=null?CfnDeploymentGroupAutoRollbackConfigurationPropertyFromCloudFormation(properties.AutoRollbackConfiguration):void 0),ret.addPropertyResult("autoScalingGroups","AutoScalingGroups",properties.AutoScalingGroups!=null?cfn_parse().FromCloudFormation.getArray(cfn_parse().FromCloudFormation.getString)(properties.AutoScalingGroups):void 0),ret.addPropertyResult("blueGreenDeploymentConfiguration","BlueGreenDeploymentConfiguration",properties.BlueGreenDeploymentConfiguration!=null?CfnDeploymentGroupBlueGreenDeploymentConfigurationPropertyFromCloudFormation(properties.BlueGreenDeploymentConfiguration):void 0),ret.addPropertyResult("deployment","Deployment",properties.Deployment!=null?CfnDeploymentGroupDeploymentPropertyFromCloudFormation(properties.Deployment):void 0),ret.addPropertyResult("deploymentConfigName","DeploymentConfigName",properties.DeploymentConfigName!=null?cfn_parse().FromCloudFormation.getString(properties.DeploymentConfigName):void 0),ret.addPropertyResult("deploymentGroupName","DeploymentGroupName",properties.DeploymentGroupName!=null?cfn_parse().FromCloudFormation.getString(properties.DeploymentGroupName):void 0),ret.addPropertyResult("deploymentStyle","DeploymentStyle",properties.DeploymentStyle!=null?CfnDeploymentGroupDeploymentStylePropertyFromCloudFormation(properties.DeploymentStyle):void 0),ret.addPropertyResult("ec2TagFilters","Ec2TagFilters",properties.Ec2TagFilters!=null?cfn_parse().FromCloudFormation.getArray(CfnDeploymentGroupEC2TagFilterPropertyFromCloudFormation)(properties.Ec2TagFilters):void 0),ret.addPropertyResult("ec2TagSet","Ec2TagSet",properties.Ec2TagSet!=null?CfnDeploymentGroupEC2TagSetPropertyFromCloudFormation(properties.Ec2TagSet):void 0),ret.addPropertyResult("ecsServices","ECSServices",properties.ECSServices!=null?cfn_parse().FromCloudFormation.getArray(CfnDeploymentGroupECSServicePropertyFromCloudFormation)(properties.ECSServices):void 0),ret.addPropertyResult("loadBalancerInfo","LoadBalancerInfo",properties.LoadBalancerInfo!=null?CfnDeploymentGroupLoadBalancerInfoPropertyFromCloudFormation(properties.LoadBalancerInfo):void 0),ret.addPropertyResult("onPremisesInstanceTagFilters","OnPremisesInstanceTagFilters",properties.OnPremisesInstanceTagFilters!=null?cfn_parse().FromCloudFormation.getArray(CfnDeploymentGroupTagFilterPropertyFromCloudFormation)(properties.OnPremisesInstanceTagFilters):void 0),ret.addPropertyResult("onPremisesTagSet","OnPremisesTagSet",properties.OnPremisesTagSet!=null?CfnDeploymentGroupOnPremisesTagSetPropertyFromCloudFormation(properties.OnPremisesTagSet):void 0),ret.addPropertyResult("outdatedInstancesStrategy","OutdatedInstancesStrategy",properties.OutdatedInstancesStrategy!=null?cfn_parse().FromCloudFormation.getString(properties.OutdatedInstancesStrategy):void 0),ret.addPropertyResult("serviceRoleArn","ServiceRoleArn",properties.ServiceRoleArn!=null?cfn_parse().FromCloudFormation.getString(properties.ServiceRoleArn):void 0),ret.addPropertyResult("tags","Tags",properties.Tags!=null?cfn_parse().FromCloudFormation.getArray(cfn_parse().FromCloudFormation.getCfnTag)(properties.Tags):void 0),ret.addPropertyResult("terminationHookEnabled","TerminationHookEnabled",properties.TerminationHookEnabled!=null?cfn_parse().FromCloudFormation.getBoolean(properties.TerminationHookEnabled):void 0),ret.addPropertyResult("triggerConfigurations","TriggerConfigurations",properties.TriggerConfigurations!=null?cfn_parse().FromCloudFormation.getArray(CfnDeploymentGroupTriggerConfigPropertyFromCloudFormation)(properties.TriggerConfigurations):void 0),ret.addUnrecognizedPropertiesAsExtra(properties),ret}
