"use strict";Object.defineProperty(exports,"__esModule",{value:!0}),exports.arnForApplication=arnForApplication,exports.nameFromDeploymentGroupArn=nameFromDeploymentGroupArn,exports.arnForDeploymentConfig=arnForDeploymentConfig,exports.renderAlarmConfiguration=renderAlarmConfiguration,exports.deploymentConfig=deploymentConfig,exports.renderAutoRollbackConfiguration=renderAutoRollbackConfiguration,exports.validateName=validateName;var core_1=()=>{var tmp=require("../../../core");return core_1=()=>tmp,tmp};function arnForApplication(stack,applicationName){return stack.formatArn({service:"codedeploy",resource:"application",resourceName:applicationName,arnFormat:core_1().ArnFormat.COLON_RESOURCE_NAME})}function nameFromDeploymentGroupArn(deploymentGroupArn){const components=core_1().Arn.split(deploymentGroupArn,core_1().ArnFormat.COLON_RESOURCE_NAME);return core_1().Fn.select(1,core_1().Fn.split("/",components.resourceName??""))}function arnForDeploymentConfig(name,resource){return core_1().Arn.format({partition:core_1().Aws.PARTITION,account:resource?.env.account??core_1().Aws.ACCOUNT_ID,region:resource?.env.region??core_1().Aws.REGION,service:"codedeploy",resource:"deploymentconfig",resourceName:name,arnFormat:core_1().ArnFormat.COLON_RESOURCE_NAME})}function renderAlarmConfiguration(props){const ignoreAlarmConfiguration=props.ignoreAlarmConfiguration??!1;return props.removeAlarms??!0?{alarms:props.alarms.length>0?props.alarms.map(a=>({name:a.alarmName})):void 0,enabled:!ignoreAlarmConfiguration&&props.alarms.length>0,ignorePollAlarmFailure:props.ignorePollAlarmFailure}:props.alarms.length===0?void 0:{alarms:props.alarms.map(a=>({name:a.alarmName})),enabled:!ignoreAlarmConfiguration,ignorePollAlarmFailure:props.ignorePollAlarmFailure}}function deploymentConfig(name){return{deploymentConfigName:name,deploymentConfigArn:arnForDeploymentConfig(name),bindEnvironment:resource=>({deploymentConfigName:name,deploymentConfigArn:arnForDeploymentConfig(name,resource)})}}var AutoRollbackEvent;(function(AutoRollbackEvent2){AutoRollbackEvent2.DEPLOYMENT_FAILURE="DEPLOYMENT_FAILURE",AutoRollbackEvent2.DEPLOYMENT_STOP_ON_ALARM="DEPLOYMENT_STOP_ON_ALARM",AutoRollbackEvent2.DEPLOYMENT_STOP_ON_REQUEST="DEPLOYMENT_STOP_ON_REQUEST"})(AutoRollbackEvent||(AutoRollbackEvent={}));function renderAutoRollbackConfiguration(scope,alarms,autoRollbackConfig={}){const events=new Array;if(autoRollbackConfig.failedDeployment!==!1&&events.push(AutoRollbackEvent.DEPLOYMENT_FAILURE),autoRollbackConfig.stoppedDeployment===!0&&events.push(AutoRollbackEvent.DEPLOYMENT_STOP_ON_REQUEST),autoRollbackConfig.deploymentInAlarm!==!1){if(alarms.length>0)events.push(AutoRollbackEvent.DEPLOYMENT_STOP_ON_ALARM);else if(autoRollbackConfig.deploymentInAlarm===!0)throw new(core_1()).ValidationError("The auto-rollback setting 'deploymentInAlarm' does not have any effect unless you associate at least one CloudWatch alarm with the Deployment Group",scope)}return autoRollbackConfig.failedDeployment===!1&&autoRollbackConfig.stoppedDeployment!==!0&&autoRollbackConfig.deploymentInAlarm===!1?{enabled:!1}:events.length>0?{enabled:!0,events}:void 0}function validateName(type,name){const ret=[];return!core_1().Token.isUnresolved(name)&&name!==void 0&&(name.length>100&&ret.push(`${type} name: "${name}" can be a max of 100 characters.`),/^[a-z0-9._+=,@-]+$/i.test(name)||ret.push(`${type} name: "${name}" can only contain letters (a-z, A-Z), numbers (0-9), periods (.), underscores (_), + (plus signs), = (equals signs), , (commas), @ (at signs), - (minus signs).`)),ret}
