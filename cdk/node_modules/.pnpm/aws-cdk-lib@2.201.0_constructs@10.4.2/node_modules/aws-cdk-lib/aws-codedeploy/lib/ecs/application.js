"use strict";var __decorate=exports&&exports.__decorate||function(decorators,target,key,desc){var c=arguments.length,r=c<3?target:desc===null?desc=Object.getOwnPropertyDescriptor(target,key):desc,d;if(typeof Reflect=="object"&&typeof Reflect.decorate=="function")r=Reflect.decorate(decorators,target,key,desc);else for(var i=decorators.length-1;i>=0;i--)(d=decorators[i])&&(r=(c<3?d(r):c>3?d(target,key,r):d(target,key))||r);return c>3&&r&&Object.defineProperty(target,key,r),r},_a;Object.defineProperty(exports,"__esModule",{value:!0}),exports.EcsApplication=void 0;var jsiiDeprecationWarnings=()=>{var tmp=require("../../../.warnings.jsii.js");return jsiiDeprecationWarnings=()=>tmp,tmp};const JSII_RTTI_SYMBOL_1=Symbol.for("jsii.rtti");var core_1=()=>{var tmp=require("../../../core");return core_1=()=>tmp,tmp},metadata_resource_1=()=>{var tmp=require("../../../core/lib/metadata-resource");return metadata_resource_1=()=>tmp,tmp},prop_injectable_1=()=>{var tmp=require("../../../core/lib/prop-injectable");return prop_injectable_1=()=>tmp,tmp},codedeploy_generated_1=()=>{var tmp=require("../codedeploy.generated");return codedeploy_generated_1=()=>tmp,tmp},utils_1=()=>{var tmp=require("../private/utils");return utils_1=()=>tmp,tmp};let EcsApplication=class EcsApplication2 extends core_1().Resource{static fromEcsApplicationName(scope,id,ecsApplicationName){class Import extends core_1().Resource{constructor(){super(...arguments),this.applicationArn=(0,utils_1().arnForApplication)(core_1().Stack.of(scope),ecsApplicationName),this.applicationName=ecsApplicationName}}return new Import(scope,id)}static fromEcsApplicationArn(scope,id,ecsApplicationArn){return new class extends core_1().Resource{constructor(){super(...arguments),this.applicationArn=ecsApplicationArn,this.applicationName=core_1().Arn.split(ecsApplicationArn,core_1().ArnFormat.COLON_RESOURCE_NAME).resourceName??"<invalid arn>"}}(scope,id,{environmentFromArn:ecsApplicationArn})}constructor(scope,id,props={}){super(scope,id,{physicalName:props.applicationName});try{jsiiDeprecationWarnings().aws_cdk_lib_aws_codedeploy_EcsApplicationProps(props)}catch(error){throw process.env.JSII_DEBUG!=="1"&&error.name==="DeprecationError"&&Error.captureStackTrace(error,EcsApplication2),error}(0,metadata_resource_1().addConstructMetadata)(this,props);const resource=new(codedeploy_generated_1()).CfnApplication(this,"Resource",{applicationName:this.physicalName,computePlatform:"ECS"});this.applicationName=this.getResourceNameAttribute(resource.ref),this.applicationArn=this.getResourceArnAttribute((0,utils_1().arnForApplication)(core_1().Stack.of(scope),resource.ref),{service:"codedeploy",resource:"application",resourceName:this.physicalName,arnFormat:core_1().ArnFormat.COLON_RESOURCE_NAME}),this.node.addValidation({validate:()=>(0,utils_1().validateName)("Application",this.physicalName)})}};exports.EcsApplication=EcsApplication,_a=JSII_RTTI_SYMBOL_1,EcsApplication[_a]={fqn:"aws-cdk-lib.aws_codedeploy.EcsApplication",version:"2.201.0"},EcsApplication.PROPERTY_INJECTION_ID="aws-cdk-lib.aws-codedeploy.EcsApplication",exports.EcsApplication=EcsApplication=__decorate([prop_injectable_1().propertyInjectable],EcsApplication);
