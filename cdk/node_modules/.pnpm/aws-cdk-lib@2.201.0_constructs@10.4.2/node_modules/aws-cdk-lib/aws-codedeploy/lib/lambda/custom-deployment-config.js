"use strict";var __decorate=exports&&exports.__decorate||function(decorators,target,key,desc){var c=arguments.length,r=c<3?target:desc===null?desc=Object.getOwnPropertyDescriptor(target,key):desc,d;if(typeof Reflect=="object"&&typeof Reflect.decorate=="function")r=Reflect.decorate(decorators,target,key,desc);else for(var i=decorators.length-1;i>=0;i--)(d=decorators[i])&&(r=(c<3?d(r):c>3?d(target,key,r):d(target,key))||r);return c>3&&r&&Object.defineProperty(target,key,r),r},_a;Object.defineProperty(exports,"__esModule",{value:!0}),exports.CustomLambdaDeploymentConfig=exports.CustomLambdaDeploymentConfigType=void 0;var jsiiDeprecationWarnings=()=>{var tmp=require("../../../.warnings.jsii.js");return jsiiDeprecationWarnings=()=>tmp,tmp};const JSII_RTTI_SYMBOL_1=Symbol.for("jsii.rtti");var core_1=()=>{var tmp=require("../../../core");return core_1=()=>tmp,tmp},metadata_resource_1=()=>{var tmp=require("../../../core/lib/metadata-resource");return metadata_resource_1=()=>tmp,tmp},prop_injectable_1=()=>{var tmp=require("../../../core/lib/prop-injectable");return prop_injectable_1=()=>tmp,tmp},custom_resources_1=()=>{var tmp=require("../../../custom-resources");return custom_resources_1=()=>tmp,tmp},utils_1=()=>{var tmp=require("../private/utils");return utils_1=()=>tmp,tmp},CustomLambdaDeploymentConfigType;(function(CustomLambdaDeploymentConfigType2){CustomLambdaDeploymentConfigType2.CANARY="Canary",CustomLambdaDeploymentConfigType2.LINEAR="Linear"})(CustomLambdaDeploymentConfigType||(exports.CustomLambdaDeploymentConfigType=CustomLambdaDeploymentConfigType={}));let CustomLambdaDeploymentConfig=class CustomLambdaDeploymentConfig2 extends core_1().Resource{constructor(scope,id,props){super(scope,id);try{jsiiDeprecationWarnings().print("aws-cdk-lib.aws_codedeploy.CustomLambdaDeploymentConfig","CloudFormation now supports Lambda deployment configurations without custom resources. Use `LambdaDeploymentConfig`."),jsiiDeprecationWarnings().aws_cdk_lib_aws_codedeploy_CustomLambdaDeploymentConfigProps(props)}catch(error){throw process.env.JSII_DEBUG!=="1"&&error.name==="DeprecationError"&&Error.captureStackTrace(error,CustomLambdaDeploymentConfig2),error}(0,metadata_resource_1().addConstructMetadata)(this,props),this.validateParameters(props);const deploymentType="TimeBased"+props.type.toString(),intervalMinutes=props.interval.toMinutes(),percentage=props.percentage;let routingConfig;props.type==CustomLambdaDeploymentConfigType.CANARY?routingConfig={type:deploymentType,timeBasedCanary:{canaryInterval:intervalMinutes,canaryPercentage:percentage}}:props.type==CustomLambdaDeploymentConfigType.LINEAR&&(routingConfig={type:deploymentType,timeBasedLinear:{linearInterval:intervalMinutes,linearPercentage:percentage}}),this.deploymentConfigName=props.deploymentConfigName??`${core_1().Names.uniqueId(this)}.Lambda${props.type}${props.percentage}Percent${props.type===CustomLambdaDeploymentConfigType.LINEAR?"Every":""}${props.interval.toMinutes()}Minutes`,this.deploymentConfigArn=(0,utils_1().arnForDeploymentConfig)(this.deploymentConfigName),new(custom_resources_1()).AwsCustomResource(this,"DeploymentConfig",{onCreate:{service:"CodeDeploy",action:"createDeploymentConfig",parameters:{deploymentConfigName:this.deploymentConfigName,computePlatform:"Lambda",trafficRoutingConfig:routingConfig},physicalResourceId:custom_resources_1().PhysicalResourceId.of(this.deploymentConfigName)},onUpdate:{service:"CodeDeploy",action:"createDeploymentConfig",parameters:{deploymentConfigName:this.deploymentConfigName,computePlatform:"Lambda",trafficRoutingConfig:routingConfig},physicalResourceId:custom_resources_1().PhysicalResourceId.of(this.deploymentConfigName)},onDelete:{service:"CodeDeploy",action:"deleteDeploymentConfig",parameters:{deploymentConfigName:this.deploymentConfigName}},policy:custom_resources_1().AwsCustomResourcePolicy.fromSdkCalls({resources:custom_resources_1().AwsCustomResourcePolicy.ANY_RESOURCE}),installLatestAwsSdk:!1}),this.node.addValidation({validate:()=>(0,utils_1().validateName)("Deployment config",this.deploymentConfigName)})}validateParameters(props){if(!(1<=props.percentage&&props.percentage<=99))throw new(core_1()).ValidationError(`Invalid deployment config percentage "${props.percentage.toString()}".         Step percentage must be an integer between 1 and 99.`,this);if(props.interval.toMinutes()>2880)throw new(core_1()).ValidationError(`Invalid deployment config interval "${props.interval.toString()}".         Traffic shifting intervals must be positive integers up to 2880 (2 days).`,this)}};exports.CustomLambdaDeploymentConfig=CustomLambdaDeploymentConfig,_a=JSII_RTTI_SYMBOL_1,CustomLambdaDeploymentConfig[_a]={fqn:"aws-cdk-lib.aws_codedeploy.CustomLambdaDeploymentConfig",version:"2.201.0"},CustomLambdaDeploymentConfig.PROPERTY_INJECTION_ID="aws-cdk-lib.aws-codedeploy.CustomLambdaDeploymentConfig",exports.CustomLambdaDeploymentConfig=CustomLambdaDeploymentConfig=__decorate([prop_injectable_1().propertyInjectable],CustomLambdaDeploymentConfig);
