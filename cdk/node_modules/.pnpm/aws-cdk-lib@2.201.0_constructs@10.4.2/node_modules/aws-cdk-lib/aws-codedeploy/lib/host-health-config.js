"use strict";var _a,_b;Object.defineProperty(exports,"__esModule",{value:!0}),exports.MinimumHealthyHostsPerZone=exports.MinimumHealthyHosts=void 0;const JSII_RTTI_SYMBOL_1=Symbol.for("jsii.rtti");var core_1=()=>{var tmp=require("../../core");return core_1=()=>tmp,tmp};class MinimumHealthyHosts{static count(value){return new MinimumHealthyHosts({type:"HOST_COUNT",value})}static percentage(value){return new MinimumHealthyHosts({type:"FLEET_PERCENT",value})}constructor(json){this.json=json}get _json(){return this.json}}exports.MinimumHealthyHosts=MinimumHealthyHosts,_a=JSII_RTTI_SYMBOL_1,MinimumHealthyHosts[_a]={fqn:"aws-cdk-lib.aws_codedeploy.MinimumHealthyHosts",version:"2.201.0"};class MinimumHealthyHostsPerZone{static count(value){return new MinimumHealthyHostsPerZone({type:"HOST_COUNT",value})}static percentage(value){return new MinimumHealthyHostsPerZone({type:"FLEET_PERCENT",value})}constructor(json){if(this.json=json,!Number.isInteger(json.value))throw new(core_1()).UnscopedValidationError(`The percentage or count value of minimumHealthyHostsPerZone must be an integer, got: ${json.value}`)}get _json(){return this.json}}exports.MinimumHealthyHostsPerZone=MinimumHealthyHostsPerZone,_b=JSII_RTTI_SYMBOL_1,MinimumHealthyHostsPerZone[_b]={fqn:"aws-cdk-lib.aws_codedeploy.MinimumHealthyHostsPerZone",version:"2.201.0"};
