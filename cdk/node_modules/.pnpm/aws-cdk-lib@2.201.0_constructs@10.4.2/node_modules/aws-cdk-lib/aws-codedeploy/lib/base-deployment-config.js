"use strict";var _a;Object.defineProperty(exports,"__esModule",{value:!0}),exports.BaseDeploymentConfig=exports.ComputePlatform=void 0;var jsiiDeprecationWarnings=()=>{var tmp=require("../../.warnings.jsii.js");return jsiiDeprecationWarnings=()=>tmp,tmp};const JSII_RTTI_SYMBOL_1=Symbol.for("jsii.rtti");var codedeploy_generated_1=()=>{var tmp=require("./codedeploy.generated");return codedeploy_generated_1=()=>tmp,tmp},utils_1=()=>{var tmp=require("./private/utils");return utils_1=()=>tmp,tmp},core_1=()=>{var tmp=require("../../core");return core_1=()=>tmp,tmp},ComputePlatform;(function(ComputePlatform2){ComputePlatform2.SERVER="Server",ComputePlatform2.LAMBDA="Lambda",ComputePlatform2.ECS="ECS"})(ComputePlatform||(exports.ComputePlatform=ComputePlatform={}));class BaseDeploymentConfig extends core_1().Resource{static fromDeploymentConfigName(scope,id,deploymentConfigName){const arn=core_1().Stack.of(scope).formatArn({service:"codedeploy",resource:"deploymentconfig",resourceName:deploymentConfigName,arnFormat:core_1().ArnFormat.COLON_RESOURCE_NAME});return{deploymentConfigName,deploymentConfigArn:arn}}constructor(scope,id,props){super(scope,id,{physicalName:props?.deploymentConfigName});try{jsiiDeprecationWarnings().aws_cdk_lib_aws_codedeploy_BaseDeploymentConfigProps(props)}catch(error){throw process.env.JSII_DEBUG!=="1"&&error.name==="DeprecationError"&&Error.captureStackTrace(error,BaseDeploymentConfig),error}if(props?.trafficRouting&&(props?.computePlatform===void 0||props?.computePlatform===ComputePlatform.SERVER))throw new(core_1()).ValidationError("Traffic routing config must not be specified for a Server-base deployment configuration",this);if(props?.minimumHealthyHosts&&props?.computePlatform&&props?.computePlatform!==ComputePlatform.SERVER)throw new(core_1()).ValidationError("Minimum healthy hosts config must only be specified for a Server-base deployment configuration",this);props?.zonalConfig&&(props.zonalConfig.monitorDuration&&this.validateMinimumDuration(props.zonalConfig.monitorDuration,"monitorDuration"),props.zonalConfig.firstZoneMonitorDuration&&this.validateMinimumDuration(props.zonalConfig.firstZoneMonitorDuration,"firstZoneMonitorDuration"));const resource=new(codedeploy_generated_1()).CfnDeploymentConfig(this,"Resource",{deploymentConfigName:this.physicalName,computePlatform:props?.computePlatform,trafficRoutingConfig:props?.trafficRouting?.bind(this),minimumHealthyHosts:props?.minimumHealthyHosts?._json,zonalConfig:props?.zonalConfig?{monitorDurationInSeconds:props.zonalConfig.monitorDuration?.toSeconds(),firstZoneMonitorDurationInSeconds:props.zonalConfig.firstZoneMonitorDuration?.toSeconds(),minimumHealthyHostsPerZone:props.zonalConfig.minimumHealthyHostsPerZone?._json}:void 0});this.deploymentConfigName=this.getResourceNameAttribute(resource.ref),this.deploymentConfigArn=this.getResourceArnAttribute((0,utils_1().arnForDeploymentConfig)(resource.ref),{service:"codedeploy",resource:"deploymentconfig",resourceName:this.physicalName,arnFormat:core_1().ArnFormat.COLON_RESOURCE_NAME}),this.node.addValidation({validate:()=>(0,utils_1().validateName)("Deployment config",this.physicalName)})}validateMinimumDuration(duration,name){const milliseconds=duration.toMilliseconds();if(milliseconds>0&&milliseconds<1e3)throw new(core_1()).ValidationError(`${name} must be greater than or equal to 1 second or be equal to 0, got ${milliseconds}ms`,this)}}exports.BaseDeploymentConfig=BaseDeploymentConfig,_a=JSII_RTTI_SYMBOL_1,BaseDeploymentConfig[_a]={fqn:"aws-cdk-lib.aws_codedeploy.BaseDeploymentConfig",version:"2.201.0"};function ignore(_x){}
