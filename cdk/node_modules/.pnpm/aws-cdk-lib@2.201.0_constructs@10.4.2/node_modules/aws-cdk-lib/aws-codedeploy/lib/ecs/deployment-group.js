"use strict";var __decorate=exports&&exports.__decorate||function(decorators,target,key,desc){var c=arguments.length,r=c<3?target:desc===null?desc=Object.getOwnPropertyDescriptor(target,key):desc,d;if(typeof Reflect=="object"&&typeof Reflect.decorate=="function")r=Reflect.decorate(decorators,target,key,desc);else for(var i=decorators.length-1;i>=0;i--)(d=decorators[i])&&(r=(c<3?d(r):c>3?d(target,key,r):d(target,key))||r);return c>3&&r&&Object.defineProperty(target,key,r),r},_a;Object.defineProperty(exports,"__esModule",{value:!0}),exports.EcsDeploymentGroup=void 0;var jsiiDeprecationWarnings=()=>{var tmp=require("../../../.warnings.jsii.js");return jsiiDeprecationWarnings=()=>tmp,tmp};const JSII_RTTI_SYMBOL_1=Symbol.for("jsii.rtti");var constructs_1=()=>{var tmp=require("constructs");return constructs_1=()=>tmp,tmp},application_1=()=>{var tmp=require("./application");return application_1=()=>tmp,tmp},deployment_config_1=()=>{var tmp=require("./deployment-config");return deployment_config_1=()=>tmp,tmp},ecs=()=>{var tmp=require("../../../aws-ecs");return ecs=()=>tmp,tmp},iam=()=>{var tmp=require("../../../aws-iam");return iam=()=>tmp,tmp},cdk=()=>{var tmp=require("../../../core");return cdk=()=>tmp,tmp},core_1=()=>{var tmp=require("../../../core");return core_1=()=>tmp,tmp},metadata_resource_1=()=>{var tmp=require("../../../core/lib/metadata-resource");return metadata_resource_1=()=>tmp,tmp},prop_injectable_1=()=>{var tmp=require("../../../core/lib/prop-injectable");return prop_injectable_1=()=>tmp,tmp},cx_api_1=()=>{var tmp=require("../../../cx-api");return cx_api_1=()=>tmp,tmp},codedeploy_generated_1=()=>{var tmp=require("../codedeploy.generated");return codedeploy_generated_1=()=>tmp,tmp},base_deployment_group_1=()=>{var tmp=require("../private/base-deployment-group");return base_deployment_group_1=()=>tmp,tmp},utils_1=()=>{var tmp=require("../private/utils");return utils_1=()=>tmp,tmp};let EcsDeploymentGroup=class EcsDeploymentGroup2 extends base_deployment_group_1().DeploymentGroupBase{static fromEcsDeploymentGroupAttributes(scope,id,attrs){try{jsiiDeprecationWarnings().aws_cdk_lib_aws_codedeploy_EcsDeploymentGroupAttributes(attrs)}catch(error){throw process.env.JSII_DEBUG!=="1"&&error.name==="DeprecationError"&&Error.captureStackTrace(error,this.fromEcsDeploymentGroupAttributes),error}return new ImportedEcsDeploymentGroup(scope,id,attrs)}constructor(scope,id,props){super(scope,id,{deploymentGroupName:props.deploymentGroupName,role:props.role,roleConstructId:"ServiceRole"});try{jsiiDeprecationWarnings().aws_cdk_lib_aws_codedeploy_EcsDeploymentGroupProps(props)}catch(error){throw process.env.JSII_DEBUG!=="1"&&error.name==="DeprecationError"&&Error.captureStackTrace(error,EcsDeploymentGroup2),error}if((0,metadata_resource_1().addConstructMetadata)(this,props),this.role=this._role,this.application=props.application||new(application_1()).EcsApplication(this,"Application"),this.alarms=props.alarms||[],this.role.addManagedPolicy(iam().ManagedPolicy.fromAwsManagedPolicyName("AWSCodeDeployRoleForECS")),this.deploymentConfig=this._bindDeploymentConfig(props.deploymentConfig||deployment_config_1().EcsDeploymentConfig.ALL_AT_ONCE),cdk().Resource.isOwnedResource(props.service)){const cfnSvc=props.service.node.defaultChild;if(cfnSvc.deploymentController===void 0||cfnSvc.deploymentController.type!==ecs().DeploymentControllerType.CODE_DEPLOY)throw new(core_1()).ValidationError("The ECS service associated with the deployment group must use the CODE_DEPLOY deployment controller type",this);if(cfnSvc.taskDefinition!==props.service.taskDefinition.family)throw new(core_1()).ValidationError("The ECS service associated with the deployment group must specify the task definition using the task definition family name only. Otherwise, the task definition cannot be updated in the stack",this)}const removeAlarmsFromDeploymentGroup=cdk().FeatureFlags.of(this).isEnabled(cx_api_1().CODEDEPLOY_REMOVE_ALARMS_FROM_DEPLOYMENT_GROUP),resource=new(codedeploy_generated_1()).CfnDeploymentGroup(this,"Resource",{applicationName:this.application.applicationName,serviceRoleArn:this.role.roleArn,deploymentGroupName:this.physicalName,deploymentConfigName:this.deploymentConfig.deploymentConfigName,deploymentStyle:{deploymentType:"BLUE_GREEN",deploymentOption:"WITH_TRAFFIC_CONTROL"},ecsServices:[{clusterName:props.service.cluster.clusterName,serviceName:props.service.serviceName}],blueGreenDeploymentConfiguration:cdk().Lazy.any({produce:()=>this.renderBlueGreenDeploymentConfiguration(props.blueGreenDeploymentConfig)}),loadBalancerInfo:cdk().Lazy.any({produce:()=>this.renderLoadBalancerInfo(props.blueGreenDeploymentConfig)}),alarmConfiguration:cdk().Lazy.any({produce:()=>(0,utils_1().renderAlarmConfiguration)({alarms:this.alarms,ignorePollAlarmFailure:props.ignorePollAlarmsFailure,removeAlarms:removeAlarmsFromDeploymentGroup,ignoreAlarmConfiguration:props.ignoreAlarmConfiguration})}),autoRollbackConfiguration:cdk().Lazy.any({produce:()=>(0,utils_1().renderAutoRollbackConfiguration)(this,this.alarms,props.autoRollback)})});this._setNameAndArn(resource,this.application),constructs_1().Construct.isConstruct(this.deploymentConfig)&&this.node.addDependency(this.deploymentConfig)}addAlarm(alarm){try{jsiiDeprecationWarnings().aws_cdk_lib_aws_cloudwatch_IAlarm(alarm)}catch(error){throw process.env.JSII_DEBUG!=="1"&&error.name==="DeprecationError"&&Error.captureStackTrace(error,this.addAlarm),error}this.alarms.push(alarm)}renderBlueGreenDeploymentConfiguration(options){return{deploymentReadyOption:{actionOnTimeout:options.deploymentApprovalWaitTime?"STOP_DEPLOYMENT":"CONTINUE_DEPLOYMENT",waitTimeInMinutes:options.deploymentApprovalWaitTime?.toMinutes()??0},terminateBlueInstancesOnDeploymentSuccess:{action:"TERMINATE",terminationWaitTimeInMinutes:options.terminationWaitTime?.toMinutes()??0}}}renderLoadBalancerInfo(options){return{targetGroupPairInfoList:[{targetGroups:[{name:options.blueTargetGroup.targetGroupName},{name:options.greenTargetGroup.targetGroupName}],prodTrafficRoute:{listenerArns:[options.listener.listenerArn]},testTrafficRoute:options.testListener?{listenerArns:[options.testListener.listenerArn]}:void 0}]}}};exports.EcsDeploymentGroup=EcsDeploymentGroup,_a=JSII_RTTI_SYMBOL_1,EcsDeploymentGroup[_a]={fqn:"aws-cdk-lib.aws_codedeploy.EcsDeploymentGroup",version:"2.201.0"},EcsDeploymentGroup.PROPERTY_INJECTION_ID="aws-cdk-lib.aws-codedeploy.EcsDeploymentGroup",__decorate([(0,metadata_resource_1().MethodMetadata)()],EcsDeploymentGroup.prototype,"addAlarm",null),exports.EcsDeploymentGroup=EcsDeploymentGroup=__decorate([prop_injectable_1().propertyInjectable],EcsDeploymentGroup);let ImportedEcsDeploymentGroup=class extends base_deployment_group_1().ImportedDeploymentGroupBase{constructor(scope,id,props){super(scope,id,{application:props.application,deploymentGroupName:props.deploymentGroupName}),(0,metadata_resource_1().addConstructMetadata)(this,props),this.application=props.application,this.deploymentConfig=this._bindDeploymentConfig(props.deploymentConfig||deployment_config_1().EcsDeploymentConfig.ALL_AT_ONCE)}};ImportedEcsDeploymentGroup.PROPERTY_INJECTION_ID="aws-cdk-lib.aws-codedeploy.ImportedEcsDeploymentGroup",ImportedEcsDeploymentGroup=__decorate([prop_injectable_1().propertyInjectable],ImportedEcsDeploymentGroup);
