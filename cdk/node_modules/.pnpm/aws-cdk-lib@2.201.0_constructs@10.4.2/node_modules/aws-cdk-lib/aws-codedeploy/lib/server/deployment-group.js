"use strict";var __decorate=exports&&exports.__decorate||function(decorators,target,key,desc){var c=arguments.length,r=c<3?target:desc===null?desc=Object.getOwnPropertyDescriptor(target,key):desc,d;if(typeof Reflect=="object"&&typeof Reflect.decorate=="function")r=Reflect.decorate(decorators,target,key,desc);else for(var i=decorators.length-1;i>=0;i--)(d=decorators[i])&&(r=(c<3?d(r):c>3?d(target,key,r):d(target,key))||r);return c>3&&r&&Object.defineProperty(target,key,r),r},_a,_b;Object.defineProperty(exports,"__esModule",{value:!0}),exports.ServerDeploymentGroup=exports.InstanceTagSet=void 0;var jsiiDeprecationWarnings=()=>{var tmp=require("../../../.warnings.jsii.js");return jsiiDeprecationWarnings=()=>tmp,tmp};const JSII_RTTI_SYMBOL_1=Symbol.for("jsii.rtti");var application_1=()=>{var tmp=require("./application");return application_1=()=>tmp,tmp},deployment_config_1=()=>{var tmp=require("./deployment-config");return deployment_config_1=()=>tmp,tmp},load_balancer_1=()=>{var tmp=require("./load-balancer");return load_balancer_1=()=>tmp,tmp},ec2=()=>{var tmp=require("../../../aws-ec2");return ec2=()=>tmp,tmp},iam=()=>{var tmp=require("../../../aws-iam");return iam=()=>tmp,tmp},s3=()=>{var tmp=require("../../../aws-s3");return s3=()=>tmp,tmp},cdk=()=>{var tmp=require("../../../core");return cdk=()=>tmp,tmp},metadata_resource_1=()=>{var tmp=require("../../../core/lib/metadata-resource");return metadata_resource_1=()=>tmp,tmp},prop_injectable_1=()=>{var tmp=require("../../../core/lib/prop-injectable");return prop_injectable_1=()=>tmp,tmp},cx_api_1=()=>{var tmp=require("../../../cx-api");return cx_api_1=()=>tmp,tmp},codedeploy_generated_1=()=>{var tmp=require("../codedeploy.generated");return codedeploy_generated_1=()=>tmp,tmp},base_deployment_group_1=()=>{var tmp=require("../private/base-deployment-group");return base_deployment_group_1=()=>tmp,tmp},utils_1=()=>{var tmp=require("../private/utils");return utils_1=()=>tmp,tmp};let ImportedServerDeploymentGroup=class extends base_deployment_group_1().ImportedDeploymentGroupBase{constructor(scope,id,props){super(scope,id,{application:props.application,deploymentGroupName:props.deploymentGroupName}),this.role=void 0,this.autoScalingGroups=void 0,(0,metadata_resource_1().addConstructMetadata)(this,props),this.application=props.application,this.deploymentConfig=this._bindDeploymentConfig(props.deploymentConfig||deployment_config_1().ServerDeploymentConfig.ONE_AT_A_TIME)}};ImportedServerDeploymentGroup.PROPERTY_INJECTION_ID="aws-cdk-lib.aws-codedeploy.ImportedServerDeploymentGroup",ImportedServerDeploymentGroup=__decorate([prop_injectable_1().propertyInjectable],ImportedServerDeploymentGroup);class InstanceTagSet{constructor(...instanceTagGroups){if(instanceTagGroups.length>3)throw new(cdk()).UnscopedValidationError(`An instance tag set can have a maximum of 3 instance tag groups, but ${instanceTagGroups.length} were provided`);this._instanceTagGroups=instanceTagGroups}get instanceTagGroups(){return this._instanceTagGroups.slice()}}exports.InstanceTagSet=InstanceTagSet,_a=JSII_RTTI_SYMBOL_1,InstanceTagSet[_a]={fqn:"aws-cdk-lib.aws_codedeploy.InstanceTagSet",version:"2.201.0"};let ServerDeploymentGroup=class ServerDeploymentGroup2 extends base_deployment_group_1().DeploymentGroupBase{static fromServerDeploymentGroupAttributes(scope,id,attrs){try{jsiiDeprecationWarnings().aws_cdk_lib_aws_codedeploy_ServerDeploymentGroupAttributes(attrs)}catch(error){throw process.env.JSII_DEBUG!=="1"&&error.name==="DeprecationError"&&Error.captureStackTrace(error,this.fromServerDeploymentGroupAttributes),error}return new ImportedServerDeploymentGroup(scope,id,attrs)}constructor(scope,id,props={}){super(scope,id,{deploymentGroupName:props.deploymentGroupName,role:props.role,roleConstructId:"Role"});try{jsiiDeprecationWarnings().aws_cdk_lib_aws_codedeploy_ServerDeploymentGroupProps(props)}catch(error){throw process.env.JSII_DEBUG!=="1"&&error.name==="DeprecationError"&&Error.captureStackTrace(error,ServerDeploymentGroup2),error}if((0,metadata_resource_1().addConstructMetadata)(this,props),this.role=this._role,this.application=props.application||new(application_1()).ServerApplication(this,"Application",{applicationName:props.deploymentGroupName===cdk().PhysicalName.GENERATE_IF_NEEDED?cdk().PhysicalName.GENERATE_IF_NEEDED:void 0}),this.deploymentConfig=this._bindDeploymentConfig(props.deploymentConfig||deployment_config_1().ServerDeploymentConfig.ONE_AT_A_TIME),this.role.addManagedPolicy(iam().ManagedPolicy.fromAwsManagedPolicyName("service-role/AWSCodeDeployRole")),this._autoScalingGroups=props.autoScalingGroups||[],this.installAgent=props.installAgent??!0,this.codeDeployBucket=s3().Bucket.fromBucketName(this,"Bucket",`aws-codedeploy-${cdk().Stack.of(this).region}`),this.loadBalancers=props.loadBalancers||(props.loadBalancer?[props.loadBalancer]:void 0),this.loadBalancers&&this.loadBalancers.length===0)throw new(cdk()).ValidationError("loadBalancers must be a non-empty array",this);for(const asg of this._autoScalingGroups)this.addCodeDeployAgentInstallUserData(asg);this.alarms=props.alarms||[];const removeAlarmsFromDeploymentGroup=cdk().FeatureFlags.of(this).isEnabled(cx_api_1().CODEDEPLOY_REMOVE_ALARMS_FROM_DEPLOYMENT_GROUP),resource=new(codedeploy_generated_1()).CfnDeploymentGroup(this,"Resource",{applicationName:this.application.applicationName,deploymentGroupName:this.physicalName,serviceRoleArn:this.role.roleArn,deploymentConfigName:props.deploymentConfig&&props.deploymentConfig.deploymentConfigName,autoScalingGroups:cdk().Lazy.list({produce:()=>this._autoScalingGroups.map(asg=>asg.autoScalingGroupName)},{omitEmpty:!0}),loadBalancerInfo:this.loadBalancersInfo(this.loadBalancers),deploymentStyle:this.loadBalancers===void 0?void 0:{deploymentOption:"WITH_TRAFFIC_CONTROL"},ec2TagSet:this.ec2TagSet(props.ec2InstanceTags),onPremisesTagSet:this.onPremiseTagSet(props.onPremiseInstanceTags),alarmConfiguration:cdk().Lazy.any({produce:()=>(0,utils_1().renderAlarmConfiguration)({alarms:this.alarms,ignorePollAlarmFailure:props.ignorePollAlarmsFailure,removeAlarms:removeAlarmsFromDeploymentGroup,ignoreAlarmConfiguration:props.ignoreAlarmConfiguration})}),autoRollbackConfiguration:cdk().Lazy.any({produce:()=>(0,utils_1().renderAutoRollbackConfiguration)(this,this.alarms,props.autoRollback)}),terminationHookEnabled:props.terminationHook});this._setNameAndArn(resource,this.application)}addAutoScalingGroup(asg){try{jsiiDeprecationWarnings().aws_cdk_lib_aws_autoscaling_AutoScalingGroup(asg)}catch(error){throw process.env.JSII_DEBUG!=="1"&&error.name==="DeprecationError"&&Error.captureStackTrace(error,this.addAutoScalingGroup),error}this._autoScalingGroups.push(asg),this.addCodeDeployAgentInstallUserData(asg)}addAlarm(alarm){try{jsiiDeprecationWarnings().aws_cdk_lib_aws_cloudwatch_IAlarm(alarm)}catch(error){throw process.env.JSII_DEBUG!=="1"&&error.name==="DeprecationError"&&Error.captureStackTrace(error,this.addAlarm),error}this.alarms.push(alarm)}get autoScalingGroups(){return this._autoScalingGroups.slice()}addCodeDeployAgentInstallUserData(asg){if(this.installAgent)switch(this.codeDeployBucket.grantRead(asg,"latest/*"),asg.osType){case ec2().OperatingSystemType.LINUX:asg.addUserData("set +e","PKG_CMD=`which yum 2>/dev/null`","set -e",'if [ -z "$PKG_CMD" ]; then',"PKG_CMD=apt-get","else","PKG_CMD=yum","fi","$PKG_CMD update -y","set +e","$PKG_CMD install -y ruby2.0","RUBY2_INSTALL=$?","set -e","if [ $RUBY2_INSTALL -ne 0 ]; then","$PKG_CMD install -y ruby","fi","AWS_CLI_PACKAGE_NAME=awscli",'if [ "$PKG_CMD" = "yum" ]; then',"AWS_CLI_PACKAGE_NAME=aws-cli","fi","$PKG_CMD install -y $AWS_CLI_PACKAGE_NAME","TMP_DIR=`mktemp -d`","cd $TMP_DIR",`aws s3 cp s3://aws-codedeploy-${cdk().Stack.of(this).region}/latest/install . --region ${cdk().Stack.of(this).region}`,"chmod +x ./install","./install auto","rm -fr $TMP_DIR");break;case ec2().OperatingSystemType.WINDOWS:asg.addUserData("Set-Variable -Name TEMPDIR -Value (New-TemporaryFile).DirectoryName",`aws s3 cp s3://aws-codedeploy-${cdk().Stack.of(this).region}/latest/codedeploy-agent.msi $TEMPDIR\\codedeploy-agent.msi`,"cd $TEMPDIR",".\\codedeploy-agent.msi /quiet /l c:\\temp\\host-agent-install-log.txt");break}}loadBalancersInfo(loadBalancers){return loadBalancers?.reduce((accumulator,loadBalancer)=>{switch(loadBalancer.generation){case load_balancer_1().LoadBalancerGeneration.FIRST:return accumulator.elbInfoList||(accumulator.elbInfoList=[]),accumulator.elbInfoList.push({name:loadBalancer.name}),accumulator;case load_balancer_1().LoadBalancerGeneration.SECOND:return accumulator.targetGroupInfoList||(accumulator.targetGroupInfoList=[]),accumulator.targetGroupInfoList.push({name:loadBalancer.name}),accumulator;default:return accumulator}},{})}ec2TagSet(tagSet){if(!(!tagSet||tagSet.instanceTagGroups.length===0))return{ec2TagSetList:tagSet.instanceTagGroups.map(tagGroup=>({ec2TagGroup:this.tagGroup2TagsArray(tagGroup)}))}}onPremiseTagSet(tagSet){if(!(!tagSet||tagSet.instanceTagGroups.length===0))return{onPremisesTagSetList:tagSet.instanceTagGroups.map(tagGroup=>({onPremisesTagGroup:this.tagGroup2TagsArray(tagGroup)}))}}tagGroup2TagsArray(tagGroup){const tagsInGroup=new Array;for(const tagKey in tagGroup)if(tagGroup.hasOwnProperty(tagKey)){const tagValues=tagGroup[tagKey];if(tagKey.length>0)if(tagValues.length>0)for(const tagValue of tagValues)tagsInGroup.push({key:tagKey,value:tagValue,type:"KEY_AND_VALUE"});else tagsInGroup.push({key:tagKey,type:"KEY_ONLY"});else if(tagValues.length>0)for(const tagValue of tagValues)tagsInGroup.push({value:tagValue,type:"VALUE_ONLY"});else throw new(cdk()).ValidationError("Cannot specify both an empty key and no values for an instance tag filter",this)}return tagsInGroup}};exports.ServerDeploymentGroup=ServerDeploymentGroup,_b=JSII_RTTI_SYMBOL_1,ServerDeploymentGroup[_b]={fqn:"aws-cdk-lib.aws_codedeploy.ServerDeploymentGroup",version:"2.201.0"},ServerDeploymentGroup.PROPERTY_INJECTION_ID="aws-cdk-lib.aws-codedeploy.ServerDeploymentGroup",__decorate([(0,metadata_resource_1().MethodMetadata)()],ServerDeploymentGroup.prototype,"addAutoScalingGroup",null),__decorate([(0,metadata_resource_1().MethodMetadata)()],ServerDeploymentGroup.prototype,"addAlarm",null),exports.ServerDeploymentGroup=ServerDeploymentGroup=__decorate([prop_injectable_1().propertyInjectable],ServerDeploymentGroup);
