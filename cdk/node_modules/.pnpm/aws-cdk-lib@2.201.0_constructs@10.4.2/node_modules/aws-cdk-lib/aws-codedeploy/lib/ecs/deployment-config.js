"use strict";var __decorate=exports&&exports.__decorate||function(decorators,target,key,desc){var c=arguments.length,r=c<3?target:desc===null?desc=Object.getOwnPropertyDescriptor(target,key):desc,d;if(typeof Reflect=="object"&&typeof Reflect.decorate=="function")r=Reflect.decorate(decorators,target,key,desc);else for(var i=decorators.length-1;i>=0;i--)(d=decorators[i])&&(r=(c<3?d(r):c>3?d(target,key,r):d(target,key))||r);return c>3&&r&&Object.defineProperty(target,key,r),r},_a,EcsDeploymentConfig_1;Object.defineProperty(exports,"__esModule",{value:!0}),exports.EcsDeploymentConfig=void 0;var jsiiDeprecationWarnings=()=>{var tmp=require("../../../.warnings.jsii.js");return jsiiDeprecationWarnings=()=>tmp,tmp};const JSII_RTTI_SYMBOL_1=Symbol.for("jsii.rtti");var metadata_resource_1=()=>{var tmp=require("../../../core/lib/metadata-resource");return metadata_resource_1=()=>tmp,tmp},prop_injectable_1=()=>{var tmp=require("../../../core/lib/prop-injectable");return prop_injectable_1=()=>tmp,tmp},base_deployment_config_1=()=>{var tmp=require("../base-deployment-config");return base_deployment_config_1=()=>tmp,tmp},utils_1=()=>{var tmp=require("../private/utils");return utils_1=()=>tmp,tmp},traffic_routing_config_1=()=>{var tmp=require("../traffic-routing-config");return traffic_routing_config_1=()=>tmp,tmp};let EcsDeploymentConfig=EcsDeploymentConfig_1=class EcsDeploymentConfig2 extends base_deployment_config_1().BaseDeploymentConfig{static fromEcsDeploymentConfigName(scope,id,ecsDeploymentConfigName){return this.fromDeploymentConfigName(scope,id,ecsDeploymentConfigName)}static deploymentConfig(name){return(0,utils_1().deploymentConfig)(name)}constructor(scope,id,props){super(scope,id,{...props,computePlatform:base_deployment_config_1().ComputePlatform.ECS,trafficRouting:props?.trafficRouting??traffic_routing_config_1().TrafficRouting.allAtOnce()});try{jsiiDeprecationWarnings().aws_cdk_lib_aws_codedeploy_EcsDeploymentConfigProps(props)}catch(error){throw process.env.JSII_DEBUG!=="1"&&error.name==="DeprecationError"&&Error.captureStackTrace(error,EcsDeploymentConfig2),error}(0,metadata_resource_1().addConstructMetadata)(this,props)}};exports.EcsDeploymentConfig=EcsDeploymentConfig,_a=JSII_RTTI_SYMBOL_1,EcsDeploymentConfig[_a]={fqn:"aws-cdk-lib.aws_codedeploy.EcsDeploymentConfig",version:"2.201.0"},EcsDeploymentConfig.PROPERTY_INJECTION_ID="aws-cdk-lib.aws-codedeploy.EcsDeploymentConfig",EcsDeploymentConfig.ALL_AT_ONCE=EcsDeploymentConfig_1.deploymentConfig("CodeDeployDefault.ECSAllAtOnce"),EcsDeploymentConfig.LINEAR_10PERCENT_EVERY_1MINUTES=EcsDeploymentConfig_1.deploymentConfig("CodeDeployDefault.ECSLinear10PercentEvery1Minutes"),EcsDeploymentConfig.LINEAR_10PERCENT_EVERY_3MINUTES=EcsDeploymentConfig_1.deploymentConfig("CodeDeployDefault.ECSLinear10PercentEvery3Minutes"),EcsDeploymentConfig.CANARY_10PERCENT_5MINUTES=EcsDeploymentConfig_1.deploymentConfig("CodeDeployDefault.ECSCanary10Percent5Minutes"),EcsDeploymentConfig.CANARY_10PERCENT_15MINUTES=EcsDeploymentConfig_1.deploymentConfig("CodeDeployDefault.ECSCanary10Percent15Minutes"),exports.EcsDeploymentConfig=EcsDeploymentConfig=EcsDeploymentConfig_1=__decorate([prop_injectable_1().propertyInjectable],EcsDeploymentConfig);
