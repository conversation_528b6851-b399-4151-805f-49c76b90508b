"use strict";var __decorate=exports&&exports.__decorate||function(decorators,target,key,desc){var c=arguments.length,r=c<3?target:desc===null?desc=Object.getOwnPropertyDescriptor(target,key):desc,d;if(typeof Reflect=="object"&&typeof Reflect.decorate=="function")r=Reflect.decorate(decorators,target,key,desc);else for(var i=decorators.length-1;i>=0;i--)(d=decorators[i])&&(r=(c<3?d(r):c>3?d(target,key,r):d(target,key))||r);return c>3&&r&&Object.defineProperty(target,key,r),r},_a,LambdaDeploymentConfig_1;Object.defineProperty(exports,"__esModule",{value:!0}),exports.LambdaDeploymentConfig=void 0;var jsiiDeprecationWarnings=()=>{var tmp=require("../../../.warnings.jsii.js");return jsiiDeprecationWarnings=()=>tmp,tmp};const JSII_RTTI_SYMBOL_1=Symbol.for("jsii.rtti");var metadata_resource_1=()=>{var tmp=require("../../../core/lib/metadata-resource");return metadata_resource_1=()=>tmp,tmp},prop_injectable_1=()=>{var tmp=require("../../../core/lib/prop-injectable");return prop_injectable_1=()=>tmp,tmp},base_deployment_config_1=()=>{var tmp=require("../base-deployment-config");return base_deployment_config_1=()=>tmp,tmp},utils_1=()=>{var tmp=require("../private/utils");return utils_1=()=>tmp,tmp},traffic_routing_config_1=()=>{var tmp=require("../traffic-routing-config");return traffic_routing_config_1=()=>tmp,tmp};let LambdaDeploymentConfig=LambdaDeploymentConfig_1=class LambdaDeploymentConfig2 extends base_deployment_config_1().BaseDeploymentConfig{static fromLambdaDeploymentConfigName(scope,id,lambdaDeploymentConfigName){return this.fromDeploymentConfigName(scope,id,lambdaDeploymentConfigName)}static import(_scope,_id,props){try{jsiiDeprecationWarnings().print("aws-cdk-lib.aws_codedeploy.LambdaDeploymentConfig#import","use `fromLambdaDeploymentConfigName`"),jsiiDeprecationWarnings().aws_cdk_lib_aws_codedeploy_LambdaDeploymentConfigImportProps(props)}catch(error){throw process.env.JSII_DEBUG!=="1"&&error.name==="DeprecationError"&&Error.captureStackTrace(error,this.import),error}return this.fromLambdaDeploymentConfigName(_scope,_id,props.deploymentConfigName)}static deploymentConfig(name){return(0,utils_1().deploymentConfig)(name)}constructor(scope,id,props){super(scope,id,{...props,computePlatform:base_deployment_config_1().ComputePlatform.LAMBDA,trafficRouting:props?.trafficRouting??traffic_routing_config_1().TrafficRouting.allAtOnce()});try{jsiiDeprecationWarnings().aws_cdk_lib_aws_codedeploy_LambdaDeploymentConfigProps(props)}catch(error){throw process.env.JSII_DEBUG!=="1"&&error.name==="DeprecationError"&&Error.captureStackTrace(error,LambdaDeploymentConfig2),error}(0,metadata_resource_1().addConstructMetadata)(this,props)}};exports.LambdaDeploymentConfig=LambdaDeploymentConfig,_a=JSII_RTTI_SYMBOL_1,LambdaDeploymentConfig[_a]={fqn:"aws-cdk-lib.aws_codedeploy.LambdaDeploymentConfig",version:"2.201.0"},LambdaDeploymentConfig.PROPERTY_INJECTION_ID="aws-cdk-lib.aws-codedeploy.LambdaDeploymentConfig",LambdaDeploymentConfig.ALL_AT_ONCE=LambdaDeploymentConfig_1.deploymentConfig("CodeDeployDefault.LambdaAllAtOnce"),LambdaDeploymentConfig.CANARY_10PERCENT_30MINUTES=LambdaDeploymentConfig_1.deploymentConfig("CodeDeployDefault.LambdaCanary10Percent30Minutes"),LambdaDeploymentConfig.CANARY_10PERCENT_5MINUTES=LambdaDeploymentConfig_1.deploymentConfig("CodeDeployDefault.LambdaCanary10Percent5Minutes"),LambdaDeploymentConfig.CANARY_10PERCENT_10MINUTES=LambdaDeploymentConfig_1.deploymentConfig("CodeDeployDefault.LambdaCanary10Percent10Minutes"),LambdaDeploymentConfig.CANARY_10PERCENT_15MINUTES=LambdaDeploymentConfig_1.deploymentConfig("CodeDeployDefault.LambdaCanary10Percent15Minutes"),LambdaDeploymentConfig.LINEAR_10PERCENT_EVERY_10MINUTES=LambdaDeploymentConfig_1.deploymentConfig("CodeDeployDefault.LambdaLinear10PercentEvery10Minutes"),LambdaDeploymentConfig.LINEAR_10PERCENT_EVERY_1MINUTE=LambdaDeploymentConfig_1.deploymentConfig("CodeDeployDefault.LambdaLinear10PercentEvery1Minute"),LambdaDeploymentConfig.LINEAR_10PERCENT_EVERY_2MINUTES=LambdaDeploymentConfig_1.deploymentConfig("CodeDeployDefault.LambdaLinear10PercentEvery2Minutes"),LambdaDeploymentConfig.LINEAR_10PERCENT_EVERY_3MINUTES=LambdaDeploymentConfig_1.deploymentConfig("CodeDeployDefault.LambdaLinear10PercentEvery3Minutes"),exports.LambdaDeploymentConfig=LambdaDeploymentConfig=LambdaDeploymentConfig_1=__decorate([prop_injectable_1().propertyInjectable],LambdaDeploymentConfig);
