"use strict";var __decorate=exports&&exports.__decorate||function(decorators,target,key,desc){var c=arguments.length,r=c<3?target:desc===null?desc=Object.getOwnPropertyDescriptor(target,key):desc,d;if(typeof Reflect=="object"&&typeof Reflect.decorate=="function")r=Reflect.decorate(decorators,target,key,desc);else for(var i=decorators.length-1;i>=0;i--)(d=decorators[i])&&(r=(c<3?d(r):c>3?d(target,key,r):d(target,key))||r);return c>3&&r&&Object.defineProperty(target,key,r),r};Object.defineProperty(exports,"__esModule",{value:!0}),exports.DeploymentGroupBase=exports.ImportedDeploymentGroupBase=void 0;var predefined_deployment_config_1=()=>{var tmp=require("./predefined-deployment-config");return predefined_deployment_config_1=()=>tmp,tmp},utils_1=()=>{var tmp=require("./utils");return utils_1=()=>tmp,tmp},iam=()=>{var tmp=require("../../../aws-iam");return iam=()=>tmp,tmp},core_1=()=>{var tmp=require("../../../core");return core_1=()=>tmp,tmp},metadata_resource_1=()=>{var tmp=require("../../../core/lib/metadata-resource");return metadata_resource_1=()=>tmp,tmp},prop_injectable_1=()=>{var tmp=require("../../../core/lib/prop-injectable");return prop_injectable_1=()=>tmp,tmp};let ImportedDeploymentGroupBase=class extends core_1().Resource{constructor(scope,id,props){const deploymentGroupName=props.deploymentGroupName,deploymentGroupArn=core_1().Arn.format({partition:core_1().Aws.PARTITION,account:props.application.env.account,region:props.application.env.region,service:"codedeploy",resource:"deploymentgroup",resourceName:`${props.application.applicationName}/${deploymentGroupName}`,arnFormat:core_1().ArnFormat.COLON_RESOURCE_NAME});super(scope,id,{environmentFromArn:deploymentGroupArn}),(0,metadata_resource_1().addConstructMetadata)(this,props),this.deploymentGroupName=deploymentGroupName,this.deploymentGroupArn=deploymentGroupArn}_bindDeploymentConfig(config){return(0,predefined_deployment_config_1().isPredefinedDeploymentConfig)(config)?config.bindEnvironment(this):config}};exports.ImportedDeploymentGroupBase=ImportedDeploymentGroupBase,ImportedDeploymentGroupBase.PROPERTY_INJECTION_ID="aws-cdk-lib.aws-codedeploy.ImportedDeploymentGroupBase",exports.ImportedDeploymentGroupBase=ImportedDeploymentGroupBase=__decorate([prop_injectable_1().propertyInjectable],ImportedDeploymentGroupBase);let DeploymentGroupBase=class extends core_1().Resource{constructor(scope,id,props){super(scope,id,{physicalName:props.deploymentGroupName}),(0,metadata_resource_1().addConstructMetadata)(this,props),this._role=props.role||new(iam()).Role(this,props.roleConstructId,{assumedBy:new(iam()).ServicePrincipal("codedeploy.amazonaws.com")}),this.node.addValidation({validate:()=>(0,utils_1().validateName)("Deployment group",this.physicalName)})}_bindDeploymentConfig(config){return(0,predefined_deployment_config_1().isPredefinedDeploymentConfig)(config)?config.bindEnvironment(this):config}_setNameAndArn(resource,application){this.deploymentGroupName=this.getResourceNameAttribute(resource.ref),this.deploymentGroupArn=this.getResourceArnAttribute(this.stack.formatArn({service:"codedeploy",resource:"deploymentgroup",resourceName:`${application.applicationName}/${resource.ref}`,arnFormat:core_1().ArnFormat.COLON_RESOURCE_NAME}),{service:"codedeploy",resource:"deploymentgroup",resourceName:`${application.applicationName}/${this.physicalName}`,arnFormat:core_1().ArnFormat.COLON_RESOURCE_NAME})}};exports.DeploymentGroupBase=DeploymentGroupBase,DeploymentGroupBase.PROPERTY_INJECTION_ID="aws-cdk-lib.aws-codedeploy.DeploymentGroupBase",exports.DeploymentGroupBase=DeploymentGroupBase=__decorate([prop_injectable_1().propertyInjectable],DeploymentGroupBase);
