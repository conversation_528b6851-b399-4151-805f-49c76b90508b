"use strict";var __createBinding=exports&&exports.__createBinding||(Object.create?function(o,m,k,k2){k2===void 0&&(k2=k);var desc=Object.getOwnPropertyDescriptor(m,k);(!desc||("get"in desc?!m.__esModule:desc.writable||desc.configurable))&&(desc={enumerable:!0,get:function(){return m[k]}}),Object.defineProperty(o,k2,desc)}:function(o,m,k,k2){k2===void 0&&(k2=k),o[k2]=m[k]}),__exportStar=exports&&exports.__exportStar||function(m,exports2){for(var p in m)p!=="default"&&!Object.prototype.hasOwnProperty.call(exports2,p)&&__createBinding(exports2,m,p)};Object.defineProperty(exports,"__esModule",{value:!0});var _noFold;exports.BucketBase=void 0,Object.defineProperty(exports,_noFold="BucketBase",{enumerable:!0,configurable:!0,get:()=>require("./lib").BucketBase}),exports.BlockPublicAccess=void 0,Object.defineProperty(exports,_noFold="BlockPublicAccess",{enumerable:!0,configurable:!0,get:()=>require("./lib").BlockPublicAccess}),exports.HttpMethods=void 0,Object.defineProperty(exports,_noFold="HttpMethods",{enumerable:!0,configurable:!0,get:()=>require("./lib").HttpMethods}),exports.RedirectProtocol=void 0,Object.defineProperty(exports,_noFold="RedirectProtocol",{enumerable:!0,configurable:!0,get:()=>require("./lib").RedirectProtocol}),exports.InventoryFormat=void 0,Object.defineProperty(exports,_noFold="InventoryFormat",{enumerable:!0,configurable:!0,get:()=>require("./lib").InventoryFormat}),exports.InventoryFrequency=void 0,Object.defineProperty(exports,_noFold="InventoryFrequency",{enumerable:!0,configurable:!0,get:()=>require("./lib").InventoryFrequency}),exports.InventoryObjectVersion=void 0,Object.defineProperty(exports,_noFold="InventoryObjectVersion",{enumerable:!0,configurable:!0,get:()=>require("./lib").InventoryObjectVersion}),exports.ObjectOwnership=void 0,Object.defineProperty(exports,_noFold="ObjectOwnership",{enumerable:!0,configurable:!0,get:()=>require("./lib").ObjectOwnership}),exports.PartitionDateSource=void 0,Object.defineProperty(exports,_noFold="PartitionDateSource",{enumerable:!0,configurable:!0,get:()=>require("./lib").PartitionDateSource}),exports.TargetObjectKeyFormat=void 0,Object.defineProperty(exports,_noFold="TargetObjectKeyFormat",{enumerable:!0,configurable:!0,get:()=>require("./lib").TargetObjectKeyFormat}),exports.ReplicationTimeValue=void 0,Object.defineProperty(exports,_noFold="ReplicationTimeValue",{enumerable:!0,configurable:!0,get:()=>require("./lib").ReplicationTimeValue}),exports.TransitionDefaultMinimumObjectSize=void 0,Object.defineProperty(exports,_noFold="TransitionDefaultMinimumObjectSize",{enumerable:!0,configurable:!0,get:()=>require("./lib").TransitionDefaultMinimumObjectSize}),exports.Bucket=void 0,Object.defineProperty(exports,_noFold="Bucket",{enumerable:!0,configurable:!0,get:()=>require("./lib").Bucket}),exports.BucketEncryption=void 0,Object.defineProperty(exports,_noFold="BucketEncryption",{enumerable:!0,configurable:!0,get:()=>require("./lib").BucketEncryption}),exports.EventType=void 0,Object.defineProperty(exports,_noFold="EventType",{enumerable:!0,configurable:!0,get:()=>require("./lib").EventType}),exports.BucketAccessControl=void 0,Object.defineProperty(exports,_noFold="BucketAccessControl",{enumerable:!0,configurable:!0,get:()=>require("./lib").BucketAccessControl}),exports.ReplaceKey=void 0,Object.defineProperty(exports,_noFold="ReplaceKey",{enumerable:!0,configurable:!0,get:()=>require("./lib").ReplaceKey}),exports.ObjectLockMode=void 0,Object.defineProperty(exports,_noFold="ObjectLockMode",{enumerable:!0,configurable:!0,get:()=>require("./lib").ObjectLockMode}),exports.ObjectLockRetention=void 0,Object.defineProperty(exports,_noFold="ObjectLockRetention",{enumerable:!0,configurable:!0,get:()=>require("./lib").ObjectLockRetention}),exports.BucketPolicy=void 0,Object.defineProperty(exports,_noFold="BucketPolicy",{enumerable:!0,configurable:!0,get:()=>require("./lib").BucketPolicy}),exports.BucketNotificationDestinationType=void 0,Object.defineProperty(exports,_noFold="BucketNotificationDestinationType",{enumerable:!0,configurable:!0,get:()=>require("./lib").BucketNotificationDestinationType}),exports.StorageClass=void 0,Object.defineProperty(exports,_noFold="StorageClass",{enumerable:!0,configurable:!0,get:()=>require("./lib").StorageClass}),exports.CfnAccessPoint=void 0,Object.defineProperty(exports,_noFold="CfnAccessPoint",{enumerable:!0,configurable:!0,get:()=>require("./lib").CfnAccessPoint}),exports.CfnBucket=void 0,Object.defineProperty(exports,_noFold="CfnBucket",{enumerable:!0,configurable:!0,get:()=>require("./lib").CfnBucket}),exports.CfnBucketPolicy=void 0,Object.defineProperty(exports,_noFold="CfnBucketPolicy",{enumerable:!0,configurable:!0,get:()=>require("./lib").CfnBucketPolicy}),exports.CfnMultiRegionAccessPoint=void 0,Object.defineProperty(exports,_noFold="CfnMultiRegionAccessPoint",{enumerable:!0,configurable:!0,get:()=>require("./lib").CfnMultiRegionAccessPoint}),exports.CfnMultiRegionAccessPointPolicy=void 0,Object.defineProperty(exports,_noFold="CfnMultiRegionAccessPointPolicy",{enumerable:!0,configurable:!0,get:()=>require("./lib").CfnMultiRegionAccessPointPolicy}),exports.CfnStorageLens=void 0,Object.defineProperty(exports,_noFold="CfnStorageLens",{enumerable:!0,configurable:!0,get:()=>require("./lib").CfnStorageLens}),exports.CfnAccessGrant=void 0,Object.defineProperty(exports,_noFold="CfnAccessGrant",{enumerable:!0,configurable:!0,get:()=>require("./lib").CfnAccessGrant}),exports.CfnAccessGrantsInstance=void 0,Object.defineProperty(exports,_noFold="CfnAccessGrantsInstance",{enumerable:!0,configurable:!0,get:()=>require("./lib").CfnAccessGrantsInstance}),exports.CfnAccessGrantsLocation=void 0,Object.defineProperty(exports,_noFold="CfnAccessGrantsLocation",{enumerable:!0,configurable:!0,get:()=>require("./lib").CfnAccessGrantsLocation}),exports.CfnStorageLensGroup=void 0,Object.defineProperty(exports,_noFold="CfnStorageLensGroup",{enumerable:!0,configurable:!0,get:()=>require("./lib").CfnStorageLensGroup});
