"use strict";var __decorate=exports&&exports.__decorate||function(decorators,target,key,desc){var c=arguments.length,r=c<3?target:desc===null?desc=Object.getOwnPropertyDescriptor(target,key):desc,d;if(typeof Reflect=="object"&&typeof Reflect.decorate=="function")r=Reflect.decorate(decorators,target,key,desc);else for(var i=decorators.length-1;i>=0;i--)(d=decorators[i])&&(r=(c<3?d(r):c>3?d(target,key,r):d(target,key))||r);return c>3&&r&&Object.defineProperty(target,key,r),r},_a,BucketPolicy_1;Object.defineProperty(exports,"__esModule",{value:!0}),exports.BucketPolicy=void 0;var jsiiDeprecationWarnings=()=>{var tmp=require("../../.warnings.jsii.js");return jsiiDeprecationWarnings=()=>tmp,tmp};const JSII_RTTI_SYMBOL_1=Symbol.for("jsii.rtti");var bucket_1=()=>{var tmp=require("./bucket");return bucket_1=()=>tmp,tmp},s3_generated_1=()=>{var tmp=require("./s3.generated");return s3_generated_1=()=>tmp,tmp},aws_iam_1=()=>{var tmp=require("../../aws-iam");return aws_iam_1=()=>tmp,tmp},core_1=()=>{var tmp=require("../../core");return core_1=()=>tmp,tmp},metadata_resource_1=()=>{var tmp=require("../../core/lib/metadata-resource");return metadata_resource_1=()=>tmp,tmp},cfn_reference_1=()=>{var tmp=require("../../core/lib/private/cfn-reference");return cfn_reference_1=()=>tmp,tmp},prop_injectable_1=()=>{var tmp=require("../../core/lib/prop-injectable");return prop_injectable_1=()=>tmp,tmp};let BucketPolicy=BucketPolicy_1=class BucketPolicy2 extends core_1().Resource{static fromCfnBucketPolicy(cfnBucketPolicy){try{jsiiDeprecationWarnings().aws_cdk_lib_aws_s3_CfnBucketPolicy(cfnBucketPolicy)}catch(error){throw process.env.JSII_DEBUG!=="1"&&error.name==="DeprecationError"&&Error.captureStackTrace(error,this.fromCfnBucketPolicy),error}const id="@FromCfnBucketPolicy",existing=cfnBucketPolicy.node.tryFindChild(id);if(existing)return existing;let bucket;if(core_1().Token.isUnresolved(cfnBucketPolicy.bucket)){const bucketIResolvable=core_1().Tokenization.reverse(cfnBucketPolicy.bucket);if(bucketIResolvable instanceof cfn_reference_1().CfnReference){const cfnElement=bucketIResolvable.target;cfnElement instanceof s3_generated_1().CfnBucket&&(bucket=bucket_1().Bucket.fromCfnBucket(cfnElement))}}bucket||(bucket=bucket_1().Bucket.fromBucketName(cfnBucketPolicy,"@FromCfnBucket",cfnBucketPolicy.bucket));const ret=new class extends BucketPolicy_1{constructor(){super(...arguments),this.document=aws_iam_1().PolicyDocument.fromJson(cfnBucketPolicy.policyDocument)}}(cfnBucketPolicy,id,{bucket});return bucket.policy=ret,ret}constructor(scope,id,props){super(scope,id),this.document=new(aws_iam_1()).PolicyDocument;try{jsiiDeprecationWarnings().aws_cdk_lib_aws_s3_BucketPolicyProps(props)}catch(error){throw process.env.JSII_DEBUG!=="1"&&error.name==="DeprecationError"&&Error.captureStackTrace(error,BucketPolicy2),error}(0,metadata_resource_1().addConstructMetadata)(this,props),this.bucket=props.bucket,this.resource=new(s3_generated_1()).CfnBucketPolicy(this,"Resource",{bucket:this.bucket.bucketName,policyDocument:this.document}),props.removalPolicy&&this.resource.applyRemovalPolicy(props.removalPolicy)}applyRemovalPolicy(removalPolicy){try{jsiiDeprecationWarnings().aws_cdk_lib_RemovalPolicy(removalPolicy)}catch(error){throw process.env.JSII_DEBUG!=="1"&&error.name==="DeprecationError"&&Error.captureStackTrace(error,this.applyRemovalPolicy),error}this.resource.applyRemovalPolicy(removalPolicy)}};exports.BucketPolicy=BucketPolicy,_a=JSII_RTTI_SYMBOL_1,BucketPolicy[_a]={fqn:"aws-cdk-lib.aws_s3.BucketPolicy",version:"2.201.0"},BucketPolicy.PROPERTY_INJECTION_ID="aws-cdk-lib.aws-s3.BucketPolicy",__decorate([(0,metadata_resource_1().MethodMetadata)()],BucketPolicy.prototype,"applyRemovalPolicy",null),exports.BucketPolicy=BucketPolicy=BucketPolicy_1=__decorate([prop_injectable_1().propertyInjectable],BucketPolicy);
