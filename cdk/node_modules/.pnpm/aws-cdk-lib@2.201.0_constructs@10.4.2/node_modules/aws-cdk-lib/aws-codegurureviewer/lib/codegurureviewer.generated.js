"use strict";var _a;Object.defineProperty(exports,"__esModule",{value:!0}),exports.CfnRepositoryAssociation=void 0;var jsiiDeprecationWarnings=()=>{var tmp=require("../../.warnings.jsii.js");return jsiiDeprecationWarnings=()=>tmp,tmp};const JSII_RTTI_SYMBOL_1=Symbol.for("jsii.rtti");var cdk=()=>{var tmp=require("../../core");return cdk=()=>tmp,tmp},cfn_parse=()=>{var tmp=require("../../core/lib/helpers-internal");return cfn_parse=()=>tmp,tmp},cdk_errors=()=>{var tmp=require("../../core/lib/errors");return cdk_errors=()=>tmp,tmp};class CfnRepositoryAssociation extends cdk().CfnResource{static _fromCloudFormation(scope,id,resourceAttributes,options){resourceAttributes=resourceAttributes||{};const resourceProperties=options.parser.parseValue(resourceAttributes.Properties),propsResult=CfnRepositoryAssociationPropsFromCloudFormation(resourceProperties);if(cdk().isResolvableObject(propsResult.value))throw new(cdk_errors()).ValidationError("Unexpected IResolvable",scope);const ret=new CfnRepositoryAssociation(scope,id,propsResult.value);for(const[propKey,propVal]of Object.entries(propsResult.extraProperties))ret.addPropertyOverride(propKey,propVal);return options.parser.handleAttributes(ret,resourceAttributes,id),ret}constructor(scope,id,props){super(scope,id,{type:CfnRepositoryAssociation.CFN_RESOURCE_TYPE_NAME,properties:props});try{jsiiDeprecationWarnings().aws_cdk_lib_aws_codegurureviewer_CfnRepositoryAssociationProps(props)}catch(error){throw process.env.JSII_DEBUG!=="1"&&error.name==="DeprecationError"&&Error.captureStackTrace(error,CfnRepositoryAssociation),error}cdk().requireProperty(props,"name",this),cdk().requireProperty(props,"type",this),this.attrAssociationArn=cdk().Token.asString(this.getAtt("AssociationArn",cdk().ResolutionTypeHint.STRING)),this.bucketName=props.bucketName,this.connectionArn=props.connectionArn,this.name=props.name,this.owner=props.owner,this.tags=new(cdk()).TagManager(cdk().TagType.STANDARD,"AWS::CodeGuruReviewer::RepositoryAssociation",props.tags,{tagPropertyName:"tags"}),this.tagsRaw=props.tags,this.type=props.type}get cfnProperties(){return{bucketName:this.bucketName,connectionArn:this.connectionArn,name:this.name,owner:this.owner,tags:this.tags.renderTags(),type:this.type}}inspect(inspector){try{jsiiDeprecationWarnings().aws_cdk_lib_TreeInspector(inspector)}catch(error){throw process.env.JSII_DEBUG!=="1"&&error.name==="DeprecationError"&&Error.captureStackTrace(error,this.inspect),error}inspector.addAttribute("aws:cdk:cloudformation:type",CfnRepositoryAssociation.CFN_RESOURCE_TYPE_NAME),inspector.addAttribute("aws:cdk:cloudformation:props",this.cfnProperties)}renderProperties(props){return convertCfnRepositoryAssociationPropsToCloudFormation(props)}}exports.CfnRepositoryAssociation=CfnRepositoryAssociation,_a=JSII_RTTI_SYMBOL_1,CfnRepositoryAssociation[_a]={fqn:"aws-cdk-lib.aws_codegurureviewer.CfnRepositoryAssociation",version:"2.201.0"},CfnRepositoryAssociation.CFN_RESOURCE_TYPE_NAME="AWS::CodeGuruReviewer::RepositoryAssociation";function CfnRepositoryAssociationPropsValidator(properties){if(!cdk().canInspect(properties))return cdk().VALIDATION_SUCCESS;const errors=new(cdk()).ValidationResults;return properties&&typeof properties=="object"&&!Array.isArray(properties)||errors.collect(new(cdk()).ValidationResult("Expected an object, but received: "+JSON.stringify(properties))),errors.collect(cdk().propertyValidator("bucketName",cdk().validateString)(properties.bucketName)),errors.collect(cdk().propertyValidator("connectionArn",cdk().validateString)(properties.connectionArn)),errors.collect(cdk().propertyValidator("name",cdk().requiredValidator)(properties.name)),errors.collect(cdk().propertyValidator("name",cdk().validateString)(properties.name)),errors.collect(cdk().propertyValidator("owner",cdk().validateString)(properties.owner)),errors.collect(cdk().propertyValidator("tags",cdk().listValidator(cdk().validateCfnTag))(properties.tags)),errors.collect(cdk().propertyValidator("type",cdk().requiredValidator)(properties.type)),errors.collect(cdk().propertyValidator("type",cdk().validateString)(properties.type)),errors.wrap('supplied properties not correct for "CfnRepositoryAssociationProps"')}function convertCfnRepositoryAssociationPropsToCloudFormation(properties){return cdk().canInspect(properties)?(CfnRepositoryAssociationPropsValidator(properties).assertSuccess(),{BucketName:cdk().stringToCloudFormation(properties.bucketName),ConnectionArn:cdk().stringToCloudFormation(properties.connectionArn),Name:cdk().stringToCloudFormation(properties.name),Owner:cdk().stringToCloudFormation(properties.owner),Tags:cdk().listMapper(cdk().cfnTagToCloudFormation)(properties.tags),Type:cdk().stringToCloudFormation(properties.type)}):properties}function CfnRepositoryAssociationPropsFromCloudFormation(properties){if(cdk().isResolvableObject(properties))return new(cfn_parse()).FromCloudFormationResult(properties);if(properties=properties??{},!(properties&&typeof properties=="object"&&!Array.isArray(properties)))return new(cfn_parse()).FromCloudFormationResult(properties);const ret=new(cfn_parse()).FromCloudFormationPropertyObject;return ret.addPropertyResult("bucketName","BucketName",properties.BucketName!=null?cfn_parse().FromCloudFormation.getString(properties.BucketName):void 0),ret.addPropertyResult("connectionArn","ConnectionArn",properties.ConnectionArn!=null?cfn_parse().FromCloudFormation.getString(properties.ConnectionArn):void 0),ret.addPropertyResult("name","Name",properties.Name!=null?cfn_parse().FromCloudFormation.getString(properties.Name):void 0),ret.addPropertyResult("owner","Owner",properties.Owner!=null?cfn_parse().FromCloudFormation.getString(properties.Owner):void 0),ret.addPropertyResult("tags","Tags",properties.Tags!=null?cfn_parse().FromCloudFormation.getArray(cfn_parse().FromCloudFormation.getCfnTag)(properties.Tags):void 0),ret.addPropertyResult("type","Type",properties.Type!=null?cfn_parse().FromCloudFormation.getString(properties.Type):void 0),ret.addUnrecognizedPropertiesAsExtra(properties),ret}
