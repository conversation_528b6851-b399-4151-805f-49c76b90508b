"use strict";var _a;Object.defineProperty(exports,"__esModule",{value:!0}),exports.LambdaDestination=void 0;var jsiiDeprecationWarnings=()=>{var tmp=require("../../.warnings.jsii.js");return jsiiDeprecationWarnings=()=>tmp,tmp};const JSII_RTTI_SYMBOL_1=Symbol.for("jsii.rtti");var constructs_1=()=>{var tmp=require("constructs");return constructs_1=()=>tmp,tmp},iam=()=>{var tmp=require("../../aws-iam");return iam=()=>tmp,tmp},s3=()=>{var tmp=require("../../aws-s3");return s3=()=>tmp,tmp},core_1=()=>{var tmp=require("../../core");return core_1=()=>tmp,tmp},errors_1=()=>{var tmp=require("../../core/lib/errors");return errors_1=()=>tmp,tmp};class LambdaDestination{constructor(fn){this.fn=fn;try{jsiiDeprecationWarnings().aws_cdk_lib_aws_lambda_IFunction(fn)}catch(error){throw process.env.JSII_DEBUG!=="1"&&error.name==="DeprecationError"&&Error.captureStackTrace(error,LambdaDestination),error}}bind(scope,bucket){try{jsiiDeprecationWarnings().aws_cdk_lib_aws_s3_IBucket(bucket)}catch(error){throw process.env.JSII_DEBUG!=="1"&&error.name==="DeprecationError"&&Error.captureStackTrace(error,this.bind),error}const permissionId=`AllowBucketNotificationsTo${core_1().Names.nodeUniqueId(this.fn.permissionsNode)}`;if(!(bucket instanceof constructs_1().Construct))throw new(errors_1()).ValidationError(`LambdaDestination for function ${core_1().Names.nodeUniqueId(this.fn.permissionsNode)} can only be configured on a
        bucket construct (Bucket ${bucket.bucketName})`,scope);bucket.node.tryFindChild(permissionId)===void 0&&this.fn.addPermission(permissionId,{sourceAccount:core_1().Stack.of(bucket).account,principal:new(iam()).ServicePrincipal("s3.amazonaws.com"),sourceArn:bucket.bucketArn,scope:bucket});const permission=bucket.node.tryFindChild(permissionId);return{type:s3().BucketNotificationDestinationType.LAMBDA,arn:this.fn.functionArn,dependencies:permission?[permission]:void 0}}}exports.LambdaDestination=LambdaDestination,_a=JSII_RTTI_SYMBOL_1,LambdaDestination[_a]={fqn:"aws-cdk-lib.aws_s3_notifications.LambdaDestination",version:"2.201.0"};
