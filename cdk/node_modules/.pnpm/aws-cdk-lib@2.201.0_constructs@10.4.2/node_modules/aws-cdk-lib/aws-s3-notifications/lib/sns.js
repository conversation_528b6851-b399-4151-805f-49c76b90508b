"use strict";var _a;Object.defineProperty(exports,"__esModule",{value:!0}),exports.SnsDestination=void 0;var jsiiDeprecationWarnings=()=>{var tmp=require("../../.warnings.jsii.js");return jsiiDeprecationWarnings=()=>tmp,tmp};const JSII_RTTI_SYMBOL_1=Symbol.for("jsii.rtti");var iam=()=>{var tmp=require("../../aws-iam");return iam=()=>tmp,tmp},s3=()=>{var tmp=require("../../aws-s3");return s3=()=>tmp,tmp},core_1=()=>{var tmp=require("../../core");return core_1=()=>tmp,tmp},cxapi=()=>{var tmp=require("../../cx-api");return cxapi=()=>tmp,tmp};class SnsDestination{constructor(topic){this.topic=topic;try{jsiiDeprecationWarnings().aws_cdk_lib_aws_sns_ITopic(topic)}catch(error){throw process.env.JSII_DEBUG!=="1"&&error.name==="DeprecationError"&&Error.captureStackTrace(error,SnsDestination),error}}bind(scope,bucket){try{jsiiDeprecationWarnings().aws_cdk_lib_aws_s3_IBucket(bucket)}catch(error){throw process.env.JSII_DEBUG!=="1"&&error.name==="DeprecationError"&&Error.captureStackTrace(error,this.bind),error}if(this.topic.addToResourcePolicy(new(iam()).PolicyStatement({principals:[new(iam()).ServicePrincipal("s3.amazonaws.com")],actions:["sns:Publish"],resources:[this.topic.topicArn],conditions:{ArnLike:{"aws:SourceArn":bucket.bucketArn}}})),core_1().FeatureFlags.of(scope).isEnabled(cxapi().S3_TRUST_KEY_POLICY_FOR_SNS_SUBSCRIPTIONS)&&this.topic.masterKey){const statement=new(iam()).PolicyStatement({principals:[new(iam()).ServicePrincipal("s3.amazonaws.com")],actions:["kms:GenerateDataKey","kms:Decrypt"],resources:["*"]});this.topic.masterKey.addToResourcePolicy(statement,!0).statementAdded||core_1().Annotations.of(this.topic.masterKey).addWarningV2("@aws-cdk/aws-s3-notifications:snsKMSPermissionsNotAdded",`Can not change key policy of imported kms key. Ensure that your key policy contains the following permissions: 
${JSON.stringify(statement.toJSON(),null,2)}`)}return{arn:this.topic.topicArn,type:s3().BucketNotificationDestinationType.TOPIC,dependencies:[this.topic]}}}exports.SnsDestination=SnsDestination,_a=JSII_RTTI_SYMBOL_1,SnsDestination[_a]={fqn:"aws-cdk-lib.aws_s3_notifications.SnsDestination",version:"2.201.0"};
