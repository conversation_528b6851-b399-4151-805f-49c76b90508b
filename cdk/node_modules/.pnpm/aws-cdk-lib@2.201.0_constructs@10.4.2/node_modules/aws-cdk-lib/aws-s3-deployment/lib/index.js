"use strict";var __createBinding=exports&&exports.__createBinding||(Object.create?function(o,m,k,k2){k2===void 0&&(k2=k);var desc=Object.getOwnPropertyDescriptor(m,k);(!desc||("get"in desc?!m.__esModule:desc.writable||desc.configurable))&&(desc={enumerable:!0,get:function(){return m[k]}}),Object.defineProperty(o,k2,desc)}:function(o,m,k,k2){k2===void 0&&(k2=k),o[k2]=m[k]}),__exportStar=exports&&exports.__exportStar||function(m,exports2){for(var p in m)p!=="default"&&!Object.prototype.hasOwnProperty.call(exports2,p)&&__createBinding(exports2,m,p)};Object.defineProperty(exports,"__esModule",{value:!0});var _noFold;exports.BucketDeployment=void 0,Object.defineProperty(exports,_noFold="BucketDeployment",{enumerable:!0,configurable:!0,get:()=>require("./bucket-deployment").BucketDeployment}),exports.DeployTimeSubstitutedFile=void 0,Object.defineProperty(exports,_noFold="DeployTimeSubstitutedFile",{enumerable:!0,configurable:!0,get:()=>require("./bucket-deployment").DeployTimeSubstitutedFile}),exports.CacheControl=void 0,Object.defineProperty(exports,_noFold="CacheControl",{enumerable:!0,configurable:!0,get:()=>require("./bucket-deployment").CacheControl}),exports.ServerSideEncryption=void 0,Object.defineProperty(exports,_noFold="ServerSideEncryption",{enumerable:!0,configurable:!0,get:()=>require("./bucket-deployment").ServerSideEncryption}),exports.StorageClass=void 0,Object.defineProperty(exports,_noFold="StorageClass",{enumerable:!0,configurable:!0,get:()=>require("./bucket-deployment").StorageClass}),exports.Expires=void 0,Object.defineProperty(exports,_noFold="Expires",{enumerable:!0,configurable:!0,get:()=>require("./bucket-deployment").Expires}),exports.Source=void 0,Object.defineProperty(exports,_noFold="Source",{enumerable:!0,configurable:!0,get:()=>require("./source").Source});
