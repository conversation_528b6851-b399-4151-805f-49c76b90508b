"use strict";var _a;Object.defineProperty(exports,"__esModule",{value:!0}),exports.Source=void 0;var jsiiDeprecationWarnings=()=>{var tmp=require("../../.warnings.jsii.js");return jsiiDeprecationWarnings=()=>tmp,tmp};const JSII_RTTI_SYMBOL_1=Symbol.for("jsii.rtti");var fs=()=>{var tmp=require("fs");return fs=()=>tmp,tmp},path_1=()=>{var tmp=require("path");return path_1=()=>tmp,tmp},render_data_1=()=>{var tmp=require("./render-data");return render_data_1=()=>tmp,tmp},s3_assets=()=>{var tmp=require("../../aws-s3-assets");return s3_assets=()=>tmp,tmp},core_1=()=>{var tmp=require("../../core");return core_1=()=>tmp,tmp},errors_1=()=>{var tmp=require("../../core/lib/errors");return errors_1=()=>tmp,tmp};class Source{static bucket(bucket,zipObjectKey){try{jsiiDeprecationWarnings().aws_cdk_lib_aws_s3_IBucket(bucket)}catch(error){throw process.env.JSII_DEBUG!=="1"&&error.name==="DeprecationError"&&Error.captureStackTrace(error,this.bucket),error}return{bind:(scope,context)=>{if(!context)throw new(errors_1()).ValidationError("To use a Source.bucket(), context must be provided",scope);return bucket.grantRead(context.handlerRole),{bucket,zipObjectKey}}}}static asset(path,options){try{jsiiDeprecationWarnings().aws_cdk_lib_aws_s3_assets_AssetOptions(options)}catch(error){throw process.env.JSII_DEBUG!=="1"&&error.name==="DeprecationError"&&Error.captureStackTrace(error,this.asset),error}return{bind(scope,context){if(!context)throw new(errors_1()).ValidationError("To use a Source.asset(), context must be provided",scope);let id=1;for(;scope.node.tryFindChild(`Asset${id}`);)id++;const asset=new(s3_assets()).Asset(scope,`Asset${id}`,{path,...options});if(!asset.isZipArchive)throw new(errors_1()).ValidationError("Asset path must be either a .zip file or a directory",scope);return asset.grantRead(context.handlerRole),{bucket:asset.bucket,zipObjectKey:asset.s3ObjectKey}}}}static data(objectKey,data,markersConfig){try{jsiiDeprecationWarnings().aws_cdk_lib_aws_s3_deployment_MarkersConfig(markersConfig)}catch(error){throw process.env.JSII_DEBUG!=="1"&&error.name==="DeprecationError"&&Error.captureStackTrace(error,this.data),error}return{bind:(scope,context)=>{const workdir=core_1().FileSystem.mkdtemp("s3-deployment"),outputPath=(0,path_1().join)(workdir,objectKey),rendered=(0,render_data_1().renderData)(scope,data);fs().mkdirSync((0,path_1().dirname)(outputPath),{recursive:!0}),fs().writeFileSync(outputPath,rendered.text);const asset=this.asset(workdir).bind(scope,context);return{bucket:asset.bucket,zipObjectKey:asset.zipObjectKey,markers:rendered.markers,markersConfig}}}}static jsonData(objectKey,obj,jsonProcessingOptions){try{jsiiDeprecationWarnings().aws_cdk_lib_aws_s3_deployment_JsonProcessingOptions(jsonProcessingOptions)}catch(error){throw process.env.JSII_DEBUG!=="1"&&error.name==="DeprecationError"&&Error.captureStackTrace(error,this.jsonData),error}let markersConfig={};return jsonProcessingOptions?.escape&&(markersConfig={jsonEscape:!0}),{bind:(scope,context)=>Source.data(objectKey,core_1().Stack.of(scope).toJsonString(obj),markersConfig).bind(scope,context)}}static yamlData(objectKey,obj){return{bind:(scope,context)=>Source.data(objectKey,core_1().Stack.of(scope).toYamlString(obj)).bind(scope,context)}}constructor(){}}exports.Source=Source,_a=JSII_RTTI_SYMBOL_1,Source[_a]={fqn:"aws-cdk-lib.aws_s3_deployment.Source",version:"2.201.0"};
