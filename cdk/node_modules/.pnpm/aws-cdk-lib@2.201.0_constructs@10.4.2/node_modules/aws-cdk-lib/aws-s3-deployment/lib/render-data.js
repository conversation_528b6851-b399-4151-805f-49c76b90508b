"use strict";Object.defineProperty(exports,"__esModule",{value:!0}),exports.renderData=renderData;var core_1=()=>{var tmp=require("../../core");return core_1=()=>tmp,tmp},errors_1=()=>{var tmp=require("../../core/lib/errors");return errors_1=()=>tmp,tmp};function renderData(scope,data){const obj=core_1().Stack.of(scope).resolve(data);if(typeof obj=="string")return{text:obj,markers:{}};if(typeof obj!="object")throw new(errors_1()).ValidationError(`Unexpected: after resolve() data must either be a string or a CloudFormation intrinsic. Got: ${JSON.stringify(obj)}`,scope);let markerIndex=0;const markers={},result=new Array,fnJoin=obj["Fn::Join"];if(fnJoin){const sep=fnJoin[0],parts=fnJoin[1];if(sep!=="")throw new(errors_1()).ValidationError(`Unexpected "Fn::Join", expecting separator to be an empty string but got "${sep}"`,scope);for(const part of parts){if(typeof part=="string"){result.push(part);continue}if(typeof part=="object"){addMarker(part);continue}throw new(errors_1()).ValidationError(`Unexpected "Fn::Join" part, expecting string or object but got ${typeof part}`,scope)}}else if(obj.Ref||obj["Fn::GetAtt"]||obj["Fn::Select"])addMarker(obj);else throw new(errors_1()).ValidationError('Unexpected: Expecting `resolve()` to return "Fn::Join", "Ref" or "Fn::GetAtt"',scope);function addMarker(part){const keys=Object.keys(part),acceptedCfnFns=["Ref","Fn::GetAtt","Fn::Select"];if(keys.length!==1||!acceptedCfnFns.includes(keys[0])){const stringifiedAcceptedCfnFns=acceptedCfnFns.map(fn=>`"${fn}"`).join(" or ");throw new(errors_1()).ValidationError(`Invalid CloudFormation reference. Key must start with any of ${stringifiedAcceptedCfnFns}. Got ${JSON.stringify(part)}`,scope)}const marker=`<<marker:0xbaba:${markerIndex++}>>`;result.push(marker),markers[marker]=part}return{text:result.join(""),markers}}
