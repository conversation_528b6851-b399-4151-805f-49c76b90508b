"use strict";var _a;Object.defineProperty(exports,"__esModule",{value:!0}),exports.Trigger=exports.ProviderType=exports.GitPullRequestEvent=void 0;var jsiiDeprecationWarnings=()=>{var tmp=require("../../.warnings.jsii.js");return jsiiDeprecationWarnings=()=>tmp,tmp};const JSII_RTTI_SYMBOL_1=Symbol.for("jsii.rtti");var validation_1=()=>{var tmp=require("./private/validation");return validation_1=()=>tmp,tmp},GitPullRequestEvent;(function(GitPullRequestEvent2){GitPullRequestEvent2.OPEN="OPEN",GitPullRequestEvent2.UPDATED="UPDATED",GitPullRequestEvent2.CLOSED="CLOSED"})(GitPullRequestEvent||(exports.GitPullRequestEvent=GitPullRequestEvent={}));var ProviderType;(function(ProviderType2){ProviderType2.CODE_STAR_SOURCE_CONNECTION="CodeStarSourceConnection"})(ProviderType||(exports.ProviderType=ProviderType={}));class Trigger{constructor(props){this.props=props;try{jsiiDeprecationWarnings().aws_cdk_lib_aws_codepipeline_TriggerProps(props)}catch(error){throw process.env.JSII_DEBUG!=="1"&&error.name==="DeprecationError"&&Error.captureStackTrace(error,Trigger),error}this.sourceAction=props.gitConfiguration?.sourceAction,this.validate()}validate(){this.props.gitConfiguration&&(0,validation_1().validateTriggers)(this.props.gitConfiguration)}_render(){let gitConfiguration;if(this.props.gitConfiguration){const{sourceAction,pushFilter,pullRequestFilter}=this.props.gitConfiguration,push=this.renderPushFilter(pushFilter),pullRequest=this.renderPullRequestFilter(pullRequestFilter);gitConfiguration={push:push?.length?push:void 0,pullRequest:pullRequest?.length?pullRequest:void 0,sourceActionName:sourceAction.actionProperties.actionName}}return{gitConfiguration,providerType:this.props.providerType}}renderPushFilter(pushFilter){return pushFilter?.map(filter=>{const branches=this.getBranchFilterProperty(filter),filePaths=this.getFilePathsFilterProperty(filter),tags=this.getTagsFilterProperty(filter);return{branches,filePaths,tags}})}renderPullRequestFilter(pullRequest){return pullRequest?.map(filter=>{const branches=this.getBranchFilterProperty(filter),filePaths=this.getFilePathsFilterProperty(filter),events=this.getEventsFilterProperty(filter);return{branches,filePaths,events}})}getBranchFilterProperty(filter){return filter.branchesExcludes?.length||filter.branchesIncludes?.length?{excludes:filter.branchesExcludes?.length?filter.branchesExcludes:void 0,includes:filter.branchesIncludes?.length?filter.branchesIncludes:void 0}:void 0}getFilePathsFilterProperty(filter){return filter.filePathsExcludes?.length||filter.filePathsIncludes?.length?{excludes:filter.filePathsExcludes?.length?filter.filePathsExcludes:void 0,includes:filter.filePathsIncludes?.length?filter.filePathsIncludes:void 0}:void 0}getTagsFilterProperty(filter){return filter.tagsExcludes?.length||filter.tagsIncludes?.length?{excludes:filter.tagsExcludes?.length?filter.tagsExcludes:void 0,includes:filter.tagsIncludes?.length?filter.tagsIncludes:void 0}:void 0}getEventsFilterProperty(filter){return filter.events?.length?Array.from(new Set(filter.events)):[GitPullRequestEvent.OPEN,GitPullRequestEvent.UPDATED,GitPullRequestEvent.CLOSED]}}exports.Trigger=Trigger,_a=JSII_RTTI_SYMBOL_1,Trigger[_a]={fqn:"aws-cdk-lib.aws_codepipeline.Trigger",version:"2.201.0"};
