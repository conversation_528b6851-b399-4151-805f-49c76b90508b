"use strict";Object.defineProperty(exports,"__esModule",{value:!0}),exports.validateArtifactBounds=validateArtifactBounds,exports.validateSourceAction=validateSourceAction,exports.validateName=validateName,exports.validateArtifactName=validateArtifactName,exports.validateNamespaceName=validateNamespaceName,exports.validatePipelineVariableName=validatePipelineVariableName,exports.validateRuleName=validateRuleName,exports.validateTriggers=validateTriggers;var cdk=()=>{var tmp=require("../../../core");return cdk=()=>tmp,tmp},core_1=()=>{var tmp=require("../../../core");return core_1=()=>tmp,tmp},action_1=()=>{var tmp=require("../action");return action_1=()=>tmp,tmp};function validateArtifactBounds(type,artifacts,min,max,category,provider){const ret=[];return artifacts.length<min&&ret.push(`${category}/${provider} must have at least ${min} ${type} artifacts`),artifacts.length>max&&ret.push(`${category}/${provider} cannot have more than ${max} ${type} artifacts`),ret}function validateSourceAction(mustBeSource,category,actionName,stageName){return mustBeSource!==(category===action_1().ActionCategory.SOURCE)?[`Action ${actionName} in stage ${stageName}: `+(mustBeSource?"first stage may only contain Source actions":"Source actions may only occur in first stage")]:[]}const VALID_IDENTIFIER_REGEX=/^[a-zA-Z0-9.@_-]{1,100}$/;function validateName(scope,thing,name){validateAgainstRegex(scope,VALID_IDENTIFIER_REGEX,thing,name)}function validateArtifactName(artifactName){validateAgainstRegex(void 0,/^[a-zA-Z0-9_-]{1,100}$/,"Artifact",artifactName)}function validateNamespaceName(scope,namespaceName){validateAgainstRegex(scope,/^[A-Za-z0-9@_-]{1,100}$/,"Namespace",namespaceName)}function validatePipelineVariableName(variableName){validateAgainstRegex(void 0,/^[A-Za-z0-9@\-_]{1,128}$/,"Variable",variableName)}function validateRuleName(ruleName){validateAgainstRegex(void 0,/^[A-Za-z0-9.@\-_]{1,100}$/,"Rule",ruleName)}function validateAgainstRegex(scope,regex,thing,name){if(!cdk().Token.isUnresolved(name)&&name!==void 0&&!regex.test(name)){const msg=`${thing} name must match regular expression: ${regex.toString()}, got '${name}'`;throw scope?new(cdk()).ValidationError(msg,scope):new(cdk()).UnscopedValidationError(msg)}}function validateTriggers(gitConfiguration){const{sourceAction,pushFilter,pullRequestFilter}=gitConfiguration;if(sourceAction.actionProperties.provider!=="CodeStarSourceConnection")throw new(core_1()).UnscopedValidationError(`provider for actionProperties in sourceAction with name '${sourceAction.actionProperties.actionName}' must be 'CodeStarSourceConnection', got '${sourceAction.actionProperties.provider}'`);if(!pushFilter?.length&&!pullRequestFilter?.length)throw new(core_1()).UnscopedValidationError(`must specify either GitPushFilter or GitPullRequestFilter for the trigger with sourceAction with name '${sourceAction.actionProperties.actionName}'`);pushFilter!==void 0&&validateGitPushFilter(pushFilter,sourceAction.actionProperties.actionName),pullRequestFilter!==void 0&&validateGitPullRequestFilter(pullRequestFilter,sourceAction.actionProperties.actionName)}function validateGitPushFilter(pushFilter,actionName){if(pushFilter.length>3)throw new(core_1()).UnscopedValidationError(`length of GitPushFilter for sourceAction with name '${actionName}' must be less than or equal to 3, got ${pushFilter.length}`);pushFilter.forEach(filter=>{validateGitFilterPropertiesLength(filter,actionName,"GitPushFilter"),validateArrayLength(filter.tagsExcludes,"tagsExcludes",actionName,"GitPushFilter"),validateArrayLength(filter.tagsIncludes,"tagsIncludes",actionName,"GitPushFilter"),validateFilePathsWithBranches(filter,actionName),validateTagsOrBranchesExist(filter,actionName)})}function validateGitPullRequestFilter(pullRequestFilter,actionName){if(pullRequestFilter.length>3)throw new(core_1()).UnscopedValidationError(`length of GitPullRequestFilter for sourceAction with name '${actionName}' must be less than or equal to 3, got ${pullRequestFilter.length}`);pullRequestFilter.forEach(filter=>{validateBranchesSpecified(filter,actionName),validateGitFilterPropertiesLength(filter,actionName,"GitPullRequestFilter")})}function validateGitFilterPropertiesLength(filter,actionName,filterType){validateArrayLength(filter.branchesExcludes,"branchesExcludes",actionName,filterType),validateArrayLength(filter.branchesIncludes,"branchesIncludes",actionName,filterType),validateArrayLength(filter.filePathsExcludes,"filePathsExcludes",actionName,filterType),validateArrayLength(filter.filePathsIncludes,"filePathsIncludes",actionName,filterType)}const validateBranchesSpecified=(filter,actionName)=>{if(!filter.branchesExcludes&&!filter.branchesIncludes)throw new(core_1()).UnscopedValidationError(`must specify branches in GitPullRequestFilter for sourceAction with name '${actionName}'`)},validateArrayLength=(array,fieldName,actionName,filterType)=>{if(array&&array.length>MAX_FILTER_LENGTH)throw new(core_1()).UnscopedValidationError(`maximum length of ${fieldName} in ${filterType} for sourceAction with name '${actionName}' is ${MAX_FILTER_LENGTH}, got ${array.length}`)},hasFilePaths=filter=>!!(filter.filePathsExcludes||filter.filePathsIncludes),hasBranches=filter=>!!(filter.branchesExcludes||filter.branchesIncludes),hasTags=filter=>!!(filter.tagsExcludes||filter.tagsIncludes),validateFilePathsWithBranches=(filter,actionName)=>{if(!hasBranches(filter)&&hasFilePaths(filter))throw new(core_1()).UnscopedValidationError(`cannot specify filePaths without branches for sourceAction with name '${actionName}'`)},validateTagsOrBranchesExist=(filter,actionName)=>{if(!hasTags(filter)&&!hasBranches(filter))throw new(core_1()).UnscopedValidationError(`must specify either tags or branches in GitpushFilter for sourceAction with name '${actionName}'`)},MAX_FILTER_LENGTH=8;
