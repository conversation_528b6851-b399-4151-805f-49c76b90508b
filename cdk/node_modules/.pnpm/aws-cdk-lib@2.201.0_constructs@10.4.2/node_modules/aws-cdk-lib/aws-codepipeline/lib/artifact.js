"use strict";var _a,_b;Object.defineProperty(exports,"__esModule",{value:!0}),exports.ArtifactPath=exports.Artifact=void 0;var jsiiDeprecationWarnings=()=>{var tmp=require("../../.warnings.jsii.js");return jsiiDeprecationWarnings=()=>tmp,tmp};const JSII_RTTI_SYMBOL_1=Symbol.for("jsii.rtti");var validation=()=>{var tmp=require("./private/validation");return validation=()=>tmp,tmp},core_1=()=>{var tmp=require("../../core");return core_1=()=>tmp,tmp};class Artifact{static artifact(name,files){return new Artifact(name,files)}constructor(artifactName,artifactFiles){if(this.metadata={},validation().validateArtifactName(artifactName),artifactFiles!==void 0&&(artifactFiles.length<1||artifactFiles.length>10))throw new(core_1()).UnscopedValidationError(`The length of the artifactFiles array must be between 1 and 10, got: ${artifactFiles.length}`);this._artifactName=artifactName,this._artifactFiles=artifactFiles}get artifactName(){return this._artifactName}get artifactFiles(){return this._artifactFiles}atPath(fileName){return new ArtifactPath(this,fileName)}get bucketName(){return artifactAttribute(this,"BucketName")}get objectKey(){return artifactAttribute(this,"ObjectKey")}get url(){return artifactAttribute(this,"URL")}getParam(jsonFile,keyName){return artifactGetParam(this,jsonFile,keyName)}get s3Location(){return{bucketName:this.bucketName,objectKey:this.objectKey}}setMetadata(key,value){this.metadata[key]=value}getMetadata(key){return this.metadata[key]}toString(){return this.artifactName}_setName(name){if(this._artifactName)throw new(core_1()).UnscopedValidationError(`Artifact already has name '${this._artifactName}', cannot override it`);this._artifactName=name}}exports.Artifact=Artifact,_a=JSII_RTTI_SYMBOL_1,Artifact[_a]={fqn:"aws-cdk-lib.aws_codepipeline.Artifact",version:"2.201.0"};class ArtifactPath{static artifactPath(artifactName,fileName){return new ArtifactPath(Artifact.artifact(artifactName),fileName)}constructor(artifact,fileName){this.artifact=artifact,this.fileName=fileName;try{jsiiDeprecationWarnings().aws_cdk_lib_aws_codepipeline_Artifact(artifact)}catch(error){throw process.env.JSII_DEBUG!=="1"&&error.name==="DeprecationError"&&Error.captureStackTrace(error,ArtifactPath),error}}get location(){return`${this.artifact.artifactName?this.artifact.artifactName:core_1().Lazy.string({produce:()=>this.artifact.artifactName})}::${this.fileName}`}}exports.ArtifactPath=ArtifactPath,_b=JSII_RTTI_SYMBOL_1,ArtifactPath[_b]={fqn:"aws-cdk-lib.aws_codepipeline.ArtifactPath",version:"2.201.0"};function artifactAttribute(artifact,attributeName){const lazyArtifactName=core_1().Lazy.string({produce:()=>artifact.artifactName});return core_1().Token.asString({"Fn::GetArtifactAtt":[lazyArtifactName,attributeName]})}function artifactGetParam(artifact,jsonFile,keyName){const lazyArtifactName=core_1().Lazy.string({produce:()=>artifact.artifactName});return core_1().Token.asString({"Fn::GetParam":[lazyArtifactName,jsonFile,keyName]})}
