"use strict";var _a,_b,_c;Object.defineProperty(exports,"__esModule",{value:!0}),exports.CfnWebhook=exports.CfnPipeline=exports.CfnCustomActionType=void 0;var jsiiDeprecationWarnings=()=>{var tmp=require("../../.warnings.jsii.js");return jsiiDeprecationWarnings=()=>tmp,tmp};const JSII_RTTI_SYMBOL_1=Symbol.for("jsii.rtti");var cdk=()=>{var tmp=require("../../core");return cdk=()=>tmp,tmp},cfn_parse=()=>{var tmp=require("../../core/lib/helpers-internal");return cfn_parse=()=>tmp,tmp},cdk_errors=()=>{var tmp=require("../../core/lib/errors");return cdk_errors=()=>tmp,tmp};class CfnCustomActionType extends cdk().CfnResource{static _fromCloudFormation(scope,id,resourceAttributes,options){resourceAttributes=resourceAttributes||{};const resourceProperties=options.parser.parseValue(resourceAttributes.Properties),propsResult=CfnCustomActionTypePropsFromCloudFormation(resourceProperties);if(cdk().isResolvableObject(propsResult.value))throw new(cdk_errors()).ValidationError("Unexpected IResolvable",scope);const ret=new CfnCustomActionType(scope,id,propsResult.value);for(const[propKey,propVal]of Object.entries(propsResult.extraProperties))ret.addPropertyOverride(propKey,propVal);return options.parser.handleAttributes(ret,resourceAttributes,id),ret}constructor(scope,id,props){super(scope,id,{type:CfnCustomActionType.CFN_RESOURCE_TYPE_NAME,properties:props});try{jsiiDeprecationWarnings().aws_cdk_lib_aws_codepipeline_CfnCustomActionTypeProps(props)}catch(error){throw process.env.JSII_DEBUG!=="1"&&error.name==="DeprecationError"&&Error.captureStackTrace(error,CfnCustomActionType),error}cdk().requireProperty(props,"category",this),cdk().requireProperty(props,"inputArtifactDetails",this),cdk().requireProperty(props,"outputArtifactDetails",this),cdk().requireProperty(props,"provider",this),cdk().requireProperty(props,"version",this),this.attrId=cdk().Token.asString(this.getAtt("Id",cdk().ResolutionTypeHint.STRING)),this.category=props.category,this.configurationProperties=props.configurationProperties,this.inputArtifactDetails=props.inputArtifactDetails,this.outputArtifactDetails=props.outputArtifactDetails,this.provider=props.provider,this.settings=props.settings,this.tags=new(cdk()).TagManager(cdk().TagType.STANDARD,"AWS::CodePipeline::CustomActionType",props.tags,{tagPropertyName:"tags"}),this.tagsRaw=props.tags,this.version=props.version}get cfnProperties(){return{category:this.category,configurationProperties:this.configurationProperties,inputArtifactDetails:this.inputArtifactDetails,outputArtifactDetails:this.outputArtifactDetails,provider:this.provider,settings:this.settings,tags:this.tags.renderTags(),version:this.version}}inspect(inspector){try{jsiiDeprecationWarnings().aws_cdk_lib_TreeInspector(inspector)}catch(error){throw process.env.JSII_DEBUG!=="1"&&error.name==="DeprecationError"&&Error.captureStackTrace(error,this.inspect),error}inspector.addAttribute("aws:cdk:cloudformation:type",CfnCustomActionType.CFN_RESOURCE_TYPE_NAME),inspector.addAttribute("aws:cdk:cloudformation:props",this.cfnProperties)}renderProperties(props){return convertCfnCustomActionTypePropsToCloudFormation(props)}}exports.CfnCustomActionType=CfnCustomActionType,_a=JSII_RTTI_SYMBOL_1,CfnCustomActionType[_a]={fqn:"aws-cdk-lib.aws_codepipeline.CfnCustomActionType",version:"2.201.0"},CfnCustomActionType.CFN_RESOURCE_TYPE_NAME="AWS::CodePipeline::CustomActionType";function CfnCustomActionTypeArtifactDetailsPropertyValidator(properties){if(!cdk().canInspect(properties))return cdk().VALIDATION_SUCCESS;const errors=new(cdk()).ValidationResults;return properties&&typeof properties=="object"&&!Array.isArray(properties)||errors.collect(new(cdk()).ValidationResult("Expected an object, but received: "+JSON.stringify(properties))),errors.collect(cdk().propertyValidator("maximumCount",cdk().requiredValidator)(properties.maximumCount)),errors.collect(cdk().propertyValidator("maximumCount",cdk().validateNumber)(properties.maximumCount)),errors.collect(cdk().propertyValidator("minimumCount",cdk().requiredValidator)(properties.minimumCount)),errors.collect(cdk().propertyValidator("minimumCount",cdk().validateNumber)(properties.minimumCount)),errors.wrap('supplied properties not correct for "ArtifactDetailsProperty"')}function convertCfnCustomActionTypeArtifactDetailsPropertyToCloudFormation(properties){return cdk().canInspect(properties)?(CfnCustomActionTypeArtifactDetailsPropertyValidator(properties).assertSuccess(),{MaximumCount:cdk().numberToCloudFormation(properties.maximumCount),MinimumCount:cdk().numberToCloudFormation(properties.minimumCount)}):properties}function CfnCustomActionTypeArtifactDetailsPropertyFromCloudFormation(properties){if(cdk().isResolvableObject(properties))return new(cfn_parse()).FromCloudFormationResult(properties);if(properties=properties??{},!(properties&&typeof properties=="object"&&!Array.isArray(properties)))return new(cfn_parse()).FromCloudFormationResult(properties);const ret=new(cfn_parse()).FromCloudFormationPropertyObject;return ret.addPropertyResult("maximumCount","MaximumCount",properties.MaximumCount!=null?cfn_parse().FromCloudFormation.getNumber(properties.MaximumCount):void 0),ret.addPropertyResult("minimumCount","MinimumCount",properties.MinimumCount!=null?cfn_parse().FromCloudFormation.getNumber(properties.MinimumCount):void 0),ret.addUnrecognizedPropertiesAsExtra(properties),ret}function CfnCustomActionTypeConfigurationPropertiesPropertyValidator(properties){if(!cdk().canInspect(properties))return cdk().VALIDATION_SUCCESS;const errors=new(cdk()).ValidationResults;return properties&&typeof properties=="object"&&!Array.isArray(properties)||errors.collect(new(cdk()).ValidationResult("Expected an object, but received: "+JSON.stringify(properties))),errors.collect(cdk().propertyValidator("description",cdk().validateString)(properties.description)),errors.collect(cdk().propertyValidator("key",cdk().requiredValidator)(properties.key)),errors.collect(cdk().propertyValidator("key",cdk().validateBoolean)(properties.key)),errors.collect(cdk().propertyValidator("name",cdk().requiredValidator)(properties.name)),errors.collect(cdk().propertyValidator("name",cdk().validateString)(properties.name)),errors.collect(cdk().propertyValidator("queryable",cdk().validateBoolean)(properties.queryable)),errors.collect(cdk().propertyValidator("required",cdk().requiredValidator)(properties.required)),errors.collect(cdk().propertyValidator("required",cdk().validateBoolean)(properties.required)),errors.collect(cdk().propertyValidator("secret",cdk().requiredValidator)(properties.secret)),errors.collect(cdk().propertyValidator("secret",cdk().validateBoolean)(properties.secret)),errors.collect(cdk().propertyValidator("type",cdk().validateString)(properties.type)),errors.wrap('supplied properties not correct for "ConfigurationPropertiesProperty"')}function convertCfnCustomActionTypeConfigurationPropertiesPropertyToCloudFormation(properties){return cdk().canInspect(properties)?(CfnCustomActionTypeConfigurationPropertiesPropertyValidator(properties).assertSuccess(),{Description:cdk().stringToCloudFormation(properties.description),Key:cdk().booleanToCloudFormation(properties.key),Name:cdk().stringToCloudFormation(properties.name),Queryable:cdk().booleanToCloudFormation(properties.queryable),Required:cdk().booleanToCloudFormation(properties.required),Secret:cdk().booleanToCloudFormation(properties.secret),Type:cdk().stringToCloudFormation(properties.type)}):properties}function CfnCustomActionTypeConfigurationPropertiesPropertyFromCloudFormation(properties){if(cdk().isResolvableObject(properties))return new(cfn_parse()).FromCloudFormationResult(properties);if(properties=properties??{},!(properties&&typeof properties=="object"&&!Array.isArray(properties)))return new(cfn_parse()).FromCloudFormationResult(properties);const ret=new(cfn_parse()).FromCloudFormationPropertyObject;return ret.addPropertyResult("description","Description",properties.Description!=null?cfn_parse().FromCloudFormation.getString(properties.Description):void 0),ret.addPropertyResult("key","Key",properties.Key!=null?cfn_parse().FromCloudFormation.getBoolean(properties.Key):void 0),ret.addPropertyResult("name","Name",properties.Name!=null?cfn_parse().FromCloudFormation.getString(properties.Name):void 0),ret.addPropertyResult("queryable","Queryable",properties.Queryable!=null?cfn_parse().FromCloudFormation.getBoolean(properties.Queryable):void 0),ret.addPropertyResult("required","Required",properties.Required!=null?cfn_parse().FromCloudFormation.getBoolean(properties.Required):void 0),ret.addPropertyResult("secret","Secret",properties.Secret!=null?cfn_parse().FromCloudFormation.getBoolean(properties.Secret):void 0),ret.addPropertyResult("type","Type",properties.Type!=null?cfn_parse().FromCloudFormation.getString(properties.Type):void 0),ret.addUnrecognizedPropertiesAsExtra(properties),ret}function CfnCustomActionTypeSettingsPropertyValidator(properties){if(!cdk().canInspect(properties))return cdk().VALIDATION_SUCCESS;const errors=new(cdk()).ValidationResults;return properties&&typeof properties=="object"&&!Array.isArray(properties)||errors.collect(new(cdk()).ValidationResult("Expected an object, but received: "+JSON.stringify(properties))),errors.collect(cdk().propertyValidator("entityUrlTemplate",cdk().validateString)(properties.entityUrlTemplate)),errors.collect(cdk().propertyValidator("executionUrlTemplate",cdk().validateString)(properties.executionUrlTemplate)),errors.collect(cdk().propertyValidator("revisionUrlTemplate",cdk().validateString)(properties.revisionUrlTemplate)),errors.collect(cdk().propertyValidator("thirdPartyConfigurationUrl",cdk().validateString)(properties.thirdPartyConfigurationUrl)),errors.wrap('supplied properties not correct for "SettingsProperty"')}function convertCfnCustomActionTypeSettingsPropertyToCloudFormation(properties){return cdk().canInspect(properties)?(CfnCustomActionTypeSettingsPropertyValidator(properties).assertSuccess(),{EntityUrlTemplate:cdk().stringToCloudFormation(properties.entityUrlTemplate),ExecutionUrlTemplate:cdk().stringToCloudFormation(properties.executionUrlTemplate),RevisionUrlTemplate:cdk().stringToCloudFormation(properties.revisionUrlTemplate),ThirdPartyConfigurationUrl:cdk().stringToCloudFormation(properties.thirdPartyConfigurationUrl)}):properties}function CfnCustomActionTypeSettingsPropertyFromCloudFormation(properties){if(cdk().isResolvableObject(properties))return new(cfn_parse()).FromCloudFormationResult(properties);if(properties=properties??{},!(properties&&typeof properties=="object"&&!Array.isArray(properties)))return new(cfn_parse()).FromCloudFormationResult(properties);const ret=new(cfn_parse()).FromCloudFormationPropertyObject;return ret.addPropertyResult("entityUrlTemplate","EntityUrlTemplate",properties.EntityUrlTemplate!=null?cfn_parse().FromCloudFormation.getString(properties.EntityUrlTemplate):void 0),ret.addPropertyResult("executionUrlTemplate","ExecutionUrlTemplate",properties.ExecutionUrlTemplate!=null?cfn_parse().FromCloudFormation.getString(properties.ExecutionUrlTemplate):void 0),ret.addPropertyResult("revisionUrlTemplate","RevisionUrlTemplate",properties.RevisionUrlTemplate!=null?cfn_parse().FromCloudFormation.getString(properties.RevisionUrlTemplate):void 0),ret.addPropertyResult("thirdPartyConfigurationUrl","ThirdPartyConfigurationUrl",properties.ThirdPartyConfigurationUrl!=null?cfn_parse().FromCloudFormation.getString(properties.ThirdPartyConfigurationUrl):void 0),ret.addUnrecognizedPropertiesAsExtra(properties),ret}function CfnCustomActionTypePropsValidator(properties){if(!cdk().canInspect(properties))return cdk().VALIDATION_SUCCESS;const errors=new(cdk()).ValidationResults;return properties&&typeof properties=="object"&&!Array.isArray(properties)||errors.collect(new(cdk()).ValidationResult("Expected an object, but received: "+JSON.stringify(properties))),errors.collect(cdk().propertyValidator("category",cdk().requiredValidator)(properties.category)),errors.collect(cdk().propertyValidator("category",cdk().validateString)(properties.category)),errors.collect(cdk().propertyValidator("configurationProperties",cdk().listValidator(CfnCustomActionTypeConfigurationPropertiesPropertyValidator))(properties.configurationProperties)),errors.collect(cdk().propertyValidator("inputArtifactDetails",cdk().requiredValidator)(properties.inputArtifactDetails)),errors.collect(cdk().propertyValidator("inputArtifactDetails",CfnCustomActionTypeArtifactDetailsPropertyValidator)(properties.inputArtifactDetails)),errors.collect(cdk().propertyValidator("outputArtifactDetails",cdk().requiredValidator)(properties.outputArtifactDetails)),errors.collect(cdk().propertyValidator("outputArtifactDetails",CfnCustomActionTypeArtifactDetailsPropertyValidator)(properties.outputArtifactDetails)),errors.collect(cdk().propertyValidator("provider",cdk().requiredValidator)(properties.provider)),errors.collect(cdk().propertyValidator("provider",cdk().validateString)(properties.provider)),errors.collect(cdk().propertyValidator("settings",CfnCustomActionTypeSettingsPropertyValidator)(properties.settings)),errors.collect(cdk().propertyValidator("tags",cdk().listValidator(cdk().validateCfnTag))(properties.tags)),errors.collect(cdk().propertyValidator("version",cdk().requiredValidator)(properties.version)),errors.collect(cdk().propertyValidator("version",cdk().validateString)(properties.version)),errors.wrap('supplied properties not correct for "CfnCustomActionTypeProps"')}function convertCfnCustomActionTypePropsToCloudFormation(properties){return cdk().canInspect(properties)?(CfnCustomActionTypePropsValidator(properties).assertSuccess(),{Category:cdk().stringToCloudFormation(properties.category),ConfigurationProperties:cdk().listMapper(convertCfnCustomActionTypeConfigurationPropertiesPropertyToCloudFormation)(properties.configurationProperties),InputArtifactDetails:convertCfnCustomActionTypeArtifactDetailsPropertyToCloudFormation(properties.inputArtifactDetails),OutputArtifactDetails:convertCfnCustomActionTypeArtifactDetailsPropertyToCloudFormation(properties.outputArtifactDetails),Provider:cdk().stringToCloudFormation(properties.provider),Settings:convertCfnCustomActionTypeSettingsPropertyToCloudFormation(properties.settings),Tags:cdk().listMapper(cdk().cfnTagToCloudFormation)(properties.tags),Version:cdk().stringToCloudFormation(properties.version)}):properties}function CfnCustomActionTypePropsFromCloudFormation(properties){if(cdk().isResolvableObject(properties))return new(cfn_parse()).FromCloudFormationResult(properties);if(properties=properties??{},!(properties&&typeof properties=="object"&&!Array.isArray(properties)))return new(cfn_parse()).FromCloudFormationResult(properties);const ret=new(cfn_parse()).FromCloudFormationPropertyObject;return ret.addPropertyResult("category","Category",properties.Category!=null?cfn_parse().FromCloudFormation.getString(properties.Category):void 0),ret.addPropertyResult("configurationProperties","ConfigurationProperties",properties.ConfigurationProperties!=null?cfn_parse().FromCloudFormation.getArray(CfnCustomActionTypeConfigurationPropertiesPropertyFromCloudFormation)(properties.ConfigurationProperties):void 0),ret.addPropertyResult("inputArtifactDetails","InputArtifactDetails",properties.InputArtifactDetails!=null?CfnCustomActionTypeArtifactDetailsPropertyFromCloudFormation(properties.InputArtifactDetails):void 0),ret.addPropertyResult("outputArtifactDetails","OutputArtifactDetails",properties.OutputArtifactDetails!=null?CfnCustomActionTypeArtifactDetailsPropertyFromCloudFormation(properties.OutputArtifactDetails):void 0),ret.addPropertyResult("provider","Provider",properties.Provider!=null?cfn_parse().FromCloudFormation.getString(properties.Provider):void 0),ret.addPropertyResult("settings","Settings",properties.Settings!=null?CfnCustomActionTypeSettingsPropertyFromCloudFormation(properties.Settings):void 0),ret.addPropertyResult("tags","Tags",properties.Tags!=null?cfn_parse().FromCloudFormation.getArray(cfn_parse().FromCloudFormation.getCfnTag)(properties.Tags):void 0),ret.addPropertyResult("version","Version",properties.Version!=null?cfn_parse().FromCloudFormation.getString(properties.Version):void 0),ret.addUnrecognizedPropertiesAsExtra(properties),ret}class CfnPipeline extends cdk().CfnResource{static _fromCloudFormation(scope,id,resourceAttributes,options){resourceAttributes=resourceAttributes||{};const resourceProperties=options.parser.parseValue(resourceAttributes.Properties),propsResult=CfnPipelinePropsFromCloudFormation(resourceProperties);if(cdk().isResolvableObject(propsResult.value))throw new(cdk_errors()).ValidationError("Unexpected IResolvable",scope);const ret=new CfnPipeline(scope,id,propsResult.value);for(const[propKey,propVal]of Object.entries(propsResult.extraProperties))ret.addPropertyOverride(propKey,propVal);return options.parser.handleAttributes(ret,resourceAttributes,id),ret}constructor(scope,id,props){super(scope,id,{type:CfnPipeline.CFN_RESOURCE_TYPE_NAME,properties:props});try{jsiiDeprecationWarnings().aws_cdk_lib_aws_codepipeline_CfnPipelineProps(props)}catch(error){throw process.env.JSII_DEBUG!=="1"&&error.name==="DeprecationError"&&Error.captureStackTrace(error,CfnPipeline),error}cdk().requireProperty(props,"roleArn",this),cdk().requireProperty(props,"stages",this),this.attrVersion=cdk().Token.asString(this.getAtt("Version",cdk().ResolutionTypeHint.STRING)),this.artifactStore=props.artifactStore,this.artifactStores=props.artifactStores,this.disableInboundStageTransitions=props.disableInboundStageTransitions,this.executionMode=props.executionMode,this.name=props.name,this.pipelineType=props.pipelineType,this.restartExecutionOnUpdate=props.restartExecutionOnUpdate,this.roleArn=props.roleArn,this.stages=props.stages,this.tags=new(cdk()).TagManager(cdk().TagType.STANDARD,"AWS::CodePipeline::Pipeline",props.tags,{tagPropertyName:"tags"}),this.tagsRaw=props.tags,this.triggers=props.triggers,this.variables=props.variables}get cfnProperties(){return{artifactStore:this.artifactStore,artifactStores:this.artifactStores,disableInboundStageTransitions:this.disableInboundStageTransitions,executionMode:this.executionMode,name:this.name,pipelineType:this.pipelineType,restartExecutionOnUpdate:this.restartExecutionOnUpdate,roleArn:this.roleArn,stages:this.stages,tags:this.tags.renderTags(),triggers:this.triggers,variables:this.variables}}inspect(inspector){try{jsiiDeprecationWarnings().aws_cdk_lib_TreeInspector(inspector)}catch(error){throw process.env.JSII_DEBUG!=="1"&&error.name==="DeprecationError"&&Error.captureStackTrace(error,this.inspect),error}inspector.addAttribute("aws:cdk:cloudformation:type",CfnPipeline.CFN_RESOURCE_TYPE_NAME),inspector.addAttribute("aws:cdk:cloudformation:props",this.cfnProperties)}renderProperties(props){return convertCfnPipelinePropsToCloudFormation(props)}}exports.CfnPipeline=CfnPipeline,_b=JSII_RTTI_SYMBOL_1,CfnPipeline[_b]={fqn:"aws-cdk-lib.aws_codepipeline.CfnPipeline",version:"2.201.0"},CfnPipeline.CFN_RESOURCE_TYPE_NAME="AWS::CodePipeline::Pipeline";function CfnPipelineEncryptionKeyPropertyValidator(properties){if(!cdk().canInspect(properties))return cdk().VALIDATION_SUCCESS;const errors=new(cdk()).ValidationResults;return properties&&typeof properties=="object"&&!Array.isArray(properties)||errors.collect(new(cdk()).ValidationResult("Expected an object, but received: "+JSON.stringify(properties))),errors.collect(cdk().propertyValidator("id",cdk().requiredValidator)(properties.id)),errors.collect(cdk().propertyValidator("id",cdk().validateString)(properties.id)),errors.collect(cdk().propertyValidator("type",cdk().requiredValidator)(properties.type)),errors.collect(cdk().propertyValidator("type",cdk().validateString)(properties.type)),errors.wrap('supplied properties not correct for "EncryptionKeyProperty"')}function convertCfnPipelineEncryptionKeyPropertyToCloudFormation(properties){return cdk().canInspect(properties)?(CfnPipelineEncryptionKeyPropertyValidator(properties).assertSuccess(),{Id:cdk().stringToCloudFormation(properties.id),Type:cdk().stringToCloudFormation(properties.type)}):properties}function CfnPipelineEncryptionKeyPropertyFromCloudFormation(properties){if(cdk().isResolvableObject(properties))return new(cfn_parse()).FromCloudFormationResult(properties);if(properties=properties??{},!(properties&&typeof properties=="object"&&!Array.isArray(properties)))return new(cfn_parse()).FromCloudFormationResult(properties);const ret=new(cfn_parse()).FromCloudFormationPropertyObject;return ret.addPropertyResult("id","Id",properties.Id!=null?cfn_parse().FromCloudFormation.getString(properties.Id):void 0),ret.addPropertyResult("type","Type",properties.Type!=null?cfn_parse().FromCloudFormation.getString(properties.Type):void 0),ret.addUnrecognizedPropertiesAsExtra(properties),ret}function CfnPipelineArtifactStorePropertyValidator(properties){if(!cdk().canInspect(properties))return cdk().VALIDATION_SUCCESS;const errors=new(cdk()).ValidationResults;return properties&&typeof properties=="object"&&!Array.isArray(properties)||errors.collect(new(cdk()).ValidationResult("Expected an object, but received: "+JSON.stringify(properties))),errors.collect(cdk().propertyValidator("encryptionKey",CfnPipelineEncryptionKeyPropertyValidator)(properties.encryptionKey)),errors.collect(cdk().propertyValidator("location",cdk().requiredValidator)(properties.location)),errors.collect(cdk().propertyValidator("location",cdk().validateString)(properties.location)),errors.collect(cdk().propertyValidator("type",cdk().requiredValidator)(properties.type)),errors.collect(cdk().propertyValidator("type",cdk().validateString)(properties.type)),errors.wrap('supplied properties not correct for "ArtifactStoreProperty"')}function convertCfnPipelineArtifactStorePropertyToCloudFormation(properties){return cdk().canInspect(properties)?(CfnPipelineArtifactStorePropertyValidator(properties).assertSuccess(),{EncryptionKey:convertCfnPipelineEncryptionKeyPropertyToCloudFormation(properties.encryptionKey),Location:cdk().stringToCloudFormation(properties.location),Type:cdk().stringToCloudFormation(properties.type)}):properties}function CfnPipelineArtifactStorePropertyFromCloudFormation(properties){if(cdk().isResolvableObject(properties))return new(cfn_parse()).FromCloudFormationResult(properties);if(properties=properties??{},!(properties&&typeof properties=="object"&&!Array.isArray(properties)))return new(cfn_parse()).FromCloudFormationResult(properties);const ret=new(cfn_parse()).FromCloudFormationPropertyObject;return ret.addPropertyResult("encryptionKey","EncryptionKey",properties.EncryptionKey!=null?CfnPipelineEncryptionKeyPropertyFromCloudFormation(properties.EncryptionKey):void 0),ret.addPropertyResult("location","Location",properties.Location!=null?cfn_parse().FromCloudFormation.getString(properties.Location):void 0),ret.addPropertyResult("type","Type",properties.Type!=null?cfn_parse().FromCloudFormation.getString(properties.Type):void 0),ret.addUnrecognizedPropertiesAsExtra(properties),ret}function CfnPipelineArtifactStoreMapPropertyValidator(properties){if(!cdk().canInspect(properties))return cdk().VALIDATION_SUCCESS;const errors=new(cdk()).ValidationResults;return properties&&typeof properties=="object"&&!Array.isArray(properties)||errors.collect(new(cdk()).ValidationResult("Expected an object, but received: "+JSON.stringify(properties))),errors.collect(cdk().propertyValidator("artifactStore",cdk().requiredValidator)(properties.artifactStore)),errors.collect(cdk().propertyValidator("artifactStore",CfnPipelineArtifactStorePropertyValidator)(properties.artifactStore)),errors.collect(cdk().propertyValidator("region",cdk().requiredValidator)(properties.region)),errors.collect(cdk().propertyValidator("region",cdk().validateString)(properties.region)),errors.wrap('supplied properties not correct for "ArtifactStoreMapProperty"')}function convertCfnPipelineArtifactStoreMapPropertyToCloudFormation(properties){return cdk().canInspect(properties)?(CfnPipelineArtifactStoreMapPropertyValidator(properties).assertSuccess(),{ArtifactStore:convertCfnPipelineArtifactStorePropertyToCloudFormation(properties.artifactStore),Region:cdk().stringToCloudFormation(properties.region)}):properties}function CfnPipelineArtifactStoreMapPropertyFromCloudFormation(properties){if(cdk().isResolvableObject(properties))return new(cfn_parse()).FromCloudFormationResult(properties);if(properties=properties??{},!(properties&&typeof properties=="object"&&!Array.isArray(properties)))return new(cfn_parse()).FromCloudFormationResult(properties);const ret=new(cfn_parse()).FromCloudFormationPropertyObject;return ret.addPropertyResult("artifactStore","ArtifactStore",properties.ArtifactStore!=null?CfnPipelineArtifactStorePropertyFromCloudFormation(properties.ArtifactStore):void 0),ret.addPropertyResult("region","Region",properties.Region!=null?cfn_parse().FromCloudFormation.getString(properties.Region):void 0),ret.addUnrecognizedPropertiesAsExtra(properties),ret}function CfnPipelineStageTransitionPropertyValidator(properties){if(!cdk().canInspect(properties))return cdk().VALIDATION_SUCCESS;const errors=new(cdk()).ValidationResults;return properties&&typeof properties=="object"&&!Array.isArray(properties)||errors.collect(new(cdk()).ValidationResult("Expected an object, but received: "+JSON.stringify(properties))),errors.collect(cdk().propertyValidator("reason",cdk().requiredValidator)(properties.reason)),errors.collect(cdk().propertyValidator("reason",cdk().validateString)(properties.reason)),errors.collect(cdk().propertyValidator("stageName",cdk().requiredValidator)(properties.stageName)),errors.collect(cdk().propertyValidator("stageName",cdk().validateString)(properties.stageName)),errors.wrap('supplied properties not correct for "StageTransitionProperty"')}function convertCfnPipelineStageTransitionPropertyToCloudFormation(properties){return cdk().canInspect(properties)?(CfnPipelineStageTransitionPropertyValidator(properties).assertSuccess(),{Reason:cdk().stringToCloudFormation(properties.reason),StageName:cdk().stringToCloudFormation(properties.stageName)}):properties}function CfnPipelineStageTransitionPropertyFromCloudFormation(properties){if(cdk().isResolvableObject(properties))return new(cfn_parse()).FromCloudFormationResult(properties);if(properties=properties??{},!(properties&&typeof properties=="object"&&!Array.isArray(properties)))return new(cfn_parse()).FromCloudFormationResult(properties);const ret=new(cfn_parse()).FromCloudFormationPropertyObject;return ret.addPropertyResult("reason","Reason",properties.Reason!=null?cfn_parse().FromCloudFormation.getString(properties.Reason):void 0),ret.addPropertyResult("stageName","StageName",properties.StageName!=null?cfn_parse().FromCloudFormation.getString(properties.StageName):void 0),ret.addUnrecognizedPropertiesAsExtra(properties),ret}function CfnPipelineActionTypeIdPropertyValidator(properties){if(!cdk().canInspect(properties))return cdk().VALIDATION_SUCCESS;const errors=new(cdk()).ValidationResults;return properties&&typeof properties=="object"&&!Array.isArray(properties)||errors.collect(new(cdk()).ValidationResult("Expected an object, but received: "+JSON.stringify(properties))),errors.collect(cdk().propertyValidator("category",cdk().requiredValidator)(properties.category)),errors.collect(cdk().propertyValidator("category",cdk().validateString)(properties.category)),errors.collect(cdk().propertyValidator("owner",cdk().requiredValidator)(properties.owner)),errors.collect(cdk().propertyValidator("owner",cdk().validateString)(properties.owner)),errors.collect(cdk().propertyValidator("provider",cdk().requiredValidator)(properties.provider)),errors.collect(cdk().propertyValidator("provider",cdk().validateString)(properties.provider)),errors.collect(cdk().propertyValidator("version",cdk().requiredValidator)(properties.version)),errors.collect(cdk().propertyValidator("version",cdk().validateString)(properties.version)),errors.wrap('supplied properties not correct for "ActionTypeIdProperty"')}function convertCfnPipelineActionTypeIdPropertyToCloudFormation(properties){return cdk().canInspect(properties)?(CfnPipelineActionTypeIdPropertyValidator(properties).assertSuccess(),{Category:cdk().stringToCloudFormation(properties.category),Owner:cdk().stringToCloudFormation(properties.owner),Provider:cdk().stringToCloudFormation(properties.provider),Version:cdk().stringToCloudFormation(properties.version)}):properties}function CfnPipelineActionTypeIdPropertyFromCloudFormation(properties){if(cdk().isResolvableObject(properties))return new(cfn_parse()).FromCloudFormationResult(properties);if(properties=properties??{},!(properties&&typeof properties=="object"&&!Array.isArray(properties)))return new(cfn_parse()).FromCloudFormationResult(properties);const ret=new(cfn_parse()).FromCloudFormationPropertyObject;return ret.addPropertyResult("category","Category",properties.Category!=null?cfn_parse().FromCloudFormation.getString(properties.Category):void 0),ret.addPropertyResult("owner","Owner",properties.Owner!=null?cfn_parse().FromCloudFormation.getString(properties.Owner):void 0),ret.addPropertyResult("provider","Provider",properties.Provider!=null?cfn_parse().FromCloudFormation.getString(properties.Provider):void 0),ret.addPropertyResult("version","Version",properties.Version!=null?cfn_parse().FromCloudFormation.getString(properties.Version):void 0),ret.addUnrecognizedPropertiesAsExtra(properties),ret}function CfnPipelineInputArtifactPropertyValidator(properties){if(!cdk().canInspect(properties))return cdk().VALIDATION_SUCCESS;const errors=new(cdk()).ValidationResults;return properties&&typeof properties=="object"&&!Array.isArray(properties)||errors.collect(new(cdk()).ValidationResult("Expected an object, but received: "+JSON.stringify(properties))),errors.collect(cdk().propertyValidator("name",cdk().requiredValidator)(properties.name)),errors.collect(cdk().propertyValidator("name",cdk().validateString)(properties.name)),errors.wrap('supplied properties not correct for "InputArtifactProperty"')}function convertCfnPipelineInputArtifactPropertyToCloudFormation(properties){return cdk().canInspect(properties)?(CfnPipelineInputArtifactPropertyValidator(properties).assertSuccess(),{Name:cdk().stringToCloudFormation(properties.name)}):properties}function CfnPipelineInputArtifactPropertyFromCloudFormation(properties){if(cdk().isResolvableObject(properties))return new(cfn_parse()).FromCloudFormationResult(properties);if(properties=properties??{},!(properties&&typeof properties=="object"&&!Array.isArray(properties)))return new(cfn_parse()).FromCloudFormationResult(properties);const ret=new(cfn_parse()).FromCloudFormationPropertyObject;return ret.addPropertyResult("name","Name",properties.Name!=null?cfn_parse().FromCloudFormation.getString(properties.Name):void 0),ret.addUnrecognizedPropertiesAsExtra(properties),ret}function CfnPipelineOutputArtifactPropertyValidator(properties){if(!cdk().canInspect(properties))return cdk().VALIDATION_SUCCESS;const errors=new(cdk()).ValidationResults;return properties&&typeof properties=="object"&&!Array.isArray(properties)||errors.collect(new(cdk()).ValidationResult("Expected an object, but received: "+JSON.stringify(properties))),errors.collect(cdk().propertyValidator("files",cdk().listValidator(cdk().validateString))(properties.files)),errors.collect(cdk().propertyValidator("name",cdk().requiredValidator)(properties.name)),errors.collect(cdk().propertyValidator("name",cdk().validateString)(properties.name)),errors.wrap('supplied properties not correct for "OutputArtifactProperty"')}function convertCfnPipelineOutputArtifactPropertyToCloudFormation(properties){return cdk().canInspect(properties)?(CfnPipelineOutputArtifactPropertyValidator(properties).assertSuccess(),{Files:cdk().listMapper(cdk().stringToCloudFormation)(properties.files),Name:cdk().stringToCloudFormation(properties.name)}):properties}function CfnPipelineOutputArtifactPropertyFromCloudFormation(properties){if(cdk().isResolvableObject(properties))return new(cfn_parse()).FromCloudFormationResult(properties);if(properties=properties??{},!(properties&&typeof properties=="object"&&!Array.isArray(properties)))return new(cfn_parse()).FromCloudFormationResult(properties);const ret=new(cfn_parse()).FromCloudFormationPropertyObject;return ret.addPropertyResult("files","Files",properties.Files!=null?cfn_parse().FromCloudFormation.getArray(cfn_parse().FromCloudFormation.getString)(properties.Files):void 0),ret.addPropertyResult("name","Name",properties.Name!=null?cfn_parse().FromCloudFormation.getString(properties.Name):void 0),ret.addUnrecognizedPropertiesAsExtra(properties),ret}function CfnPipelineEnvironmentVariablePropertyValidator(properties){if(!cdk().canInspect(properties))return cdk().VALIDATION_SUCCESS;const errors=new(cdk()).ValidationResults;return properties&&typeof properties=="object"&&!Array.isArray(properties)||errors.collect(new(cdk()).ValidationResult("Expected an object, but received: "+JSON.stringify(properties))),errors.collect(cdk().propertyValidator("name",cdk().requiredValidator)(properties.name)),errors.collect(cdk().propertyValidator("name",cdk().validateString)(properties.name)),errors.collect(cdk().propertyValidator("type",cdk().validateString)(properties.type)),errors.collect(cdk().propertyValidator("value",cdk().requiredValidator)(properties.value)),errors.collect(cdk().propertyValidator("value",cdk().validateString)(properties.value)),errors.wrap('supplied properties not correct for "EnvironmentVariableProperty"')}function convertCfnPipelineEnvironmentVariablePropertyToCloudFormation(properties){return cdk().canInspect(properties)?(CfnPipelineEnvironmentVariablePropertyValidator(properties).assertSuccess(),{Name:cdk().stringToCloudFormation(properties.name),Type:cdk().stringToCloudFormation(properties.type),Value:cdk().stringToCloudFormation(properties.value)}):properties}function CfnPipelineEnvironmentVariablePropertyFromCloudFormation(properties){if(cdk().isResolvableObject(properties))return new(cfn_parse()).FromCloudFormationResult(properties);if(properties=properties??{},!(properties&&typeof properties=="object"&&!Array.isArray(properties)))return new(cfn_parse()).FromCloudFormationResult(properties);const ret=new(cfn_parse()).FromCloudFormationPropertyObject;return ret.addPropertyResult("name","Name",properties.Name!=null?cfn_parse().FromCloudFormation.getString(properties.Name):void 0),ret.addPropertyResult("type","Type",properties.Type!=null?cfn_parse().FromCloudFormation.getString(properties.Type):void 0),ret.addPropertyResult("value","Value",properties.Value!=null?cfn_parse().FromCloudFormation.getString(properties.Value):void 0),ret.addUnrecognizedPropertiesAsExtra(properties),ret}function CfnPipelineActionDeclarationPropertyValidator(properties){if(!cdk().canInspect(properties))return cdk().VALIDATION_SUCCESS;const errors=new(cdk()).ValidationResults;return properties&&typeof properties=="object"&&!Array.isArray(properties)||errors.collect(new(cdk()).ValidationResult("Expected an object, but received: "+JSON.stringify(properties))),errors.collect(cdk().propertyValidator("actionTypeId",cdk().requiredValidator)(properties.actionTypeId)),errors.collect(cdk().propertyValidator("actionTypeId",CfnPipelineActionTypeIdPropertyValidator)(properties.actionTypeId)),errors.collect(cdk().propertyValidator("commands",cdk().listValidator(cdk().validateString))(properties.commands)),errors.collect(cdk().propertyValidator("configuration",cdk().validateObject)(properties.configuration)),errors.collect(cdk().propertyValidator("environmentVariables",cdk().listValidator(CfnPipelineEnvironmentVariablePropertyValidator))(properties.environmentVariables)),errors.collect(cdk().propertyValidator("inputArtifacts",cdk().listValidator(CfnPipelineInputArtifactPropertyValidator))(properties.inputArtifacts)),errors.collect(cdk().propertyValidator("name",cdk().requiredValidator)(properties.name)),errors.collect(cdk().propertyValidator("name",cdk().validateString)(properties.name)),errors.collect(cdk().propertyValidator("namespace",cdk().validateString)(properties.namespace)),errors.collect(cdk().propertyValidator("outputArtifacts",cdk().listValidator(CfnPipelineOutputArtifactPropertyValidator))(properties.outputArtifacts)),errors.collect(cdk().propertyValidator("outputVariables",cdk().listValidator(cdk().validateString))(properties.outputVariables)),errors.collect(cdk().propertyValidator("region",cdk().validateString)(properties.region)),errors.collect(cdk().propertyValidator("roleArn",cdk().validateString)(properties.roleArn)),errors.collect(cdk().propertyValidator("runOrder",cdk().validateNumber)(properties.runOrder)),errors.collect(cdk().propertyValidator("timeoutInMinutes",cdk().validateNumber)(properties.timeoutInMinutes)),errors.wrap('supplied properties not correct for "ActionDeclarationProperty"')}function convertCfnPipelineActionDeclarationPropertyToCloudFormation(properties){return cdk().canInspect(properties)?(CfnPipelineActionDeclarationPropertyValidator(properties).assertSuccess(),{ActionTypeId:convertCfnPipelineActionTypeIdPropertyToCloudFormation(properties.actionTypeId),Commands:cdk().listMapper(cdk().stringToCloudFormation)(properties.commands),Configuration:cdk().objectToCloudFormation(properties.configuration),EnvironmentVariables:cdk().listMapper(convertCfnPipelineEnvironmentVariablePropertyToCloudFormation)(properties.environmentVariables),InputArtifacts:cdk().listMapper(convertCfnPipelineInputArtifactPropertyToCloudFormation)(properties.inputArtifacts),Name:cdk().stringToCloudFormation(properties.name),Namespace:cdk().stringToCloudFormation(properties.namespace),OutputArtifacts:cdk().listMapper(convertCfnPipelineOutputArtifactPropertyToCloudFormation)(properties.outputArtifacts),OutputVariables:cdk().listMapper(cdk().stringToCloudFormation)(properties.outputVariables),Region:cdk().stringToCloudFormation(properties.region),RoleArn:cdk().stringToCloudFormation(properties.roleArn),RunOrder:cdk().numberToCloudFormation(properties.runOrder),TimeoutInMinutes:cdk().numberToCloudFormation(properties.timeoutInMinutes)}):properties}function CfnPipelineActionDeclarationPropertyFromCloudFormation(properties){if(cdk().isResolvableObject(properties))return new(cfn_parse()).FromCloudFormationResult(properties);if(properties=properties??{},!(properties&&typeof properties=="object"&&!Array.isArray(properties)))return new(cfn_parse()).FromCloudFormationResult(properties);const ret=new(cfn_parse()).FromCloudFormationPropertyObject;return ret.addPropertyResult("actionTypeId","ActionTypeId",properties.ActionTypeId!=null?CfnPipelineActionTypeIdPropertyFromCloudFormation(properties.ActionTypeId):void 0),ret.addPropertyResult("commands","Commands",properties.Commands!=null?cfn_parse().FromCloudFormation.getArray(cfn_parse().FromCloudFormation.getString)(properties.Commands):void 0),ret.addPropertyResult("configuration","Configuration",properties.Configuration!=null?cfn_parse().FromCloudFormation.getAny(properties.Configuration):void 0),ret.addPropertyResult("environmentVariables","EnvironmentVariables",properties.EnvironmentVariables!=null?cfn_parse().FromCloudFormation.getArray(CfnPipelineEnvironmentVariablePropertyFromCloudFormation)(properties.EnvironmentVariables):void 0),ret.addPropertyResult("inputArtifacts","InputArtifacts",properties.InputArtifacts!=null?cfn_parse().FromCloudFormation.getArray(CfnPipelineInputArtifactPropertyFromCloudFormation)(properties.InputArtifacts):void 0),ret.addPropertyResult("name","Name",properties.Name!=null?cfn_parse().FromCloudFormation.getString(properties.Name):void 0),ret.addPropertyResult("namespace","Namespace",properties.Namespace!=null?cfn_parse().FromCloudFormation.getString(properties.Namespace):void 0),ret.addPropertyResult("outputArtifacts","OutputArtifacts",properties.OutputArtifacts!=null?cfn_parse().FromCloudFormation.getArray(CfnPipelineOutputArtifactPropertyFromCloudFormation)(properties.OutputArtifacts):void 0),ret.addPropertyResult("outputVariables","OutputVariables",properties.OutputVariables!=null?cfn_parse().FromCloudFormation.getArray(cfn_parse().FromCloudFormation.getString)(properties.OutputVariables):void 0),ret.addPropertyResult("region","Region",properties.Region!=null?cfn_parse().FromCloudFormation.getString(properties.Region):void 0),ret.addPropertyResult("roleArn","RoleArn",properties.RoleArn!=null?cfn_parse().FromCloudFormation.getString(properties.RoleArn):void 0),ret.addPropertyResult("runOrder","RunOrder",properties.RunOrder!=null?cfn_parse().FromCloudFormation.getNumber(properties.RunOrder):void 0),ret.addPropertyResult("timeoutInMinutes","TimeoutInMinutes",properties.TimeoutInMinutes!=null?cfn_parse().FromCloudFormation.getNumber(properties.TimeoutInMinutes):void 0),ret.addUnrecognizedPropertiesAsExtra(properties),ret}function CfnPipelineBlockerDeclarationPropertyValidator(properties){if(!cdk().canInspect(properties))return cdk().VALIDATION_SUCCESS;const errors=new(cdk()).ValidationResults;return properties&&typeof properties=="object"&&!Array.isArray(properties)||errors.collect(new(cdk()).ValidationResult("Expected an object, but received: "+JSON.stringify(properties))),errors.collect(cdk().propertyValidator("name",cdk().requiredValidator)(properties.name)),errors.collect(cdk().propertyValidator("name",cdk().validateString)(properties.name)),errors.collect(cdk().propertyValidator("type",cdk().requiredValidator)(properties.type)),errors.collect(cdk().propertyValidator("type",cdk().validateString)(properties.type)),errors.wrap('supplied properties not correct for "BlockerDeclarationProperty"')}function convertCfnPipelineBlockerDeclarationPropertyToCloudFormation(properties){return cdk().canInspect(properties)?(CfnPipelineBlockerDeclarationPropertyValidator(properties).assertSuccess(),{Name:cdk().stringToCloudFormation(properties.name),Type:cdk().stringToCloudFormation(properties.type)}):properties}function CfnPipelineBlockerDeclarationPropertyFromCloudFormation(properties){if(cdk().isResolvableObject(properties))return new(cfn_parse()).FromCloudFormationResult(properties);if(properties=properties??{},!(properties&&typeof properties=="object"&&!Array.isArray(properties)))return new(cfn_parse()).FromCloudFormationResult(properties);const ret=new(cfn_parse()).FromCloudFormationPropertyObject;return ret.addPropertyResult("name","Name",properties.Name!=null?cfn_parse().FromCloudFormation.getString(properties.Name):void 0),ret.addPropertyResult("type","Type",properties.Type!=null?cfn_parse().FromCloudFormation.getString(properties.Type):void 0),ret.addUnrecognizedPropertiesAsExtra(properties),ret}function CfnPipelineRetryConfigurationPropertyValidator(properties){if(!cdk().canInspect(properties))return cdk().VALIDATION_SUCCESS;const errors=new(cdk()).ValidationResults;return properties&&typeof properties=="object"&&!Array.isArray(properties)||errors.collect(new(cdk()).ValidationResult("Expected an object, but received: "+JSON.stringify(properties))),errors.collect(cdk().propertyValidator("retryMode",cdk().validateString)(properties.retryMode)),errors.wrap('supplied properties not correct for "RetryConfigurationProperty"')}function convertCfnPipelineRetryConfigurationPropertyToCloudFormation(properties){return cdk().canInspect(properties)?(CfnPipelineRetryConfigurationPropertyValidator(properties).assertSuccess(),{RetryMode:cdk().stringToCloudFormation(properties.retryMode)}):properties}function CfnPipelineRetryConfigurationPropertyFromCloudFormation(properties){if(cdk().isResolvableObject(properties))return new(cfn_parse()).FromCloudFormationResult(properties);if(properties=properties??{},!(properties&&typeof properties=="object"&&!Array.isArray(properties)))return new(cfn_parse()).FromCloudFormationResult(properties);const ret=new(cfn_parse()).FromCloudFormationPropertyObject;return ret.addPropertyResult("retryMode","RetryMode",properties.RetryMode!=null?cfn_parse().FromCloudFormation.getString(properties.RetryMode):void 0),ret.addUnrecognizedPropertiesAsExtra(properties),ret}function CfnPipelineRuleTypeIdPropertyValidator(properties){if(!cdk().canInspect(properties))return cdk().VALIDATION_SUCCESS;const errors=new(cdk()).ValidationResults;return properties&&typeof properties=="object"&&!Array.isArray(properties)||errors.collect(new(cdk()).ValidationResult("Expected an object, but received: "+JSON.stringify(properties))),errors.collect(cdk().propertyValidator("category",cdk().validateString)(properties.category)),errors.collect(cdk().propertyValidator("owner",cdk().validateString)(properties.owner)),errors.collect(cdk().propertyValidator("provider",cdk().validateString)(properties.provider)),errors.collect(cdk().propertyValidator("version",cdk().validateString)(properties.version)),errors.wrap('supplied properties not correct for "RuleTypeIdProperty"')}function convertCfnPipelineRuleTypeIdPropertyToCloudFormation(properties){return cdk().canInspect(properties)?(CfnPipelineRuleTypeIdPropertyValidator(properties).assertSuccess(),{Category:cdk().stringToCloudFormation(properties.category),Owner:cdk().stringToCloudFormation(properties.owner),Provider:cdk().stringToCloudFormation(properties.provider),Version:cdk().stringToCloudFormation(properties.version)}):properties}function CfnPipelineRuleTypeIdPropertyFromCloudFormation(properties){if(cdk().isResolvableObject(properties))return new(cfn_parse()).FromCloudFormationResult(properties);if(properties=properties??{},!(properties&&typeof properties=="object"&&!Array.isArray(properties)))return new(cfn_parse()).FromCloudFormationResult(properties);const ret=new(cfn_parse()).FromCloudFormationPropertyObject;return ret.addPropertyResult("category","Category",properties.Category!=null?cfn_parse().FromCloudFormation.getString(properties.Category):void 0),ret.addPropertyResult("owner","Owner",properties.Owner!=null?cfn_parse().FromCloudFormation.getString(properties.Owner):void 0),ret.addPropertyResult("provider","Provider",properties.Provider!=null?cfn_parse().FromCloudFormation.getString(properties.Provider):void 0),ret.addPropertyResult("version","Version",properties.Version!=null?cfn_parse().FromCloudFormation.getString(properties.Version):void 0),ret.addUnrecognizedPropertiesAsExtra(properties),ret}function CfnPipelineRuleDeclarationPropertyValidator(properties){if(!cdk().canInspect(properties))return cdk().VALIDATION_SUCCESS;const errors=new(cdk()).ValidationResults;return properties&&typeof properties=="object"&&!Array.isArray(properties)||errors.collect(new(cdk()).ValidationResult("Expected an object, but received: "+JSON.stringify(properties))),errors.collect(cdk().propertyValidator("commands",cdk().listValidator(cdk().validateString))(properties.commands)),errors.collect(cdk().propertyValidator("configuration",cdk().validateObject)(properties.configuration)),errors.collect(cdk().propertyValidator("inputArtifacts",cdk().listValidator(CfnPipelineInputArtifactPropertyValidator))(properties.inputArtifacts)),errors.collect(cdk().propertyValidator("name",cdk().validateString)(properties.name)),errors.collect(cdk().propertyValidator("region",cdk().validateString)(properties.region)),errors.collect(cdk().propertyValidator("roleArn",cdk().validateString)(properties.roleArn)),errors.collect(cdk().propertyValidator("ruleTypeId",CfnPipelineRuleTypeIdPropertyValidator)(properties.ruleTypeId)),errors.wrap('supplied properties not correct for "RuleDeclarationProperty"')}function convertCfnPipelineRuleDeclarationPropertyToCloudFormation(properties){return cdk().canInspect(properties)?(CfnPipelineRuleDeclarationPropertyValidator(properties).assertSuccess(),{Commands:cdk().listMapper(cdk().stringToCloudFormation)(properties.commands),Configuration:cdk().objectToCloudFormation(properties.configuration),InputArtifacts:cdk().listMapper(convertCfnPipelineInputArtifactPropertyToCloudFormation)(properties.inputArtifacts),Name:cdk().stringToCloudFormation(properties.name),Region:cdk().stringToCloudFormation(properties.region),RoleArn:cdk().stringToCloudFormation(properties.roleArn),RuleTypeId:convertCfnPipelineRuleTypeIdPropertyToCloudFormation(properties.ruleTypeId)}):properties}function CfnPipelineRuleDeclarationPropertyFromCloudFormation(properties){if(cdk().isResolvableObject(properties))return new(cfn_parse()).FromCloudFormationResult(properties);if(properties=properties??{},!(properties&&typeof properties=="object"&&!Array.isArray(properties)))return new(cfn_parse()).FromCloudFormationResult(properties);const ret=new(cfn_parse()).FromCloudFormationPropertyObject;return ret.addPropertyResult("commands","Commands",properties.Commands!=null?cfn_parse().FromCloudFormation.getArray(cfn_parse().FromCloudFormation.getString)(properties.Commands):void 0),ret.addPropertyResult("configuration","Configuration",properties.Configuration!=null?cfn_parse().FromCloudFormation.getAny(properties.Configuration):void 0),ret.addPropertyResult("inputArtifacts","InputArtifacts",properties.InputArtifacts!=null?cfn_parse().FromCloudFormation.getArray(CfnPipelineInputArtifactPropertyFromCloudFormation)(properties.InputArtifacts):void 0),ret.addPropertyResult("name","Name",properties.Name!=null?cfn_parse().FromCloudFormation.getString(properties.Name):void 0),ret.addPropertyResult("region","Region",properties.Region!=null?cfn_parse().FromCloudFormation.getString(properties.Region):void 0),ret.addPropertyResult("roleArn","RoleArn",properties.RoleArn!=null?cfn_parse().FromCloudFormation.getString(properties.RoleArn):void 0),ret.addPropertyResult("ruleTypeId","RuleTypeId",properties.RuleTypeId!=null?CfnPipelineRuleTypeIdPropertyFromCloudFormation(properties.RuleTypeId):void 0),ret.addUnrecognizedPropertiesAsExtra(properties),ret}function CfnPipelineConditionPropertyValidator(properties){if(!cdk().canInspect(properties))return cdk().VALIDATION_SUCCESS;const errors=new(cdk()).ValidationResults;return properties&&typeof properties=="object"&&!Array.isArray(properties)||errors.collect(new(cdk()).ValidationResult("Expected an object, but received: "+JSON.stringify(properties))),errors.collect(cdk().propertyValidator("result",cdk().validateString)(properties.result)),errors.collect(cdk().propertyValidator("rules",cdk().listValidator(CfnPipelineRuleDeclarationPropertyValidator))(properties.rules)),errors.wrap('supplied properties not correct for "ConditionProperty"')}function convertCfnPipelineConditionPropertyToCloudFormation(properties){return cdk().canInspect(properties)?(CfnPipelineConditionPropertyValidator(properties).assertSuccess(),{Result:cdk().stringToCloudFormation(properties.result),Rules:cdk().listMapper(convertCfnPipelineRuleDeclarationPropertyToCloudFormation)(properties.rules)}):properties}function CfnPipelineConditionPropertyFromCloudFormation(properties){if(cdk().isResolvableObject(properties))return new(cfn_parse()).FromCloudFormationResult(properties);if(properties=properties??{},!(properties&&typeof properties=="object"&&!Array.isArray(properties)))return new(cfn_parse()).FromCloudFormationResult(properties);const ret=new(cfn_parse()).FromCloudFormationPropertyObject;return ret.addPropertyResult("result","Result",properties.Result!=null?cfn_parse().FromCloudFormation.getString(properties.Result):void 0),ret.addPropertyResult("rules","Rules",properties.Rules!=null?cfn_parse().FromCloudFormation.getArray(CfnPipelineRuleDeclarationPropertyFromCloudFormation)(properties.Rules):void 0),ret.addUnrecognizedPropertiesAsExtra(properties),ret}function CfnPipelineFailureConditionsPropertyValidator(properties){if(!cdk().canInspect(properties))return cdk().VALIDATION_SUCCESS;const errors=new(cdk()).ValidationResults;return properties&&typeof properties=="object"&&!Array.isArray(properties)||errors.collect(new(cdk()).ValidationResult("Expected an object, but received: "+JSON.stringify(properties))),errors.collect(cdk().propertyValidator("conditions",cdk().listValidator(CfnPipelineConditionPropertyValidator))(properties.conditions)),errors.collect(cdk().propertyValidator("result",cdk().validateString)(properties.result)),errors.collect(cdk().propertyValidator("retryConfiguration",CfnPipelineRetryConfigurationPropertyValidator)(properties.retryConfiguration)),errors.wrap('supplied properties not correct for "FailureConditionsProperty"')}function convertCfnPipelineFailureConditionsPropertyToCloudFormation(properties){return cdk().canInspect(properties)?(CfnPipelineFailureConditionsPropertyValidator(properties).assertSuccess(),{Conditions:cdk().listMapper(convertCfnPipelineConditionPropertyToCloudFormation)(properties.conditions),Result:cdk().stringToCloudFormation(properties.result),RetryConfiguration:convertCfnPipelineRetryConfigurationPropertyToCloudFormation(properties.retryConfiguration)}):properties}function CfnPipelineFailureConditionsPropertyFromCloudFormation(properties){if(cdk().isResolvableObject(properties))return new(cfn_parse()).FromCloudFormationResult(properties);if(properties=properties??{},!(properties&&typeof properties=="object"&&!Array.isArray(properties)))return new(cfn_parse()).FromCloudFormationResult(properties);const ret=new(cfn_parse()).FromCloudFormationPropertyObject;return ret.addPropertyResult("conditions","Conditions",properties.Conditions!=null?cfn_parse().FromCloudFormation.getArray(CfnPipelineConditionPropertyFromCloudFormation)(properties.Conditions):void 0),ret.addPropertyResult("result","Result",properties.Result!=null?cfn_parse().FromCloudFormation.getString(properties.Result):void 0),ret.addPropertyResult("retryConfiguration","RetryConfiguration",properties.RetryConfiguration!=null?CfnPipelineRetryConfigurationPropertyFromCloudFormation(properties.RetryConfiguration):void 0),ret.addUnrecognizedPropertiesAsExtra(properties),ret}function CfnPipelineSuccessConditionsPropertyValidator(properties){if(!cdk().canInspect(properties))return cdk().VALIDATION_SUCCESS;const errors=new(cdk()).ValidationResults;return properties&&typeof properties=="object"&&!Array.isArray(properties)||errors.collect(new(cdk()).ValidationResult("Expected an object, but received: "+JSON.stringify(properties))),errors.collect(cdk().propertyValidator("conditions",cdk().listValidator(CfnPipelineConditionPropertyValidator))(properties.conditions)),errors.wrap('supplied properties not correct for "SuccessConditionsProperty"')}function convertCfnPipelineSuccessConditionsPropertyToCloudFormation(properties){return cdk().canInspect(properties)?(CfnPipelineSuccessConditionsPropertyValidator(properties).assertSuccess(),{Conditions:cdk().listMapper(convertCfnPipelineConditionPropertyToCloudFormation)(properties.conditions)}):properties}function CfnPipelineSuccessConditionsPropertyFromCloudFormation(properties){if(cdk().isResolvableObject(properties))return new(cfn_parse()).FromCloudFormationResult(properties);if(properties=properties??{},!(properties&&typeof properties=="object"&&!Array.isArray(properties)))return new(cfn_parse()).FromCloudFormationResult(properties);const ret=new(cfn_parse()).FromCloudFormationPropertyObject;return ret.addPropertyResult("conditions","Conditions",properties.Conditions!=null?cfn_parse().FromCloudFormation.getArray(CfnPipelineConditionPropertyFromCloudFormation)(properties.Conditions):void 0),ret.addUnrecognizedPropertiesAsExtra(properties),ret}function CfnPipelineBeforeEntryConditionsPropertyValidator(properties){if(!cdk().canInspect(properties))return cdk().VALIDATION_SUCCESS;const errors=new(cdk()).ValidationResults;return properties&&typeof properties=="object"&&!Array.isArray(properties)||errors.collect(new(cdk()).ValidationResult("Expected an object, but received: "+JSON.stringify(properties))),errors.collect(cdk().propertyValidator("conditions",cdk().listValidator(CfnPipelineConditionPropertyValidator))(properties.conditions)),errors.wrap('supplied properties not correct for "BeforeEntryConditionsProperty"')}function convertCfnPipelineBeforeEntryConditionsPropertyToCloudFormation(properties){return cdk().canInspect(properties)?(CfnPipelineBeforeEntryConditionsPropertyValidator(properties).assertSuccess(),{Conditions:cdk().listMapper(convertCfnPipelineConditionPropertyToCloudFormation)(properties.conditions)}):properties}function CfnPipelineBeforeEntryConditionsPropertyFromCloudFormation(properties){if(cdk().isResolvableObject(properties))return new(cfn_parse()).FromCloudFormationResult(properties);if(properties=properties??{},!(properties&&typeof properties=="object"&&!Array.isArray(properties)))return new(cfn_parse()).FromCloudFormationResult(properties);const ret=new(cfn_parse()).FromCloudFormationPropertyObject;return ret.addPropertyResult("conditions","Conditions",properties.Conditions!=null?cfn_parse().FromCloudFormation.getArray(CfnPipelineConditionPropertyFromCloudFormation)(properties.Conditions):void 0),ret.addUnrecognizedPropertiesAsExtra(properties),ret}function CfnPipelineStageDeclarationPropertyValidator(properties){if(!cdk().canInspect(properties))return cdk().VALIDATION_SUCCESS;const errors=new(cdk()).ValidationResults;return properties&&typeof properties=="object"&&!Array.isArray(properties)||errors.collect(new(cdk()).ValidationResult("Expected an object, but received: "+JSON.stringify(properties))),errors.collect(cdk().propertyValidator("actions",cdk().requiredValidator)(properties.actions)),errors.collect(cdk().propertyValidator("actions",cdk().listValidator(CfnPipelineActionDeclarationPropertyValidator))(properties.actions)),errors.collect(cdk().propertyValidator("beforeEntry",CfnPipelineBeforeEntryConditionsPropertyValidator)(properties.beforeEntry)),errors.collect(cdk().propertyValidator("blockers",cdk().listValidator(CfnPipelineBlockerDeclarationPropertyValidator))(properties.blockers)),errors.collect(cdk().propertyValidator("name",cdk().requiredValidator)(properties.name)),errors.collect(cdk().propertyValidator("name",cdk().validateString)(properties.name)),errors.collect(cdk().propertyValidator("onFailure",CfnPipelineFailureConditionsPropertyValidator)(properties.onFailure)),errors.collect(cdk().propertyValidator("onSuccess",CfnPipelineSuccessConditionsPropertyValidator)(properties.onSuccess)),errors.wrap('supplied properties not correct for "StageDeclarationProperty"')}function convertCfnPipelineStageDeclarationPropertyToCloudFormation(properties){return cdk().canInspect(properties)?(CfnPipelineStageDeclarationPropertyValidator(properties).assertSuccess(),{Actions:cdk().listMapper(convertCfnPipelineActionDeclarationPropertyToCloudFormation)(properties.actions),BeforeEntry:convertCfnPipelineBeforeEntryConditionsPropertyToCloudFormation(properties.beforeEntry),Blockers:cdk().listMapper(convertCfnPipelineBlockerDeclarationPropertyToCloudFormation)(properties.blockers),Name:cdk().stringToCloudFormation(properties.name),OnFailure:convertCfnPipelineFailureConditionsPropertyToCloudFormation(properties.onFailure),OnSuccess:convertCfnPipelineSuccessConditionsPropertyToCloudFormation(properties.onSuccess)}):properties}function CfnPipelineStageDeclarationPropertyFromCloudFormation(properties){if(cdk().isResolvableObject(properties))return new(cfn_parse()).FromCloudFormationResult(properties);if(properties=properties??{},!(properties&&typeof properties=="object"&&!Array.isArray(properties)))return new(cfn_parse()).FromCloudFormationResult(properties);const ret=new(cfn_parse()).FromCloudFormationPropertyObject;return ret.addPropertyResult("actions","Actions",properties.Actions!=null?cfn_parse().FromCloudFormation.getArray(CfnPipelineActionDeclarationPropertyFromCloudFormation)(properties.Actions):void 0),ret.addPropertyResult("beforeEntry","BeforeEntry",properties.BeforeEntry!=null?CfnPipelineBeforeEntryConditionsPropertyFromCloudFormation(properties.BeforeEntry):void 0),ret.addPropertyResult("blockers","Blockers",properties.Blockers!=null?cfn_parse().FromCloudFormation.getArray(CfnPipelineBlockerDeclarationPropertyFromCloudFormation)(properties.Blockers):void 0),ret.addPropertyResult("name","Name",properties.Name!=null?cfn_parse().FromCloudFormation.getString(properties.Name):void 0),ret.addPropertyResult("onFailure","OnFailure",properties.OnFailure!=null?CfnPipelineFailureConditionsPropertyFromCloudFormation(properties.OnFailure):void 0),ret.addPropertyResult("onSuccess","OnSuccess",properties.OnSuccess!=null?CfnPipelineSuccessConditionsPropertyFromCloudFormation(properties.OnSuccess):void 0),ret.addUnrecognizedPropertiesAsExtra(properties),ret}function CfnPipelineGitFilePathFilterCriteriaPropertyValidator(properties){if(!cdk().canInspect(properties))return cdk().VALIDATION_SUCCESS;const errors=new(cdk()).ValidationResults;return properties&&typeof properties=="object"&&!Array.isArray(properties)||errors.collect(new(cdk()).ValidationResult("Expected an object, but received: "+JSON.stringify(properties))),errors.collect(cdk().propertyValidator("excludes",cdk().listValidator(cdk().validateString))(properties.excludes)),errors.collect(cdk().propertyValidator("includes",cdk().listValidator(cdk().validateString))(properties.includes)),errors.wrap('supplied properties not correct for "GitFilePathFilterCriteriaProperty"')}function convertCfnPipelineGitFilePathFilterCriteriaPropertyToCloudFormation(properties){return cdk().canInspect(properties)?(CfnPipelineGitFilePathFilterCriteriaPropertyValidator(properties).assertSuccess(),{Excludes:cdk().listMapper(cdk().stringToCloudFormation)(properties.excludes),Includes:cdk().listMapper(cdk().stringToCloudFormation)(properties.includes)}):properties}function CfnPipelineGitFilePathFilterCriteriaPropertyFromCloudFormation(properties){if(cdk().isResolvableObject(properties))return new(cfn_parse()).FromCloudFormationResult(properties);if(properties=properties??{},!(properties&&typeof properties=="object"&&!Array.isArray(properties)))return new(cfn_parse()).FromCloudFormationResult(properties);const ret=new(cfn_parse()).FromCloudFormationPropertyObject;return ret.addPropertyResult("excludes","Excludes",properties.Excludes!=null?cfn_parse().FromCloudFormation.getArray(cfn_parse().FromCloudFormation.getString)(properties.Excludes):void 0),ret.addPropertyResult("includes","Includes",properties.Includes!=null?cfn_parse().FromCloudFormation.getArray(cfn_parse().FromCloudFormation.getString)(properties.Includes):void 0),ret.addUnrecognizedPropertiesAsExtra(properties),ret}function CfnPipelineGitBranchFilterCriteriaPropertyValidator(properties){if(!cdk().canInspect(properties))return cdk().VALIDATION_SUCCESS;const errors=new(cdk()).ValidationResults;return properties&&typeof properties=="object"&&!Array.isArray(properties)||errors.collect(new(cdk()).ValidationResult("Expected an object, but received: "+JSON.stringify(properties))),errors.collect(cdk().propertyValidator("excludes",cdk().listValidator(cdk().validateString))(properties.excludes)),errors.collect(cdk().propertyValidator("includes",cdk().listValidator(cdk().validateString))(properties.includes)),errors.wrap('supplied properties not correct for "GitBranchFilterCriteriaProperty"')}function convertCfnPipelineGitBranchFilterCriteriaPropertyToCloudFormation(properties){return cdk().canInspect(properties)?(CfnPipelineGitBranchFilterCriteriaPropertyValidator(properties).assertSuccess(),{Excludes:cdk().listMapper(cdk().stringToCloudFormation)(properties.excludes),Includes:cdk().listMapper(cdk().stringToCloudFormation)(properties.includes)}):properties}function CfnPipelineGitBranchFilterCriteriaPropertyFromCloudFormation(properties){if(cdk().isResolvableObject(properties))return new(cfn_parse()).FromCloudFormationResult(properties);if(properties=properties??{},!(properties&&typeof properties=="object"&&!Array.isArray(properties)))return new(cfn_parse()).FromCloudFormationResult(properties);const ret=new(cfn_parse()).FromCloudFormationPropertyObject;return ret.addPropertyResult("excludes","Excludes",properties.Excludes!=null?cfn_parse().FromCloudFormation.getArray(cfn_parse().FromCloudFormation.getString)(properties.Excludes):void 0),ret.addPropertyResult("includes","Includes",properties.Includes!=null?cfn_parse().FromCloudFormation.getArray(cfn_parse().FromCloudFormation.getString)(properties.Includes):void 0),ret.addUnrecognizedPropertiesAsExtra(properties),ret}function CfnPipelineGitTagFilterCriteriaPropertyValidator(properties){if(!cdk().canInspect(properties))return cdk().VALIDATION_SUCCESS;const errors=new(cdk()).ValidationResults;return properties&&typeof properties=="object"&&!Array.isArray(properties)||errors.collect(new(cdk()).ValidationResult("Expected an object, but received: "+JSON.stringify(properties))),errors.collect(cdk().propertyValidator("excludes",cdk().listValidator(cdk().validateString))(properties.excludes)),errors.collect(cdk().propertyValidator("includes",cdk().listValidator(cdk().validateString))(properties.includes)),errors.wrap('supplied properties not correct for "GitTagFilterCriteriaProperty"')}function convertCfnPipelineGitTagFilterCriteriaPropertyToCloudFormation(properties){return cdk().canInspect(properties)?(CfnPipelineGitTagFilterCriteriaPropertyValidator(properties).assertSuccess(),{Excludes:cdk().listMapper(cdk().stringToCloudFormation)(properties.excludes),Includes:cdk().listMapper(cdk().stringToCloudFormation)(properties.includes)}):properties}function CfnPipelineGitTagFilterCriteriaPropertyFromCloudFormation(properties){if(cdk().isResolvableObject(properties))return new(cfn_parse()).FromCloudFormationResult(properties);if(properties=properties??{},!(properties&&typeof properties=="object"&&!Array.isArray(properties)))return new(cfn_parse()).FromCloudFormationResult(properties);const ret=new(cfn_parse()).FromCloudFormationPropertyObject;return ret.addPropertyResult("excludes","Excludes",properties.Excludes!=null?cfn_parse().FromCloudFormation.getArray(cfn_parse().FromCloudFormation.getString)(properties.Excludes):void 0),ret.addPropertyResult("includes","Includes",properties.Includes!=null?cfn_parse().FromCloudFormation.getArray(cfn_parse().FromCloudFormation.getString)(properties.Includes):void 0),ret.addUnrecognizedPropertiesAsExtra(properties),ret}function CfnPipelineGitPushFilterPropertyValidator(properties){if(!cdk().canInspect(properties))return cdk().VALIDATION_SUCCESS;const errors=new(cdk()).ValidationResults;return properties&&typeof properties=="object"&&!Array.isArray(properties)||errors.collect(new(cdk()).ValidationResult("Expected an object, but received: "+JSON.stringify(properties))),errors.collect(cdk().propertyValidator("branches",CfnPipelineGitBranchFilterCriteriaPropertyValidator)(properties.branches)),errors.collect(cdk().propertyValidator("filePaths",CfnPipelineGitFilePathFilterCriteriaPropertyValidator)(properties.filePaths)),errors.collect(cdk().propertyValidator("tags",CfnPipelineGitTagFilterCriteriaPropertyValidator)(properties.tags)),errors.wrap('supplied properties not correct for "GitPushFilterProperty"')}function convertCfnPipelineGitPushFilterPropertyToCloudFormation(properties){return cdk().canInspect(properties)?(CfnPipelineGitPushFilterPropertyValidator(properties).assertSuccess(),{Branches:convertCfnPipelineGitBranchFilterCriteriaPropertyToCloudFormation(properties.branches),FilePaths:convertCfnPipelineGitFilePathFilterCriteriaPropertyToCloudFormation(properties.filePaths),Tags:convertCfnPipelineGitTagFilterCriteriaPropertyToCloudFormation(properties.tags)}):properties}function CfnPipelineGitPushFilterPropertyFromCloudFormation(properties){if(cdk().isResolvableObject(properties))return new(cfn_parse()).FromCloudFormationResult(properties);if(properties=properties??{},!(properties&&typeof properties=="object"&&!Array.isArray(properties)))return new(cfn_parse()).FromCloudFormationResult(properties);const ret=new(cfn_parse()).FromCloudFormationPropertyObject;return ret.addPropertyResult("branches","Branches",properties.Branches!=null?CfnPipelineGitBranchFilterCriteriaPropertyFromCloudFormation(properties.Branches):void 0),ret.addPropertyResult("filePaths","FilePaths",properties.FilePaths!=null?CfnPipelineGitFilePathFilterCriteriaPropertyFromCloudFormation(properties.FilePaths):void 0),ret.addPropertyResult("tags","Tags",properties.Tags!=null?CfnPipelineGitTagFilterCriteriaPropertyFromCloudFormation(properties.Tags):void 0),ret.addUnrecognizedPropertiesAsExtra(properties),ret}function CfnPipelineGitPullRequestFilterPropertyValidator(properties){if(!cdk().canInspect(properties))return cdk().VALIDATION_SUCCESS;const errors=new(cdk()).ValidationResults;return properties&&typeof properties=="object"&&!Array.isArray(properties)||errors.collect(new(cdk()).ValidationResult("Expected an object, but received: "+JSON.stringify(properties))),errors.collect(cdk().propertyValidator("branches",CfnPipelineGitBranchFilterCriteriaPropertyValidator)(properties.branches)),errors.collect(cdk().propertyValidator("events",cdk().listValidator(cdk().validateString))(properties.events)),errors.collect(cdk().propertyValidator("filePaths",CfnPipelineGitFilePathFilterCriteriaPropertyValidator)(properties.filePaths)),errors.wrap('supplied properties not correct for "GitPullRequestFilterProperty"')}function convertCfnPipelineGitPullRequestFilterPropertyToCloudFormation(properties){return cdk().canInspect(properties)?(CfnPipelineGitPullRequestFilterPropertyValidator(properties).assertSuccess(),{Branches:convertCfnPipelineGitBranchFilterCriteriaPropertyToCloudFormation(properties.branches),Events:cdk().listMapper(cdk().stringToCloudFormation)(properties.events),FilePaths:convertCfnPipelineGitFilePathFilterCriteriaPropertyToCloudFormation(properties.filePaths)}):properties}function CfnPipelineGitPullRequestFilterPropertyFromCloudFormation(properties){if(cdk().isResolvableObject(properties))return new(cfn_parse()).FromCloudFormationResult(properties);if(properties=properties??{},!(properties&&typeof properties=="object"&&!Array.isArray(properties)))return new(cfn_parse()).FromCloudFormationResult(properties);const ret=new(cfn_parse()).FromCloudFormationPropertyObject;return ret.addPropertyResult("branches","Branches",properties.Branches!=null?CfnPipelineGitBranchFilterCriteriaPropertyFromCloudFormation(properties.Branches):void 0),ret.addPropertyResult("events","Events",properties.Events!=null?cfn_parse().FromCloudFormation.getArray(cfn_parse().FromCloudFormation.getString)(properties.Events):void 0),ret.addPropertyResult("filePaths","FilePaths",properties.FilePaths!=null?CfnPipelineGitFilePathFilterCriteriaPropertyFromCloudFormation(properties.FilePaths):void 0),ret.addUnrecognizedPropertiesAsExtra(properties),ret}function CfnPipelineGitConfigurationPropertyValidator(properties){if(!cdk().canInspect(properties))return cdk().VALIDATION_SUCCESS;const errors=new(cdk()).ValidationResults;return properties&&typeof properties=="object"&&!Array.isArray(properties)||errors.collect(new(cdk()).ValidationResult("Expected an object, but received: "+JSON.stringify(properties))),errors.collect(cdk().propertyValidator("pullRequest",cdk().listValidator(CfnPipelineGitPullRequestFilterPropertyValidator))(properties.pullRequest)),errors.collect(cdk().propertyValidator("push",cdk().listValidator(CfnPipelineGitPushFilterPropertyValidator))(properties.push)),errors.collect(cdk().propertyValidator("sourceActionName",cdk().requiredValidator)(properties.sourceActionName)),errors.collect(cdk().propertyValidator("sourceActionName",cdk().validateString)(properties.sourceActionName)),errors.wrap('supplied properties not correct for "GitConfigurationProperty"')}function convertCfnPipelineGitConfigurationPropertyToCloudFormation(properties){return cdk().canInspect(properties)?(CfnPipelineGitConfigurationPropertyValidator(properties).assertSuccess(),{PullRequest:cdk().listMapper(convertCfnPipelineGitPullRequestFilterPropertyToCloudFormation)(properties.pullRequest),Push:cdk().listMapper(convertCfnPipelineGitPushFilterPropertyToCloudFormation)(properties.push),SourceActionName:cdk().stringToCloudFormation(properties.sourceActionName)}):properties}function CfnPipelineGitConfigurationPropertyFromCloudFormation(properties){if(cdk().isResolvableObject(properties))return new(cfn_parse()).FromCloudFormationResult(properties);if(properties=properties??{},!(properties&&typeof properties=="object"&&!Array.isArray(properties)))return new(cfn_parse()).FromCloudFormationResult(properties);const ret=new(cfn_parse()).FromCloudFormationPropertyObject;return ret.addPropertyResult("pullRequest","PullRequest",properties.PullRequest!=null?cfn_parse().FromCloudFormation.getArray(CfnPipelineGitPullRequestFilterPropertyFromCloudFormation)(properties.PullRequest):void 0),ret.addPropertyResult("push","Push",properties.Push!=null?cfn_parse().FromCloudFormation.getArray(CfnPipelineGitPushFilterPropertyFromCloudFormation)(properties.Push):void 0),ret.addPropertyResult("sourceActionName","SourceActionName",properties.SourceActionName!=null?cfn_parse().FromCloudFormation.getString(properties.SourceActionName):void 0),ret.addUnrecognizedPropertiesAsExtra(properties),ret}function CfnPipelinePipelineTriggerDeclarationPropertyValidator(properties){if(!cdk().canInspect(properties))return cdk().VALIDATION_SUCCESS;const errors=new(cdk()).ValidationResults;return properties&&typeof properties=="object"&&!Array.isArray(properties)||errors.collect(new(cdk()).ValidationResult("Expected an object, but received: "+JSON.stringify(properties))),errors.collect(cdk().propertyValidator("gitConfiguration",CfnPipelineGitConfigurationPropertyValidator)(properties.gitConfiguration)),errors.collect(cdk().propertyValidator("providerType",cdk().requiredValidator)(properties.providerType)),errors.collect(cdk().propertyValidator("providerType",cdk().validateString)(properties.providerType)),errors.wrap('supplied properties not correct for "PipelineTriggerDeclarationProperty"')}function convertCfnPipelinePipelineTriggerDeclarationPropertyToCloudFormation(properties){return cdk().canInspect(properties)?(CfnPipelinePipelineTriggerDeclarationPropertyValidator(properties).assertSuccess(),{GitConfiguration:convertCfnPipelineGitConfigurationPropertyToCloudFormation(properties.gitConfiguration),ProviderType:cdk().stringToCloudFormation(properties.providerType)}):properties}function CfnPipelinePipelineTriggerDeclarationPropertyFromCloudFormation(properties){if(cdk().isResolvableObject(properties))return new(cfn_parse()).FromCloudFormationResult(properties);if(properties=properties??{},!(properties&&typeof properties=="object"&&!Array.isArray(properties)))return new(cfn_parse()).FromCloudFormationResult(properties);const ret=new(cfn_parse()).FromCloudFormationPropertyObject;return ret.addPropertyResult("gitConfiguration","GitConfiguration",properties.GitConfiguration!=null?CfnPipelineGitConfigurationPropertyFromCloudFormation(properties.GitConfiguration):void 0),ret.addPropertyResult("providerType","ProviderType",properties.ProviderType!=null?cfn_parse().FromCloudFormation.getString(properties.ProviderType):void 0),ret.addUnrecognizedPropertiesAsExtra(properties),ret}function CfnPipelineVariableDeclarationPropertyValidator(properties){if(!cdk().canInspect(properties))return cdk().VALIDATION_SUCCESS;const errors=new(cdk()).ValidationResults;return properties&&typeof properties=="object"&&!Array.isArray(properties)||errors.collect(new(cdk()).ValidationResult("Expected an object, but received: "+JSON.stringify(properties))),errors.collect(cdk().propertyValidator("defaultValue",cdk().validateString)(properties.defaultValue)),errors.collect(cdk().propertyValidator("description",cdk().validateString)(properties.description)),errors.collect(cdk().propertyValidator("name",cdk().requiredValidator)(properties.name)),errors.collect(cdk().propertyValidator("name",cdk().validateString)(properties.name)),errors.wrap('supplied properties not correct for "VariableDeclarationProperty"')}function convertCfnPipelineVariableDeclarationPropertyToCloudFormation(properties){return cdk().canInspect(properties)?(CfnPipelineVariableDeclarationPropertyValidator(properties).assertSuccess(),{DefaultValue:cdk().stringToCloudFormation(properties.defaultValue),Description:cdk().stringToCloudFormation(properties.description),Name:cdk().stringToCloudFormation(properties.name)}):properties}function CfnPipelineVariableDeclarationPropertyFromCloudFormation(properties){if(cdk().isResolvableObject(properties))return new(cfn_parse()).FromCloudFormationResult(properties);if(properties=properties??{},!(properties&&typeof properties=="object"&&!Array.isArray(properties)))return new(cfn_parse()).FromCloudFormationResult(properties);const ret=new(cfn_parse()).FromCloudFormationPropertyObject;return ret.addPropertyResult("defaultValue","DefaultValue",properties.DefaultValue!=null?cfn_parse().FromCloudFormation.getString(properties.DefaultValue):void 0),ret.addPropertyResult("description","Description",properties.Description!=null?cfn_parse().FromCloudFormation.getString(properties.Description):void 0),ret.addPropertyResult("name","Name",properties.Name!=null?cfn_parse().FromCloudFormation.getString(properties.Name):void 0),ret.addUnrecognizedPropertiesAsExtra(properties),ret}function CfnPipelinePropsValidator(properties){if(!cdk().canInspect(properties))return cdk().VALIDATION_SUCCESS;const errors=new(cdk()).ValidationResults;return properties&&typeof properties=="object"&&!Array.isArray(properties)||errors.collect(new(cdk()).ValidationResult("Expected an object, but received: "+JSON.stringify(properties))),errors.collect(cdk().propertyValidator("artifactStore",CfnPipelineArtifactStorePropertyValidator)(properties.artifactStore)),errors.collect(cdk().propertyValidator("artifactStores",cdk().listValidator(CfnPipelineArtifactStoreMapPropertyValidator))(properties.artifactStores)),errors.collect(cdk().propertyValidator("disableInboundStageTransitions",cdk().listValidator(CfnPipelineStageTransitionPropertyValidator))(properties.disableInboundStageTransitions)),errors.collect(cdk().propertyValidator("executionMode",cdk().validateString)(properties.executionMode)),errors.collect(cdk().propertyValidator("name",cdk().validateString)(properties.name)),errors.collect(cdk().propertyValidator("pipelineType",cdk().validateString)(properties.pipelineType)),errors.collect(cdk().propertyValidator("restartExecutionOnUpdate",cdk().validateBoolean)(properties.restartExecutionOnUpdate)),errors.collect(cdk().propertyValidator("roleArn",cdk().requiredValidator)(properties.roleArn)),errors.collect(cdk().propertyValidator("roleArn",cdk().validateString)(properties.roleArn)),errors.collect(cdk().propertyValidator("stages",cdk().requiredValidator)(properties.stages)),errors.collect(cdk().propertyValidator("stages",cdk().listValidator(CfnPipelineStageDeclarationPropertyValidator))(properties.stages)),errors.collect(cdk().propertyValidator("tags",cdk().listValidator(cdk().validateCfnTag))(properties.tags)),errors.collect(cdk().propertyValidator("triggers",cdk().listValidator(CfnPipelinePipelineTriggerDeclarationPropertyValidator))(properties.triggers)),errors.collect(cdk().propertyValidator("variables",cdk().listValidator(CfnPipelineVariableDeclarationPropertyValidator))(properties.variables)),errors.wrap('supplied properties not correct for "CfnPipelineProps"')}function convertCfnPipelinePropsToCloudFormation(properties){return cdk().canInspect(properties)?(CfnPipelinePropsValidator(properties).assertSuccess(),{ArtifactStore:convertCfnPipelineArtifactStorePropertyToCloudFormation(properties.artifactStore),ArtifactStores:cdk().listMapper(convertCfnPipelineArtifactStoreMapPropertyToCloudFormation)(properties.artifactStores),DisableInboundStageTransitions:cdk().listMapper(convertCfnPipelineStageTransitionPropertyToCloudFormation)(properties.disableInboundStageTransitions),ExecutionMode:cdk().stringToCloudFormation(properties.executionMode),Name:cdk().stringToCloudFormation(properties.name),PipelineType:cdk().stringToCloudFormation(properties.pipelineType),RestartExecutionOnUpdate:cdk().booleanToCloudFormation(properties.restartExecutionOnUpdate),RoleArn:cdk().stringToCloudFormation(properties.roleArn),Stages:cdk().listMapper(convertCfnPipelineStageDeclarationPropertyToCloudFormation)(properties.stages),Tags:cdk().listMapper(cdk().cfnTagToCloudFormation)(properties.tags),Triggers:cdk().listMapper(convertCfnPipelinePipelineTriggerDeclarationPropertyToCloudFormation)(properties.triggers),Variables:cdk().listMapper(convertCfnPipelineVariableDeclarationPropertyToCloudFormation)(properties.variables)}):properties}function CfnPipelinePropsFromCloudFormation(properties){if(cdk().isResolvableObject(properties))return new(cfn_parse()).FromCloudFormationResult(properties);if(properties=properties??{},!(properties&&typeof properties=="object"&&!Array.isArray(properties)))return new(cfn_parse()).FromCloudFormationResult(properties);const ret=new(cfn_parse()).FromCloudFormationPropertyObject;return ret.addPropertyResult("artifactStore","ArtifactStore",properties.ArtifactStore!=null?CfnPipelineArtifactStorePropertyFromCloudFormation(properties.ArtifactStore):void 0),ret.addPropertyResult("artifactStores","ArtifactStores",properties.ArtifactStores!=null?cfn_parse().FromCloudFormation.getArray(CfnPipelineArtifactStoreMapPropertyFromCloudFormation)(properties.ArtifactStores):void 0),ret.addPropertyResult("disableInboundStageTransitions","DisableInboundStageTransitions",properties.DisableInboundStageTransitions!=null?cfn_parse().FromCloudFormation.getArray(CfnPipelineStageTransitionPropertyFromCloudFormation)(properties.DisableInboundStageTransitions):void 0),ret.addPropertyResult("executionMode","ExecutionMode",properties.ExecutionMode!=null?cfn_parse().FromCloudFormation.getString(properties.ExecutionMode):void 0),ret.addPropertyResult("name","Name",properties.Name!=null?cfn_parse().FromCloudFormation.getString(properties.Name):void 0),ret.addPropertyResult("pipelineType","PipelineType",properties.PipelineType!=null?cfn_parse().FromCloudFormation.getString(properties.PipelineType):void 0),ret.addPropertyResult("restartExecutionOnUpdate","RestartExecutionOnUpdate",properties.RestartExecutionOnUpdate!=null?cfn_parse().FromCloudFormation.getBoolean(properties.RestartExecutionOnUpdate):void 0),ret.addPropertyResult("roleArn","RoleArn",properties.RoleArn!=null?cfn_parse().FromCloudFormation.getString(properties.RoleArn):void 0),ret.addPropertyResult("stages","Stages",properties.Stages!=null?cfn_parse().FromCloudFormation.getArray(CfnPipelineStageDeclarationPropertyFromCloudFormation)(properties.Stages):void 0),ret.addPropertyResult("tags","Tags",properties.Tags!=null?cfn_parse().FromCloudFormation.getArray(cfn_parse().FromCloudFormation.getCfnTag)(properties.Tags):void 0),ret.addPropertyResult("triggers","Triggers",properties.Triggers!=null?cfn_parse().FromCloudFormation.getArray(CfnPipelinePipelineTriggerDeclarationPropertyFromCloudFormation)(properties.Triggers):void 0),ret.addPropertyResult("variables","Variables",properties.Variables!=null?cfn_parse().FromCloudFormation.getArray(CfnPipelineVariableDeclarationPropertyFromCloudFormation)(properties.Variables):void 0),ret.addUnrecognizedPropertiesAsExtra(properties),ret}class CfnWebhook extends cdk().CfnResource{static _fromCloudFormation(scope,id,resourceAttributes,options){resourceAttributes=resourceAttributes||{};const resourceProperties=options.parser.parseValue(resourceAttributes.Properties),propsResult=CfnWebhookPropsFromCloudFormation(resourceProperties);if(cdk().isResolvableObject(propsResult.value))throw new(cdk_errors()).ValidationError("Unexpected IResolvable",scope);const ret=new CfnWebhook(scope,id,propsResult.value);for(const[propKey,propVal]of Object.entries(propsResult.extraProperties))ret.addPropertyOverride(propKey,propVal);return options.parser.handleAttributes(ret,resourceAttributes,id),ret}constructor(scope,id,props){super(scope,id,{type:CfnWebhook.CFN_RESOURCE_TYPE_NAME,properties:props});try{jsiiDeprecationWarnings().aws_cdk_lib_aws_codepipeline_CfnWebhookProps(props)}catch(error){throw process.env.JSII_DEBUG!=="1"&&error.name==="DeprecationError"&&Error.captureStackTrace(error,CfnWebhook),error}cdk().requireProperty(props,"authentication",this),cdk().requireProperty(props,"authenticationConfiguration",this),cdk().requireProperty(props,"filters",this),cdk().requireProperty(props,"targetAction",this),cdk().requireProperty(props,"targetPipeline",this),cdk().requireProperty(props,"targetPipelineVersion",this),this.attrId=cdk().Token.asString(this.getAtt("Id",cdk().ResolutionTypeHint.STRING)),this.attrUrl=cdk().Token.asString(this.getAtt("Url",cdk().ResolutionTypeHint.STRING)),this.authentication=props.authentication,this.authenticationConfiguration=props.authenticationConfiguration,this.filters=props.filters,this.name=props.name,this.registerWithThirdParty=props.registerWithThirdParty,this.targetAction=props.targetAction,this.targetPipeline=props.targetPipeline,this.targetPipelineVersion=props.targetPipelineVersion}get cfnProperties(){return{authentication:this.authentication,authenticationConfiguration:this.authenticationConfiguration,filters:this.filters,name:this.name,registerWithThirdParty:this.registerWithThirdParty,targetAction:this.targetAction,targetPipeline:this.targetPipeline,targetPipelineVersion:this.targetPipelineVersion}}inspect(inspector){try{jsiiDeprecationWarnings().aws_cdk_lib_TreeInspector(inspector)}catch(error){throw process.env.JSII_DEBUG!=="1"&&error.name==="DeprecationError"&&Error.captureStackTrace(error,this.inspect),error}inspector.addAttribute("aws:cdk:cloudformation:type",CfnWebhook.CFN_RESOURCE_TYPE_NAME),inspector.addAttribute("aws:cdk:cloudformation:props",this.cfnProperties)}renderProperties(props){return convertCfnWebhookPropsToCloudFormation(props)}}exports.CfnWebhook=CfnWebhook,_c=JSII_RTTI_SYMBOL_1,CfnWebhook[_c]={fqn:"aws-cdk-lib.aws_codepipeline.CfnWebhook",version:"2.201.0"},CfnWebhook.CFN_RESOURCE_TYPE_NAME="AWS::CodePipeline::Webhook";function CfnWebhookWebhookAuthConfigurationPropertyValidator(properties){if(!cdk().canInspect(properties))return cdk().VALIDATION_SUCCESS;const errors=new(cdk()).ValidationResults;return properties&&typeof properties=="object"&&!Array.isArray(properties)||errors.collect(new(cdk()).ValidationResult("Expected an object, but received: "+JSON.stringify(properties))),errors.collect(cdk().propertyValidator("allowedIpRange",cdk().validateString)(properties.allowedIpRange)),errors.collect(cdk().propertyValidator("secretToken",cdk().validateString)(properties.secretToken)),errors.wrap('supplied properties not correct for "WebhookAuthConfigurationProperty"')}function convertCfnWebhookWebhookAuthConfigurationPropertyToCloudFormation(properties){return cdk().canInspect(properties)?(CfnWebhookWebhookAuthConfigurationPropertyValidator(properties).assertSuccess(),{AllowedIPRange:cdk().stringToCloudFormation(properties.allowedIpRange),SecretToken:cdk().stringToCloudFormation(properties.secretToken)}):properties}function CfnWebhookWebhookAuthConfigurationPropertyFromCloudFormation(properties){if(cdk().isResolvableObject(properties))return new(cfn_parse()).FromCloudFormationResult(properties);if(properties=properties??{},!(properties&&typeof properties=="object"&&!Array.isArray(properties)))return new(cfn_parse()).FromCloudFormationResult(properties);const ret=new(cfn_parse()).FromCloudFormationPropertyObject;return ret.addPropertyResult("allowedIpRange","AllowedIPRange",properties.AllowedIPRange!=null?cfn_parse().FromCloudFormation.getString(properties.AllowedIPRange):void 0),ret.addPropertyResult("secretToken","SecretToken",properties.SecretToken!=null?cfn_parse().FromCloudFormation.getString(properties.SecretToken):void 0),ret.addUnrecognizedPropertiesAsExtra(properties),ret}function CfnWebhookWebhookFilterRulePropertyValidator(properties){if(!cdk().canInspect(properties))return cdk().VALIDATION_SUCCESS;const errors=new(cdk()).ValidationResults;return properties&&typeof properties=="object"&&!Array.isArray(properties)||errors.collect(new(cdk()).ValidationResult("Expected an object, but received: "+JSON.stringify(properties))),errors.collect(cdk().propertyValidator("jsonPath",cdk().requiredValidator)(properties.jsonPath)),errors.collect(cdk().propertyValidator("jsonPath",cdk().validateString)(properties.jsonPath)),errors.collect(cdk().propertyValidator("matchEquals",cdk().validateString)(properties.matchEquals)),errors.wrap('supplied properties not correct for "WebhookFilterRuleProperty"')}function convertCfnWebhookWebhookFilterRulePropertyToCloudFormation(properties){return cdk().canInspect(properties)?(CfnWebhookWebhookFilterRulePropertyValidator(properties).assertSuccess(),{JsonPath:cdk().stringToCloudFormation(properties.jsonPath),MatchEquals:cdk().stringToCloudFormation(properties.matchEquals)}):properties}function CfnWebhookWebhookFilterRulePropertyFromCloudFormation(properties){if(cdk().isResolvableObject(properties))return new(cfn_parse()).FromCloudFormationResult(properties);if(properties=properties??{},!(properties&&typeof properties=="object"&&!Array.isArray(properties)))return new(cfn_parse()).FromCloudFormationResult(properties);const ret=new(cfn_parse()).FromCloudFormationPropertyObject;return ret.addPropertyResult("jsonPath","JsonPath",properties.JsonPath!=null?cfn_parse().FromCloudFormation.getString(properties.JsonPath):void 0),ret.addPropertyResult("matchEquals","MatchEquals",properties.MatchEquals!=null?cfn_parse().FromCloudFormation.getString(properties.MatchEquals):void 0),ret.addUnrecognizedPropertiesAsExtra(properties),ret}function CfnWebhookPropsValidator(properties){if(!cdk().canInspect(properties))return cdk().VALIDATION_SUCCESS;const errors=new(cdk()).ValidationResults;return properties&&typeof properties=="object"&&!Array.isArray(properties)||errors.collect(new(cdk()).ValidationResult("Expected an object, but received: "+JSON.stringify(properties))),errors.collect(cdk().propertyValidator("authentication",cdk().requiredValidator)(properties.authentication)),errors.collect(cdk().propertyValidator("authentication",cdk().validateString)(properties.authentication)),errors.collect(cdk().propertyValidator("authenticationConfiguration",cdk().requiredValidator)(properties.authenticationConfiguration)),errors.collect(cdk().propertyValidator("authenticationConfiguration",CfnWebhookWebhookAuthConfigurationPropertyValidator)(properties.authenticationConfiguration)),errors.collect(cdk().propertyValidator("filters",cdk().requiredValidator)(properties.filters)),errors.collect(cdk().propertyValidator("filters",cdk().listValidator(CfnWebhookWebhookFilterRulePropertyValidator))(properties.filters)),errors.collect(cdk().propertyValidator("name",cdk().validateString)(properties.name)),errors.collect(cdk().propertyValidator("registerWithThirdParty",cdk().validateBoolean)(properties.registerWithThirdParty)),errors.collect(cdk().propertyValidator("targetAction",cdk().requiredValidator)(properties.targetAction)),errors.collect(cdk().propertyValidator("targetAction",cdk().validateString)(properties.targetAction)),errors.collect(cdk().propertyValidator("targetPipeline",cdk().requiredValidator)(properties.targetPipeline)),errors.collect(cdk().propertyValidator("targetPipeline",cdk().validateString)(properties.targetPipeline)),errors.collect(cdk().propertyValidator("targetPipelineVersion",cdk().requiredValidator)(properties.targetPipelineVersion)),errors.collect(cdk().propertyValidator("targetPipelineVersion",cdk().validateNumber)(properties.targetPipelineVersion)),errors.wrap('supplied properties not correct for "CfnWebhookProps"')}function convertCfnWebhookPropsToCloudFormation(properties){return cdk().canInspect(properties)?(CfnWebhookPropsValidator(properties).assertSuccess(),{Authentication:cdk().stringToCloudFormation(properties.authentication),AuthenticationConfiguration:convertCfnWebhookWebhookAuthConfigurationPropertyToCloudFormation(properties.authenticationConfiguration),Filters:cdk().listMapper(convertCfnWebhookWebhookFilterRulePropertyToCloudFormation)(properties.filters),Name:cdk().stringToCloudFormation(properties.name),RegisterWithThirdParty:cdk().booleanToCloudFormation(properties.registerWithThirdParty),TargetAction:cdk().stringToCloudFormation(properties.targetAction),TargetPipeline:cdk().stringToCloudFormation(properties.targetPipeline),TargetPipelineVersion:cdk().numberToCloudFormation(properties.targetPipelineVersion)}):properties}function CfnWebhookPropsFromCloudFormation(properties){if(cdk().isResolvableObject(properties))return new(cfn_parse()).FromCloudFormationResult(properties);if(properties=properties??{},!(properties&&typeof properties=="object"&&!Array.isArray(properties)))return new(cfn_parse()).FromCloudFormationResult(properties);const ret=new(cfn_parse()).FromCloudFormationPropertyObject;return ret.addPropertyResult("authentication","Authentication",properties.Authentication!=null?cfn_parse().FromCloudFormation.getString(properties.Authentication):void 0),ret.addPropertyResult("authenticationConfiguration","AuthenticationConfiguration",properties.AuthenticationConfiguration!=null?CfnWebhookWebhookAuthConfigurationPropertyFromCloudFormation(properties.AuthenticationConfiguration):void 0),ret.addPropertyResult("filters","Filters",properties.Filters!=null?cfn_parse().FromCloudFormation.getArray(CfnWebhookWebhookFilterRulePropertyFromCloudFormation)(properties.Filters):void 0),ret.addPropertyResult("name","Name",properties.Name!=null?cfn_parse().FromCloudFormation.getString(properties.Name):void 0),ret.addPropertyResult("registerWithThirdParty","RegisterWithThirdParty",properties.RegisterWithThirdParty!=null?cfn_parse().FromCloudFormation.getBoolean(properties.RegisterWithThirdParty):void 0),ret.addPropertyResult("targetAction","TargetAction",properties.TargetAction!=null?cfn_parse().FromCloudFormation.getString(properties.TargetAction):void 0),ret.addPropertyResult("targetPipeline","TargetPipeline",properties.TargetPipeline!=null?cfn_parse().FromCloudFormation.getString(properties.TargetPipeline):void 0),ret.addPropertyResult("targetPipelineVersion","TargetPipelineVersion",properties.TargetPipelineVersion!=null?cfn_parse().FromCloudFormation.getNumber(properties.TargetPipelineVersion):void 0),ret.addUnrecognizedPropertiesAsExtra(properties),ret}
