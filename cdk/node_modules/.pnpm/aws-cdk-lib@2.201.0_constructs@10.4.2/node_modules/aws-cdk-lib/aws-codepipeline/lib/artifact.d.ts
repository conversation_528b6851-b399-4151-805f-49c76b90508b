import * as s3 from '../../aws-s3';
/**
 * An output artifact of an action. Artifacts can be used as input by some actions.
 */
export declare class Artifact {
    /**
     * A static factory method used to create instances of the Artifact class.
     * Mainly meant to be used from `decdk`.
     *
     * @param name the (required) name of the Artifact
     * @param files file paths that you want to export as the output artifact for the action.
     * This property can only be used in the artifact for `CommandsAction`.
     * The length of the files array must be between 1 and 10.
     */
    static artifact(name: string, files?: string[]): Artifact;
    private _artifactName?;
    private _artifactFiles?;
    private readonly metadata;
    /**
     * An output artifact of an action. Artifacts can be used as input by some actions.
     *
     * @param artifactName the (required) name of the Artifact
     * @param artifactFiles file paths that you want to export as the output artifact for the action.
     * This property can only be used in the artifact for `CommandsAction`.
     * The length of the artifactFiles array must be between 1 and 10.
     */
    constructor(artifactName?: string, artifactFiles?: string[]);
    get artifactName(): string | undefined;
    /**
     * The file paths that you want to export as the output artifact for the action.
     *
     * This property can only be used in artifacts for `CommandsAction`.
     */
    get artifactFiles(): string[] | undefined;
    /**
     * Returns an ArtifactPath for a file within this artifact.
     * CfnOutput is in the form "<artifact-name>::<file-name>"
     * @param fileName The name of the file
     */
    atPath(fileName: string): ArtifactPath;
    /**
     * The artifact attribute for the name of the S3 bucket where the artifact is stored.
     */
    get bucketName(): string;
    /**
     * The artifact attribute for The name of the .zip file that contains the artifact that is
     * generated by AWS CodePipeline, such as 1ABCyZZ.zip.
     */
    get objectKey(): string;
    /**
     * The artifact attribute of the Amazon Simple Storage Service (Amazon S3) URL of the artifact,
     * such as https://s3-us-west-2.amazonaws.com/artifactstorebucket-yivczw8jma0c/test/TemplateSo/1ABCyZZ.zip.
     */
    get url(): string;
    /**
     * Returns a token for a value inside a JSON file within this artifact.
     * @param jsonFile The JSON file name.
     * @param keyName The hash key.
     */
    getParam(jsonFile: string, keyName: string): string;
    /**
     * Returns the location of the .zip file in S3 that this Artifact represents.
     * Used by Lambda's `CfnParametersCode` when being deployed in a CodePipeline.
     */
    get s3Location(): s3.Location;
    /**
     * Add arbitrary extra payload to the artifact under a given key.
     * This can be used by CodePipeline actions to communicate data between themselves.
     * If metadata was already present under the given key,
     * it will be overwritten with the new value.
     */
    setMetadata(key: string, value: any): void;
    /**
     * Retrieve the metadata stored in this artifact under the given key.
     * If there is no metadata stored under the given key,
     * null will be returned.
     */
    getMetadata(key: string): any;
    toString(): string | undefined;
    /** @internal */
    protected _setName(name: string): void;
}
/**
 * A specific file within an output artifact.
 *
 * The most common use case for this is specifying the template file
 * for a CloudFormation action.
 */
export declare class ArtifactPath {
    readonly artifact: Artifact;
    readonly fileName: string;
    static artifactPath(artifactName: string, fileName: string): ArtifactPath;
    constructor(artifact: Artifact, fileName: string);
    get location(): string;
}
