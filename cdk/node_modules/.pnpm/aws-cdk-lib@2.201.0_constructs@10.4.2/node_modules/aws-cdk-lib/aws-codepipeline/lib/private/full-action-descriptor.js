"use strict";Object.defineProperty(exports,"__esModule",{value:!0}),exports.FullActionDescriptor=void 0;class FullActionDescriptor{constructor(props){this.action=props.action;const actionProperties=props.action.actionProperties;this.actionName=actionProperties.actionName,this.category=actionProperties.category,this.owner=actionProperties.owner||"AWS",this.provider=actionProperties.provider,this.version=actionProperties.version||"1",this.runOrder=actionProperties.runOrder??1,this.artifactBounds=actionProperties.artifactBounds,this.namespace=actionProperties.variablesNamespace,this.inputs=deduplicateArtifacts(actionProperties.inputs),this.outputs=deduplicateArtifacts(actionProperties.outputs),this.region=props.actionRegion||actionProperties.region,this.role=actionProperties.role??props.actionRole,this.configuration=props.actionConfig.configuration,this.commands=actionProperties.commands,this.outputVariables=actionProperties.outputVariables,this.timeout=actionProperties.timeout}}exports.FullActionDescriptor=FullActionDescriptor;function deduplicateArtifacts(artifacts){const ret=new Array;for(const artifact of artifacts||[]){if(artifact.artifactName){if(ret.find(a=>a.artifactName===artifact.artifactName))continue}else if(ret.find(a=>a===artifact))continue;ret.push(artifact)}return ret}
