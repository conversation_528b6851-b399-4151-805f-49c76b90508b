"use strict";var __decorate=exports&&exports.__decorate||function(decorators,target,key,desc){var c=arguments.length,r=c<3?target:desc===null?desc=Object.getOwnPropertyDescriptor(target,key):desc,d;if(typeof Reflect=="object"&&typeof Reflect.decorate=="function")r=Reflect.decorate(decorators,target,key,desc);else for(var i=decorators.length-1;i>=0;i--)(d=decorators[i])&&(r=(c<3?d(r):c>3?d(target,key,r):d(target,key))||r);return c>3&&r&&Object.defineProperty(target,key,r),r};Object.defineProperty(exports,"__esModule",{value:!0}),exports.CrossRegionSupportStack=exports.CrossRegionSupportConstruct=void 0;var constructs_1=()=>{var tmp=require("constructs");return constructs_1=()=>tmp,tmp},kms=()=>{var tmp=require("../../../aws-kms");return kms=()=>tmp,tmp},s3=()=>{var tmp=require("../../../aws-s3");return s3=()=>tmp,tmp},cdk=()=>{var tmp=require("../../../core");return cdk=()=>tmp,tmp},unique_resource_name_1=()=>{var tmp=require("../../../core/lib/private/unique-resource-name");return unique_resource_name_1=()=>tmp,tmp},prop_injectable_1=()=>{var tmp=require("../../../core/lib/prop-injectable");return prop_injectable_1=()=>tmp,tmp};const REQUIRED_ALIAS_PREFIX="alias/";let AliasWithShorterGeneratedName=class extends kms().Alias{generatePhysicalName(){let baseName=super.generatePhysicalName();baseName.startsWith(REQUIRED_ALIAS_PREFIX)&&(baseName=baseName.substring(REQUIRED_ALIAS_PREFIX.length));const maxLength=50-REQUIRED_ALIAS_PREFIX.length;return REQUIRED_ALIAS_PREFIX+lastNCharacters(baseName,maxLength)}};AliasWithShorterGeneratedName.PROPERTY_INJECTION_ID="aws-cdk-lib.aws-codepipeline.AliasWithShorterGeneratedName",AliasWithShorterGeneratedName=__decorate([prop_injectable_1().propertyInjectable],AliasWithShorterGeneratedName);function lastNCharacters(str,n){const startIndex=Math.max(str.length-n,0);return str.substring(startIndex)}class CrossRegionSupportConstruct extends constructs_1().Construct{constructor(scope,id,props={}){super(scope,id);const createKmsKey=props.createKmsKey??!0;let encryptionAlias;if(createKmsKey){const encryptionKey=new(kms()).Key(this,"CrossRegionCodePipelineReplicationBucketEncryptionKey",{removalPolicy:cdk().RemovalPolicy.DESTROY,enableKeyRotation:props.enableKeyRotation});encryptionAlias=new AliasWithShorterGeneratedName(this,"CrossRegionCodePipelineReplicationBucketEncryptionAlias",{targetKey:encryptionKey,aliasName:cdk().PhysicalName.GENERATE_IF_NEEDED,removalPolicy:cdk().RemovalPolicy.DESTROY})}this.replicationBucket=new(s3()).Bucket(this,"CrossRegionCodePipelineReplicationBucket",{bucketName:cdk().PhysicalName.GENERATE_IF_NEEDED,encryption:encryptionAlias?s3().BucketEncryption.KMS:s3().BucketEncryption.KMS_MANAGED,encryptionKey:encryptionAlias,enforceSSL:!0,blockPublicAccess:s3().BlockPublicAccess.BLOCK_ALL})}}exports.CrossRegionSupportConstruct=CrossRegionSupportConstruct;class CrossRegionSupportStack extends cdk().Stack{constructor(scope,id,props){super(scope,id,{stackName:generateStackName(props),env:{region:props.region,account:props.account},synthesizer:props.synthesizer});const crossRegionSupportConstruct=new CrossRegionSupportConstruct(this,"Default",{createKmsKey:props.createKmsKey,enableKeyRotation:props.enableKeyRotation});this.replicationBucket=crossRegionSupportConstruct.replicationBucket}}exports.CrossRegionSupportStack=CrossRegionSupportStack;function generateStackName(props){return cdk().Token.isUnresolved(props.pipelineStackName)?(0,unique_resource_name_1().makeUniqueResourceName)([`cross-region-support-${props.region}`],{maxLength:128,allowedSpecialCharacters:"-"}):`${props.pipelineStackName}-support-${props.region}`}
