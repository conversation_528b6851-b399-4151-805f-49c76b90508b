"use strict";Object.defineProperty(exports,"__esModule",{value:!0}),exports.RichAction=void 0;var core_1=()=>{var tmp=require("../../../core");return core_1=()=>tmp,tmp};class RichAction{constructor(action,pipeline){this.action=action,this.pipeline=pipeline,this.actionProperties=action.actionProperties}bind(scope,stage,options){return this.action.bind(scope,stage,options)}onStateChange(name,target,options){return this.action.onStateChange(name,target,options)}get isCrossRegion(){return!actionDimensionSameAsPipelineDimension(this.effectiveRegion,this.pipeline.env.region)}get isCrossAccount(){return!actionDimensionSameAsPipelineDimension(this.effectiveAccount,this.pipeline.env.account)}get resourceStack(){const actionResource=this.actionProperties.resource;if(!actionResource)return;const actionResourceStack=core_1().Stack.of(actionResource),actionResourceStackEnv={region:actionResourceStack.region,account:actionResourceStack.account};return sameEnv(actionResource.env,actionResourceStackEnv)?actionResourceStack:void 0}get effectiveRegion(){return this.action.actionProperties.resource?.env.region??this.action.actionProperties.region}get effectiveAccount(){return this.action.actionProperties.role?.env.account??this.action.actionProperties?.resource?.env.account??this.action.actionProperties.account}}exports.RichAction=RichAction;function actionDimensionSameAsPipelineDimension(actionDim,pipelineDim){return!actionDim||core_1().Token.isUnresolved(actionDim)?!0:core_1().Token.compareStrings(actionDim,pipelineDim)===core_1().TokenComparison.SAME}function sameEnv(env1,env2){return sameEnvDimension(env1.region,env2.region)&&sameEnvDimension(env1.account,env2.account)}function sameEnvDimension(dim1,dim2){return[core_1().TokenComparison.SAME,core_1().TokenComparison.BOTH_UNRESOLVED].includes(core_1().Token.compareStrings(dim1,dim2))}
