"use strict";var _a;Object.defineProperty(exports,"__esModule",{value:!0}),exports.Rule=void 0;var jsiiDeprecationWarnings=()=>{var tmp=require("../../.warnings.jsii.js");return jsiiDeprecationWarnings=()=>tmp,tmp};const JSII_RTTI_SYMBOL_1=Symbol.for("jsii.rtti");var validation_1=()=>{var tmp=require("./private/validation");return validation_1=()=>tmp,tmp};class Rule{constructor(props){try{jsiiDeprecationWarnings().aws_cdk_lib_aws_codepipeline_RuleProps(props)}catch(error){throw process.env.JSII_DEBUG!=="1"&&error.name==="DeprecationError"&&Error.captureStackTrace(error,Rule),error}this.ruleName=props.name,this._props=props,this.validate()}validate(){(0,validation_1().validateRuleName)(this.ruleName)}reference(){return`#{rule.${this.ruleName}}`}_render(){return{name:this.ruleName,region:this._props.region,roleArn:this._props.role?.roleArn,configuration:this._props.configuration,inputArtifacts:this._props.inputArtifacts?.map(artifact=>({name:artifact})),commands:this._props.commands,ruleTypeId:{provider:this._props.provider,version:this._props.version,category:"Rule",owner:"AWS"}}}}exports.Rule=Rule,_a=JSII_RTTI_SYMBOL_1,Rule[_a]={fqn:"aws-cdk-lib.aws_codepipeline.Rule",version:"2.201.0"};
