"use strict";var _a;Object.defineProperty(exports,"__esModule",{value:!0}),exports.Variable=void 0;var jsiiDeprecationWarnings=()=>{var tmp=require("../../.warnings.jsii.js");return jsiiDeprecationWarnings=()=>tmp,tmp};const JSII_RTTI_SYMBOL_1=Symbol.for("jsii.rtti");var validation_1=()=>{var tmp=require("./private/validation");return validation_1=()=>tmp,tmp},core_1=()=>{var tmp=require("../../core");return core_1=()=>tmp,tmp};class Variable{constructor(props){try{jsiiDeprecationWarnings().aws_cdk_lib_aws_codepipeline_VariableProps(props)}catch(error){throw process.env.JSII_DEBUG!=="1"&&error.name==="DeprecationError"&&Error.captureStackTrace(error,Variable),error}this.variableName=props.variableName,this.description=props.description,this.defaultValue=props.defaultValue,this.validate()}validate(){if((0,validation_1().validatePipelineVariableName)(this.variableName),this.defaultValue!==void 0&&!core_1().Token.isUnresolved(this.defaultValue)&&(this.defaultValue.length<1||this.defaultValue.length>1e3))throw new(core_1()).UnscopedValidationError(`Default value for variable '${this.variableName}' must be between 1 and 1000 characters long, got ${this.defaultValue.length}`);if(this.description!==void 0&&!core_1().Token.isUnresolved(this.description)&&this.description.length>200)throw new(core_1()).UnscopedValidationError(`Description for variable '${this.variableName}' must not be greater than 200 characters long, got ${this.description.length}`)}reference(){return`#{variables.${this.variableName}}`}_render(){return{defaultValue:this.defaultValue,description:this.description,name:this.variableName}}}exports.Variable=Variable,_a=JSII_RTTI_SYMBOL_1,Variable[_a]={fqn:"aws-cdk-lib.aws_codepipeline.Variable",version:"2.201.0"};
