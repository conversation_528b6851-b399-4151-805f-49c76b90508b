"use strict";var __decorate=exports&&exports.__decorate||function(decorators,target,key,desc){var c=arguments.length,r=c<3?target:desc===null?desc=Object.getOwnPropertyDescriptor(target,key):desc,d;if(typeof Reflect=="object"&&typeof Reflect.decorate=="function")r=Reflect.decorate(decorators,target,key,desc);else for(var i=decorators.length-1;i>=0;i--)(d=decorators[i])&&(r=(c<3?d(r):c>3?d(target,key,r):d(target,key))||r);return c>3&&r&&Object.defineProperty(target,key,r),r},_a;Object.defineProperty(exports,"__esModule",{value:!0}),exports.Pipeline=exports.ExecutionMode=exports.PipelineType=exports.RetryMode=exports.Result=void 0;var jsiiDeprecationWarnings=()=>{var tmp=require("../../.warnings.jsii.js");return jsiiDeprecationWarnings=()=>tmp,tmp};const JSII_RTTI_SYMBOL_1=Symbol.for("jsii.rtti");var action_1=()=>{var tmp=require("./action");return action_1=()=>tmp,tmp},codepipeline_generated_1=()=>{var tmp=require("./codepipeline.generated");return codepipeline_generated_1=()=>tmp,tmp},cross_region_support_stack_1=()=>{var tmp=require("./private/cross-region-support-stack");return cross_region_support_stack_1=()=>tmp,tmp},full_action_descriptor_1=()=>{var tmp=require("./private/full-action-descriptor");return full_action_descriptor_1=()=>tmp,tmp},rich_action_1=()=>{var tmp=require("./private/rich-action");return rich_action_1=()=>tmp,tmp},stage_1=()=>{var tmp=require("./private/stage");return stage_1=()=>tmp,tmp},validation_1=()=>{var tmp=require("./private/validation");return validation_1=()=>tmp,tmp},trigger_1=()=>{var tmp=require("./trigger");return trigger_1=()=>tmp,tmp},notifications=()=>{var tmp=require("../../aws-codestarnotifications");return notifications=()=>tmp,tmp},events=()=>{var tmp=require("../../aws-events");return events=()=>tmp,tmp},iam=()=>{var tmp=require("../../aws-iam");return iam=()=>tmp,tmp},kms=()=>{var tmp=require("../../aws-kms");return kms=()=>tmp,tmp},s3=()=>{var tmp=require("../../aws-s3");return s3=()=>tmp,tmp},core_1=()=>{var tmp=require("../../core");return core_1=()=>tmp,tmp},metadata_resource_1=()=>{var tmp=require("../../core/lib/metadata-resource");return metadata_resource_1=()=>tmp,tmp},prop_injectable_1=()=>{var tmp=require("../../core/lib/prop-injectable");return prop_injectable_1=()=>tmp,tmp},cxapi=()=>{var tmp=require("../../cx-api");return cxapi=()=>tmp,tmp},Result;(function(Result2){Result2.ROLLBACK="ROLLBACK",Result2.FAIL="FAIL",Result2.RETRY="RETRY",Result2.SKIP="SKIP"})(Result||(exports.Result=Result={}));var RetryMode;(function(RetryMode2){RetryMode2.ALL_ACTIONS="ALL_ACTIONS",RetryMode2.FAILED_ACTIONS="FAILED_ACTIONS"})(RetryMode||(exports.RetryMode=RetryMode={}));var PipelineType;(function(PipelineType2){PipelineType2.V1="V1",PipelineType2.V2="V2"})(PipelineType||(exports.PipelineType=PipelineType={}));var ExecutionMode;(function(ExecutionMode2){ExecutionMode2.QUEUED="QUEUED",ExecutionMode2.SUPERSEDED="SUPERSEDED",ExecutionMode2.PARALLEL="PARALLEL"})(ExecutionMode||(exports.ExecutionMode=ExecutionMode={}));class PipelineBase extends core_1().Resource{onEvent(id,options={}){const rule=new(events()).Rule(this,id,options);return rule.addTarget(options.target),rule.addEventPattern({source:["aws.codepipeline"],resources:[this.pipelineArn]}),rule}onStateChange(id,options={}){const rule=this.onEvent(id,options);return rule.addEventPattern({detailType:["CodePipeline Pipeline Execution State Change"]}),rule}bindAsNotificationRuleSource(_scope){return{sourceArn:this.pipelineArn}}notifyOn(id,target,options){return new(notifications()).NotificationRule(this,id,{...options,source:this,targets:[target]})}notifyOnExecutionStateChange(id,target,options){return this.notifyOn(id,target,{...options,events:[action_1().PipelineNotificationEvents.PIPELINE_EXECUTION_FAILED,action_1().PipelineNotificationEvents.PIPELINE_EXECUTION_CANCELED,action_1().PipelineNotificationEvents.PIPELINE_EXECUTION_STARTED,action_1().PipelineNotificationEvents.PIPELINE_EXECUTION_RESUMED,action_1().PipelineNotificationEvents.PIPELINE_EXECUTION_SUCCEEDED,action_1().PipelineNotificationEvents.PIPELINE_EXECUTION_SUPERSEDED]})}notifyOnAnyStageStateChange(id,target,options){return this.notifyOn(id,target,{...options,events:[action_1().PipelineNotificationEvents.STAGE_EXECUTION_CANCELED,action_1().PipelineNotificationEvents.STAGE_EXECUTION_FAILED,action_1().PipelineNotificationEvents.STAGE_EXECUTION_RESUMED,action_1().PipelineNotificationEvents.STAGE_EXECUTION_STARTED,action_1().PipelineNotificationEvents.STAGE_EXECUTION_SUCCEEDED]})}notifyOnAnyActionStateChange(id,target,options){return this.notifyOn(id,target,{...options,events:[action_1().PipelineNotificationEvents.ACTION_EXECUTION_CANCELED,action_1().PipelineNotificationEvents.ACTION_EXECUTION_FAILED,action_1().PipelineNotificationEvents.ACTION_EXECUTION_STARTED,action_1().PipelineNotificationEvents.ACTION_EXECUTION_SUCCEEDED]})}notifyOnAnyManualApprovalStateChange(id,target,options){return this.notifyOn(id,target,{...options,events:[action_1().PipelineNotificationEvents.MANUAL_APPROVAL_FAILED,action_1().PipelineNotificationEvents.MANUAL_APPROVAL_NEEDED,action_1().PipelineNotificationEvents.MANUAL_APPROVAL_SUCCEEDED]})}}let Pipeline=class Pipeline2 extends PipelineBase{static fromPipelineArn(scope,id,pipelineArn){class Import extends PipelineBase{constructor(){super(...arguments),this.pipelineName=core_1().Stack.of(scope).splitArn(pipelineArn,core_1().ArnFormat.SLASH_RESOURCE_NAME).resource,this.pipelineArn=pipelineArn}}return new Import(scope,id,{environmentFromArn:pipelineArn})}constructor(scope,id,props={}){super(scope,id,{physicalName:props.pipelineName}),this._stages=new Array,this._crossRegionSupport={},this._crossAccountSupport={},this.variables=new Array,this.triggers=new Array;try{jsiiDeprecationWarnings().aws_cdk_lib_aws_codepipeline_PipelineProps(props)}catch(error){throw process.env.JSII_DEBUG!=="1"&&error.name==="DeprecationError"&&Error.captureStackTrace(error,Pipeline2),error}if((0,metadata_resource_1().addConstructMetadata)(this,props),(0,validation_1().validateName)(this,"Pipeline",this.physicalName),props.artifactBucket&&props.crossRegionReplicationBuckets)throw new(core_1()).ValidationError("Only one of artifactBucket and crossRegionReplicationBuckets can be specified!",this);if(this.crossAccountKeys=props.crossAccountKeys??!core_1().FeatureFlags.of(this).isEnabled(cxapi().CODEPIPELINE_CROSS_ACCOUNT_KEYS_DEFAULT_VALUE_TO_FALSE),this.enableKeyRotation=props.enableKeyRotation,this.enableKeyRotation&&!this.crossAccountKeys)throw new(core_1()).ValidationError("Setting 'enableKeyRotation' to true also requires 'crossAccountKeys' to be enabled",this);this.reuseCrossRegionSupportStacks=props.reuseCrossRegionSupportStacks??!0,this.usePipelineRoleForActions=props.usePipelineRoleForActions??!1;let propsBucket=this.getArtifactBucketFromProps(props);if(!propsBucket){let encryptionKey;this.crossAccountKeys&&(encryptionKey=new(kms()).Key(this,"ArtifactsBucketEncryptionKey",{removalPolicy:core_1().RemovalPolicy.DESTROY,enableKeyRotation:this.enableKeyRotation}),new(kms()).Alias(this,"ArtifactsBucketEncryptionKeyAlias",{aliasName:this.generateNameForDefaultBucketKeyAlias(),targetKey:encryptionKey,removalPolicy:core_1().RemovalPolicy.DESTROY})),propsBucket=new(s3()).Bucket(this,"ArtifactsBucket",{bucketName:core_1().PhysicalName.GENERATE_IF_NEEDED,encryptionKey,encryption:encryptionKey?s3().BucketEncryption.KMS:s3().BucketEncryption.KMS_MANAGED,enforceSSL:!0,blockPublicAccess:new(s3()).BlockPublicAccess(s3().BlockPublicAccess.BLOCK_ALL),removalPolicy:core_1().RemovalPolicy.RETAIN})}this.artifactBucket=propsBucket;const isRemoveRootPrincipal=core_1().FeatureFlags.of(this).isEnabled(cxapi().PIPELINE_REDUCE_CROSS_ACCOUNT_ACTION_ROLE_TRUST_SCOPE);this.role=props.role||new(iam()).Role(this,"Role",{assumedBy:new(iam()).ServicePrincipal("codepipeline.amazonaws.com"),roleName:isRemoveRootPrincipal?core_1().PhysicalName.GENERATE_IF_NEEDED:void 0});const isDefaultV2=core_1().FeatureFlags.of(this).isEnabled(cxapi().CODEPIPELINE_DEFAULT_PIPELINE_TYPE_TO_V2);if(!isDefaultV2&&props.pipelineType===void 0&&core_1().Annotations.of(this).addWarningV2("@aws-cdk/aws-codepipeline:unspecifiedPipelineType","V1 pipeline type is implicitly selected when `pipelineType` is not set. If you want to use V2 type, set `PipelineType.V2`."),this.pipelineType=props.pipelineType??(isDefaultV2?PipelineType.V2:PipelineType.V1),props.executionMode&&[ExecutionMode.QUEUED,ExecutionMode.PARALLEL].includes(props.executionMode)&&this.pipelineType!==PipelineType.V2)throw new(core_1()).ValidationError(`${props.executionMode} execution mode can only be used with V2 pipelines, \`PipelineType.V2\` must be specified for \`pipelineType\``,this);this.codePipeline=new(codepipeline_generated_1()).CfnPipeline(this,"Resource",{artifactStore:core_1().Lazy.any({produce:()=>this.renderArtifactStoreProperty()}),artifactStores:core_1().Lazy.any({produce:()=>this.renderArtifactStoresProperty()}),stages:core_1().Lazy.any({produce:()=>this.renderStages()}),disableInboundStageTransitions:core_1().Lazy.any({produce:()=>this.renderDisabledTransitions()},{omitEmptyArray:!0}),roleArn:this.role.roleArn,restartExecutionOnUpdate:props&&props.restartExecutionOnUpdate,pipelineType:props.pipelineType??(isDefaultV2?PipelineType.V2:void 0),variables:core_1().Lazy.any({produce:()=>this.renderVariables()},{omitEmptyArray:!0}),triggers:core_1().Lazy.any({produce:()=>this.renderTriggers()},{omitEmptyArray:!0}),executionMode:props.executionMode,name:this.physicalName}),this.codePipeline.node.addDependency(this.role),this.artifactBucket.grantReadWrite(this.role),this.pipelineName=this.getResourceNameAttribute(this.codePipeline.ref),this.pipelineVersion=this.codePipeline.attrVersion,this.crossRegionBucketsPassed=!!props.crossRegionReplicationBuckets;for(const[region,replicationBucket]of Object.entries(props.crossRegionReplicationBuckets||{}))this._crossRegionSupport[region]={replicationBucket,stack:core_1().Stack.of(replicationBucket)};this.pipelineArn=core_1().Stack.of(this).formatArn({service:"codepipeline",resource:this.pipelineName});for(const stage of props.stages||[])this.addStage(stage);for(const variable of props.variables||[])this.addVariable(variable);for(const trigger of props.triggers||[])this.addTrigger(trigger);this.node.addValidation({validate:()=>this.validatePipeline()})}addStage(props){try{jsiiDeprecationWarnings().aws_cdk_lib_aws_codepipeline_StageOptions(props)}catch(error){throw process.env.JSII_DEBUG!=="1"&&error.name==="DeprecationError"&&Error.captureStackTrace(error,this.addStage),error}if(this._stages.find(s=>s.stageName===props.stageName))throw new(core_1()).ValidationError(`Stage with duplicate name '${props.stageName}' added to the Pipeline`,this);const stage=new(stage_1()).Stage(props,this),index=props.placement?this.calculateInsertIndexFromPlacement(props.placement):this.stageCount;return this._stages.splice(index,0,stage),stage}addToRolePolicy(statement){try{jsiiDeprecationWarnings().aws_cdk_lib_aws_iam_PolicyStatement(statement)}catch(error){throw process.env.JSII_DEBUG!=="1"&&error.name==="DeprecationError"&&Error.captureStackTrace(error,this.addToRolePolicy),error}this.role.addToPrincipalPolicy(statement)}addVariable(variable){try{jsiiDeprecationWarnings().aws_cdk_lib_aws_codepipeline_Variable(variable)}catch(error){throw process.env.JSII_DEBUG!=="1"&&error.name==="DeprecationError"&&Error.captureStackTrace(error,this.addVariable),error}if(this.variables.find(v=>v.variableName===variable.variableName))throw new(core_1()).ValidationError(`Variable with duplicate name '${variable.variableName}' added to the Pipeline`,this);return this.variables.push(variable),variable}addTrigger(props){try{jsiiDeprecationWarnings().aws_cdk_lib_aws_codepipeline_TriggerProps(props)}catch(error){throw process.env.JSII_DEBUG!=="1"&&error.name==="DeprecationError"&&Error.captureStackTrace(error,this.addTrigger),error}const trigger=new(trigger_1()).Trigger(props),actionName=props.gitConfiguration?.sourceAction.actionProperties.actionName;if(actionName!==void 0&&this.triggers.find(t=>t.sourceAction?.actionProperties.actionName===actionName))throw new(core_1()).ValidationError(`Trigger with duplicate source action '${actionName}' added to the Pipeline`,this);return this.triggers.push(trigger),trigger}get stageCount(){return this._stages.length}get stages(){return this._stages.slice()}stage(stageName){for(const stage of this._stages)if(stage.stageName===stageName)return stage;throw new(core_1()).ValidationError(`Pipeline does not contain a stage named '${stageName}'. Available stages: ${this._stages.map(s=>s.stageName).join(", ")}`,this)}get crossRegionSupport(){const ret={};return Object.keys(this._crossRegionSupport).forEach(key=>{ret[key]=this._crossRegionSupport[key]}),ret}_attachActionToPipeline(stage,action,actionScope){const richAction=new(rich_action_1()).RichAction(action,this),crossRegionInfo=this.ensureReplicationResourcesExistFor(richAction),actionRole=this.getRoleForAction(stage,richAction,actionScope);(0,validation_1().validateNamespaceName)(this,richAction.actionProperties.variablesNamespace);const actionConfig=richAction.bind(actionScope,stage,{role:actionRole||this.role,bucket:crossRegionInfo.artifactBucket});return new(full_action_descriptor_1()).FullActionDescriptor({action,actionConfig,actionRole,actionRegion:crossRegionInfo.region})}validatePipeline(){return[...this.validateSourceActionLocations(),...this.validateHasStages(),...this.validateStages(),...this.validateArtifacts(),...this.validateVariables(),...this.validateTriggers()]}ensureReplicationResourcesExistFor(action){if(!action.isCrossRegion)return{artifactBucket:this.artifactBucket};if(this.requireRegion(),action.actionProperties.category===action_1().ActionCategory.SOURCE)throw new(core_1()).ValidationError(`Source action '${action.actionProperties.actionName}' must be in the same region as the pipeline`,this);const crossRegionSupport=this.obtainCrossRegionSupportFor(action);return core_1().Stack.of(this).addDependency(crossRegionSupport.stack),crossRegionSupport.replicationBucket.grantReadWrite(this.role),{artifactBucket:crossRegionSupport.replicationBucket,region:action.effectiveRegion}}obtainCrossRegionSupportFor(action){const actionRegion=action.effectiveRegion;let crossRegionSupport=this._crossRegionSupport[actionRegion];if(!crossRegionSupport){const otherStack=action.resourceStack;crossRegionSupport=this.createSupportResourcesForRegion(otherStack,actionRegion),this._crossRegionSupport[actionRegion]=crossRegionSupport}return crossRegionSupport}createSupportResourcesForRegion(otherStack,actionRegion){if(otherStack){const id=`CrossRegionReplicationSupport-d823f1d8-a990-4e5c-be18-4ac698532e65-${actionRegion}`;let crossRegionSupportConstruct=otherStack.node.tryFindChild(id);return crossRegionSupportConstruct||(crossRegionSupportConstruct=new(cross_region_support_stack_1()).CrossRegionSupportConstruct(otherStack,id,{createKmsKey:this.crossAccountKeys,enableKeyRotation:this.enableKeyRotation})),{replicationBucket:crossRegionSupportConstruct.replicationBucket,stack:otherStack}}const pipelineStack=core_1().Stack.of(this),pipelineAccount=pipelineStack.account;if(core_1().Token.isUnresolved(pipelineAccount))throw new(core_1()).ValidationError("You need to specify an explicit account when using CodePipeline's cross-region support",this);const app=this.supportScope(),supportStackId=`cross-region-stack-${this.reuseCrossRegionSupportStacks?pipelineAccount:pipelineStack.stackName}:${actionRegion}`;let supportStack=app.node.tryFindChild(supportStackId);return supportStack||(supportStack=new(cross_region_support_stack_1()).CrossRegionSupportStack(app,supportStackId,{pipelineStackName:pipelineStack.stackName,region:actionRegion,account:pipelineAccount,synthesizer:this.getCrossRegionSupportSynthesizer(),createKmsKey:this.crossAccountKeys,enableKeyRotation:this.enableKeyRotation})),{stack:supportStack,replicationBucket:supportStack.replicationBucket}}getCrossRegionSupportSynthesizer(){if(this.stack.synthesizer instanceof core_1().DefaultStackSynthesizer)return new(core_1()).BootstraplessSynthesizer({deployRoleArn:this.stack.synthesizer.deployRoleArn,cloudFormationExecutionRoleArn:this.stack.synthesizer.cloudFormationExecutionRoleArn})}generateNameForDefaultBucketKeyAlias(){const prefix="alias/codepipeline-",maxResourceNameLength=256-prefix.length,uniqueId=core_1().FeatureFlags.of(this).isEnabled(cxapi().CODEPIPELINE_CROSS_ACCOUNT_KEY_ALIAS_STACK_SAFE_RESOURCE_NAME)?core_1().Names.uniqueResourceName(this,{separator:"-",maxLength:maxResourceNameLength,allowedSpecialCharacters:"/_-"}):core_1().Names.uniqueId(this).slice(-maxResourceNameLength);return prefix+uniqueId.toLowerCase()}getRoleForAction(stage,action,actionScope){const pipelineStack=core_1().Stack.of(this);let actionRole=this.getRoleFromActionPropsOrGenerateIfCrossAccount(stage,action);if(!actionRole&&this.isAwsOwned(action)){if(this.usePipelineRoleForActions)return;const roleProps=core_1().FeatureFlags.of(this).isEnabled(cxapi().PIPELINE_REDUCE_STAGE_ROLE_TRUST_SCOPE)?{assumedBy:new(iam()).ArnPrincipal(this.role.roleArn)}:{assumedBy:new(iam()).AccountPrincipal(pipelineStack.account)};actionRole=new(iam()).Role(actionScope,"CodePipelineActionRole",roleProps)}return actionRole?.grantAssumeRole(this.role)?.applyBefore(this.codePipeline),actionRole}getRoleFromActionPropsOrGenerateIfCrossAccount(stage,action){const pipelineStack=core_1().Stack.of(this);if(action.isCrossAccount&&!this.ensureReplicationResourcesExistFor(action).artifactBucket.encryptionKey)throw new(core_1()).ValidationError(`Artifact Bucket must have a KMS Key to add cross-account action '${action.actionProperties.actionName}' (pipeline account: '${renderEnvDimension(this.env.account)}', action account: '${renderEnvDimension(action.effectiveAccount)}'). Create Pipeline with 'crossAccountKeys: true' (or pass an existing Bucket with a key)`,this);if(action.actionProperties.role)if(this.isAwsOwned(action)){if(iam().Role.isRole(action.actionProperties.role)){const roleStack=core_1().Stack.of(action.actionProperties.role);pipelineStack.addDependency(roleStack)}return action.actionProperties.role}else throw new(core_1()).ValidationError(`Specifying a Role is not supported for actions with an owner different than 'AWS' - got '${action.actionProperties.owner}' (Action: '${action.actionProperties.actionName}' in Stage: '${stage.stageName}')`,this);const otherAccountStack=this.getOtherStackIfActionIsCrossAccount(action);if(!otherAccountStack)return;const isRemoveRootPrincipal=core_1().FeatureFlags.of(this).isEnabled(cxapi().PIPELINE_REDUCE_CROSS_ACCOUNT_ACTION_ROLE_TRUST_SCOPE),basePrincipal=new(iam()).AccountPrincipal(pipelineStack.account),roleProps={roleName:core_1().PhysicalName.GENERATE_IF_NEEDED,assumedBy:isRemoveRootPrincipal?basePrincipal.withConditions({ArnEquals:{"aws:PrincipalArn":this.role.roleArn}}):basePrincipal},ret=new(iam()).Role(otherAccountStack,`${core_1().Names.uniqueId(this)}-${stage.stageName}-${action.actionProperties.actionName}-ActionRole`,roleProps);return pipelineStack.addDependency(otherAccountStack),ret}getOtherStackIfActionIsCrossAccount(action){const targetAccount=action.actionProperties.resource?action.actionProperties.resource.env.account:action.actionProperties.account;if(targetAccount===void 0)return;if(core_1().Token.isUnresolved(targetAccount)){if(core_1().Token.isUnresolved(this.env.account))return;throw new(core_1()).ValidationError(`The 'account' property must be a concrete value (action: '${action.actionProperties.actionName}')`,this)}if(core_1().Token.isUnresolved(this.env.account))throw new(core_1()).ValidationError("Pipeline stack which uses cross-environment actions must have an explicitly set account",this);if(this.env.account===targetAccount)return;const candidateActionResourceStack=action.actionProperties.resource?core_1().Stack.of(action.actionProperties.resource):void 0;if(candidateActionResourceStack?.account===targetAccount)return this._crossAccountSupport[targetAccount]=candidateActionResourceStack,candidateActionResourceStack;let targetAccountStack=this._crossAccountSupport[targetAccount];if(!targetAccountStack){const stackId=`cross-account-support-stack-${targetAccount}`,app=this.supportScope();if(targetAccountStack=app.node.tryFindChild(stackId),!targetAccountStack){const actionRegion=action.actionProperties.resource?action.actionProperties.resource.env.region:action.actionProperties.region,pipelineStack=core_1().Stack.of(this),stackName=core_1().Token.isUnresolved(pipelineStack.stackName)?void 0:`${pipelineStack.stackName}-support-${targetAccount}`;targetAccountStack=new(core_1()).Stack(app,stackId,{stackName,env:{account:targetAccount,region:actionRegion??pipelineStack.region}})}this._crossAccountSupport[targetAccount]=targetAccountStack}return targetAccountStack}isAwsOwned(action){const owner=action.actionProperties.owner;return!owner||owner==="AWS"}getArtifactBucketFromProps(props){if(props.artifactBucket)return props.artifactBucket;if(props.crossRegionReplicationBuckets){const pipelineRegion=this.requireRegion();return props.crossRegionReplicationBuckets[pipelineRegion]}}calculateInsertIndexFromPlacement(placement){const providedPlacementProps=["rightBefore","justAfter","atIndex"].filter(prop=>placement[prop]!==void 0);if(providedPlacementProps.length>1)throw new(core_1()).ValidationError(`Error adding Stage to the Pipeline: you can only provide at most one placement property, but '${providedPlacementProps.join(", ")}' were given`,this);if(placement.rightBefore!==void 0){const targetIndex=this.findStageIndex(placement.rightBefore);if(targetIndex===-1)throw new(core_1()).ValidationError(`Error adding Stage to the Pipeline: the requested Stage to add it before, '${placement.rightBefore.stageName}', was not found`,this);return targetIndex}if(placement.justAfter!==void 0){const targetIndex=this.findStageIndex(placement.justAfter);if(targetIndex===-1)throw new(core_1()).ValidationError(`Error adding Stage to the Pipeline: the requested Stage to add it after, '${placement.justAfter.stageName}', was not found`,this);return targetIndex+1}return this.stageCount}findStageIndex(targetStage){return this._stages.findIndex(stage=>stage===targetStage)}validateSourceActionLocations(){const errors=new Array;let firstStage=!0;for(const stage of this._stages){const onlySourceActionsPermitted=firstStage;for(const action of stage.actionDescriptors)errors.push(...(0,validation_1().validateSourceAction)(onlySourceActionsPermitted,action.category,action.actionName,stage.stageName));firstStage=!1}return errors}validateHasStages(){return this.stageCount<2?["Pipeline must have at least two stages"]:[]}validateStages(){const ret=new Array;for(const stage of this._stages)ret.push(...stage.validate());return ret}validateArtifacts(){const ret=new Array,producers={},firstConsumers={};for(const[stageIndex,stage]of enumerate(this._stages))for(const action of stage.actionDescriptors){const actionLoc=new PipelineLocation(stageIndex,stage,action);for(const outputArtifact of action.outputs){const name=outputArtifact.artifactName;if(producers[name]){ret.push(`Both Actions '${producers[name].actionName}' and '${action.actionName}' are producting Artifact '${name}'. Every artifact can only be produced once.`);continue}producers[name]=actionLoc}for(const inputArtifact of action.inputs){const name=inputArtifact.artifactName;if(!name){ret.push(`Action '${action.actionName}' is using an unnamed input Artifact, which is not being produced in this pipeline`);continue}firstConsumers[name]=firstConsumers[name]?firstConsumers[name].first(actionLoc):actionLoc}}for(const[artifactName,consumerLoc]of Object.entries(firstConsumers)){const producerLoc=producers[artifactName];if(!producerLoc){ret.push(`Action '${consumerLoc.actionName}' is using input Artifact '${artifactName}', which is not being produced in this pipeline`);continue}consumerLoc.beforeOrEqual(producerLoc)&&ret.push(`${consumerLoc} is consuming input Artifact '${artifactName}' before it is being produced at ${producerLoc}`)}return ret}validateVariables(){const errors=[];return this.variables.length&&this.pipelineType!==PipelineType.V2&&errors.push("Pipeline variables can only be used with V2 pipelines, `PipelineType.V2` must be specified for `pipelineType`"),errors}validateTriggers(){const errors=[];return this.triggers.length&&this.pipelineType!==PipelineType.V2&&errors.push("Triggers can only be used with V2 pipelines, `PipelineType.V2` must be specified for `pipelineType`"),errors}renderArtifactStoresProperty(){if(!this.crossRegion)return;const primaryRegion=this.requireRegion();return this._crossRegionSupport[primaryRegion]={replicationBucket:this.artifactBucket,stack:core_1().Stack.of(this)},Object.entries(this._crossRegionSupport).map(([region,support])=>({region,artifactStore:this.renderArtifactStore(support.replicationBucket)}))}renderArtifactStoreProperty(){if(!this.crossRegion)return this.renderPrimaryArtifactStore()}renderPrimaryArtifactStore(){return this.renderArtifactStore(this.artifactBucket)}renderArtifactStore(bucket){let encryptionKey;const bucketKey=bucket.encryptionKey;return bucketKey&&(encryptionKey={type:"KMS",id:bucketKey.keyArn}),{type:"S3",location:bucket.bucketName,encryptionKey}}get crossRegion(){return this.crossRegionBucketsPassed?!0:this._stages.some(stage=>stage.actionDescriptors.some(action=>action.region!==void 0))}renderStages(){return this._stages.map(stage=>stage.render())}renderDisabledTransitions(){return this._stages.filter(stage=>!stage.transitionToEnabled).map(stage=>({reason:stage.transitionDisabledReason,stageName:stage.stageName}))}renderVariables(){return this.variables.map(variable=>variable._render())}renderTriggers(){return this.triggers.map(trigger=>trigger._render())}requireRegion(){const region=this.env.region;if(core_1().Token.isUnresolved(region))throw new(core_1()).ValidationError("Pipeline stack which uses cross-environment actions must have an explicitly set region",this);return region}supportScope(){const scope=core_1().Stage.of(this);if(!scope)throw new(core_1()).ValidationError("Pipeline stack which uses cross-environment actions must be part of a CDK App or Stage",this);return scope}};exports.Pipeline=Pipeline,_a=JSII_RTTI_SYMBOL_1,Pipeline[_a]={fqn:"aws-cdk-lib.aws_codepipeline.Pipeline",version:"2.201.0"},Pipeline.PROPERTY_INJECTION_ID="aws-cdk-lib.aws-codepipeline.Pipeline",__decorate([(0,metadata_resource_1().MethodMetadata)()],Pipeline.prototype,"addStage",null),__decorate([(0,metadata_resource_1().MethodMetadata)()],Pipeline.prototype,"addToRolePolicy",null),__decorate([(0,metadata_resource_1().MethodMetadata)()],Pipeline.prototype,"addVariable",null),__decorate([(0,metadata_resource_1().MethodMetadata)()],Pipeline.prototype,"addTrigger",null),__decorate([(0,metadata_resource_1().MethodMetadata)()],Pipeline.prototype,"stage",null),exports.Pipeline=Pipeline=__decorate([prop_injectable_1().propertyInjectable],Pipeline);function enumerate(xs){const ret=new Array;for(let i=0;i<xs.length;i++)ret.push([i,xs[i]]);return ret}class PipelineLocation{constructor(stageIndex,stage,action){this.stageIndex=stageIndex,this.stage=stage,this.action=action}get stageName(){return this.stage.stageName}get actionName(){return this.action.actionName}beforeOrEqual(rhs){return this.stageIndex!==rhs.stageIndex?rhs.stageIndex<rhs.stageIndex:this.action.runOrder<=rhs.action.runOrder}first(rhs){return this.beforeOrEqual(rhs)?this:rhs}toString(){return`Stage ${this.stageIndex+1} Action ${this.action.runOrder} ('${this.stageName}'/'${this.actionName}')`}}function renderEnvDimension(s){return core_1().Token.isUnresolved(s)?"(current)":s}
