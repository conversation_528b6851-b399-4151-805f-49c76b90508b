"use strict";Object.defineProperty(exports,"__esModule",{value:!0}),exports.Stage=void 0;var constructs_1=()=>{var tmp=require("constructs");return constructs_1=()=>tmp,tmp},validation=()=>{var tmp=require("./validation");return validation=()=>tmp,tmp},events=()=>{var tmp=require("../../../aws-events");return events=()=>tmp,tmp},cdk=()=>{var tmp=require("../../../core");return cdk=()=>tmp,tmp},core_1=()=>{var tmp=require("../../../core");return core_1=()=>tmp,tmp};class Stage{constructor(props,pipeline){this._actions=new Array,this.stageName=props.stageName,this.transitionToEnabled=props.transitionToEnabled??!0,this.transitionDisabledReason=props.transitionDisabledReason??"Transition disabled",this.beforeEntry=props.beforeEntry,this.onSuccess=props.onSuccess,this.onFailure=props.onFailure,this._pipeline=pipeline,this.scope=new(constructs_1()).Construct(pipeline,this.stageName),validation().validateName(this.scope,"Stage",props.stageName);for(const action of props.actions||[])this.addAction(action)}get actionDescriptors(){return this._actions.slice()}get actions(){return this._actions.map(actionDescriptor=>actionDescriptor.action)}get pipeline(){return this._pipeline}render(){for(const action of this._actions){const outputArtifacts=action.outputs,unnamedOutputs=outputArtifacts.filter(o=>!o.artifactName);for(const outputArtifact of outputArtifacts)if(!outputArtifact.artifactName){const unsanitizedArtifactName=`Artifact_${this.stageName}_${action.actionName}`+(unnamedOutputs.length===1?"":"_"+(unnamedOutputs.indexOf(outputArtifact)+1)),artifactName=sanitizeArtifactName(unsanitizedArtifactName);outputArtifact._setName(artifactName)}}return{name:this.stageName,actions:this._actions.map(action=>this.renderAction(action)),...this.beforeEntry&&{beforeEntry:this.renderBeforeEntry(this.beforeEntry)},...this.onSuccess&&{onSuccess:this.renderOnSuccess(this.onSuccess)},...this.onFailure&&{onFailure:this.renderOnFailure(this.onFailure)}}}addAction(action){const actionName=action.actionProperties.actionName;if(validation().validateName(this.scope,"Action",actionName),this._actions.find(a=>a.actionName===actionName))throw new(cdk()).ValidationError(`Stage ${this.stageName} already contains an action with name '${actionName}'`,this.scope);this._actions.push(this.attachActionToPipeline(action))}onStateChange(name,target,options){const rule=new(events()).Rule(this.scope,name,options);return rule.addTarget(target),rule.addEventPattern({detailType:["CodePipeline Stage Execution State Change"],source:["aws.codepipeline"],resources:[this.pipeline.pipelineArn],detail:{stage:[this.stageName]}}),rule}validate(){return[...this.validateHasActions(),...this.validateActions()]}validateHasActions(){return this._actions.length===0?[`Stage '${this.stageName}' must have at least one action`]:[]}validateActions(){const ret=new Array;for(const action of this.actionDescriptors)ret.push(...this.validateAction(action));return ret}validateAction(action){return validation().validateArtifactBounds("input",action.inputs,action.artifactBounds.minInputs,action.artifactBounds.maxInputs,action.category,action.provider).concat(validation().validateArtifactBounds("output",action.outputs,action.artifactBounds.minOutputs,action.artifactBounds.maxOutputs,action.category,action.provider))}attachActionToPipeline(action){let actionScope=constructs_1().Node.of(this.scope).tryFindChild(action.actionProperties.actionName);if(!actionScope){let id=action.actionProperties.actionName;core_1().Token.isUnresolved(id)&&(id=findUniqueConstructId(this.scope,action.actionProperties.provider)),actionScope=new(constructs_1()).Construct(this.scope,id)}return this._pipeline._attachActionToPipeline(this,action,actionScope)}renderAction(action){const outputArtifacts=cdk().Lazy.any({produce:()=>this.renderArtifacts(action.outputs)},{omitEmptyArray:!0}),inputArtifacts=cdk().Lazy.any({produce:()=>this.renderArtifacts(action.inputs)},{omitEmptyArray:!0});return{name:action.actionName,inputArtifacts,outputArtifacts,actionTypeId:{category:action.category.toString(),version:action.version,owner:action.owner,provider:action.provider},configuration:action.configuration,commands:action.commands,outputVariables:action.outputVariables,runOrder:action.runOrder,timeoutInMinutes:action.timeout?.toMinutes(),roleArn:action.role?action.role.roleArn:void 0,region:action.region,namespace:action.namespace}}renderArtifacts(artifacts){return artifacts.filter(a=>a.artifactName).map(a=>({name:a.artifactName,files:a.artifactFiles}))}renderBeforeEntry(conditions){return{conditions:this.getConditions(conditions)}}renderOnSuccess(conditions){return{conditions:this.getConditions(conditions)}}renderOnFailure(conditions){return{conditions:this.getConditions(conditions),result:conditions.result??void 0,retryConfiguration:conditions.retryMode?{retryMode:conditions.retryMode}:void 0}}getConditions(conditions){if(conditions?.conditions?.length)return conditions.conditions.map(({rules=[],result})=>({...rules.length&&{rules:rules.map(rule=>rule._render())},...result&&{result:result??void 0}}))}}exports.Stage=Stage;function sanitizeArtifactName(artifactName){return artifactName.replace(/[@.]/g,"")}function findUniqueConstructId(scope,prefix){let current=prefix,ctr=1;for(;constructs_1().Node.of(scope).tryFindChild(current)!==void 0;)current=`${prefix}${++ctr}`;return current}
