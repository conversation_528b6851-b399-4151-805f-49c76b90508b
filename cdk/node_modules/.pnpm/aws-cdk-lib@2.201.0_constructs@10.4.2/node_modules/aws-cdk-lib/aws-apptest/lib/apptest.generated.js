"use strict";var _a;Object.defineProperty(exports,"__esModule",{value:!0}),exports.CfnTestCase=void 0;var jsiiDeprecationWarnings=()=>{var tmp=require("../../.warnings.jsii.js");return jsiiDeprecationWarnings=()=>tmp,tmp};const JSII_RTTI_SYMBOL_1=Symbol.for("jsii.rtti");var cdk=()=>{var tmp=require("../../core");return cdk=()=>tmp,tmp},cfn_parse=()=>{var tmp=require("../../core/lib/helpers-internal");return cfn_parse=()=>tmp,tmp},cdk_errors=()=>{var tmp=require("../../core/lib/errors");return cdk_errors=()=>tmp,tmp};class CfnTestCase extends cdk().CfnResource{static _fromCloudFormation(scope,id,resourceAttributes,options){resourceAttributes=resourceAttributes||{};const resourceProperties=options.parser.parseValue(resourceAttributes.Properties),propsResult=CfnTestCasePropsFromCloudFormation(resourceProperties);if(cdk().isResolvableObject(propsResult.value))throw new(cdk_errors()).ValidationError("Unexpected IResolvable",scope);const ret=new CfnTestCase(scope,id,propsResult.value);for(const[propKey,propVal]of Object.entries(propsResult.extraProperties))ret.addPropertyOverride(propKey,propVal);return options.parser.handleAttributes(ret,resourceAttributes,id),ret}constructor(scope,id,props){super(scope,id,{type:CfnTestCase.CFN_RESOURCE_TYPE_NAME,properties:props});try{jsiiDeprecationWarnings().aws_cdk_lib_aws_apptest_CfnTestCaseProps(props)}catch(error){throw process.env.JSII_DEBUG!=="1"&&error.name==="DeprecationError"&&Error.captureStackTrace(error,CfnTestCase),error}cdk().requireProperty(props,"name",this),cdk().requireProperty(props,"steps",this),this.attrCreationTime=cdk().Token.asString(this.getAtt("CreationTime",cdk().ResolutionTypeHint.STRING)),this.attrLastUpdateTime=cdk().Token.asString(this.getAtt("LastUpdateTime",cdk().ResolutionTypeHint.STRING)),this.attrLatestVersion=this.getAtt("LatestVersion"),this.attrStatus=cdk().Token.asString(this.getAtt("Status",cdk().ResolutionTypeHint.STRING)),this.attrTestCaseArn=cdk().Token.asString(this.getAtt("TestCaseArn",cdk().ResolutionTypeHint.STRING)),this.attrTestCaseId=cdk().Token.asString(this.getAtt("TestCaseId",cdk().ResolutionTypeHint.STRING)),this.attrTestCaseVersion=this.getAtt("TestCaseVersion",cdk().ResolutionTypeHint.NUMBER),this.cdkTagManager=new(cdk()).TagManager(cdk().TagType.MAP,"AWS::AppTest::TestCase",void 0,{tagPropertyName:"tags"}),this.description=props.description,this.name=props.name,this.steps=props.steps,this.tags=props.tags}get cfnProperties(){return{tags:this.cdkTagManager.renderTags(this.tags),description:this.description,name:this.name,steps:this.steps}}inspect(inspector){try{jsiiDeprecationWarnings().aws_cdk_lib_TreeInspector(inspector)}catch(error){throw process.env.JSII_DEBUG!=="1"&&error.name==="DeprecationError"&&Error.captureStackTrace(error,this.inspect),error}inspector.addAttribute("aws:cdk:cloudformation:type",CfnTestCase.CFN_RESOURCE_TYPE_NAME),inspector.addAttribute("aws:cdk:cloudformation:props",this.cfnProperties)}renderProperties(props){return convertCfnTestCasePropsToCloudFormation(props)}}exports.CfnTestCase=CfnTestCase,_a=JSII_RTTI_SYMBOL_1,CfnTestCase[_a]={fqn:"aws-cdk-lib.aws_apptest.CfnTestCase",version:"2.201.0"},CfnTestCase.CFN_RESOURCE_TYPE_NAME="AWS::AppTest::TestCase";function CfnTestCaseM2ManagedActionPropertiesPropertyValidator(properties){if(!cdk().canInspect(properties))return cdk().VALIDATION_SUCCESS;const errors=new(cdk()).ValidationResults;return properties&&typeof properties=="object"&&!Array.isArray(properties)||errors.collect(new(cdk()).ValidationResult("Expected an object, but received: "+JSON.stringify(properties))),errors.collect(cdk().propertyValidator("forceStop",cdk().validateBoolean)(properties.forceStop)),errors.collect(cdk().propertyValidator("importDataSetLocation",cdk().validateString)(properties.importDataSetLocation)),errors.wrap('supplied properties not correct for "M2ManagedActionPropertiesProperty"')}function convertCfnTestCaseM2ManagedActionPropertiesPropertyToCloudFormation(properties){return cdk().canInspect(properties)?(CfnTestCaseM2ManagedActionPropertiesPropertyValidator(properties).assertSuccess(),{ForceStop:cdk().booleanToCloudFormation(properties.forceStop),ImportDataSetLocation:cdk().stringToCloudFormation(properties.importDataSetLocation)}):properties}function CfnTestCaseM2ManagedActionPropertiesPropertyFromCloudFormation(properties){if(cdk().isResolvableObject(properties))return new(cfn_parse()).FromCloudFormationResult(properties);if(properties=properties??{},!(properties&&typeof properties=="object"&&!Array.isArray(properties)))return new(cfn_parse()).FromCloudFormationResult(properties);const ret=new(cfn_parse()).FromCloudFormationPropertyObject;return ret.addPropertyResult("forceStop","ForceStop",properties.ForceStop!=null?cfn_parse().FromCloudFormation.getBoolean(properties.ForceStop):void 0),ret.addPropertyResult("importDataSetLocation","ImportDataSetLocation",properties.ImportDataSetLocation!=null?cfn_parse().FromCloudFormation.getString(properties.ImportDataSetLocation):void 0),ret.addUnrecognizedPropertiesAsExtra(properties),ret}function CfnTestCaseM2ManagedApplicationActionPropertyValidator(properties){if(!cdk().canInspect(properties))return cdk().VALIDATION_SUCCESS;const errors=new(cdk()).ValidationResults;return properties&&typeof properties=="object"&&!Array.isArray(properties)||errors.collect(new(cdk()).ValidationResult("Expected an object, but received: "+JSON.stringify(properties))),errors.collect(cdk().propertyValidator("actionType",cdk().requiredValidator)(properties.actionType)),errors.collect(cdk().propertyValidator("actionType",cdk().validateString)(properties.actionType)),errors.collect(cdk().propertyValidator("properties",CfnTestCaseM2ManagedActionPropertiesPropertyValidator)(properties.properties)),errors.collect(cdk().propertyValidator("resource",cdk().requiredValidator)(properties.resource)),errors.collect(cdk().propertyValidator("resource",cdk().validateString)(properties.resource)),errors.wrap('supplied properties not correct for "M2ManagedApplicationActionProperty"')}function convertCfnTestCaseM2ManagedApplicationActionPropertyToCloudFormation(properties){return cdk().canInspect(properties)?(CfnTestCaseM2ManagedApplicationActionPropertyValidator(properties).assertSuccess(),{ActionType:cdk().stringToCloudFormation(properties.actionType),Properties:convertCfnTestCaseM2ManagedActionPropertiesPropertyToCloudFormation(properties.properties),Resource:cdk().stringToCloudFormation(properties.resource)}):properties}function CfnTestCaseM2ManagedApplicationActionPropertyFromCloudFormation(properties){if(cdk().isResolvableObject(properties))return new(cfn_parse()).FromCloudFormationResult(properties);if(properties=properties??{},!(properties&&typeof properties=="object"&&!Array.isArray(properties)))return new(cfn_parse()).FromCloudFormationResult(properties);const ret=new(cfn_parse()).FromCloudFormationPropertyObject;return ret.addPropertyResult("actionType","ActionType",properties.ActionType!=null?cfn_parse().FromCloudFormation.getString(properties.ActionType):void 0),ret.addPropertyResult("properties","Properties",properties.Properties!=null?CfnTestCaseM2ManagedActionPropertiesPropertyFromCloudFormation(properties.Properties):void 0),ret.addPropertyResult("resource","Resource",properties.Resource!=null?cfn_parse().FromCloudFormation.getString(properties.Resource):void 0),ret.addUnrecognizedPropertiesAsExtra(properties),ret}function CfnTestCaseM2NonManagedApplicationActionPropertyValidator(properties){if(!cdk().canInspect(properties))return cdk().VALIDATION_SUCCESS;const errors=new(cdk()).ValidationResults;return properties&&typeof properties=="object"&&!Array.isArray(properties)||errors.collect(new(cdk()).ValidationResult("Expected an object, but received: "+JSON.stringify(properties))),errors.collect(cdk().propertyValidator("actionType",cdk().requiredValidator)(properties.actionType)),errors.collect(cdk().propertyValidator("actionType",cdk().validateString)(properties.actionType)),errors.collect(cdk().propertyValidator("resource",cdk().requiredValidator)(properties.resource)),errors.collect(cdk().propertyValidator("resource",cdk().validateString)(properties.resource)),errors.wrap('supplied properties not correct for "M2NonManagedApplicationActionProperty"')}function convertCfnTestCaseM2NonManagedApplicationActionPropertyToCloudFormation(properties){return cdk().canInspect(properties)?(CfnTestCaseM2NonManagedApplicationActionPropertyValidator(properties).assertSuccess(),{ActionType:cdk().stringToCloudFormation(properties.actionType),Resource:cdk().stringToCloudFormation(properties.resource)}):properties}function CfnTestCaseM2NonManagedApplicationActionPropertyFromCloudFormation(properties){if(cdk().isResolvableObject(properties))return new(cfn_parse()).FromCloudFormationResult(properties);if(properties=properties??{},!(properties&&typeof properties=="object"&&!Array.isArray(properties)))return new(cfn_parse()).FromCloudFormationResult(properties);const ret=new(cfn_parse()).FromCloudFormationPropertyObject;return ret.addPropertyResult("actionType","ActionType",properties.ActionType!=null?cfn_parse().FromCloudFormation.getString(properties.ActionType):void 0),ret.addPropertyResult("resource","Resource",properties.Resource!=null?cfn_parse().FromCloudFormation.getString(properties.Resource):void 0),ret.addUnrecognizedPropertiesAsExtra(properties),ret}function CfnTestCaseCloudFormationActionPropertyValidator(properties){if(!cdk().canInspect(properties))return cdk().VALIDATION_SUCCESS;const errors=new(cdk()).ValidationResults;return properties&&typeof properties=="object"&&!Array.isArray(properties)||errors.collect(new(cdk()).ValidationResult("Expected an object, but received: "+JSON.stringify(properties))),errors.collect(cdk().propertyValidator("actionType",cdk().validateString)(properties.actionType)),errors.collect(cdk().propertyValidator("resource",cdk().requiredValidator)(properties.resource)),errors.collect(cdk().propertyValidator("resource",cdk().validateString)(properties.resource)),errors.wrap('supplied properties not correct for "CloudFormationActionProperty"')}function convertCfnTestCaseCloudFormationActionPropertyToCloudFormation(properties){return cdk().canInspect(properties)?(CfnTestCaseCloudFormationActionPropertyValidator(properties).assertSuccess(),{ActionType:cdk().stringToCloudFormation(properties.actionType),Resource:cdk().stringToCloudFormation(properties.resource)}):properties}function CfnTestCaseCloudFormationActionPropertyFromCloudFormation(properties){if(cdk().isResolvableObject(properties))return new(cfn_parse()).FromCloudFormationResult(properties);if(properties=properties??{},!(properties&&typeof properties=="object"&&!Array.isArray(properties)))return new(cfn_parse()).FromCloudFormationResult(properties);const ret=new(cfn_parse()).FromCloudFormationPropertyObject;return ret.addPropertyResult("actionType","ActionType",properties.ActionType!=null?cfn_parse().FromCloudFormation.getString(properties.ActionType):void 0),ret.addPropertyResult("resource","Resource",properties.Resource!=null?cfn_parse().FromCloudFormation.getString(properties.Resource):void 0),ret.addUnrecognizedPropertiesAsExtra(properties),ret}function CfnTestCaseResourceActionPropertyValidator(properties){if(!cdk().canInspect(properties))return cdk().VALIDATION_SUCCESS;const errors=new(cdk()).ValidationResults;return properties&&typeof properties=="object"&&!Array.isArray(properties)||errors.collect(new(cdk()).ValidationResult("Expected an object, but received: "+JSON.stringify(properties))),errors.collect(cdk().propertyValidator("cloudFormationAction",CfnTestCaseCloudFormationActionPropertyValidator)(properties.cloudFormationAction)),errors.collect(cdk().propertyValidator("m2ManagedApplicationAction",CfnTestCaseM2ManagedApplicationActionPropertyValidator)(properties.m2ManagedApplicationAction)),errors.collect(cdk().propertyValidator("m2NonManagedApplicationAction",CfnTestCaseM2NonManagedApplicationActionPropertyValidator)(properties.m2NonManagedApplicationAction)),errors.wrap('supplied properties not correct for "ResourceActionProperty"')}function convertCfnTestCaseResourceActionPropertyToCloudFormation(properties){return cdk().canInspect(properties)?(CfnTestCaseResourceActionPropertyValidator(properties).assertSuccess(),{CloudFormationAction:convertCfnTestCaseCloudFormationActionPropertyToCloudFormation(properties.cloudFormationAction),M2ManagedApplicationAction:convertCfnTestCaseM2ManagedApplicationActionPropertyToCloudFormation(properties.m2ManagedApplicationAction),M2NonManagedApplicationAction:convertCfnTestCaseM2NonManagedApplicationActionPropertyToCloudFormation(properties.m2NonManagedApplicationAction)}):properties}function CfnTestCaseResourceActionPropertyFromCloudFormation(properties){if(cdk().isResolvableObject(properties))return new(cfn_parse()).FromCloudFormationResult(properties);if(properties=properties??{},!(properties&&typeof properties=="object"&&!Array.isArray(properties)))return new(cfn_parse()).FromCloudFormationResult(properties);const ret=new(cfn_parse()).FromCloudFormationPropertyObject;return ret.addPropertyResult("cloudFormationAction","CloudFormationAction",properties.CloudFormationAction!=null?CfnTestCaseCloudFormationActionPropertyFromCloudFormation(properties.CloudFormationAction):void 0),ret.addPropertyResult("m2ManagedApplicationAction","M2ManagedApplicationAction",properties.M2ManagedApplicationAction!=null?CfnTestCaseM2ManagedApplicationActionPropertyFromCloudFormation(properties.M2ManagedApplicationAction):void 0),ret.addPropertyResult("m2NonManagedApplicationAction","M2NonManagedApplicationAction",properties.M2NonManagedApplicationAction!=null?CfnTestCaseM2NonManagedApplicationActionPropertyFromCloudFormation(properties.M2NonManagedApplicationAction):void 0),ret.addUnrecognizedPropertiesAsExtra(properties),ret}function CfnTestCaseBatchPropertyValidator(properties){if(!cdk().canInspect(properties))return cdk().VALIDATION_SUCCESS;const errors=new(cdk()).ValidationResults;return properties&&typeof properties=="object"&&!Array.isArray(properties)||errors.collect(new(cdk()).ValidationResult("Expected an object, but received: "+JSON.stringify(properties))),errors.collect(cdk().propertyValidator("batchJobName",cdk().requiredValidator)(properties.batchJobName)),errors.collect(cdk().propertyValidator("batchJobName",cdk().validateString)(properties.batchJobName)),errors.collect(cdk().propertyValidator("batchJobParameters",cdk().hashValidator(cdk().validateString))(properties.batchJobParameters)),errors.collect(cdk().propertyValidator("exportDataSetNames",cdk().listValidator(cdk().validateString))(properties.exportDataSetNames)),errors.wrap('supplied properties not correct for "BatchProperty"')}function convertCfnTestCaseBatchPropertyToCloudFormation(properties){return cdk().canInspect(properties)?(CfnTestCaseBatchPropertyValidator(properties).assertSuccess(),{BatchJobName:cdk().stringToCloudFormation(properties.batchJobName),BatchJobParameters:cdk().hashMapper(cdk().stringToCloudFormation)(properties.batchJobParameters),ExportDataSetNames:cdk().listMapper(cdk().stringToCloudFormation)(properties.exportDataSetNames)}):properties}function CfnTestCaseBatchPropertyFromCloudFormation(properties){if(cdk().isResolvableObject(properties))return new(cfn_parse()).FromCloudFormationResult(properties);if(properties=properties??{},!(properties&&typeof properties=="object"&&!Array.isArray(properties)))return new(cfn_parse()).FromCloudFormationResult(properties);const ret=new(cfn_parse()).FromCloudFormationPropertyObject;return ret.addPropertyResult("batchJobName","BatchJobName",properties.BatchJobName!=null?cfn_parse().FromCloudFormation.getString(properties.BatchJobName):void 0),ret.addPropertyResult("batchJobParameters","BatchJobParameters",properties.BatchJobParameters!=null?cfn_parse().FromCloudFormation.getMap(cfn_parse().FromCloudFormation.getString)(properties.BatchJobParameters):void 0),ret.addPropertyResult("exportDataSetNames","ExportDataSetNames",properties.ExportDataSetNames!=null?cfn_parse().FromCloudFormation.getArray(cfn_parse().FromCloudFormation.getString)(properties.ExportDataSetNames):void 0),ret.addUnrecognizedPropertiesAsExtra(properties),ret}function CfnTestCaseScriptPropertyValidator(properties){if(!cdk().canInspect(properties))return cdk().VALIDATION_SUCCESS;const errors=new(cdk()).ValidationResults;return properties&&typeof properties=="object"&&!Array.isArray(properties)||errors.collect(new(cdk()).ValidationResult("Expected an object, but received: "+JSON.stringify(properties))),errors.collect(cdk().propertyValidator("scriptLocation",cdk().requiredValidator)(properties.scriptLocation)),errors.collect(cdk().propertyValidator("scriptLocation",cdk().validateString)(properties.scriptLocation)),errors.collect(cdk().propertyValidator("type",cdk().requiredValidator)(properties.type)),errors.collect(cdk().propertyValidator("type",cdk().validateString)(properties.type)),errors.wrap('supplied properties not correct for "ScriptProperty"')}function convertCfnTestCaseScriptPropertyToCloudFormation(properties){return cdk().canInspect(properties)?(CfnTestCaseScriptPropertyValidator(properties).assertSuccess(),{ScriptLocation:cdk().stringToCloudFormation(properties.scriptLocation),Type:cdk().stringToCloudFormation(properties.type)}):properties}function CfnTestCaseScriptPropertyFromCloudFormation(properties){if(cdk().isResolvableObject(properties))return new(cfn_parse()).FromCloudFormationResult(properties);if(properties=properties??{},!(properties&&typeof properties=="object"&&!Array.isArray(properties)))return new(cfn_parse()).FromCloudFormationResult(properties);const ret=new(cfn_parse()).FromCloudFormationPropertyObject;return ret.addPropertyResult("scriptLocation","ScriptLocation",properties.ScriptLocation!=null?cfn_parse().FromCloudFormation.getString(properties.ScriptLocation):void 0),ret.addPropertyResult("type","Type",properties.Type!=null?cfn_parse().FromCloudFormation.getString(properties.Type):void 0),ret.addUnrecognizedPropertiesAsExtra(properties),ret}function CfnTestCaseTN3270PropertyValidator(properties){if(!cdk().canInspect(properties))return cdk().VALIDATION_SUCCESS;const errors=new(cdk()).ValidationResults;return properties&&typeof properties=="object"&&!Array.isArray(properties)||errors.collect(new(cdk()).ValidationResult("Expected an object, but received: "+JSON.stringify(properties))),errors.collect(cdk().propertyValidator("exportDataSetNames",cdk().listValidator(cdk().validateString))(properties.exportDataSetNames)),errors.collect(cdk().propertyValidator("script",cdk().requiredValidator)(properties.script)),errors.collect(cdk().propertyValidator("script",CfnTestCaseScriptPropertyValidator)(properties.script)),errors.wrap('supplied properties not correct for "TN3270Property"')}function convertCfnTestCaseTN3270PropertyToCloudFormation(properties){return cdk().canInspect(properties)?(CfnTestCaseTN3270PropertyValidator(properties).assertSuccess(),{ExportDataSetNames:cdk().listMapper(cdk().stringToCloudFormation)(properties.exportDataSetNames),Script:convertCfnTestCaseScriptPropertyToCloudFormation(properties.script)}):properties}function CfnTestCaseTN3270PropertyFromCloudFormation(properties){if(cdk().isResolvableObject(properties))return new(cfn_parse()).FromCloudFormationResult(properties);if(properties=properties??{},!(properties&&typeof properties=="object"&&!Array.isArray(properties)))return new(cfn_parse()).FromCloudFormationResult(properties);const ret=new(cfn_parse()).FromCloudFormationPropertyObject;return ret.addPropertyResult("exportDataSetNames","ExportDataSetNames",properties.ExportDataSetNames!=null?cfn_parse().FromCloudFormation.getArray(cfn_parse().FromCloudFormation.getString)(properties.ExportDataSetNames):void 0),ret.addPropertyResult("script","Script",properties.Script!=null?CfnTestCaseScriptPropertyFromCloudFormation(properties.Script):void 0),ret.addUnrecognizedPropertiesAsExtra(properties),ret}function CfnTestCaseMainframeActionTypePropertyValidator(properties){if(!cdk().canInspect(properties))return cdk().VALIDATION_SUCCESS;const errors=new(cdk()).ValidationResults;return properties&&typeof properties=="object"&&!Array.isArray(properties)||errors.collect(new(cdk()).ValidationResult("Expected an object, but received: "+JSON.stringify(properties))),errors.collect(cdk().propertyValidator("batch",CfnTestCaseBatchPropertyValidator)(properties.batch)),errors.collect(cdk().propertyValidator("tn3270",CfnTestCaseTN3270PropertyValidator)(properties.tn3270)),errors.wrap('supplied properties not correct for "MainframeActionTypeProperty"')}function convertCfnTestCaseMainframeActionTypePropertyToCloudFormation(properties){return cdk().canInspect(properties)?(CfnTestCaseMainframeActionTypePropertyValidator(properties).assertSuccess(),{Batch:convertCfnTestCaseBatchPropertyToCloudFormation(properties.batch),Tn3270:convertCfnTestCaseTN3270PropertyToCloudFormation(properties.tn3270)}):properties}function CfnTestCaseMainframeActionTypePropertyFromCloudFormation(properties){if(cdk().isResolvableObject(properties))return new(cfn_parse()).FromCloudFormationResult(properties);if(properties=properties??{},!(properties&&typeof properties=="object"&&!Array.isArray(properties)))return new(cfn_parse()).FromCloudFormationResult(properties);const ret=new(cfn_parse()).FromCloudFormationPropertyObject;return ret.addPropertyResult("batch","Batch",properties.Batch!=null?CfnTestCaseBatchPropertyFromCloudFormation(properties.Batch):void 0),ret.addPropertyResult("tn3270","Tn3270",properties.Tn3270!=null?CfnTestCaseTN3270PropertyFromCloudFormation(properties.Tn3270):void 0),ret.addUnrecognizedPropertiesAsExtra(properties),ret}function CfnTestCaseMainframeActionPropertiesPropertyValidator(properties){if(!cdk().canInspect(properties))return cdk().VALIDATION_SUCCESS;const errors=new(cdk()).ValidationResults;return properties&&typeof properties=="object"&&!Array.isArray(properties)||errors.collect(new(cdk()).ValidationResult("Expected an object, but received: "+JSON.stringify(properties))),errors.collect(cdk().propertyValidator("dmsTaskArn",cdk().validateString)(properties.dmsTaskArn)),errors.wrap('supplied properties not correct for "MainframeActionPropertiesProperty"')}function convertCfnTestCaseMainframeActionPropertiesPropertyToCloudFormation(properties){return cdk().canInspect(properties)?(CfnTestCaseMainframeActionPropertiesPropertyValidator(properties).assertSuccess(),{DmsTaskArn:cdk().stringToCloudFormation(properties.dmsTaskArn)}):properties}function CfnTestCaseMainframeActionPropertiesPropertyFromCloudFormation(properties){if(cdk().isResolvableObject(properties))return new(cfn_parse()).FromCloudFormationResult(properties);if(properties=properties??{},!(properties&&typeof properties=="object"&&!Array.isArray(properties)))return new(cfn_parse()).FromCloudFormationResult(properties);const ret=new(cfn_parse()).FromCloudFormationPropertyObject;return ret.addPropertyResult("dmsTaskArn","DmsTaskArn",properties.DmsTaskArn!=null?cfn_parse().FromCloudFormation.getString(properties.DmsTaskArn):void 0),ret.addUnrecognizedPropertiesAsExtra(properties),ret}function CfnTestCaseMainframeActionPropertyValidator(properties){if(!cdk().canInspect(properties))return cdk().VALIDATION_SUCCESS;const errors=new(cdk()).ValidationResults;return properties&&typeof properties=="object"&&!Array.isArray(properties)||errors.collect(new(cdk()).ValidationResult("Expected an object, but received: "+JSON.stringify(properties))),errors.collect(cdk().propertyValidator("actionType",cdk().requiredValidator)(properties.actionType)),errors.collect(cdk().propertyValidator("actionType",CfnTestCaseMainframeActionTypePropertyValidator)(properties.actionType)),errors.collect(cdk().propertyValidator("properties",CfnTestCaseMainframeActionPropertiesPropertyValidator)(properties.properties)),errors.collect(cdk().propertyValidator("resource",cdk().requiredValidator)(properties.resource)),errors.collect(cdk().propertyValidator("resource",cdk().validateString)(properties.resource)),errors.wrap('supplied properties not correct for "MainframeActionProperty"')}function convertCfnTestCaseMainframeActionPropertyToCloudFormation(properties){return cdk().canInspect(properties)?(CfnTestCaseMainframeActionPropertyValidator(properties).assertSuccess(),{ActionType:convertCfnTestCaseMainframeActionTypePropertyToCloudFormation(properties.actionType),Properties:convertCfnTestCaseMainframeActionPropertiesPropertyToCloudFormation(properties.properties),Resource:cdk().stringToCloudFormation(properties.resource)}):properties}function CfnTestCaseMainframeActionPropertyFromCloudFormation(properties){if(cdk().isResolvableObject(properties))return new(cfn_parse()).FromCloudFormationResult(properties);if(properties=properties??{},!(properties&&typeof properties=="object"&&!Array.isArray(properties)))return new(cfn_parse()).FromCloudFormationResult(properties);const ret=new(cfn_parse()).FromCloudFormationPropertyObject;return ret.addPropertyResult("actionType","ActionType",properties.ActionType!=null?CfnTestCaseMainframeActionTypePropertyFromCloudFormation(properties.ActionType):void 0),ret.addPropertyResult("properties","Properties",properties.Properties!=null?CfnTestCaseMainframeActionPropertiesPropertyFromCloudFormation(properties.Properties):void 0),ret.addPropertyResult("resource","Resource",properties.Resource!=null?cfn_parse().FromCloudFormation.getString(properties.Resource):void 0),ret.addUnrecognizedPropertiesAsExtra(properties),ret}function CfnTestCaseDataSetPropertyValidator(properties){if(!cdk().canInspect(properties))return cdk().VALIDATION_SUCCESS;const errors=new(cdk()).ValidationResults;return properties&&typeof properties=="object"&&!Array.isArray(properties)||errors.collect(new(cdk()).ValidationResult("Expected an object, but received: "+JSON.stringify(properties))),errors.collect(cdk().propertyValidator("ccsid",cdk().requiredValidator)(properties.ccsid)),errors.collect(cdk().propertyValidator("ccsid",cdk().validateString)(properties.ccsid)),errors.collect(cdk().propertyValidator("format",cdk().requiredValidator)(properties.format)),errors.collect(cdk().propertyValidator("format",cdk().validateString)(properties.format)),errors.collect(cdk().propertyValidator("length",cdk().requiredValidator)(properties.length)),errors.collect(cdk().propertyValidator("length",cdk().validateNumber)(properties.length)),errors.collect(cdk().propertyValidator("name",cdk().requiredValidator)(properties.name)),errors.collect(cdk().propertyValidator("name",cdk().validateString)(properties.name)),errors.collect(cdk().propertyValidator("type",cdk().requiredValidator)(properties.type)),errors.collect(cdk().propertyValidator("type",cdk().validateString)(properties.type)),errors.wrap('supplied properties not correct for "DataSetProperty"')}function convertCfnTestCaseDataSetPropertyToCloudFormation(properties){return cdk().canInspect(properties)?(CfnTestCaseDataSetPropertyValidator(properties).assertSuccess(),{Ccsid:cdk().stringToCloudFormation(properties.ccsid),Format:cdk().stringToCloudFormation(properties.format),Length:cdk().numberToCloudFormation(properties.length),Name:cdk().stringToCloudFormation(properties.name),Type:cdk().stringToCloudFormation(properties.type)}):properties}function CfnTestCaseDataSetPropertyFromCloudFormation(properties){if(cdk().isResolvableObject(properties))return new(cfn_parse()).FromCloudFormationResult(properties);if(properties=properties??{},!(properties&&typeof properties=="object"&&!Array.isArray(properties)))return new(cfn_parse()).FromCloudFormationResult(properties);const ret=new(cfn_parse()).FromCloudFormationPropertyObject;return ret.addPropertyResult("ccsid","Ccsid",properties.Ccsid!=null?cfn_parse().FromCloudFormation.getString(properties.Ccsid):void 0),ret.addPropertyResult("format","Format",properties.Format!=null?cfn_parse().FromCloudFormation.getString(properties.Format):void 0),ret.addPropertyResult("length","Length",properties.Length!=null?cfn_parse().FromCloudFormation.getNumber(properties.Length):void 0),ret.addPropertyResult("name","Name",properties.Name!=null?cfn_parse().FromCloudFormation.getString(properties.Name):void 0),ret.addPropertyResult("type","Type",properties.Type!=null?cfn_parse().FromCloudFormation.getString(properties.Type):void 0),ret.addUnrecognizedPropertiesAsExtra(properties),ret}function CfnTestCaseSourceDatabaseMetadataPropertyValidator(properties){if(!cdk().canInspect(properties))return cdk().VALIDATION_SUCCESS;const errors=new(cdk()).ValidationResults;return properties&&typeof properties=="object"&&!Array.isArray(properties)||errors.collect(new(cdk()).ValidationResult("Expected an object, but received: "+JSON.stringify(properties))),errors.collect(cdk().propertyValidator("captureTool",cdk().requiredValidator)(properties.captureTool)),errors.collect(cdk().propertyValidator("captureTool",cdk().validateString)(properties.captureTool)),errors.collect(cdk().propertyValidator("type",cdk().requiredValidator)(properties.type)),errors.collect(cdk().propertyValidator("type",cdk().validateString)(properties.type)),errors.wrap('supplied properties not correct for "SourceDatabaseMetadataProperty"')}function convertCfnTestCaseSourceDatabaseMetadataPropertyToCloudFormation(properties){return cdk().canInspect(properties)?(CfnTestCaseSourceDatabaseMetadataPropertyValidator(properties).assertSuccess(),{CaptureTool:cdk().stringToCloudFormation(properties.captureTool),Type:cdk().stringToCloudFormation(properties.type)}):properties}function CfnTestCaseSourceDatabaseMetadataPropertyFromCloudFormation(properties){if(cdk().isResolvableObject(properties))return new(cfn_parse()).FromCloudFormationResult(properties);if(properties=properties??{},!(properties&&typeof properties=="object"&&!Array.isArray(properties)))return new(cfn_parse()).FromCloudFormationResult(properties);const ret=new(cfn_parse()).FromCloudFormationPropertyObject;return ret.addPropertyResult("captureTool","CaptureTool",properties.CaptureTool!=null?cfn_parse().FromCloudFormation.getString(properties.CaptureTool):void 0),ret.addPropertyResult("type","Type",properties.Type!=null?cfn_parse().FromCloudFormation.getString(properties.Type):void 0),ret.addUnrecognizedPropertiesAsExtra(properties),ret}function CfnTestCaseTargetDatabaseMetadataPropertyValidator(properties){if(!cdk().canInspect(properties))return cdk().VALIDATION_SUCCESS;const errors=new(cdk()).ValidationResults;return properties&&typeof properties=="object"&&!Array.isArray(properties)||errors.collect(new(cdk()).ValidationResult("Expected an object, but received: "+JSON.stringify(properties))),errors.collect(cdk().propertyValidator("captureTool",cdk().requiredValidator)(properties.captureTool)),errors.collect(cdk().propertyValidator("captureTool",cdk().validateString)(properties.captureTool)),errors.collect(cdk().propertyValidator("type",cdk().requiredValidator)(properties.type)),errors.collect(cdk().propertyValidator("type",cdk().validateString)(properties.type)),errors.wrap('supplied properties not correct for "TargetDatabaseMetadataProperty"')}function convertCfnTestCaseTargetDatabaseMetadataPropertyToCloudFormation(properties){return cdk().canInspect(properties)?(CfnTestCaseTargetDatabaseMetadataPropertyValidator(properties).assertSuccess(),{CaptureTool:cdk().stringToCloudFormation(properties.captureTool),Type:cdk().stringToCloudFormation(properties.type)}):properties}function CfnTestCaseTargetDatabaseMetadataPropertyFromCloudFormation(properties){if(cdk().isResolvableObject(properties))return new(cfn_parse()).FromCloudFormationResult(properties);if(properties=properties??{},!(properties&&typeof properties=="object"&&!Array.isArray(properties)))return new(cfn_parse()).FromCloudFormationResult(properties);const ret=new(cfn_parse()).FromCloudFormationPropertyObject;return ret.addPropertyResult("captureTool","CaptureTool",properties.CaptureTool!=null?cfn_parse().FromCloudFormation.getString(properties.CaptureTool):void 0),ret.addPropertyResult("type","Type",properties.Type!=null?cfn_parse().FromCloudFormation.getString(properties.Type):void 0),ret.addUnrecognizedPropertiesAsExtra(properties),ret}function CfnTestCaseDatabaseCDCPropertyValidator(properties){if(!cdk().canInspect(properties))return cdk().VALIDATION_SUCCESS;const errors=new(cdk()).ValidationResults;return properties&&typeof properties=="object"&&!Array.isArray(properties)||errors.collect(new(cdk()).ValidationResult("Expected an object, but received: "+JSON.stringify(properties))),errors.collect(cdk().propertyValidator("sourceMetadata",cdk().requiredValidator)(properties.sourceMetadata)),errors.collect(cdk().propertyValidator("sourceMetadata",CfnTestCaseSourceDatabaseMetadataPropertyValidator)(properties.sourceMetadata)),errors.collect(cdk().propertyValidator("targetMetadata",cdk().requiredValidator)(properties.targetMetadata)),errors.collect(cdk().propertyValidator("targetMetadata",CfnTestCaseTargetDatabaseMetadataPropertyValidator)(properties.targetMetadata)),errors.wrap('supplied properties not correct for "DatabaseCDCProperty"')}function convertCfnTestCaseDatabaseCDCPropertyToCloudFormation(properties){return cdk().canInspect(properties)?(CfnTestCaseDatabaseCDCPropertyValidator(properties).assertSuccess(),{SourceMetadata:convertCfnTestCaseSourceDatabaseMetadataPropertyToCloudFormation(properties.sourceMetadata),TargetMetadata:convertCfnTestCaseTargetDatabaseMetadataPropertyToCloudFormation(properties.targetMetadata)}):properties}function CfnTestCaseDatabaseCDCPropertyFromCloudFormation(properties){if(cdk().isResolvableObject(properties))return new(cfn_parse()).FromCloudFormationResult(properties);if(properties=properties??{},!(properties&&typeof properties=="object"&&!Array.isArray(properties)))return new(cfn_parse()).FromCloudFormationResult(properties);const ret=new(cfn_parse()).FromCloudFormationPropertyObject;return ret.addPropertyResult("sourceMetadata","SourceMetadata",properties.SourceMetadata!=null?CfnTestCaseSourceDatabaseMetadataPropertyFromCloudFormation(properties.SourceMetadata):void 0),ret.addPropertyResult("targetMetadata","TargetMetadata",properties.TargetMetadata!=null?CfnTestCaseTargetDatabaseMetadataPropertyFromCloudFormation(properties.TargetMetadata):void 0),ret.addUnrecognizedPropertiesAsExtra(properties),ret}function CfnTestCaseFileMetadataPropertyValidator(properties){if(!cdk().canInspect(properties))return cdk().VALIDATION_SUCCESS;const errors=new(cdk()).ValidationResults;return properties&&typeof properties=="object"&&!Array.isArray(properties)||errors.collect(new(cdk()).ValidationResult("Expected an object, but received: "+JSON.stringify(properties))),errors.collect(cdk().propertyValidator("dataSets",cdk().listValidator(CfnTestCaseDataSetPropertyValidator))(properties.dataSets)),errors.collect(cdk().propertyValidator("databaseCdc",CfnTestCaseDatabaseCDCPropertyValidator)(properties.databaseCdc)),errors.wrap('supplied properties not correct for "FileMetadataProperty"')}function convertCfnTestCaseFileMetadataPropertyToCloudFormation(properties){return cdk().canInspect(properties)?(CfnTestCaseFileMetadataPropertyValidator(properties).assertSuccess(),{DataSets:cdk().listMapper(convertCfnTestCaseDataSetPropertyToCloudFormation)(properties.dataSets),DatabaseCDC:convertCfnTestCaseDatabaseCDCPropertyToCloudFormation(properties.databaseCdc)}):properties}function CfnTestCaseFileMetadataPropertyFromCloudFormation(properties){if(cdk().isResolvableObject(properties))return new(cfn_parse()).FromCloudFormationResult(properties);if(properties=properties??{},!(properties&&typeof properties=="object"&&!Array.isArray(properties)))return new(cfn_parse()).FromCloudFormationResult(properties);const ret=new(cfn_parse()).FromCloudFormationPropertyObject;return ret.addPropertyResult("databaseCdc","DatabaseCDC",properties.DatabaseCDC!=null?CfnTestCaseDatabaseCDCPropertyFromCloudFormation(properties.DatabaseCDC):void 0),ret.addPropertyResult("dataSets","DataSets",properties.DataSets!=null?cfn_parse().FromCloudFormation.getArray(CfnTestCaseDataSetPropertyFromCloudFormation)(properties.DataSets):void 0),ret.addUnrecognizedPropertiesAsExtra(properties),ret}function CfnTestCaseInputFilePropertyValidator(properties){if(!cdk().canInspect(properties))return cdk().VALIDATION_SUCCESS;const errors=new(cdk()).ValidationResults;return properties&&typeof properties=="object"&&!Array.isArray(properties)||errors.collect(new(cdk()).ValidationResult("Expected an object, but received: "+JSON.stringify(properties))),errors.collect(cdk().propertyValidator("fileMetadata",cdk().requiredValidator)(properties.fileMetadata)),errors.collect(cdk().propertyValidator("fileMetadata",CfnTestCaseFileMetadataPropertyValidator)(properties.fileMetadata)),errors.collect(cdk().propertyValidator("sourceLocation",cdk().requiredValidator)(properties.sourceLocation)),errors.collect(cdk().propertyValidator("sourceLocation",cdk().validateString)(properties.sourceLocation)),errors.collect(cdk().propertyValidator("targetLocation",cdk().requiredValidator)(properties.targetLocation)),errors.collect(cdk().propertyValidator("targetLocation",cdk().validateString)(properties.targetLocation)),errors.wrap('supplied properties not correct for "InputFileProperty"')}function convertCfnTestCaseInputFilePropertyToCloudFormation(properties){return cdk().canInspect(properties)?(CfnTestCaseInputFilePropertyValidator(properties).assertSuccess(),{FileMetadata:convertCfnTestCaseFileMetadataPropertyToCloudFormation(properties.fileMetadata),SourceLocation:cdk().stringToCloudFormation(properties.sourceLocation),TargetLocation:cdk().stringToCloudFormation(properties.targetLocation)}):properties}function CfnTestCaseInputFilePropertyFromCloudFormation(properties){if(cdk().isResolvableObject(properties))return new(cfn_parse()).FromCloudFormationResult(properties);if(properties=properties??{},!(properties&&typeof properties=="object"&&!Array.isArray(properties)))return new(cfn_parse()).FromCloudFormationResult(properties);const ret=new(cfn_parse()).FromCloudFormationPropertyObject;return ret.addPropertyResult("fileMetadata","FileMetadata",properties.FileMetadata!=null?CfnTestCaseFileMetadataPropertyFromCloudFormation(properties.FileMetadata):void 0),ret.addPropertyResult("sourceLocation","SourceLocation",properties.SourceLocation!=null?cfn_parse().FromCloudFormation.getString(properties.SourceLocation):void 0),ret.addPropertyResult("targetLocation","TargetLocation",properties.TargetLocation!=null?cfn_parse().FromCloudFormation.getString(properties.TargetLocation):void 0),ret.addUnrecognizedPropertiesAsExtra(properties),ret}function CfnTestCaseInputPropertyValidator(properties){if(!cdk().canInspect(properties))return cdk().VALIDATION_SUCCESS;const errors=new(cdk()).ValidationResults;return properties&&typeof properties=="object"&&!Array.isArray(properties)||errors.collect(new(cdk()).ValidationResult("Expected an object, but received: "+JSON.stringify(properties))),errors.collect(cdk().propertyValidator("file",cdk().requiredValidator)(properties.file)),errors.collect(cdk().propertyValidator("file",CfnTestCaseInputFilePropertyValidator)(properties.file)),errors.wrap('supplied properties not correct for "InputProperty"')}function convertCfnTestCaseInputPropertyToCloudFormation(properties){return cdk().canInspect(properties)?(CfnTestCaseInputPropertyValidator(properties).assertSuccess(),{File:convertCfnTestCaseInputFilePropertyToCloudFormation(properties.file)}):properties}function CfnTestCaseInputPropertyFromCloudFormation(properties){if(cdk().isResolvableObject(properties))return new(cfn_parse()).FromCloudFormationResult(properties);if(properties=properties??{},!(properties&&typeof properties=="object"&&!Array.isArray(properties)))return new(cfn_parse()).FromCloudFormationResult(properties);const ret=new(cfn_parse()).FromCloudFormationPropertyObject;return ret.addPropertyResult("file","File",properties.File!=null?CfnTestCaseInputFilePropertyFromCloudFormation(properties.File):void 0),ret.addUnrecognizedPropertiesAsExtra(properties),ret}function CfnTestCaseOutputFilePropertyValidator(properties){if(!cdk().canInspect(properties))return cdk().VALIDATION_SUCCESS;const errors=new(cdk()).ValidationResults;return properties&&typeof properties=="object"&&!Array.isArray(properties)||errors.collect(new(cdk()).ValidationResult("Expected an object, but received: "+JSON.stringify(properties))),errors.collect(cdk().propertyValidator("fileLocation",cdk().validateString)(properties.fileLocation)),errors.wrap('supplied properties not correct for "OutputFileProperty"')}function convertCfnTestCaseOutputFilePropertyToCloudFormation(properties){return cdk().canInspect(properties)?(CfnTestCaseOutputFilePropertyValidator(properties).assertSuccess(),{FileLocation:cdk().stringToCloudFormation(properties.fileLocation)}):properties}function CfnTestCaseOutputFilePropertyFromCloudFormation(properties){if(cdk().isResolvableObject(properties))return new(cfn_parse()).FromCloudFormationResult(properties);if(properties=properties??{},!(properties&&typeof properties=="object"&&!Array.isArray(properties)))return new(cfn_parse()).FromCloudFormationResult(properties);const ret=new(cfn_parse()).FromCloudFormationPropertyObject;return ret.addPropertyResult("fileLocation","FileLocation",properties.FileLocation!=null?cfn_parse().FromCloudFormation.getString(properties.FileLocation):void 0),ret.addUnrecognizedPropertiesAsExtra(properties),ret}function CfnTestCaseOutputPropertyValidator(properties){if(!cdk().canInspect(properties))return cdk().VALIDATION_SUCCESS;const errors=new(cdk()).ValidationResults;return properties&&typeof properties=="object"&&!Array.isArray(properties)||errors.collect(new(cdk()).ValidationResult("Expected an object, but received: "+JSON.stringify(properties))),errors.collect(cdk().propertyValidator("file",cdk().requiredValidator)(properties.file)),errors.collect(cdk().propertyValidator("file",CfnTestCaseOutputFilePropertyValidator)(properties.file)),errors.wrap('supplied properties not correct for "OutputProperty"')}function convertCfnTestCaseOutputPropertyToCloudFormation(properties){return cdk().canInspect(properties)?(CfnTestCaseOutputPropertyValidator(properties).assertSuccess(),{File:convertCfnTestCaseOutputFilePropertyToCloudFormation(properties.file)}):properties}function CfnTestCaseOutputPropertyFromCloudFormation(properties){if(cdk().isResolvableObject(properties))return new(cfn_parse()).FromCloudFormationResult(properties);if(properties=properties??{},!(properties&&typeof properties=="object"&&!Array.isArray(properties)))return new(cfn_parse()).FromCloudFormationResult(properties);const ret=new(cfn_parse()).FromCloudFormationPropertyObject;return ret.addPropertyResult("file","File",properties.File!=null?CfnTestCaseOutputFilePropertyFromCloudFormation(properties.File):void 0),ret.addUnrecognizedPropertiesAsExtra(properties),ret}function CfnTestCaseCompareActionPropertyValidator(properties){if(!cdk().canInspect(properties))return cdk().VALIDATION_SUCCESS;const errors=new(cdk()).ValidationResults;return properties&&typeof properties=="object"&&!Array.isArray(properties)||errors.collect(new(cdk()).ValidationResult("Expected an object, but received: "+JSON.stringify(properties))),errors.collect(cdk().propertyValidator("input",cdk().requiredValidator)(properties.input)),errors.collect(cdk().propertyValidator("input",CfnTestCaseInputPropertyValidator)(properties.input)),errors.collect(cdk().propertyValidator("output",CfnTestCaseOutputPropertyValidator)(properties.output)),errors.wrap('supplied properties not correct for "CompareActionProperty"')}function convertCfnTestCaseCompareActionPropertyToCloudFormation(properties){return cdk().canInspect(properties)?(CfnTestCaseCompareActionPropertyValidator(properties).assertSuccess(),{Input:convertCfnTestCaseInputPropertyToCloudFormation(properties.input),Output:convertCfnTestCaseOutputPropertyToCloudFormation(properties.output)}):properties}function CfnTestCaseCompareActionPropertyFromCloudFormation(properties){if(cdk().isResolvableObject(properties))return new(cfn_parse()).FromCloudFormationResult(properties);if(properties=properties??{},!(properties&&typeof properties=="object"&&!Array.isArray(properties)))return new(cfn_parse()).FromCloudFormationResult(properties);const ret=new(cfn_parse()).FromCloudFormationPropertyObject;return ret.addPropertyResult("input","Input",properties.Input!=null?CfnTestCaseInputPropertyFromCloudFormation(properties.Input):void 0),ret.addPropertyResult("output","Output",properties.Output!=null?CfnTestCaseOutputPropertyFromCloudFormation(properties.Output):void 0),ret.addUnrecognizedPropertiesAsExtra(properties),ret}function CfnTestCaseStepActionPropertyValidator(properties){if(!cdk().canInspect(properties))return cdk().VALIDATION_SUCCESS;const errors=new(cdk()).ValidationResults;return properties&&typeof properties=="object"&&!Array.isArray(properties)||errors.collect(new(cdk()).ValidationResult("Expected an object, but received: "+JSON.stringify(properties))),errors.collect(cdk().propertyValidator("compareAction",CfnTestCaseCompareActionPropertyValidator)(properties.compareAction)),errors.collect(cdk().propertyValidator("mainframeAction",CfnTestCaseMainframeActionPropertyValidator)(properties.mainframeAction)),errors.collect(cdk().propertyValidator("resourceAction",CfnTestCaseResourceActionPropertyValidator)(properties.resourceAction)),errors.wrap('supplied properties not correct for "StepActionProperty"')}function convertCfnTestCaseStepActionPropertyToCloudFormation(properties){return cdk().canInspect(properties)?(CfnTestCaseStepActionPropertyValidator(properties).assertSuccess(),{CompareAction:convertCfnTestCaseCompareActionPropertyToCloudFormation(properties.compareAction),MainframeAction:convertCfnTestCaseMainframeActionPropertyToCloudFormation(properties.mainframeAction),ResourceAction:convertCfnTestCaseResourceActionPropertyToCloudFormation(properties.resourceAction)}):properties}function CfnTestCaseStepActionPropertyFromCloudFormation(properties){if(cdk().isResolvableObject(properties))return new(cfn_parse()).FromCloudFormationResult(properties);if(properties=properties??{},!(properties&&typeof properties=="object"&&!Array.isArray(properties)))return new(cfn_parse()).FromCloudFormationResult(properties);const ret=new(cfn_parse()).FromCloudFormationPropertyObject;return ret.addPropertyResult("compareAction","CompareAction",properties.CompareAction!=null?CfnTestCaseCompareActionPropertyFromCloudFormation(properties.CompareAction):void 0),ret.addPropertyResult("mainframeAction","MainframeAction",properties.MainframeAction!=null?CfnTestCaseMainframeActionPropertyFromCloudFormation(properties.MainframeAction):void 0),ret.addPropertyResult("resourceAction","ResourceAction",properties.ResourceAction!=null?CfnTestCaseResourceActionPropertyFromCloudFormation(properties.ResourceAction):void 0),ret.addUnrecognizedPropertiesAsExtra(properties),ret}function CfnTestCaseStepPropertyValidator(properties){if(!cdk().canInspect(properties))return cdk().VALIDATION_SUCCESS;const errors=new(cdk()).ValidationResults;return properties&&typeof properties=="object"&&!Array.isArray(properties)||errors.collect(new(cdk()).ValidationResult("Expected an object, but received: "+JSON.stringify(properties))),errors.collect(cdk().propertyValidator("action",cdk().requiredValidator)(properties.action)),errors.collect(cdk().propertyValidator("action",CfnTestCaseStepActionPropertyValidator)(properties.action)),errors.collect(cdk().propertyValidator("description",cdk().validateString)(properties.description)),errors.collect(cdk().propertyValidator("name",cdk().requiredValidator)(properties.name)),errors.collect(cdk().propertyValidator("name",cdk().validateString)(properties.name)),errors.wrap('supplied properties not correct for "StepProperty"')}function convertCfnTestCaseStepPropertyToCloudFormation(properties){return cdk().canInspect(properties)?(CfnTestCaseStepPropertyValidator(properties).assertSuccess(),{Action:convertCfnTestCaseStepActionPropertyToCloudFormation(properties.action),Description:cdk().stringToCloudFormation(properties.description),Name:cdk().stringToCloudFormation(properties.name)}):properties}function CfnTestCaseStepPropertyFromCloudFormation(properties){if(cdk().isResolvableObject(properties))return new(cfn_parse()).FromCloudFormationResult(properties);if(properties=properties??{},!(properties&&typeof properties=="object"&&!Array.isArray(properties)))return new(cfn_parse()).FromCloudFormationResult(properties);const ret=new(cfn_parse()).FromCloudFormationPropertyObject;return ret.addPropertyResult("action","Action",properties.Action!=null?CfnTestCaseStepActionPropertyFromCloudFormation(properties.Action):void 0),ret.addPropertyResult("description","Description",properties.Description!=null?cfn_parse().FromCloudFormation.getString(properties.Description):void 0),ret.addPropertyResult("name","Name",properties.Name!=null?cfn_parse().FromCloudFormation.getString(properties.Name):void 0),ret.addUnrecognizedPropertiesAsExtra(properties),ret}function CfnTestCaseTestCaseLatestVersionPropertyValidator(properties){if(!cdk().canInspect(properties))return cdk().VALIDATION_SUCCESS;const errors=new(cdk()).ValidationResults;return properties&&typeof properties=="object"&&!Array.isArray(properties)||errors.collect(new(cdk()).ValidationResult("Expected an object, but received: "+JSON.stringify(properties))),errors.collect(cdk().propertyValidator("status",cdk().requiredValidator)(properties.status)),errors.collect(cdk().propertyValidator("status",cdk().validateString)(properties.status)),errors.collect(cdk().propertyValidator("version",cdk().requiredValidator)(properties.version)),errors.collect(cdk().propertyValidator("version",cdk().validateNumber)(properties.version)),errors.wrap('supplied properties not correct for "TestCaseLatestVersionProperty"')}function convertCfnTestCaseTestCaseLatestVersionPropertyToCloudFormation(properties){return cdk().canInspect(properties)?(CfnTestCaseTestCaseLatestVersionPropertyValidator(properties).assertSuccess(),{Status:cdk().stringToCloudFormation(properties.status),Version:cdk().numberToCloudFormation(properties.version)}):properties}function CfnTestCaseTestCaseLatestVersionPropertyFromCloudFormation(properties){if(cdk().isResolvableObject(properties))return new(cfn_parse()).FromCloudFormationResult(properties);if(properties=properties??{},!(properties&&typeof properties=="object"&&!Array.isArray(properties)))return new(cfn_parse()).FromCloudFormationResult(properties);const ret=new(cfn_parse()).FromCloudFormationPropertyObject;return ret.addPropertyResult("status","Status",properties.Status!=null?cfn_parse().FromCloudFormation.getString(properties.Status):void 0),ret.addPropertyResult("version","Version",properties.Version!=null?cfn_parse().FromCloudFormation.getNumber(properties.Version):void 0),ret.addUnrecognizedPropertiesAsExtra(properties),ret}function CfnTestCasePropsValidator(properties){if(!cdk().canInspect(properties))return cdk().VALIDATION_SUCCESS;const errors=new(cdk()).ValidationResults;return properties&&typeof properties=="object"&&!Array.isArray(properties)||errors.collect(new(cdk()).ValidationResult("Expected an object, but received: "+JSON.stringify(properties))),errors.collect(cdk().propertyValidator("description",cdk().validateString)(properties.description)),errors.collect(cdk().propertyValidator("name",cdk().requiredValidator)(properties.name)),errors.collect(cdk().propertyValidator("name",cdk().validateString)(properties.name)),errors.collect(cdk().propertyValidator("steps",cdk().requiredValidator)(properties.steps)),errors.collect(cdk().propertyValidator("steps",cdk().listValidator(CfnTestCaseStepPropertyValidator))(properties.steps)),errors.collect(cdk().propertyValidator("tags",cdk().hashValidator(cdk().validateString))(properties.tags)),errors.wrap('supplied properties not correct for "CfnTestCaseProps"')}function convertCfnTestCasePropsToCloudFormation(properties){return cdk().canInspect(properties)?(CfnTestCasePropsValidator(properties).assertSuccess(),{Description:cdk().stringToCloudFormation(properties.description),Name:cdk().stringToCloudFormation(properties.name),Steps:cdk().listMapper(convertCfnTestCaseStepPropertyToCloudFormation)(properties.steps),Tags:cdk().hashMapper(cdk().stringToCloudFormation)(properties.tags)}):properties}function CfnTestCasePropsFromCloudFormation(properties){if(cdk().isResolvableObject(properties))return new(cfn_parse()).FromCloudFormationResult(properties);if(properties=properties??{},!(properties&&typeof properties=="object"&&!Array.isArray(properties)))return new(cfn_parse()).FromCloudFormationResult(properties);const ret=new(cfn_parse()).FromCloudFormationPropertyObject;return ret.addPropertyResult("description","Description",properties.Description!=null?cfn_parse().FromCloudFormation.getString(properties.Description):void 0),ret.addPropertyResult("name","Name",properties.Name!=null?cfn_parse().FromCloudFormation.getString(properties.Name):void 0),ret.addPropertyResult("steps","Steps",properties.Steps!=null?cfn_parse().FromCloudFormation.getArray(CfnTestCaseStepPropertyFromCloudFormation)(properties.Steps):void 0),ret.addPropertyResult("tags","Tags",properties.Tags!=null?cfn_parse().FromCloudFormation.getMap(cfn_parse().FromCloudFormation.getString)(properties.Tags):void 0),ret.addUnrecognizedPropertiesAsExtra(properties),ret}
