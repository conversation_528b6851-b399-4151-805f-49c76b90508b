"use strict";var __decorate=exports&&exports.__decorate||function(decorators,target,key,desc){var c=arguments.length,r=c<3?target:desc===null?desc=Object.getOwnPropertyDescriptor(target,key):desc,d;if(typeof Reflect=="object"&&typeof Reflect.decorate=="function")r=Reflect.decorate(decorators,target,key,desc);else for(var i=decorators.length-1;i>=0;i--)(d=decorators[i])&&(r=(c<3?d(r):c>3?d(target,key,r):d(target,key))||r);return c>3&&r&&Object.defineProperty(target,key,r),r},_a;Object.defineProperty(exports,"__esModule",{value:!0}),exports.ProfilingGroup=exports.ComputePlatform=void 0;var jsiiDeprecationWarnings=()=>{var tmp=require("../../.warnings.jsii.js");return jsiiDeprecationWarnings=()=>tmp,tmp};const JSII_RTTI_SYMBOL_1=Symbol.for("jsii.rtti");var codeguruprofiler_generated_1=()=>{var tmp=require("./codeguruprofiler.generated");return codeguruprofiler_generated_1=()=>tmp,tmp},aws_iam_1=()=>{var tmp=require("../../aws-iam");return aws_iam_1=()=>tmp,tmp},core_1=()=>{var tmp=require("../../core");return core_1=()=>tmp,tmp},metadata_resource_1=()=>{var tmp=require("../../core/lib/metadata-resource");return metadata_resource_1=()=>tmp,tmp},prop_injectable_1=()=>{var tmp=require("../../core/lib/prop-injectable");return prop_injectable_1=()=>tmp,tmp},ComputePlatform;(function(ComputePlatform2){ComputePlatform2.AWS_LAMBDA="AWSLambda",ComputePlatform2.DEFAULT="Default"})(ComputePlatform||(exports.ComputePlatform=ComputePlatform={}));class ProfilingGroupBase extends core_1().Resource{grantPublish(grantee){return aws_iam_1().Grant.addToPrincipal({grantee,actions:["codeguru-profiler:ConfigureAgent","codeguru-profiler:PostAgentProfile"],resourceArns:[this.profilingGroupArn]})}grantRead(grantee){return aws_iam_1().Grant.addToPrincipal({grantee,actions:["codeguru-profiler:GetProfile","codeguru-profiler:DescribeProfilingGroup"],resourceArns:[this.profilingGroupArn]})}}let ProfilingGroup=class ProfilingGroup2 extends ProfilingGroupBase{static fromProfilingGroupName(scope,id,profilingGroupName){const stack=core_1().Stack.of(scope);return this.fromProfilingGroupArn(scope,id,stack.formatArn({service:"codeguru-profiler",resource:"profilingGroup",resourceName:profilingGroupName}))}static fromProfilingGroupArn(scope,id,profilingGroupArn){class Import extends ProfilingGroupBase{constructor(){super(...arguments),this.profilingGroupName=core_1().Stack.of(scope).splitArn(profilingGroupArn,core_1().ArnFormat.SLASH_RESOURCE_NAME).resourceName,this.profilingGroupArn=profilingGroupArn}}return new Import(scope,id,{environmentFromArn:profilingGroupArn})}constructor(scope,id,props={}){super(scope,id,{physicalName:props.profilingGroupName??core_1().Lazy.string({produce:()=>this.generateUniqueId()})});try{jsiiDeprecationWarnings().aws_cdk_lib_aws_codeguruprofiler_ProfilingGroupProps(props)}catch(error){throw process.env.JSII_DEBUG!=="1"&&error.name==="DeprecationError"&&Error.captureStackTrace(error,ProfilingGroup2),error}(0,metadata_resource_1().addConstructMetadata)(this,props);const profilingGroup=new(codeguruprofiler_generated_1()).CfnProfilingGroup(this,"ProfilingGroup",{profilingGroupName:this.physicalName,computePlatform:props.computePlatform});this.profilingGroupName=this.getResourceNameAttribute(profilingGroup.ref),this.profilingGroupArn=this.getResourceArnAttribute(profilingGroup.attrArn,{service:"codeguru-profiler",resource:"profilingGroup",resourceName:this.physicalName})}generateUniqueId(){const name=core_1().Names.uniqueId(this);return name.length>240?name.substring(0,120)+name.substring(name.length-120):name}};exports.ProfilingGroup=ProfilingGroup,_a=JSII_RTTI_SYMBOL_1,ProfilingGroup[_a]={fqn:"aws-cdk-lib.aws_codeguruprofiler.ProfilingGroup",version:"2.201.0"},ProfilingGroup.PROPERTY_INJECTION_ID="aws-cdk-lib.aws-codeguruprofiler.ProfilingGroup",exports.ProfilingGroup=ProfilingGroup=__decorate([prop_injectable_1().propertyInjectable],ProfilingGroup);
