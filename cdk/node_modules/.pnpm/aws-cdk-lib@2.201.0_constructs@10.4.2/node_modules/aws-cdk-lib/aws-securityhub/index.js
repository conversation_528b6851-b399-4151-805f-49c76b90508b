"use strict";var __createBinding=exports&&exports.__createBinding||(Object.create?function(o,m,k,k2){k2===void 0&&(k2=k);var desc=Object.getOwnPropertyDescriptor(m,k);(!desc||("get"in desc?!m.__esModule:desc.writable||desc.configurable))&&(desc={enumerable:!0,get:function(){return m[k]}}),Object.defineProperty(o,k2,desc)}:function(o,m,k,k2){k2===void 0&&(k2=k),o[k2]=m[k]}),__exportStar=exports&&exports.__exportStar||function(m,exports2){for(var p in m)p!=="default"&&!Object.prototype.hasOwnProperty.call(exports2,p)&&__createBinding(exports2,m,p)};Object.defineProperty(exports,"__esModule",{value:!0});var _noFold;exports.CfnAutomationRule=void 0,Object.defineProperty(exports,_noFold="CfnAutomationRule",{enumerable:!0,configurable:!0,get:()=>require("./lib").CfnAutomationRule}),exports.CfnHub=void 0,Object.defineProperty(exports,_noFold="CfnHub",{enumerable:!0,configurable:!0,get:()=>require("./lib").CfnHub}),exports.CfnStandard=void 0,Object.defineProperty(exports,_noFold="CfnStandard",{enumerable:!0,configurable:!0,get:()=>require("./lib").CfnStandard}),exports.CfnConfigurationPolicy=void 0,Object.defineProperty(exports,_noFold="CfnConfigurationPolicy",{enumerable:!0,configurable:!0,get:()=>require("./lib").CfnConfigurationPolicy}),exports.CfnDelegatedAdmin=void 0,Object.defineProperty(exports,_noFold="CfnDelegatedAdmin",{enumerable:!0,configurable:!0,get:()=>require("./lib").CfnDelegatedAdmin}),exports.CfnFindingAggregator=void 0,Object.defineProperty(exports,_noFold="CfnFindingAggregator",{enumerable:!0,configurable:!0,get:()=>require("./lib").CfnFindingAggregator}),exports.CfnInsight=void 0,Object.defineProperty(exports,_noFold="CfnInsight",{enumerable:!0,configurable:!0,get:()=>require("./lib").CfnInsight}),exports.CfnOrganizationConfiguration=void 0,Object.defineProperty(exports,_noFold="CfnOrganizationConfiguration",{enumerable:!0,configurable:!0,get:()=>require("./lib").CfnOrganizationConfiguration}),exports.CfnPolicyAssociation=void 0,Object.defineProperty(exports,_noFold="CfnPolicyAssociation",{enumerable:!0,configurable:!0,get:()=>require("./lib").CfnPolicyAssociation}),exports.CfnProductSubscription=void 0,Object.defineProperty(exports,_noFold="CfnProductSubscription",{enumerable:!0,configurable:!0,get:()=>require("./lib").CfnProductSubscription}),exports.CfnSecurityControl=void 0,Object.defineProperty(exports,_noFold="CfnSecurityControl",{enumerable:!0,configurable:!0,get:()=>require("./lib").CfnSecurityControl});
