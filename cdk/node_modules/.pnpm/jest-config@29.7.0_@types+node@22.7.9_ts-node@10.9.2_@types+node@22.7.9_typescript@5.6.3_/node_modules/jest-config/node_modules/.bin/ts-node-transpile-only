#!/bin/sh
basedir=$(dirname "$(echo "$0" | sed -e 's,\\,/,g')")

case `uname` in
    *CYGWIN*) basedir=`cygpath -w "$basedir"`;;
esac

if [ -z "$NODE_PATH" ]; then
  export NODE_PATH="/Users/<USER>/dev/Graduation_Project/erd-ai-platform/eap-infra/cdk/node_modules/.pnpm/ts-node@10.9.2_@types+node@22.7.9_typescript@5.6.3/node_modules/ts-node/dist/node_modules:/Users/<USER>/dev/Graduation_Project/erd-ai-platform/eap-infra/cdk/node_modules/.pnpm/ts-node@10.9.2_@types+node@22.7.9_typescript@5.6.3/node_modules/ts-node/node_modules:/Users/<USER>/dev/Graduation_Project/erd-ai-platform/eap-infra/cdk/node_modules/.pnpm/ts-node@10.9.2_@types+node@22.7.9_typescript@5.6.3/node_modules:/Users/<USER>/dev/Graduation_Project/erd-ai-platform/eap-infra/cdk/node_modules/.pnpm/node_modules"
else
  export NODE_PATH="/Users/<USER>/dev/Graduation_Project/erd-ai-platform/eap-infra/cdk/node_modules/.pnpm/ts-node@10.9.2_@types+node@22.7.9_typescript@5.6.3/node_modules/ts-node/dist/node_modules:/Users/<USER>/dev/Graduation_Project/erd-ai-platform/eap-infra/cdk/node_modules/.pnpm/ts-node@10.9.2_@types+node@22.7.9_typescript@5.6.3/node_modules/ts-node/node_modules:/Users/<USER>/dev/Graduation_Project/erd-ai-platform/eap-infra/cdk/node_modules/.pnpm/ts-node@10.9.2_@types+node@22.7.9_typescript@5.6.3/node_modules:/Users/<USER>/dev/Graduation_Project/erd-ai-platform/eap-infra/cdk/node_modules/.pnpm/node_modules:$NODE_PATH"
fi
if [ -x "$basedir/node" ]; then
  exec "$basedir/node"  "$basedir/../../../../../ts-node@10.9.2_@types+node@22.7.9_typescript@5.6.3/node_modules/ts-node/dist/bin-transpile.js" "$@"
else
  exec node  "$basedir/../../../../../ts-node@10.9.2_@types+node@22.7.9_typescript@5.6.3/node_modules/ts-node/dist/bin-transpile.js" "$@"
fi
