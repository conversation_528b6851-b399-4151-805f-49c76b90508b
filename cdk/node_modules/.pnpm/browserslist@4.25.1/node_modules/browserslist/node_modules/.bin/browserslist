#!/bin/sh
basedir=$(dirname "$(echo "$0" | sed -e 's,\\,/,g')")

case `uname` in
    *CYGWIN*) basedir=`cygpath -w "$basedir"`;;
esac

if [ -z "$NODE_PATH" ]; then
  export NODE_PATH="/Users/<USER>/dev/Graduation_Project/erd-ai-platform/eap-infra/cdk/node_modules/.pnpm/browserslist@4.25.1/node_modules/browserslist/node_modules:/Users/<USER>/dev/Graduation_Project/erd-ai-platform/eap-infra/cdk/node_modules/.pnpm/browserslist@4.25.1/node_modules:/Users/<USER>/dev/Graduation_Project/erd-ai-platform/eap-infra/cdk/node_modules/.pnpm/node_modules"
else
  export NODE_PATH="/Users/<USER>/dev/Graduation_Project/erd-ai-platform/eap-infra/cdk/node_modules/.pnpm/browserslist@4.25.1/node_modules/browserslist/node_modules:/Users/<USER>/dev/Graduation_Project/erd-ai-platform/eap-infra/cdk/node_modules/.pnpm/browserslist@4.25.1/node_modules:/Users/<USER>/dev/Graduation_Project/erd-ai-platform/eap-infra/cdk/node_modules/.pnpm/node_modules:$NODE_PATH"
fi
if [ -x "$basedir/node" ]; then
  exec "$basedir/node"  "$basedir/../../cli.js" "$@"
else
  exec node  "$basedir/../../cli.js" "$@"
fi
