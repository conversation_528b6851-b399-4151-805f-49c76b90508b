#!/bin/sh
basedir=$(dirname "$(echo "$0" | sed -e 's,\\,/,g')")

case `uname` in
    *CYGWIN*) basedir=`cygpath -w "$basedir"`;;
esac

if [ -z "$NODE_PATH" ]; then
  export NODE_PATH="/Users/<USER>/dev/Graduation_Project/erd-ai-platform/eap-infra/cdk/node_modules/.pnpm/js-yaml@3.14.1/node_modules/js-yaml/bin/node_modules:/Users/<USER>/dev/Graduation_Project/erd-ai-platform/eap-infra/cdk/node_modules/.pnpm/js-yaml@3.14.1/node_modules/js-yaml/node_modules:/Users/<USER>/dev/Graduation_Project/erd-ai-platform/eap-infra/cdk/node_modules/.pnpm/js-yaml@3.14.1/node_modules:/Users/<USER>/dev/Graduation_Project/erd-ai-platform/eap-infra/cdk/node_modules/.pnpm/node_modules"
else
  export NODE_PATH="/Users/<USER>/dev/Graduation_Project/erd-ai-platform/eap-infra/cdk/node_modules/.pnpm/js-yaml@3.14.1/node_modules/js-yaml/bin/node_modules:/Users/<USER>/dev/Graduation_Project/erd-ai-platform/eap-infra/cdk/node_modules/.pnpm/js-yaml@3.14.1/node_modules/js-yaml/node_modules:/Users/<USER>/dev/Graduation_Project/erd-ai-platform/eap-infra/cdk/node_modules/.pnpm/js-yaml@3.14.1/node_modules:/Users/<USER>/dev/Graduation_Project/erd-ai-platform/eap-infra/cdk/node_modules/.pnpm/node_modules:$NODE_PATH"
fi
if [ -x "$basedir/node" ]; then
  exec "$basedir/node"  "$basedir/../../bin/js-yaml.js" "$@"
else
  exec node  "$basedir/../../bin/js-yaml.js" "$@"
fi
